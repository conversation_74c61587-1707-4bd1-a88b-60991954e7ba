#!/bin/bash

# Quick fix script for immediate troubleshooting
# This will diagnose and fix common issues

SERVER_IP="***************"
DOMAIN="partner.nawrasinchina.com"

echo "🔍 Quick diagnosis and fix for Nawras Admin..."

# Function to run commands on server
run_on_server() {
    ssh -o StrictHostKeyChecking=no root@$SERVER_IP "$1"
}

echo "📊 Checking server status..."

# Check if server is accessible
if ! ping -c 1 $SERVER_IP > /dev/null 2>&1; then
    echo "❌ Server $SERVER_IP is not reachable"
    exit 1
fi

echo "✅ Server is reachable"

# Check Docker containers
echo "🐳 Checking Docker containers..."
run_on_server "
cd /opt/nawras-admin 2>/dev/null || cd /root

echo '=== Docker Status ==='
docker --version
docker-compose --version

echo '=== Container Status ==='
docker ps -a

echo '=== Container Logs ==='
docker-compose logs --tail=20 2>/dev/null || echo 'No docker-compose found'
"

# Check ports
echo "🔌 Checking open ports..."
run_on_server "
echo '=== Open Ports ==='
netstat -tlnp | grep -E ':(80|443|3001) '

echo '=== Firewall Status ==='
ufw status
"

# Check Nginx
echo "🌐 Checking Nginx..."
run_on_server "
echo '=== Nginx Status ==='
systemctl status nginx --no-pager

echo '=== Nginx Configuration Test ==='
nginx -t 2>&1 || echo 'Nginx config has issues'

echo '=== Nginx Error Logs ==='
tail -20 /var/log/nginx/error.log 2>/dev/null || echo 'No nginx error logs'
"

# Quick restart attempt
echo "🔄 Attempting quick restart..."
run_on_server "
cd /opt/nawras-admin 2>/dev/null || {
    echo 'App directory not found, creating...'
    mkdir -p /opt/nawras-admin
    exit 1
}

# Restart containers
docker-compose down
docker-compose up -d

# Restart nginx
systemctl restart nginx

# Show final status
echo '=== Final Status ==='
docker-compose ps
systemctl status nginx --no-pager -l
"

echo "🧪 Testing endpoints..."
echo "Testing HTTP access..."
curl -I http://$SERVER_IP 2>/dev/null || echo "❌ HTTP not accessible"
curl -I http://$SERVER_IP:3001/api/expenses 2>/dev/null || echo "❌ API not accessible"

echo ""
echo "✅ Quick fix completed!"
echo "🔗 Try accessing: http://$SERVER_IP"
echo "🔗 API endpoint: http://$SERVER_IP:3001/api/expenses"
