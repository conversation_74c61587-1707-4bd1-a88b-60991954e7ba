{"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.9.1", "author": "whitequark <<EMAIL>>", "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "nodeunit": "^0.11.3", "uglify-js": "~3.0.19"}, "scripts": {"test": "cake build test"}, "files": ["lib/", "LICENSE", "ipaddr.min.js"], "keywords": ["ip", "ipv4", "ipv6"], "repository": "git://github.com/whitequark/ipaddr.js", "main": "./lib/ipaddr.js", "engines": {"node": ">= 0.10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts"}