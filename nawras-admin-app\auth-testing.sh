#!/bin/bash

# Authentication Testing Suite
# Comprehensive testing for Better Auth implementation

DOMAIN="partner.nawrasinchina.com"
SERVER_IP="*************"

echo "🔐 AUTHENTICATION TESTING SUITE"
echo "==============================="
echo "Domain: $DOMAIN"
echo "Server: $SERVER_IP"
echo ""

# Initialize test results
declare -A auth_test_results
total_auth_tests=0
passed_auth_tests=0

# Function to record auth test result
record_auth_test() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    auth_test_results["$test_name"]="$result|$details"
    ((total_auth_tests++))
    
    if [ "$result" = "PASS" ]; then
        ((passed_auth_tests++))
        echo "✅ $test_name: PASSED - $details"
    else
        echo "❌ $test_name: FAILED - $details"
    fi
}

# Test Suite 1: Authentication Endpoints
echo "🔑 TEST SUITE 1: AUTHENTICATION ENDPOINTS"
echo "========================================="

# Test 1.1: Auth Session Endpoint
echo "Testing auth session endpoint..."
session_response=$(curl -s -w "%{http_code}" https://$DOMAIN/api/auth/session -o /tmp/session_test.json)
if [ "$session_response" = "200" ]; then
    if jq -e '.success' /tmp/session_test.json >/dev/null 2>&1; then
        record_auth_test "Auth_Session_Endpoint" "PASS" "Session endpoint responding correctly"
    else
        record_auth_test "Auth_Session_Endpoint" "FAIL" "Invalid JSON response structure"
    fi
else
    record_auth_test "Auth_Session_Endpoint" "FAIL" "Endpoint not accessible (HTTP $session_response)"
fi

# Test 1.2: Sign-in Endpoint Structure
echo "Testing sign-in endpoint structure..."
signin_test=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"invalid"}' \
    -o /tmp/signin_test.json)

if [ "$signin_test" = "401" ] || [ "$signin_test" = "400" ]; then
    if jq -e '.success == false' /tmp/signin_test.json >/dev/null 2>&1; then
        record_auth_test "SignIn_Endpoint_Structure" "PASS" "Properly rejects invalid credentials"
    else
        record_auth_test "SignIn_Endpoint_Structure" "FAIL" "Invalid error response structure"
    fi
else
    record_auth_test "SignIn_Endpoint_Structure" "FAIL" "Unexpected response (HTTP $signin_test)"
fi

# Test 1.3: Sign-out Endpoint
echo "Testing sign-out endpoint..."
signout_response=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-out -o /tmp/signout_test.json)
if [ "$signout_response" = "200" ]; then
    if jq -e '.success' /tmp/signout_test.json >/dev/null 2>&1; then
        record_auth_test "SignOut_Endpoint" "PASS" "Sign-out endpoint working"
    else
        record_auth_test "SignOut_Endpoint" "FAIL" "Invalid sign-out response"
    fi
else
    record_auth_test "SignOut_Endpoint" "FAIL" "Sign-out endpoint not accessible (HTTP $signout_response)"
fi

echo ""

# Test Suite 2: User Authentication Flow
echo "👥 TEST SUITE 2: USER AUTHENTICATION FLOW"
echo "=========================================="

# Test 2.1: Taha Authentication
echo "Testing Taha authentication..."
taha_auth=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"taha2024"}' \
    -c /tmp/taha_cookies.txt \
    -o /tmp/taha_auth.json)

if [ "$taha_auth" = "200" ]; then
    if jq -e '.success and .data.user.name == "Taha"' /tmp/taha_auth.json >/dev/null 2>&1; then
        record_auth_test "Taha_Authentication" "PASS" "Taha can sign in successfully"
        TAHA_SESSION_VALID=true
    else
        record_auth_test "Taha_Authentication" "FAIL" "Invalid authentication response for Taha"
        TAHA_SESSION_VALID=false
    fi
else
    record_auth_test "Taha_Authentication" "FAIL" "Taha authentication failed (HTTP $taha_auth)"
    TAHA_SESSION_VALID=false
fi

# Test 2.2: Burak Authentication
echo "Testing Burak authentication..."
burak_auth=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"burak2024"}' \
    -c /tmp/burak_cookies.txt \
    -o /tmp/burak_auth.json)

if [ "$burak_auth" = "200" ]; then
    if jq -e '.success and .data.user.name == "Burak"' /tmp/burak_auth.json >/dev/null 2>&1; then
        record_auth_test "Burak_Authentication" "PASS" "Burak can sign in successfully"
        BURAK_SESSION_VALID=true
    else
        record_auth_test "Burak_Authentication" "FAIL" "Invalid authentication response for Burak"
        BURAK_SESSION_VALID=false
    fi
else
    record_auth_test "Burak_Authentication" "FAIL" "Burak authentication failed (HTTP $burak_auth)"
    BURAK_SESSION_VALID=false
fi

# Test 2.3: Invalid User Rejection
echo "Testing invalid user rejection..."
invalid_auth=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"password123"}' \
    -o /tmp/invalid_auth.json)

if [ "$invalid_auth" = "401" ] || [ "$invalid_auth" = "400" ]; then
    if jq -e '.success == false' /tmp/invalid_auth.json >/dev/null 2>&1; then
        record_auth_test "Invalid_User_Rejection" "PASS" "Properly rejects unauthorized users"
    else
        record_auth_test "Invalid_User_Rejection" "FAIL" "Invalid error response for unauthorized user"
    fi
else
    record_auth_test "Invalid_User_Rejection" "FAIL" "Should reject unauthorized users (HTTP $invalid_auth)"
fi

echo ""

# Test Suite 3: Protected Route Access
echo "🛡️ TEST SUITE 3: PROTECTED ROUTE ACCESS"
echo "========================================"

# Test 3.1: Unauthenticated API Access
echo "Testing unauthenticated API access..."
unauth_api=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses -o /tmp/unauth_api.json)
if [ "$unauth_api" = "401" ]; then
    record_auth_test "Unauthenticated_API_Block" "PASS" "API properly blocks unauthenticated access"
elif [ "$unauth_api" = "200" ]; then
    # Check if it's actually protected or just returns empty data
    if jq -e '.success == false' /tmp/unauth_api.json >/dev/null 2>&1; then
        record_auth_test "Unauthenticated_API_Block" "PASS" "API returns error for unauthenticated access"
    else
        record_auth_test "Unauthenticated_API_Block" "FAIL" "API allows unauthenticated access"
    fi
else
    record_auth_test "Unauthenticated_API_Block" "FAIL" "Unexpected API response (HTTP $unauth_api)"
fi

# Test 3.2: Authenticated API Access (if Taha session is valid)
if [ "$TAHA_SESSION_VALID" = true ]; then
    echo "Testing authenticated API access with Taha's session..."
    auth_api=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses \
        -b /tmp/taha_cookies.txt \
        -o /tmp/auth_api.json)
    
    if [ "$auth_api" = "200" ]; then
        if jq -e '.success and .data' /tmp/auth_api.json >/dev/null 2>&1; then
            expense_count=$(jq '.data | length' /tmp/auth_api.json)
            record_auth_test "Authenticated_API_Access" "PASS" "API accessible with valid session ($expense_count expenses)"
        else
            record_auth_test "Authenticated_API_Access" "FAIL" "Invalid API response structure"
        fi
    else
        record_auth_test "Authenticated_API_Access" "FAIL" "API not accessible with valid session (HTTP $auth_api)"
    fi
else
    record_auth_test "Authenticated_API_Access" "SKIP" "Skipped - no valid session available"
fi

# Test 3.3: Session Persistence
if [ "$TAHA_SESSION_VALID" = true ]; then
    echo "Testing session persistence..."
    session_check=$(curl -s -w "%{http_code}" https://$DOMAIN/api/auth/session \
        -b /tmp/taha_cookies.txt \
        -o /tmp/session_check.json)
    
    if [ "$session_check" = "200" ]; then
        if jq -e '.success and .data.user.name == "Taha"' /tmp/session_check.json >/dev/null 2>&1; then
            record_auth_test "Session_Persistence" "PASS" "Session persists correctly"
        else
            record_auth_test "Session_Persistence" "FAIL" "Session data invalid"
        fi
    else
        record_auth_test "Session_Persistence" "FAIL" "Session check failed (HTTP $session_check)"
    fi
else
    record_auth_test "Session_Persistence" "SKIP" "Skipped - no valid session available"
fi

echo ""

# Test Suite 4: Frontend Authentication
echo "🖥️ TEST SUITE 4: FRONTEND AUTHENTICATION"
echo "========================================"

# Test 4.1: Login Page Accessibility
echo "Testing login page accessibility..."
login_page=$(curl -s -w "%{http_code}" https://$DOMAIN -o /tmp/login_page.html)
if [ "$login_page" = "200" ]; then
    # Check if it contains login-related content
    if grep -qi "sign.*in\|login\|email\|password" /tmp/login_page.html; then
        record_auth_test "Login_Page_Access" "PASS" "Login page accessible and contains auth elements"
    else
        record_auth_test "Login_Page_Access" "FAIL" "Page accessible but no login elements found"
    fi
else
    record_auth_test "Login_Page_Access" "FAIL" "Login page not accessible (HTTP $login_page)"
fi

# Test 4.2: React App Loading
echo "Testing React app loading..."
if grep -qi "react\|vite" /tmp/login_page.html; then
    record_auth_test "React_App_Loading" "PASS" "React application detected"
else
    record_auth_test "React_App_Loading" "FAIL" "React application not detected"
fi

# Test 4.3: Authentication Components
echo "Testing authentication components..."
if grep -qi "AuthProvider\|ProtectedRoute\|LoginPage" /tmp/login_page.html; then
    record_auth_test "Auth_Components" "PASS" "Authentication components detected"
else
    record_auth_test "Auth_Components" "FAIL" "Authentication components not detected"
fi

echo ""

# Test Suite 5: Security Validation
echo "🔒 TEST SUITE 5: SECURITY VALIDATION"
echo "===================================="

# Test 5.1: HTTPS for Auth Endpoints
echo "Testing HTTPS for auth endpoints..."
https_auth=$(curl -s -I https://$DOMAIN/api/auth/session | head -1)
if echo "$https_auth" | grep -q "200\|401"; then
    record_auth_test "HTTPS_Auth_Endpoints" "PASS" "Auth endpoints accessible over HTTPS"
else
    record_auth_test "HTTPS_Auth_Endpoints" "FAIL" "Auth endpoints not properly secured"
fi

# Test 5.2: Secure Cookie Configuration
if [ "$TAHA_SESSION_VALID" = true ]; then
    echo "Testing secure cookie configuration..."
    # Check if cookies are set with security flags
    cookie_headers=$(curl -s -I -X POST https://$DOMAIN/api/auth/sign-in \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"taha2024"}')
    
    if echo "$cookie_headers" | grep -qi "set-cookie.*httponly\|set-cookie.*secure"; then
        record_auth_test "Secure_Cookies" "PASS" "Cookies configured with security flags"
    else
        record_auth_test "Secure_Cookies" "FAIL" "Cookies not properly secured"
    fi
else
    record_auth_test "Secure_Cookies" "SKIP" "Skipped - no valid authentication to test"
fi

# Test 5.3: Password Security
echo "Testing password security..."
# Test weak password rejection (this would be in a real implementation)
weak_password=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"123"}' \
    -o /tmp/weak_password.json)

if [ "$weak_password" = "401" ] || [ "$weak_password" = "400" ]; then
    record_auth_test "Password_Security" "PASS" "Weak passwords properly rejected"
else
    record_auth_test "Password_Security" "FAIL" "Weak password validation needs improvement"
fi

echo ""

# Generate Authentication Test Report
echo "📋 AUTHENTICATION TEST REPORT"
echo "============================="
echo ""

# Calculate success rate
auth_success_rate=$(echo "scale=1; $passed_auth_tests * 100 / $total_auth_tests" | bc -l)

echo "📊 Authentication Test Summary:"
echo "   Total Tests: $total_auth_tests"
echo "   Passed: $passed_auth_tests"
echo "   Failed: $((total_auth_tests - passed_auth_tests))"
echo "   Success Rate: $auth_success_rate%"
echo ""

# Determine overall auth status
if (( $(echo "$auth_success_rate >= 90" | bc -l) )); then
    auth_status="🟢 EXCELLENT"
elif (( $(echo "$auth_success_rate >= 80" | bc -l) )); then
    auth_status="🟡 GOOD"
elif (( $(echo "$auth_success_rate >= 70" | bc -l) )); then
    auth_status="🟠 FAIR"
else
    auth_status="🔴 NEEDS IMPROVEMENT"
fi

echo "🎯 Authentication Status: $auth_status"
echo ""

# Show failed auth tests
if [ $((total_auth_tests - passed_auth_tests)) -gt 0 ]; then
    echo "❌ Failed Authentication Tests:"
    for test_name in "${!auth_test_results[@]}"; do
        IFS='|' read -r result details <<< "${auth_test_results[$test_name]}"
        if [ "$result" = "FAIL" ]; then
            echo "   • $test_name: $details"
        fi
    done
    echo ""
fi

# Authentication-specific recommendations
echo "💡 Authentication Recommendations:"
if (( $(echo "$auth_success_rate >= 90" | bc -l) )); then
    echo "   ✅ Authentication system is production-ready!"
    echo "   ✅ Both users can sign in successfully"
    echo "   ✅ API endpoints are properly protected"
    echo "   ✅ Security measures are in place"
else
    echo "   ⚠️ Address failed authentication tests"
    echo "   ⚠️ Verify user credentials and session management"
    echo "   ⚠️ Check API protection and security headers"
fi

echo ""

# User Credentials Summary
echo "👥 USER CREDENTIALS SUMMARY:"
echo "   • Taha: <EMAIL> / taha2024"
echo "   • Burak: <EMAIL> / burak2024"
echo ""

# Cleanup temporary files
rm -f /tmp/session_test.json /tmp/signin_test.json /tmp/signout_test.json
rm -f /tmp/taha_auth.json /tmp/burak_auth.json /tmp/invalid_auth.json
rm -f /tmp/unauth_api.json /tmp/auth_api.json /tmp/session_check.json
rm -f /tmp/login_page.html /tmp/weak_password.json
rm -f /tmp/taha_cookies.txt /tmp/burak_cookies.txt

echo "🎉 AUTHENTICATION TESTING COMPLETE!"
echo "==================================="

# Exit with appropriate code
if (( $(echo "$auth_success_rate >= 80" | bc -l) )); then
    exit 0
else
    exit 1
fi
