const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage
const users = [
    { id: 'taha', name: 'Taha', email: '<EMAIL>', password: 'taha2024' },
    { id: 'burak', name: '<PERSON><PERSON><PERSON>', email: '<EMAIL>', password: 'burak2024' }
];

const sessions = new Map();
const expenses = [
    { id: 1, amount: 25.50, category: 'Food', paidById: 'taha', date: '2024-01-15', description: 'Lunch at downtown cafe' },
    { id: 2, amount: 120.00, category: 'Groceries', paidById: 'burak', date: '2024-01-14', description: 'Grocery shopping' },
    { id: 3, amount: 45.75, category: 'Transportation', paidById: 'taha', date: '2024-01-13', description: 'Gas station fill-up' },
    { id: 4, amount: 89.99, category: 'Utilities', paidById: 'burak', date: '2024-01-12', description: 'Monthly internet bill' },
    { id: 5, amount: 15.25, category: 'Food', paidById: 'taha', date: '2024-01-11', description: 'Coffee and pastry' }
];

const settlements = [
    { id: 1, amount: 50.00, paidBy: 'taha', paidTo: 'burak', date: '2024-01-10', description: 'Settlement for shared expenses' },
    { id: 2, amount: 30.00, paidBy: 'burak', paidTo: 'taha', date: '2024-01-05', description: 'Dinner split payment' }
];

// Helper functions
function generateSessionToken() {
    return 'session_' + Date.now() + '_' + Math.random();
}

function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const session = sessions.get(token);
    if (!session || session.expiresAt < Date.now()) {
        sessions.delete(token);
        return res.status(401).json({ success: false, error: 'Invalid or expired token' });
    }

    req.user = session.user;
    next();
}

// Serve the main frontend page
app.get('/', (req, res) => {
    res.send(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Expense Tracker</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
        }
        .login-container { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
            padding: 3rem; 
            max-width: 400px; 
            width: 100%; 
        }
        .form-group { margin-bottom: 1rem; }
        .form-group label { 
            display: block; 
            margin-bottom: 0.5rem; 
            color: #2c3e50; 
            font-weight: 500; 
        }
        .form-group input { 
            width: 100%; 
            padding: 0.75rem; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            font-size: 1rem; 
        }
        .btn-primary { 
            background: #007bff; 
            color: white; 
            border: none; 
            width: 100%; 
            padding: 1rem; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 1rem; 
        }
        .btn-primary:hover { background: #0056b3; }
        .error-message { 
            background: #f8d7da; 
            color: #721c24; 
            padding: 1rem; 
            border-radius: 5px; 
            margin-bottom: 1rem; 
            border: 1px solid #f5c6cb; 
            display: none; 
        }
        .success-message { 
            background: #d4edda; 
            color: #155724; 
            padding: 1rem; 
            border-radius: 5px; 
            margin-bottom: 1rem; 
            border: 1px solid #c3e6cb; 
            display: none; 
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2 style="text-align: center; margin-bottom: 2rem; color: #2c3e50;">Nawras Admin Login</h2>
        <div id="loginError" class="error-message"></div>
        <div id="loginSuccess" class="success-message"></div>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn-primary">Sign In</button>
        </form>
        <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 5px; font-size: 0.9rem;">
            <strong>Demo Accounts:</strong><br>
            • <EMAIL> / taha2024<br>
            • <EMAIL> / burak2024
        </div>
        <div style="margin-top: 1rem; padding: 1rem; background: #e7f3ff; border-radius: 5px; font-size: 0.9rem;">
            <strong>Status:</strong> ✅ Authentication Working<br>
            <strong>Access:</strong> Direct on port 3001<br>
            <strong>Note:</strong> Nginx issue bypassed
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const submitBtn = document.querySelector('.btn-primary');
            submitBtn.textContent = 'Signing in...';
            submitBtn.disabled = true;
            
            fetch('/api/auth/sign-in', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: email, password: password })
            })
            .then(function(response) { 
                return response.json(); 
            })
            .then(function(data) {
                if (data.success) {
                    localStorage.setItem('authToken', data.data.session.token);
                    document.getElementById('loginSuccess').textContent = 'Login successful! Authentication system working.';
                    document.getElementById('loginSuccess').style.display = 'block';
                    document.getElementById('loginError').style.display = 'none';
                } else {
                    document.getElementById('loginError').textContent = data.error || 'Login failed';
                    document.getElementById('loginError').style.display = 'block';
                    document.getElementById('loginSuccess').style.display = 'none';
                }
            })
            .catch(function(error) {
                console.error('Login error:', error);
                document.getElementById('loginError').textContent = 'Network error. Please try again.';
                document.getElementById('loginError').style.display = 'block';
                document.getElementById('loginSuccess').style.display = 'none';
            })
            .finally(function() {
                submitBtn.textContent = 'Sign In';
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>`);
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        server: 'frontend-enabled',
        port: PORT
    });
});

// Authentication endpoints
app.post('/api/auth/sign-in', (req, res) => {
    const { email, password } = req.body;

    const user = users.find(u => u.email === email && u.password === password);
    if (!user) {
        return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    const token = generateSessionToken();
    const expiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days

    sessions.set(token, {
        user: { id: user.id, name: user.name, email: user.email },
        expiresAt
    });

    res.json({
        success: true,
        data: {
            user: { id: user.id, name: user.name, email: user.email },
            session: { token, expiresAt }
        }
    });
});

app.get('/api/auth/session', authenticateToken, (req, res) => {
    res.json({
        success: true,
        data: {
            user: req.user
        }
    });
});

app.post('/api/auth/sign-out', authenticateToken, (req, res) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (token) {
        sessions.delete(token);
    }

    res.json({ success: true, message: 'Signed out successfully' });
});

// Protected endpoints
app.get('/api/expenses', authenticateToken, (req, res) => {
    res.json({ success: true, data: expenses });
});

app.post('/api/expenses', authenticateToken, (req, res) => {
    const { amount, category, paidById, date, description } = req.body;
    
    const newExpense = {
        id: expenses.length + 1,
        amount: parseFloat(amount),
        category,
        paidById,
        date,
        description
    };
    
    expenses.push(newExpense);
    res.json({ success: true, data: newExpense });
});

app.get('/api/settlements', authenticateToken, (req, res) => {
    res.json({ success: true, data: settlements });
});

// Start server
app.listen(PORT, () => {
    console.log(`Server with frontend running on port ${PORT}`);
    console.log('Access: http://partner.nawrasinchina.com:3001');
});