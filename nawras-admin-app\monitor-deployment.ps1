# 🔍 DEPLOYMENT MONITORING SCRIPT
# This script will continuously monitor the deployment progress

$SERVER_IP = "*************"
$DOMAIN = "partner.nawrasinchina.com"

Write-Host "🔍 DEPLOYMENT MONITORING STARTED" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "Domain: $DOMAIN" -ForegroundColor Yellow
Write-Host "Server IP: $SERVER_IP" -ForegroundColor Yellow
Write-Host "Start Time: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

# Function to test endpoint
function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Name
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            return @{ Success = $true; Status = $response.StatusCode; Content = $response.Content }
        } else {
            return @{ Success = $false; Status = $response.StatusCode; Error = "HTTP $($response.StatusCode)" }
        }
    } catch {
        return @{ Success = $false; Status = 0; Error = $_.Exception.Message }
    }
}

# Function to display test results
function Show-TestResults {
    param(
        [int]$Iteration,
        [object]$DomainResult,
        [object]$HealthResult,
        [object]$ExpensesResult,
        [object]$SettlementsResult
    )
    
    Write-Host "📊 Test Iteration #$Iteration - $(Get-Date -Format 'HH:mm:ss')" -ForegroundColor Blue
    Write-Host "================================================" -ForegroundColor Blue
    
    # Domain test
    if ($DomainResult.Success) {
        Write-Host "✅ Domain (http://$DOMAIN): HTTP $($DomainResult.Status)" -ForegroundColor Green
        
        # Check if it contains our success message
        if ($DomainResult.Content -like "*SUCCESS!*") {
            Write-Host "   🎉 SUCCESS MESSAGE DETECTED!" -ForegroundColor Green
        }
        if ($DomainResult.Content -like "*Nawras Admin*") {
            Write-Host "   🏢 NAWRAS ADMIN TITLE DETECTED!" -ForegroundColor Green
        }
    } else {
        Write-Host "❌ Domain (http://$DOMAIN): $($DomainResult.Error)" -ForegroundColor Red
    }
    
    # API Health test
    if ($HealthResult.Success) {
        Write-Host "✅ API Health: HTTP $($HealthResult.Status)" -ForegroundColor Green
        
        try {
            $healthData = $HealthResult.Content | ConvertFrom-Json
            Write-Host "   Server: $($healthData.server)" -ForegroundColor White
            Write-Host "   Version: $($healthData.version)" -ForegroundColor White
            
            if ($healthData.server -eq "emergency-fix-deployed") {
                Write-Host "   🚀 NEW SERVER DEPLOYED!" -ForegroundColor Green
            }
        } catch {
            Write-Host "   ⚠️ Could not parse health response" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ API Health: $($HealthResult.Error)" -ForegroundColor Red
    }
    
    # API Expenses test
    if ($ExpensesResult.Success) {
        Write-Host "✅ API Expenses: HTTP $($ExpensesResult.Status)" -ForegroundColor Green
    } else {
        Write-Host "❌ API Expenses: $($ExpensesResult.Error)" -ForegroundColor Red
    }
    
    # API Settlements test
    if ($SettlementsResult.Success) {
        Write-Host "✅ API Settlements: HTTP $($SettlementsResult.Status)" -ForegroundColor Green
    } else {
        Write-Host "❌ API Settlements: $($SettlementsResult.Error)" -ForegroundColor Red
    }
    
    # Calculate success rate
    $successCount = 0
    if ($DomainResult.Success) { $successCount++ }
    if ($HealthResult.Success) { $successCount++ }
    if ($ExpensesResult.Success) { $successCount++ }
    if ($SettlementsResult.Success) { $successCount++ }
    
    $successRate = ($successCount / 4) * 100
    
    Write-Host ""
    Write-Host "📈 Success Rate: $successRate% ($successCount/4 tests passed)" -ForegroundColor $(
        if ($successRate -eq 100) { "Green" }
        elseif ($successRate -ge 75) { "Yellow" }
        else { "Red" }
    )
    
    Write-Host ""
    
    return $successRate
}

# Main monitoring loop
$iteration = 1
$maxIterations = 20  # Monitor for about 10 minutes (30 seconds * 20)
$deploymentSuccessful = $false

Write-Host "🚀 Starting continuous monitoring..." -ForegroundColor Cyan
Write-Host "Will test every 30 seconds for up to 10 minutes" -ForegroundColor White
Write-Host ""

while ($iteration -le $maxIterations -and -not $deploymentSuccessful) {
    # Test all endpoints
    $domainResult = Test-Endpoint -Url "http://$DOMAIN" -Name "Domain"
    $healthResult = Test-Endpoint -Url "http://$DOMAIN/api/health" -Name "Health"
    $expensesResult = Test-Endpoint -Url "http://$DOMAIN/api/expenses" -Name "Expenses"
    $settlementsResult = Test-Endpoint -Url "http://$DOMAIN/api/settlements" -Name "Settlements"
    
    # Show results
    $successRate = Show-TestResults -Iteration $iteration -DomainResult $domainResult -HealthResult $healthResult -ExpensesResult $expensesResult -SettlementsResult $settlementsResult
    
    # Check if deployment is successful
    if ($successRate -eq 100) {
        Write-Host "🎉 DEPLOYMENT SUCCESSFUL!" -ForegroundColor Green
        Write-Host "=========================" -ForegroundColor Green
        Write-Host "All tests are passing. The website is fully operational!" -ForegroundColor Green
        $deploymentSuccessful = $true
        break
    }
    
    # Wait before next iteration
    if ($iteration -lt $maxIterations) {
        Write-Host "⏱️ Waiting 30 seconds before next test..." -ForegroundColor Gray
        Start-Sleep -Seconds 30
    }
    
    $iteration++
}

# Final summary
Write-Host ""
Write-Host "📋 MONITORING SUMMARY" -ForegroundColor Blue
Write-Host "=====================" -ForegroundColor Blue
Write-Host "Total Iterations: $($iteration - 1)" -ForegroundColor White
Write-Host "End Time: $(Get-Date)" -ForegroundColor White

if ($deploymentSuccessful) {
    Write-Host "Result: ✅ DEPLOYMENT SUCCESSFUL" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Your website is now live at:" -ForegroundColor Green
    Write-Host "   Main Site: http://$DOMAIN" -ForegroundColor Green
    Write-Host "   API Health: http://$DOMAIN/api/health" -ForegroundColor Green
    Write-Host "   API Expenses: http://$DOMAIN/api/expenses" -ForegroundColor Green
    Write-Host "   API Settlements: http://$DOMAIN/api/settlements" -ForegroundColor Green
    Write-Host ""
    Write-Host "🎯 Next Steps:" -ForegroundColor Blue
    Write-Host "1. Configure SSL/HTTPS for security" -ForegroundColor White
    Write-Host "2. Add authentication system" -ForegroundColor White
    Write-Host "3. Deploy full React frontend" -ForegroundColor White
    Write-Host "4. Set up automated backups" -ForegroundColor White
} else {
    Write-Host "Result: ⚠️ DEPLOYMENT IN PROGRESS OR FAILED" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "💡 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Check if the deployment command was executed correctly" -ForegroundColor White
    Write-Host "2. Verify the DigitalOcean console output for errors" -ForegroundColor White
    Write-Host "3. Try running the deployment command again" -ForegroundColor White
    Write-Host "4. Check server logs: tail -f /opt/nawras-admin/app.log" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
