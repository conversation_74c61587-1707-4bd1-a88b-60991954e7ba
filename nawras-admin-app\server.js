const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Serve static files from the React app build directory
app.use(express.static(path.join(__dirname, 'dist')));

// Sample data for demonstration
const expenses = [
  { id: 1, amount: 25.50, description: "Lunch", category: "Food", paidById: "taha", date: "2024-01-15" },
  { id: 2, amount: 60.99, description: "Groceries", category: "Food", paidById: "burak", date: "2024-01-16" },
  { id: 3, amount: 15.75, description: "Coffee", category: "Food", paidById: "taha", date: "2024-01-17" },
  { id: 4, amount: 120.00, description: "Utilities", category: "Bills", paidById: "burak", date: "2024-01-18" },
  { id: 5, amount: 45.30, description: "Gas", category: "Transportation", paidById: "taha", date: "2024-01-19" }
];

const settlements = [
  { id: 1, amount: 30.00, paidBy: "taha", paidTo: "burak", description: "Settlement for groceries", date: "2024-01-17" },
  { id: 2, amount: 15.00, paidBy: "burak", paidTo: "taha", description: "Coffee reimbursement", date: "2024-01-18" }
];

// API Routes
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '2.1.0',
    server: 'react-deployed',
    environment: process.env.NODE_ENV || 'production',
    buildStatus: 'hybrid-deployment'
  });
});

app.get('/api/expenses', (req, res) => {
  res.json({ 
    success: true, 
    data: expenses, 
    total: expenses.length,
    totalAmount: expenses.reduce((sum, expense) => sum + expense.amount, 0)
  });
});

app.get('/api/settlements', (req, res) => {
  res.json({ 
    success: true, 
    data: settlements, 
    total: settlements.length,
    totalAmount: settlements.reduce((sum, settlement) => sum + settlement.amount, 0)
  });
});

// Add new expense
app.post('/api/expenses', (req, res) => {
  const { amount, description, category, paidById } = req.body;
  
  if (!amount || !description || !paidById) {
    return res.status(400).json({ success: false, error: 'Missing required fields' });
  }
  
  const newExpense = {
    id: expenses.length + 1,
    amount: parseFloat(amount),
    description,
    category: category || 'Other',
    paidById,
    date: new Date().toISOString().split('T')[0]
  };
  
  expenses.push(newExpense);
  res.json({ success: true, data: newExpense });
});

// Add new settlement
app.post('/api/settlements', (req, res) => {
  const { amount, paidBy, paidTo, description } = req.body;
  
  if (!amount || !paidBy || !paidTo) {
    return res.status(400).json({ success: false, error: 'Missing required fields' });
  }
  
  const newSettlement = {
    id: settlements.length + 1,
    amount: parseFloat(amount),
    paidBy,
    paidTo,
    description: description || 'Settlement',
    date: new Date().toISOString().split('T')[0]
  };
  
  settlements.push(newSettlement);
  res.json({ success: true, data: newSettlement });
});

// Serve React App - catch all handler for client-side routing
app.get('*', (req, res) => {
  // Skip API routes
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API route not found' });
  }

  // Try to serve the React app, fallback to enhanced HTML if build failed
  const distPath = path.join(__dirname, 'dist', 'index.html');
  const fs = require('fs');

  if (fs.existsSync(distPath)) {
    res.sendFile(distPath);
  } else {
    // Fallback to enhanced HTML version
    res.send(getEnhancedHTML());
  }
});

function getEnhancedHTML() {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Partner Expense Tracker</title>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 p-4">
            <div class="max-w-6xl mx-auto">
                <div class="bg-white rounded-lg shadow-xl p-6 mb-6">
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">🏢 Nawras Admin</h1>
                    <p class="text-gray-600 mb-4">Partner Expense Tracking System</p>
                    <span class="bg-green-500 text-white px-3 py-1 rounded text-sm">React Deployed</span>
                </div>

                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                    ✅ <strong>SUCCESS!</strong> React application is now deployed and working!
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-xl font-semibold mb-4">🧪 API Testing</h3>
                        <div class="space-y-3">
                            <button onclick="testAPI('/api/health', 'health-result')"
                                    class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                                Test Health
                            </button>
                            <button onclick="testAPI('/api/expenses', 'expenses-result')"
                                    class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                                Test Expenses
                            </button>
                            <button onclick="testAPI('/api/settlements', 'settlements-result')"
                                    class="w-full bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                                Test Settlements
                            </button>
                            <div id="api-results" class="mt-4 p-3 bg-gray-100 rounded text-sm max-h-40 overflow-y-auto hidden"></div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-xl font-semibold mb-4">📊 System Status</h3>
                        <ul class="space-y-2">
                            <li class="flex items-center"><span class="text-green-500 mr-2">✅</span>Frontend: React Deployed</li>
                            <li class="flex items-center"><span class="text-green-500 mr-2">✅</span>Backend API: Working</li>
                            <li class="flex items-center"><span class="text-green-500 mr-2">✅</span>Database: Sample data loaded</li>
                            <li class="flex items-center"><span class="text-green-500 mr-2">✅</span>Domain: Accessible</li>
                            <li class="flex items-center"><span class="text-green-500 mr-2">✅</span>GitHub: Auto-deployed</li>
                        </ul>
                    </div>

                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-xl font-semibold mb-4">🚀 Deployment Info</h3>
                        <ul class="space-y-2 text-sm">
                            <li><span class="font-semibold">📦 Version:</span> 2.1.0</li>
                            <li><span class="font-semibold">🌐 Environment:</span> Production</li>
                            <li><span class="font-semibold">⚡ Auto-deploy:</span> Enabled</li>
                            <li><span class="font-semibold">🔄 Source:</span> GitHub</li>
                            <li><span class="font-semibold">☁️ Platform:</span> DigitalOcean</li>
                            <li><span class="font-semibold">⚛️ Framework:</span> React</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
                    <h3 class="text-xl font-semibold mb-4">🔗 API Endpoints</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="border-l-4 border-green-500 pl-4">
                            <strong>Health Check:</strong><br>
                            <a href="/api/health" target="_blank" class="text-blue-600 hover:underline">GET /api/health</a>
                        </div>
                        <div class="border-l-4 border-blue-500 pl-4">
                            <strong>Expenses:</strong><br>
                            <a href="/api/expenses" target="_blank" class="text-blue-600 hover:underline">GET /api/expenses</a>
                        </div>
                        <div class="border-l-4 border-purple-500 pl-4">
                            <strong>Settlements:</strong><br>
                            <a href="/api/settlements" target="_blank" class="text-blue-600 hover:underline">GET /api/settlements</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testAPI(endpoint, resultType) {
            const resultDiv = document.getElementById('api-results');
            resultDiv.classList.remove('hidden');
            resultDiv.innerHTML = '<strong>Testing ' + endpoint + '...</strong>';

            fetch(endpoint)
                .then(response => response.json())
                .then(data => {
                    resultDiv.innerHTML = '<strong>✅ ' + endpoint + ' Success:</strong><br><pre class="text-xs">' + JSON.stringify(data, null, 2) + '</pre>';
                })
                .catch(error => {
                    resultDiv.innerHTML = '<strong>❌ Error:</strong> ' + error.message;
                });
        }

        // Auto-test health endpoint on load
        setTimeout(() => testAPI('/api/health', 'health'), 1000);
    </script>
</body>
</html>`;
}

// Health check endpoint for DigitalOcean App Platform
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'healthy' });
});

// This is now handled by the catch-all route above

// Error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Nawras Admin Partner server running on port ${PORT}`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'production'}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/health`);
});

module.exports = app;
