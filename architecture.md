# Nawras Admin Application - Full Technical Architecture

## 1. 📁 File & Folder Structure

```
/nawras-admin-app
├── public/
│   └── favicon.ico            # Favicon and static assets
│
├── src/
│   ├── assets/                # Static images, logos, icons
│   ├── components/            # Shared UI components (buttons, modals, layout wrappers)
│   ├── features/              # Core app modules (expenses, dashboard, reports, etc.)
│   │   ├── dashboard/
│   │   │   ├── BalanceSummary.tsx
│   │   │   ├── ExpenseChart.tsx
│   │   │   ├── CategoryBreakdown.tsx
│   │   │   └── RecentExpenses.tsx
│   │   ├── expenses/
│   │   │   ├── AddExpenseForm.tsx
│   │   │   └── ExpenseList.tsx
│   │   ├── reports/
│   │   │   └── ReportCharts.tsx
│   │   ├── history/
│   │   │   └── HistoryTable.tsx
│   │   └── settlement/
│   │       ├── SettlementForm.tsx
│   │       └── SettlementHistory.tsx
│   │
│   ├── hooks/                 # Custom React hooks (e.g. useExpenses, useBalance)
│   ├── lib/                   # Utility functions and helpers
│   ├── pages/                 # Wouter route-based page entries
│   │   ├── index.tsx          # Dashboard
│   │   ├── add-expense.tsx
│   │   ├── settlement.tsx
│   │   ├── history.tsx
│   │   ├── reports.tsx
│   │   └── settings.tsx
│   │
│   ├── providers/             # Context providers (Auth, QueryClient, Theme, etc.)
│   ├── services/              # API services using React Query and Axios/fetch
│   │   ├── expenses.ts
│   │   ├── settlements.ts
│   │   ├── categories.ts
│   │   └── users.ts
│   │
│   ├── types/                 # TypeScript type definitions
│   ├── config/                # App configuration (env vars, constants)
│   ├── routes/                # Route definitions and route hooks
│   ├── styles/                # Tailwind CSS config and global styles
│   ├── App.tsx                # Root app wrapper
│   └── main.tsx               # Entry point for Vite
│
├── .env                      # Environment variables (API endpoints, keys)
├── tailwind.config.ts        # TailwindCSS configuration
├── tsconfig.json             # TypeScript config
├── vite.config.ts            # Vite bundler setup
└── package.json              # Dependencies and scripts
```

## 2. ⚙️ What Each Part Does

### 📂 components/

Reusable UI blocks like buttons, input fields, layout components (`Sidebar`, `MobileNav`), cards, loaders, etc.

### 📂 features/

Contains app logic grouped by functional domain:

* **dashboard/**: Balance cards, recent expenses, graphs
* **expenses/**: Expense forms, filtering, uploads
* **reports/**: Monthly and quarterly visual analytics
* **settlement/**: Balance settlement and history

### 📂 hooks/

Contains `useExpenses`, `useBalance`, etc. Custom logic separated from UI components.

### 📂 lib/

Pure JS utilities like formatting dates, calculating totals, handling currency, etc.

### 📂 pages/

Maps directly to Wouter routes, rendering specific sections.

### 📂 services/

API logic with React Query for fetching, caching, mutation handling.

### 📂 types/

Defines types like `Expense`, `User`, `Category`, `Settlement`, and enums/constants.

### 📂 config/

Holds constant values, color maps, or category presets.

### 📂 routes/

Holds centralized definitions for app routes and icons/labels for sidebar.

### 📂 styles/

Tailwind setup and optional custom styles.

## 3. 🌐 State Management

### Global State

* **React Context API** used for user auth context
* **React Query (TanStack)** handles async state:

  * Expenses
  * Settlements
  * Categories
  * Users

### Local State

* Form input, modal toggles, and filters managed via useState or useReducer in feature-level components

## 4. 🔌 Services and API Connections

### Service Integration Flow

```
Component ↔ Hook (useX) ↔ React Query ↔ services/*.ts ↔ Express Backend ↔ DB (via Drizzle ORM)
```

### Example Service (expenses.ts)

```ts
export const useExpenses = () => {
  return useQuery(['expenses'], async () => {
    const res = await fetch('/api/expenses');
    return res.json();
  });
};
```

## 5. ☁️ Backend Overview

### Express API Routes

```
/api
├── /expenses          // CRUD for expenses
├── /settlements       // Create/view settlements
├── /categories        // Get/Add/Remove categories
├── /users             // Auth & user details
```

### Middleware

* **CORS**, **Rate Limiter**, **CSRF**, **cookie-parser**

### Auth

* JWT-based session management with cookie storage

### DB Schema (via Drizzle ORM)

* Users
* Expenses
* Categories
* Settlements

## 6. 🧪 Testing

* Unit tests using Vitest for services and utility functions
* E2E manual test flows via deployed staging app

## 7. 📊 Charts and Visuals

* **Recharts** for bar graphs, pie charts in reports
* **Lucide-react** icons across UI

## 8. 🌐 Deployment

* **Frontend & Backend**: Hosted on \[DigitalOcean App Platform]
* CI/CD: GitHub Actions for auto-deploy on push to `main`
* Live URL: [https://partner.nawrasinchina.com](https://partner.nawrasinchina.com)

---

> ✅ This modular structure is optimized for speed, readability, and future expansion.
