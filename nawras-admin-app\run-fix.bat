@echo off
echo ========================================
echo Nawras Admin - DigitalOcean Fix Tool
echo ========================================
echo.

REM Check if we're in WSL or have bash available
where bash >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Running fix script with bash...
    bash fix-droplets.sh
) else (
    echo Bash not found. Checking droplets with PowerShell...
    powershell -ExecutionPolicy Bypass -File check-droplets.ps1
)

pause
