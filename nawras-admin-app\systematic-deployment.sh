#!/bin/bash

# Systematic Deployment Script - Complete System Setup
# This script will install everything from scratch and deploy the authenticated server

set -e

echo "🚀 SYSTEMATIC DEPLOYMENT - NAWRAS ADMIN"
echo "======================================="
echo "Starting comprehensive system setup..."
echo ""

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check command success
check_success() {
    if [ $? -eq 0 ]; then
        log "✅ $1 - SUCCESS"
    else
        log "❌ $1 - FAILED"
        exit 1
    fi
}

# Step 1: System Update and Basic Packages
echo "📦 STEP 1: SYSTEM UPDATE AND BASIC PACKAGES"
echo "==========================================="

log "Updating system packages..."
export DEBIAN_FRONTEND=noninteractive
apt-get update -y
check_success "System package update"

apt-get upgrade -y
check_success "System package upgrade"

log "Installing basic packages..."
apt-get install -y curl wget git unzip software-properties-common
check_success "Basic packages installation"

# Step 2: Install Node.js 18 LTS
echo ""
echo "📦 STEP 2: NODE.JS INSTALLATION"
echo "==============================="

log "Installing Node.js 18 LTS..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
check_success "Node.js repository setup"

apt-get install -y nodejs
check_success "Node.js installation"

log "Verifying Node.js installation..."
node_version=$(node --version)
npm_version=$(npm --version)
log "Node.js version: $node_version"
log "NPM version: $npm_version"

# Step 3: Install PM2 Process Manager
echo ""
echo "📦 STEP 3: PM2 PROCESS MANAGER"
echo "=============================="

log "Installing PM2 globally..."
npm install -g pm2
check_success "PM2 installation"

log "Setting up PM2 startup script..."
pm2 startup systemd -u root --hp /root
check_success "PM2 startup configuration"

# Step 4: Install and Configure Nginx
echo ""
echo "📦 STEP 4: NGINX INSTALLATION AND CONFIGURATION"
echo "=============================================="

log "Installing nginx..."
apt-get install -y nginx
check_success "Nginx installation"

log "Starting and enabling nginx..."
systemctl start nginx
systemctl enable nginx
check_success "Nginx service setup"

# Step 5: Configure Firewall
echo ""
echo "🔒 STEP 5: FIREWALL CONFIGURATION"
echo "================================="

log "Configuring UFW firewall..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3001/tcp
ufw --force enable
check_success "Firewall configuration"

log "Firewall status:"
ufw status

# Step 6: Create Application Directory and Setup
echo ""
echo "📁 STEP 6: APPLICATION SETUP"
echo "============================"

log "Creating application directory..."
mkdir -p /opt/nawras-admin
cd /opt/nawras-admin
check_success "Application directory creation"

log "Creating package.json..."
cat > package.json << 'EOF'
{
  "name": "nawras-admin",
  "version": "1.0.0",
  "description": "Nawras Admin Expense Tracker",
  "main": "server-simple.cjs",
  "scripts": {
    "start": "node server-simple.cjs",
    "dev": "node server-simple.cjs"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "cookie-parser": "^1.4.6"
  },
  "engines": {
    "node": ">=18.0.0"
  }
}
EOF
check_success "Package.json creation"

log "Installing Node.js dependencies..."
npm install --production
check_success "Dependencies installation"

# Step 7: Create Authenticated Server
echo ""
echo "🔐 STEP 7: AUTHENTICATED SERVER CREATION"
echo "========================================"

log "Creating authenticated server file..."
cat > server-simple.cjs << 'EOF'
const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: [
    'http://localhost:5173', 
    'http://localhost:3000', 
    'https://partner.nawrasinchina.com', 
    'http://partner.nawrasinchina.com',
    'http://*************'
  ],
  credentials: true
}));
app.use(express.json());
app.use(cookieParser());

// Session storage
const sessions = new Map();

// Mock users for authentication
const mockUsers = [
  {
    id: "taha",
    email: "<EMAIL>",
    name: "Taha",
    password: "taha2024", // In production, this would be hashed
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: "burak",
    email: "<EMAIL>",
    name: "Burak",
    password: "burak2024", // In production, this would be hashed
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

// Authentication middleware
const requireAuth = (req, res, next) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') ||
                      req.cookies?.session;

  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  const session = sessions.get(sessionToken);

  // Check if session is expired
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    res.clearCookie('session');
    return res.status(401).json({
      success: false,
      error: 'Session expired'
    });
  }

  req.user = session.user;
  req.session = session;
  next();
};

// Health check endpoint (no auth required)
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    server: 'authenticated'
  });
});

// Authentication Routes
app.post('/api/auth/sign-in', (req, res) => {
  const { email, password } = req.body;

  const user = mockUsers.find(u => u.email === email && u.password === password);

  if (!user) {
    return res.status(401).json({
      success: false,
      error: 'Invalid email or password'
    });
  }

  // Create session
  const sessionToken = `session_${Date.now()}_${Math.random()}`;
  const session = {
    id: sessionToken,
    userId: user.id,
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    },
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    token: sessionToken,
  };

  sessions.set(sessionToken, session);

  // Set cookie
  res.cookie('session', sessionToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  });

  res.json({
    success: true,
    data: {
      session,
      user: session.user,
    }
  });
});

app.post('/api/auth/sign-out', (req, res) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') ||
                      req.cookies?.session;

  if (sessionToken) {
    sessions.delete(sessionToken);
  }

  res.clearCookie('session');
  res.json({ success: true });
});

app.get('/api/auth/session', (req, res) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') ||
                      req.cookies?.session;

  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.json({ success: true, data: null });
  }

  const session = sessions.get(sessionToken);

  // Check if session is expired
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    res.clearCookie('session');
    return res.json({ success: true, data: null });
  }

  res.json({
    success: true,
    data: session
  });
});

// Protected API Routes
app.get('/api/expenses', requireAuth, (req, res) => {
  const { paidBy, category, limit } = req.query;
  
  let expenses = [
    {id: 1, amount: 25.5, description: "Lunch at downtown cafe", category: "Food", paidById: "taha", date: "2024-01-15", createdAt: "2024-01-15T12:30:00Z"},
    {id: 2, amount: 120, description: "Grocery shopping", category: "Groceries", paidById: "burak", date: "2024-01-14", createdAt: "2024-01-14T18:45:00Z"},
    {id: 3, amount: 45.75, description: "Gas station fill-up", category: "Transportation", paidById: "taha", date: "2024-01-13", createdAt: "2024-01-13T09:15:00Z"},
    {id: 4, amount: 89.99, description: "Monthly internet bill", category: "Utilities", paidById: "burak", date: "2024-01-12", createdAt: "2024-01-12T14:20:00Z"},
    {id: 5, amount: 15.25, description: "Coffee and pastry", category: "Food", paidById: "taha", date: "2024-01-11", createdAt: "2024-01-11T08:30:00Z"}
  ];

  // Apply filters
  if (paidBy) {
    expenses = expenses.filter(e => e.paidById === paidBy);
  }
  
  if (category) {
    expenses = expenses.filter(e => e.category === category);
  }
  
  if (limit) {
    expenses = expenses.slice(0, parseInt(limit));
  }

  res.json({
    success: true,
    data: expenses,
    total: expenses.length,
    totalUnfiltered: 5,
    filters: {
      sortBy: "date",
      sortOrder: "desc"
    },
    timestamp: new Date().toISOString()
  });
});

app.get('/api/settlements', requireAuth, (req, res) => {
  const settlements = [
    {id: 1, amount: 30.00, paidBy: "taha", paidTo: "burak", description: "Settlement", date: "2024-01-17", createdAt: "2024-01-17T10:00:00Z"},
    {id: 2, amount: 25.75, paidBy: "burak", paidTo: "taha", description: "Utilities", date: "2024-01-18", createdAt: "2024-01-18T15:30:00Z"}
  ];

  res.json({
    success: true,
    data: settlements,
    total: settlements.length,
    timestamp: new Date().toISOString()
  });
});

// 404 handler for API routes
app.use('/api/*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'API endpoint not found' 
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Nawras Admin server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🌐 Frontend: http://localhost:${PORT}`);
  console.log(`🔐 Authentication enabled`);
  console.log(`👥 Users: <EMAIL>, <EMAIL>`);
});
EOF
check_success "Authenticated server creation"

# Step 8: Start Server with PM2
echo ""
echo "🚀 STEP 8: SERVER STARTUP"
echo "========================="

log "Starting server with PM2..."
pm2 stop all || echo "No existing PM2 processes"
pm2 delete all || echo "No existing PM2 processes to delete"

pm2 start server-simple.cjs --name nawras-admin-auth
check_success "PM2 server startup"

pm2 save
check_success "PM2 configuration save"

log "Waiting for server to start..."
sleep 5

# Step 9: Test Server
echo ""
echo "🧪 STEP 9: SERVER TESTING"
echo "========================="

log "Testing health endpoint..."
if curl -s http://localhost:3001/api/health | grep -q '"status":"ok"'; then
    log "✅ Health check PASSED"
    health_response=$(curl -s http://localhost:3001/api/health)
    log "Health response: $health_response"
else
    log "❌ Health check FAILED"
    log "PM2 status:"
    pm2 status
    log "Server logs:"
    pm2 logs nawras-admin-auth --lines 10 --nostream
    exit 1
fi

log "Testing authentication requirement..."
expenses_response=$(curl -s -w "%{http_code}" http://localhost:3001/api/expenses -o /tmp/expenses_test.json)
if [ "$expenses_response" = "401" ]; then
    log "✅ Authentication requirement PASSED"
    log "Expenses response: $(cat /tmp/expenses_test.json)"
else
    log "❌ Authentication requirement FAILED (HTTP $expenses_response)"
    log "Expenses response: $(cat /tmp/expenses_test.json)"
fi

# Step 10: Configure Nginx Proxy
echo ""
echo "🌐 STEP 10: NGINX PROXY CONFIGURATION"
echo "====================================="

log "Configuring nginx proxy..."
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80;
    server_name partner.nawrasinchina.com *************;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Serve static files
    location / {
        root /opt/nawras-admin/dist;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Proxy API requests to Node.js server
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # CORS headers
        add_header Access-Control-Allow-Origin "http://partner.nawrasinchina.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Authorization, Content-Type" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
}
EOF
check_success "Nginx configuration creation"

log "Testing nginx configuration..."
nginx -t
check_success "Nginx configuration test"

log "Reloading nginx..."
systemctl reload nginx
check_success "Nginx reload"

# Step 11: Final System Test
echo ""
echo "🎯 STEP 11: FINAL SYSTEM TEST"
echo "============================="

log "Testing complete system..."

log "Testing frontend access..."
if curl -s http://localhost | grep -q "Nawras Admin"; then
    log "✅ Frontend access PASSED"
else
    log "❌ Frontend access FAILED"
fi

log "Testing API proxy..."
if curl -s http://localhost/api/health | grep -q '"status":"ok"'; then
    log "✅ API proxy PASSED"
    api_response=$(curl -s http://localhost/api/health)
    log "API response: $api_response"
else
    log "❌ API proxy FAILED"
    log "Nginx error log:"
    tail -10 /var/log/nginx/error.log
fi

log "Testing protected endpoint..."
protected_response=$(curl -s -w "%{http_code}" http://localhost/api/expenses -o /tmp/protected_test.json)
if [ "$protected_response" = "401" ]; then
    log "✅ Protected endpoint PASSED"
else
    log "❌ Protected endpoint FAILED (HTTP $protected_response)"
fi

echo ""
echo "🎉 SYSTEMATIC DEPLOYMENT COMPLETED!"
echo "==================================="
echo ""
log "✅ System Summary:"
log "   • Node.js $(node --version) installed"
log "   • PM2 process manager configured"
log "   • Nginx proxy configured"
log "   • Firewall configured"
log "   • Authenticated server running"
log ""
log "🌐 Application URLs:"
log "   • Frontend: http://partner.nawrasinchina.com"
log "   • API Health: http://partner.nawrasinchina.com/api/health"
log "   • Direct API: http://*************:3001/api/health"
log ""
log "👥 User Credentials:"
log "   • Taha: <EMAIL> / taha2024"
log "   • Burak: <EMAIL> / burak2024"
log ""
log "🔧 Management Commands:"
log "   • Check server: pm2 status"
log "   • View logs: pm2 logs nawras-admin-auth"
log "   • Restart server: pm2 restart nawras-admin-auth"
log ""
log "📞 Next Steps:"
log "   1. Test authentication in browser"
log "   2. Configure SSL/HTTPS"
log "   3. Deploy updated frontend"
log "   4. Run comprehensive validation"

echo ""
echo "Deployment script completed successfully! 🚀"
