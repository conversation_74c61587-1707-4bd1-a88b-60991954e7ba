To deploy the Nawras Admin app on your DigitalOcean droplet (104.236.243.130), follow these steps:

1. Access your droplet console at: https://cloud.digitalocean.com/droplets/498520148/console

2. Run this one-liner command:

curl -fsSL https://raw.githubusercontent.com/your-username/nawras-admin/main/deploy.sh | bash

OR manually execute these commands:

# Update system and install Node.js
apt-get update -y
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs nginx

# Install PM2
npm install -g pm2

# Create app
mkdir -p /opt/nawras-admin && cd /opt/nawras-admin

# Create package.json
echo '{"name":"nawras-admin","version":"1.0.0","main":"server.js","dependencies":{"express":"^4.18.2","cors":"^2.8.5"},"scripts":{"start":"node server.js"}}' > package.json

# Install dependencies
npm install

# Create server (see server.js content in the deployment files)

# Start with PM2
pm2 start server.js --name "nawras-backend"
pm2 startup
pm2 save

# Configure Nginx and firewall
# (see nginx configuration in deployment files)

# Test
curl http://localhost:3001/api/health
