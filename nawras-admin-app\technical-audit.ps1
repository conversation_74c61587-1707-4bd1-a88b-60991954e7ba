# Comprehensive Technical Audit for Nawras Admin Application

$IP = "*************"
$DOMAIN = "partner.nawrasinchina.com"

Write-Host "🔍 COMPREHENSIVE TECHNICAL AUDIT" -ForegroundColor Blue
Write-Host "================================" -ForegroundColor Blue
Write-Host ""

# 1. API Endpoint Testing
Write-Host "🌐 1. API Endpoint Testing:" -ForegroundColor Cyan
Write-Host ""

$endpoints = @(
    @{ Name = "Health Check"; URL = "http://$IP/health"; Type = "health" }
    @{ Name = "Expenses API"; URL = "http://$IP`:3001/api/expenses"; Type = "data" }
    @{ Name = "Settlements API"; URL = "http://$IP`:3001/api/settlements"; Type = "data" }
    @{ Name = "Frontend"; URL = "http://$IP"; Type = "frontend" }
    @{ Name = "Domain Frontend"; URL = "http://$DOMAIN"; Type = "frontend" }
)

$apiResults = @{}

foreach ($endpoint in $endpoints) {
    try {
        $response = Invoke-WebRequest -Uri $endpoint.URL -TimeoutSec 10 -UseBasicParsing
        Write-Host "   ✅ $($endpoint.Name): HTTP $($response.StatusCode)" -ForegroundColor Green
        $apiResults[$endpoint.Name] = @{ Status = "Success"; Code = $response.StatusCode; Content = $response.Content }
        
        if ($endpoint.Type -eq "data") {
            try {
                $data = $response.Content | ConvertFrom-Json
                if ($data.success) {
                    Write-Host "      📊 Data: $($data.data.Count) items" -ForegroundColor Green
                    $apiResults[$endpoint.Name].DataCount = $data.data.Count
                } else {
                    Write-Host "      ⚠️ API Error: $($data.error)" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "      ⚠️ Invalid JSON response" -ForegroundColor Yellow
            }
        }
    } catch {
        Write-Host "   ❌ $($endpoint.Name): Failed - $($_.Exception.Message)" -ForegroundColor Red
        $apiResults[$endpoint.Name] = @{ Status = "Failed"; Error = $_.Exception.Message }
    }
}

Write-Host ""

# 2. Data Flow Analysis
Write-Host "📊 2. Data Flow Analysis:" -ForegroundColor Cyan
Write-Host ""

if ($apiResults["Expenses API"].Status -eq "Success") {
    try {
        $expensesData = $apiResults["Expenses API"].Content | ConvertFrom-Json
        $expenses = $expensesData.data
        
        Write-Host "   ✅ Expenses Data Structure:" -ForegroundColor Green
        Write-Host "      • Total Expenses: $($expenses.Count)" -ForegroundColor White
        
        if ($expenses.Count -gt 0) {
            $categories = $expenses | Group-Object category | Sort-Object Count -Descending
            Write-Host "      • Categories: $($categories.Name -join ', ')" -ForegroundColor White
            
            $users = $expenses | Group-Object paidById | Sort-Object Count -Descending
            Write-Host "      • Users: $($users.Name -join ', ')" -ForegroundColor White
            
            $totalAmount = ($expenses | Measure-Object amount -Sum).Sum
            Write-Host "      • Total Amount: $totalAmount" -ForegroundColor White
            
            # Balance calculation
            $tahaTotal = ($expenses | Where-Object { $_.paidById -eq "taha" } | Measure-Object amount -Sum).Sum
            $burakTotal = ($expenses | Where-Object { $_.paidById -eq "burak" } | Measure-Object amount -Sum).Sum
            $balance = [Math]::Abs($tahaTotal - $burakTotal)
            
            Write-Host "      • Taha's Total: $tahaTotal" -ForegroundColor Blue
            Write-Host "      • Burak's Total: $burakTotal" -ForegroundColor Green
            Write-Host "      • Balance: $balance" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   ❌ Failed to analyze expenses data" -ForegroundColor Red
    }
}

if ($apiResults["Settlements API"].Status -eq "Success") {
    try {
        $settlementsData = $apiResults["Settlements API"].Content | ConvertFrom-Json
        $settlements = $settlementsData.data
        
        Write-Host "   ✅ Settlements Data Structure:" -ForegroundColor Green
        Write-Host "      • Total Settlements: $($settlements.Count)" -ForegroundColor White
        
        if ($settlements.Count -gt 0) {
            $totalSettlements = ($settlements | Measure-Object amount -Sum).Sum
            Write-Host "      • Total Settlement Amount: $totalSettlements" -ForegroundColor White
        }
    } catch {
        Write-Host "   ❌ Failed to analyze settlements data" -ForegroundColor Red
    }
}

Write-Host ""

# 3. Frontend Analysis
Write-Host "🖥️ 3. Frontend Analysis:" -ForegroundColor Cyan
Write-Host ""

if ($apiResults["Frontend"].Status -eq "Success") {
    $frontendContent = $apiResults["Frontend"].Content
    
    # Check for React app indicators
    $hasReact = $frontendContent -match "react|React"
    $hasVite = $frontendContent -match "vite|Vite"
    $hasTitle = $frontendContent -match "<title>([^<]+)</title>"
    
    Write-Host "   ✅ Frontend Structure:" -ForegroundColor Green
    Write-Host "      • React App: $(if ($hasReact) { '✅' } else { '❌' })" -ForegroundColor White
    Write-Host "      • Vite Build: $(if ($hasVite) { '✅' } else { '❌' })" -ForegroundColor White
    Write-Host "      • Has Title: $(if ($hasTitle) { '✅' } else { '❌' })" -ForegroundColor White
    Write-Host "      • Content Size: $($frontendContent.Length) bytes" -ForegroundColor White
}

# 4. Route Testing
Write-Host "🛣️ 4. Route Testing:" -ForegroundColor Cyan
Write-Host ""

$routes = @("/", "/add-expense", "/settlement", "/history", "/reports", "/settings")

foreach ($route in $routes) {
    try {
        $routeUrl = "http://$IP$route"
        $response = Invoke-WebRequest -Uri $routeUrl -TimeoutSec 5 -UseBasicParsing
        Write-Host "   ✅ Route $route`: HTTP $($response.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Host "   ❌ Route $route`: Failed" -ForegroundColor Red
    }
}

Write-Host ""

# 5. Performance Analysis
Write-Host "⚡ 5. Performance Analysis:" -ForegroundColor Cyan
Write-Host ""

$performanceTests = @()
for ($i = 1; $i -le 3; $i++) {
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    try {
        $perfResponse = Invoke-WebRequest -Uri "http://$IP" -TimeoutSec 10 -UseBasicParsing
        $stopwatch.Stop()
        $performanceTests += $stopwatch.ElapsedMilliseconds
        Write-Host "   Test $i`: $($stopwatch.ElapsedMilliseconds)ms" -ForegroundColor White
    } catch {
        Write-Host "   Test $i`: Failed" -ForegroundColor Red
    }
}

if ($performanceTests.Count -gt 0) {
    $avgTime = ($performanceTests | Measure-Object -Average).Average
    Write-Host "   📊 Average Response Time: $([math]::Round($avgTime, 2))ms" -ForegroundColor Green
    
    $performanceRating = if ($avgTime -lt 500) { "Excellent" } 
                        elseif ($avgTime -lt 1000) { "Good" } 
                        elseif ($avgTime -lt 2000) { "Fair" } 
                        else { "Poor" }
    
    Write-Host "   🎯 Performance Rating: $performanceRating" -ForegroundColor $(
        if ($performanceRating -eq "Excellent") { "Green" }
        elseif ($performanceRating -eq "Good") { "Yellow" }
        else { "Red" }
    )
}

Write-Host ""

# 6. Security Analysis
Write-Host "🔒 6. Security Analysis:" -ForegroundColor Cyan
Write-Host ""

try {
    $response = Invoke-WebRequest -Uri "http://$IP" -TimeoutSec 5 -UseBasicParsing
    $headers = $response.Headers
    
    $securityHeaders = @(
        "X-Frame-Options",
        "X-Content-Type-Options", 
        "X-XSS-Protection",
        "Strict-Transport-Security",
        "Content-Security-Policy"
    )
    
    foreach ($header in $securityHeaders) {
        $hasHeader = $headers.ContainsKey($header)
        Write-Host "   $header`: $(if ($hasHeader) { '✅ Present' } else { '❌ Missing' })" -ForegroundColor $(if ($hasHeader) { "Green" } else { "Red" })
    }
    
    # Check for HTTPS
    $httpsUrl = "https://$DOMAIN"
    try {
        $httpsResponse = Invoke-WebRequest -Uri $httpsUrl -TimeoutSec 5 -UseBasicParsing
        Write-Host "   HTTPS Support: ✅ Available" -ForegroundColor Green
    } catch {
        Write-Host "   HTTPS Support: ❌ Not configured" -ForegroundColor Red
    }
    
} catch {
    Write-Host "   ❌ Failed to analyze security headers" -ForegroundColor Red
}

Write-Host ""

# 7. Production Readiness Summary
Write-Host "🎯 7. Production Readiness Summary:" -ForegroundColor Cyan
Write-Host ""

$readinessScore = 0
$totalChecks = 10

# API functionality
if ($apiResults["Expenses API"].Status -eq "Success") { $readinessScore++ }
if ($apiResults["Settlements API"].Status -eq "Success") { $readinessScore++ }
if ($apiResults["Health Check"].Status -eq "Success") { $readinessScore++ }

# Frontend functionality
if ($apiResults["Frontend"].Status -eq "Success") { $readinessScore++ }
if ($apiResults["Domain Frontend"].Status -eq "Success") { $readinessScore++ }

# Performance
if ($performanceTests.Count -gt 0 -and ($performanceTests | Measure-Object -Average).Average -lt 2000) { $readinessScore++ }

# Data integrity
if ($apiResults["Expenses API"].DataCount -gt 0) { $readinessScore++ }

# Basic security
try {
    $response = Invoke-WebRequest -Uri "http://$IP" -TimeoutSec 5 -UseBasicParsing
    if ($response.Headers.ContainsKey("X-Frame-Options")) { $readinessScore++ }
} catch { }

# Domain access
try {
    $domainResponse = Invoke-WebRequest -Uri "http://$DOMAIN" -TimeoutSec 5 -UseBasicParsing
    if ($domainResponse.StatusCode -eq 200) { $readinessScore++ }
} catch { }

# Route functionality
$workingRoutes = 0
foreach ($route in $routes) {
    try {
        $routeResponse = Invoke-WebRequest -Uri "http://$IP$route" -TimeoutSec 5 -UseBasicParsing
        if ($routeResponse.StatusCode -eq 200) { $workingRoutes++ }
    } catch { }
}
if ($workingRoutes -eq $routes.Count) { $readinessScore++ }

$readinessPercentage = [math]::Round(($readinessScore / $totalChecks) * 100, 1)

Write-Host "📊 Production Readiness Score: $readinessScore/$totalChecks ($readinessPercentage%)" -ForegroundColor $(
    if ($readinessPercentage -ge 80) { "Green" }
    elseif ($readinessPercentage -ge 60) { "Yellow" }
    else { "Red" }
)

$readinessLevel = if ($readinessPercentage -ge 90) { "PRODUCTION READY" }
                 elseif ($readinessPercentage -ge 80) { "MOSTLY READY" }
                 elseif ($readinessPercentage -ge 60) { "NEEDS WORK" }
                 else { "NOT READY" }

Write-Host "🎯 Status: $readinessLevel" -ForegroundColor $(
    if ($readinessLevel -eq "PRODUCTION READY") { "Green" }
    elseif ($readinessLevel -eq "MOSTLY READY") { "Yellow" }
    else { "Red" }
)

Write-Host ""
Write-Host "✅ AUDIT COMPLETE" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
