# 🚀 AUTOMATED FRONTEND DEPLOYMENT COMMAND
# Copy and paste this single command into your DigitalOcean console:

curl -s https://raw.githubusercontent.com/your-repo/auto-deploy-frontend.sh | bash

# OR if you prefer to run it step by step, copy and paste this entire block:

#!/bin/bash
echo "🚀 STARTING AUTOMATED FRONTEND DEPLOYMENT" && \
echo "==========================================" && \
ps aux | grep node | grep -v grep && \
netstat -tlnp | grep -E ':(80|3001|8080)' || echo "No services on target ports" && \
echo "🛑 Stopping existing frontend servers..." && \
pkill -f "frontend-server" || echo "No frontend servers to stop" && \
pkill -f "port.*8080" || echo "No port 8080 services to stop" && \
echo "📄 Creating frontend HTML file..." && \
cat > /root/nawras-frontend.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Expense Tracker</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .login-container { display: flex; align-items: center; justify-content: center; min-height: 100vh; padding: 20px; }
        .login-box { background: white; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 3rem; max-width: 450px; width: 100%; }
        .app-container { background: white; min-height: 100vh; display: flex; }
        .sidebar { background: #2c3e50; color: white; width: 280px; padding: 2rem 0; display: flex; flex-direction: column; }
        .main-content { flex: 1; padding: 2rem; background: #f8f9fa; }
        .form-group { margin-bottom: 1.5rem; }
        .form-group label { display: block; margin-bottom: 0.5rem; color: #2c3e50; font-weight: 500; }
        .form-group input { width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 8px; font-size: 1rem; }
        .btn { background: #667eea; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; cursor: pointer; font-size: 1rem; transition: all 0.3s; }
        .btn:hover { background: #5a6fd8; }
        .btn-primary { background: #007bff; width: 100%; padding: 1rem; }
        .btn-primary:hover { background: #0056b3; }
        .success-message { background: #d4edda; color: #155724; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; }
        .error-message { background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; display: none; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem; }
        .stat-card { background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .stat-value { font-size: 2rem; font-weight: 600; margin-bottom: 0.5rem; }
        .expenses-list { background: white; border-radius: 12px; padding: 1.5rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .expense-item { display: flex; justify-content: space-between; align-items: center; padding: 1rem 0; border-bottom: 1px solid #eee; }
        .hidden { display: none !important; }
        .demo-accounts { background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-top: 1.5rem; font-size: 0.9rem; }
    </style>
</head>
<body>
    <div id="loginScreen" class="login-container">
        <div class="login-box">
            <h1 style="text-align: center; margin-bottom: 2rem; color: #2c3e50;">🏢 Nawras Admin</h1>
            <div class="success-message">
                <strong>✅ Successfully Deployed!</strong><br>
                Live website with Supabase integration
            </div>
            <div id="loginError" class="error-message"></div>
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" required placeholder="Enter your email">
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" required placeholder="Enter your password">
                </div>
                <button type="submit" class="btn-primary" id="loginBtn">Sign In</button>
            </form>
            <div class="demo-accounts">
                <strong>Demo Accounts:</strong><br>
                • <EMAIL> / taha2024<br>
                • <EMAIL> / burak2024
            </div>
        </div>
    </div>
    <div id="mainApp" class="app-container hidden">
        <div class="sidebar">
            <div style="padding: 0 2rem 2rem; border-bottom: 1px solid #34495e; margin-bottom: 2rem;">
                <h1 style="font-size: 1.5rem;">🏢 Nawras Admin</h1>
                <p style="font-size: 0.8rem; color: #bdc3c7; margin-top: 0.5rem;">Live Supabase Data</p>
            </div>
            <div style="margin-top: auto; padding: 1rem 2rem; border-top: 1px solid #34495e;">
                <p><strong id="userName">User</strong></p>
                <p id="userEmail" style="font-size: 0.9rem; margin-bottom: 0.5rem;"><EMAIL></p>
                <button onclick="logout()" style="background: #dc3545; color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer; width: 100%;">Sign Out</button>
            </div>
        </div>
        <div class="main-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <div>
                    <h2 style="color: #2c3e50; font-size: 2rem;">📊 Dashboard</h2>
                    <p style="color: #6c757d; margin-top: 0.5rem;">Live expense tracking from Supabase</p>
                </div>
            </div>
            <div id="errorMessage" class="error-message"></div>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 style="color: #6c757d; font-size: 0.9rem; margin-bottom: 0.5rem;">Taha's Expenses</h3>
                    <div class="stat-value" style="color: #007bff;" id="tahaExpenses">$0.00</div>
                    <div style="color: #6c757d; font-size: 0.9rem;" id="tahaCount">0 expenses</div>
                </div>
                <div class="stat-card">
                    <h3 style="color: #6c757d; font-size: 0.9rem; margin-bottom: 0.5rem;">Burak's Expenses</h3>
                    <div class="stat-value" style="color: #28a745;" id="burakExpenses">$0.00</div>
                    <div style="color: #6c757d; font-size: 0.9rem;" id="burakCount">0 expenses</div>
                </div>
                <div class="stat-card">
                    <h3 style="color: #6c757d; font-size: 0.9rem; margin-bottom: 0.5rem;">Combined Total</h3>
                    <div class="stat-value" style="color: #6f42c1;" id="totalExpenses">$0.00</div>
                    <div style="color: #6c757d; font-size: 0.9rem;" id="totalCount">0 total expenses</div>
                </div>
            </div>
            <div class="expenses-list">
                <h3 style="margin-bottom: 1rem;">Recent Expenses</h3>
                <div id="expensesList">
                    <div style="text-align: center; padding: 2rem; color: #6c757d;">Loading expenses from Supabase...</div>
                </div>
            </div>
        </div>
    </div>
    <script>
        const API_BASE_URL = 'http://partner.nawrasinchina.com:3001';
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;
        if (authToken) { checkSession(); } else { showLogin(); }
        function showLogin() { document.getElementById('loginScreen').classList.remove('hidden'); document.getElementById('mainApp').classList.add('hidden'); }
        function showApp() { document.getElementById('loginScreen').classList.add('hidden'); document.getElementById('mainApp').classList.remove('hidden'); loadDashboardData(); }
        function showError(message, elementId = 'loginError') { const errorEl = document.getElementById(elementId); if (errorEl) { errorEl.textContent = message; errorEl.style.display = 'block'; setTimeout(() => errorEl.style.display = 'none', 5000); } }
        function checkSession() { fetch(`${API_BASE_URL}/api/auth/session`, { headers: { 'Authorization': 'Bearer ' + authToken } }).then(response => response.json()).then(data => { if (data.success && data.data) { currentUser = data.data.user; updateUserInfo(); showApp(); } else { localStorage.removeItem('authToken'); authToken = null; showLogin(); } }).catch(error => { localStorage.removeItem('authToken'); authToken = null; showLogin(); }); }
        function updateUserInfo() { if (currentUser) { document.getElementById('userName').textContent = currentUser.name; document.getElementById('userEmail').textContent = currentUser.email; } }
        document.getElementById('loginForm').addEventListener('submit', function(e) { e.preventDefault(); const email = document.getElementById('email').value; const password = document.getElementById('password').value; const submitBtn = document.getElementById('loginBtn'); submitBtn.textContent = 'Signing in...'; submitBtn.disabled = true; fetch(`${API_BASE_URL}/api/auth/sign-in`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ email: email, password: password }) }).then(response => response.json()).then(data => { if (data.success) { authToken = data.data.session.token; currentUser = data.data.user; localStorage.setItem('authToken', authToken); updateUserInfo(); showApp(); } else { showError(data.error || 'Login failed'); } }).catch(error => { showError('Network error. Please try again.'); }).finally(() => { submitBtn.textContent = 'Sign In'; submitBtn.disabled = false; }); });
        function logout() { fetch(`${API_BASE_URL}/api/auth/sign-out`, { method: 'POST', headers: { 'Authorization': 'Bearer ' + authToken } }).catch(() => {}); localStorage.removeItem('authToken'); authToken = null; currentUser = null; showLogin(); }
        function loadDashboardData() { fetch(`${API_BASE_URL}/api/expenses`, { headers: { 'Authorization': 'Bearer ' + authToken } }).then(response => { if (response.status === 401) { logout(); return; } return response.json(); }).then(expensesData => { if (expensesData && expensesData.success) { updateExpensesDisplay(expensesData.data); } else { showError('Failed to load expenses', 'errorMessage'); } }).catch(error => { showError('Failed to load data. Please refresh.', 'errorMessage'); }); }
        function updateExpensesDisplay(expenses) { let tahaTotal = 0, burakTotal = 0, tahaCount = 0, burakCount = 0; expenses.forEach(expense => { if (expense.paid_by_id === 'taha') { tahaTotal += parseFloat(expense.amount); tahaCount++; } else if (expense.paid_by_id === 'burak') { burakTotal += parseFloat(expense.amount); burakCount++; } }); const total = tahaTotal + burakTotal; document.getElementById('tahaExpenses').textContent = '$' + tahaTotal.toFixed(2); document.getElementById('tahaCount').textContent = tahaCount + ' expenses'; document.getElementById('burakExpenses').textContent = '$' + burakTotal.toFixed(2); document.getElementById('burakCount').textContent = burakCount + ' expenses'; document.getElementById('totalExpenses').textContent = '$' + total.toFixed(2); document.getElementById('totalCount').textContent = expenses.length + ' total expenses'; const expensesList = document.getElementById('expensesList'); if (expenses.length === 0) { expensesList.innerHTML = '<div style="text-align: center; padding: 2rem; color: #6c757d;">No expenses found</div>'; } else { let html = ''; expenses.forEach(expense => { html += `<div class="expense-item"><div><h4 style="color: #2c3e50; margin-bottom: 0.25rem;">${expense.description}</h4><p style="color: #6c757d; font-size: 0.9rem;">${expense.category} • Paid by ${expense.paid_by_id === 'taha' ? 'Taha' : 'Burak'} • ${expense.date}</p></div><div style="font-size: 1.25rem; font-weight: 600; color: #2c3e50;">$${parseFloat(expense.amount).toFixed(2)}</div></div>`; }); expensesList.innerHTML = html; } }
    </script>
</body>
</html>
EOF
echo "✅ Frontend HTML file created successfully" && \
cat > /root/frontend-server.js << 'EOF'
const express = require('express');
const app = express();
const PORT = 8080;
app.use((req, res, next) => { res.header('Access-Control-Allow-Origin', '*'); res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization'); res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS'); next(); });
app.get('/', (req, res) => { res.sendFile('/root/nawras-frontend.html'); });
app.get('/health', (req, res) => { res.json({ status: 'ok', server: 'frontend-server', port: PORT, message: 'Frontend server operational' }); });
app.listen(PORT, () => { console.log(`✅ Frontend server running on port ${PORT}`); console.log(`🌐 Access: http://partner.nawrasinchina.com:${PORT}`); });
EOF
echo "✅ Frontend server created" && \
rm -f /etc/nginx/sites-enabled/default && \
cat > /etc/nginx/sites-available/partner.nawrasinchina.com << 'EOF'
server {
    listen 80;
    server_name partner.nawrasinchina.com;
    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF
ln -sf /etc/nginx/sites-available/partner.nawrasinchina.com /etc/nginx/sites-enabled/ && \
nginx -t && systemctl reload nginx && \
echo "✅ Nginx configured and reloaded" && \
cd /root && \
nohup node frontend-server.js > frontend.log 2>&1 & \
sleep 3 && \
curl -s http://localhost:8080/health && \
curl -s http://localhost:3001/api/health && \
echo "" && \
echo "🎉 DEPLOYMENT COMPLETED!" && \
echo "✅ Frontend: http://partner.nawrasinchina.com" && \
echo "✅ API: http://partner.nawrasinchina.com:3001" && \
echo "✅ Demo: <EMAIL> / taha2024"
