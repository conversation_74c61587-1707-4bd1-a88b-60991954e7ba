const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();

// Serve the main frontend page
app.get('/', (req, res) => {
    res.send(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Expense Tracker</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
        }
        .login-container { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
            padding: 3rem; 
            max-width: 400px; 
            width: 100%; 
        }
        .form-group { margin-bottom: 1rem; }
        .form-group label { 
            display: block; 
            margin-bottom: 0.5rem; 
            color: #2c3e50; 
            font-weight: 500; 
        }
        .form-group input { 
            width: 100%; 
            padding: 0.75rem; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            font-size: 1rem; 
        }
        .btn-primary { 
            background: #007bff; 
            color: white; 
            border: none; 
            width: 100%; 
            padding: 1rem; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 1rem; 
        }
        .btn-primary:hover { background: #0056b3; }
        .error-message { 
            background: #f8d7da; 
            color: #721c24; 
            padding: 1rem; 
            border-radius: 5px; 
            margin-bottom: 1rem; 
            border: 1px solid #f5c6cb; 
            display: none; 
        }
        .success-message { 
            background: #d4edda; 
            color: #155724; 
            padding: 1rem; 
            border-radius: 5px; 
            margin-bottom: 1rem; 
            border: 1px solid #c3e6cb; 
            display: none; 
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2 style="text-align: center; margin-bottom: 2rem; color: #2c3e50;">Nawras Admin Login</h2>
        <div id="loginError" class="error-message"></div>
        <div id="loginSuccess" class="success-message"></div>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn-primary">Sign In</button>
        </form>
        <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 5px; font-size: 0.9rem;">
            <strong>Demo Accounts:</strong><br>
            • <EMAIL> / taha2024<br>
            • <EMAIL> / burak2024
        </div>
        <div style="margin-top: 1rem; padding: 1rem; background: #e7f3ff; border-radius: 5px; font-size: 0.9rem;">
            <strong>Status:</strong> ✅ Port 80 Fixed<br>
            <strong>Backend:</strong> Authentication Working
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const submitBtn = document.querySelector('.btn-primary');
            submitBtn.textContent = 'Signing in...';
            submitBtn.disabled = true;
            
            fetch('/api/auth/sign-in', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: email, password: password })
            })
            .then(function(response) { 
                return response.json(); 
            })
            .then(function(data) {
                if (data.success) {
                    localStorage.setItem('authToken', data.data.session.token);
                    document.getElementById('loginSuccess').textContent = 'Login successful! Authentication system working on port 80.';
                    document.getElementById('loginSuccess').style.display = 'block';
                    document.getElementById('loginError').style.display = 'none';
                } else {
                    document.getElementById('loginError').textContent = data.error || 'Login failed';
                    document.getElementById('loginError').style.display = 'block';
                    document.getElementById('loginSuccess').style.display = 'none';
                }
            })
            .catch(function(error) {
                console.error('Login error:', error);
                document.getElementById('loginError').textContent = 'Network error. Please try again.';
                document.getElementById('loginError').style.display = 'block';
                document.getElementById('loginSuccess').style.display = 'none';
            })
            .finally(function() {
                submitBtn.textContent = 'Sign In';
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>`);
});

// Proxy API requests to the backend server on port 3001
app.use('/api', createProxyMiddleware({
    target: 'http://localhost:3001',
    changeOrigin: true,
    logLevel: 'debug'
}));

// Health check for port 80 server
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        port: 80, 
        message: 'Frontend server working',
        backend: 'http://localhost:3001'
    });
});

app.listen(80, () => {
    console.log('Frontend server running on port 80');
    console.log('Proxying API requests to port 3001');
});