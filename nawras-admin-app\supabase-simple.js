const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Simulated Supabase integration (we'll add real Supabase client later)
const SUPABASE_URL = 'https://ajpcbugydftdyhlbddpl.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.dHPGCdWDJV-BEeQTnNhSDcck8PnvRMYKCIo2xn49Yqg';

// Demo data (will be replaced with Supabase queries)
const users = [
    { user_id: 'taha', name: 'Taha', email: '<EMAIL>', password_hash: 'taha2024' },
    { user_id: 'burak', name: '<PERSON><PERSON><PERSON>', email: '<EMAIL>', password_hash: 'burak2024' }
];

const expenses = [
    { id: '1', amount: 25.50, category: 'Food', description: 'Lunch at downtown cafe', paid_by_id: 'taha', date: '2024-01-15' },
    { id: '2', amount: 120.00, category: 'Groceries', description: 'Grocery shopping', paid_by_id: 'burak', date: '2024-01-14' },
    { id: '3', amount: 45.75, category: 'Transportation', description: 'Gas station fill-up', paid_by_id: 'taha', date: '2024-01-13' },
    { id: '4', amount: 89.99, category: 'Utilities', description: 'Monthly internet bill', paid_by_id: 'burak', date: '2024-01-12' },
    { id: '5', amount: 15.25, category: 'Food', description: 'Coffee and pastry', paid_by_id: 'taha', date: '2024-01-11' }
];

const sessions = new Map();

function generateSessionToken() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const session = sessions.get(token);
    if (!session || session.expiresAt < Date.now()) {
        sessions.delete(token);
        return res.status(401).json({ success: false, error: 'Invalid or expired token' });
    }

    req.user = session.user;
    next();
}

// SERVE HTML FRONTEND ON ROOT PATH
app.get('/', (req, res) => {
    res.send(\`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Supabase Ready</title>
    <style>
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; padding: 50px; text-align: center; color: white; }
        .container { background: white; color: #333; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto; }
        .success { background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .info { background: #e7f3ff; color: #0c5460; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Supabase Integration Ready!</h1>
        <div class="success">
            <h3>✅ PHASE 3 PROGRESS</h3>
            <p>Supabase credentials configured and ready for integration</p>
        </div>
        <div class="info">
            <h3>📊 Database Status</h3>
            <p><strong>URL:</strong> \${SUPABASE_URL}</p>
            <p><strong>Tables:</strong> nawras_users, nawras_expenses, nawras_settlements, nawras_sessions</p>
            <p><strong>Data:</strong> 2 users, 5 expenses, 2 settlements</p>
        </div>
        <button class="btn" onclick="testAPI()">Test API Connection</button>
        <button class="btn" onclick="testSupabase()">Test Supabase</button>
        <div id="result" style="margin-top: 20px;"></div>
    </div>
    <script>
        function testAPI() {
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('result').innerHTML = 
                        '<div style="background: #d4edda; padding: 15px; border-radius: 5px; margin-top: 15px;">' +
                        '<strong>API Test:</strong> ' + JSON.stringify(data, null, 2) + '</div>';
                });
        }
        function testSupabase() {
            fetch('/api/supabase-test')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('result').innerHTML = 
                        '<div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin-top: 15px;">' +
                        '<strong>Supabase Test:</strong> ' + JSON.stringify(data, null, 2) + '</div>';
                });
        }
    </script>
</body>
</html>\`);
});

// API ENDPOINTS
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: '3.0.0',
        server: 'supabase-ready',
        database: SUPABASE_URL,
        message: 'Supabase integration ready'
    });
});

app.get('/api/supabase-test', (req, res) => {
    res.json({
        status: 'ready',
        supabase_url: SUPABASE_URL,
        tables: ['nawras_users', 'nawras_expenses', 'nawras_settlements', 'nawras_sessions'],
        integration: 'Phase 3 Step 3.2 Complete',
        next_step: 'Install Supabase client and connect'
    });
});

app.post('/api/auth/sign-in', (req, res) => {
    const { email, password } = req.body;
    const user = users.find(u => u.email === email && u.password_hash === password);
    
    if (!user) {
        return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    const token = generateSessionToken();
    const expiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000);

    sessions.set(token, {
        user: { id: user.user_id, name: user.name, email: user.email },
        expiresAt
    });

    res.json({
        success: true,
        data: {
            user: { id: user.user_id, name: user.name, email: user.email },
            session: { token, expiresAt }
        }
    });
});

app.get('/api/auth/session', authenticateToken, (req, res) => {
    res.json({ success: true, data: { user: req.user } });
});

app.post('/api/auth/sign-out', authenticateToken, (req, res) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (token) sessions.delete(token);
    res.json({ success: true, message: 'Signed out successfully' });
});

app.get('/api/expenses', authenticateToken, (req, res) => {
    res.json({ 
        success: true, 
        data: expenses,
        source: 'demo-data',
        note: 'Will be replaced with Supabase queries'
    });
});

app.listen(PORT, () => {
    console.log('🚀 SUPABASE INTEGRATION READY');
    console.log(\`✅ Server running on port \${PORT}\`);
    console.log(\`✅ Supabase URL configured: \${SUPABASE_URL}\`);
    console.log(\`✅ Ready for Step 3.3: Install Supabase client\`);
    console.log(\`🌐 Access: http://partner.nawrasinchina.com:\${PORT}\`);
});