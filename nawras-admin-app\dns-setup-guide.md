# DNS Setup Guide for partner.nawrasinchina.com

## Current Server Information
- **Primary Server IP**: ************* (nawras-admin-deployed)
- **Secondary Server IP**: *************** (nawras-admin-app)
- **Domain**: partner.nawrasinchina.com

## Step 1: DNS Configuration

You need to configure DNS records for your domain. Here's what you need to add:

### A Records
Add these A records in your DNS provider (where nawrasinchina.com is hosted):

```
Type: A
Name: partner
Value: *************
TTL: 300 (5 minutes)
```

### Alternative (if using the secondary server):
```
Type: A
Name: partner
Value: ***************
TTL: 300 (5 minutes)
```

## Step 2: Verify DNS Propagation

After adding the DNS record, verify it's working:

```bash
# Check DNS resolution
nslookup partner.nawrasinchina.com

# Check from different locations
dig partner.nawrasinchina.com @*******
dig partner.nawrasinchina.com @*******
```

## Step 3: Test Website Access

Once DNS is propagated (5-30 minutes), test these URLs:

- http://partner.nawrasinchina.com
- http://*************
- http://***************

## Step 4: SSL Certificate Setup

After DNS is working, run the SSL setup:

```bash
# On the server
certbot --nginx -d partner.nawrasinchina.com --non-interactive --agree-tos --email <EMAIL>
```

## Troubleshooting

### If the website is not loading:

1. **Check server status:**
   ```bash
   ssh root@************* 'docker ps'
   ssh root@************* 'systemctl status nginx'
   ```

2. **Check firewall:**
   ```bash
   ssh root@************* 'ufw status'
   ```

3. **Check application logs:**
   ```bash
   ssh root@************* 'cd /opt/nawras-admin && docker-compose logs'
   ```

4. **Restart services:**
   ```bash
   ssh root@************* 'cd /opt/nawras-admin && docker-compose restart'
   ssh root@************* 'systemctl restart nginx'
   ```

### If DNS is not resolving:

1. **Check your DNS provider settings**
2. **Verify the A record is correctly set**
3. **Wait for propagation (up to 48 hours, usually 5-30 minutes)**
4. **Use online DNS checker tools**

## Quick Commands

### Deploy to primary server (*************):
```bash
chmod +x fix-deployment.sh
./fix-deployment.sh
```

### Quick troubleshooting:
```bash
chmod +x quick-fix.sh
./quick-fix.sh
```

### Check which server is working:
```bash
curl -I http://*************
curl -I http://***************
```

## Expected Results

After successful setup:
- ✅ http://partner.nawrasinchina.com → Shows Nawras Admin app
- ✅ https://partner.nawrasinchina.com → Shows Nawras Admin app (with SSL)
- ✅ API accessible at http://partner.nawrasinchina.com/api/expenses

## Contact Information

If you need help with DNS configuration, contact your domain registrar or DNS provider support.
