#!/bin/bash

# Comprehensive Testing Script for Production Fixes
# Tests security, functionality, performance, and dashboard components

DOMAIN="partner.nawrasinchina.com"
SERVER_IP="*************"

echo "🧪 COMPREHENSIVE TESTING SUITE"
echo "=============================="
echo "Domain: $DOMAIN"
echo "Server: $SERVER_IP"
echo ""

# Initialize test results
declare -A test_results
total_tests=0
passed_tests=0

# Function to record test result
record_test() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    test_results["$test_name"]="$result|$details"
    ((total_tests++))
    
    if [ "$result" = "PASS" ]; then
        ((passed_tests++))
        echo "✅ $test_name: PASSED - $details"
    else
        echo "❌ $test_name: FAILED - $details"
    fi
}

# Test Suite 1: Security Tests
echo "🔒 TEST SUITE 1: SECURITY VALIDATION"
echo "===================================="

# Test 1.1: HTTPS Certificate
echo "Testing HTTPS certificate..."
if curl -s -I https://$DOMAIN | grep -q "HTTP/2 200\|HTTP/1.1 200"; then
    cert_info=$(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -subject 2>/dev/null)
    record_test "HTTPS_Certificate" "PASS" "Valid SSL certificate"
else
    record_test "HTTPS_Certificate" "FAIL" "SSL certificate not working"
fi

# Test 1.2: Security Headers
echo "Testing security headers..."
response=$(curl -s -I https://$DOMAIN)

# HSTS Header
if echo "$response" | grep -qi "strict-transport-security"; then
    record_test "HSTS_Header" "PASS" "HSTS header present"
else
    record_test "HSTS_Header" "FAIL" "HSTS header missing"
fi

# XSS Protection
if echo "$response" | grep -qi "x-xss-protection"; then
    record_test "XSS_Protection" "PASS" "XSS protection header present"
else
    record_test "XSS_Protection" "FAIL" "XSS protection header missing"
fi

# Content Security Policy
if echo "$response" | grep -qi "content-security-policy"; then
    record_test "CSP_Header" "PASS" "CSP header present"
else
    record_test "CSP_Header" "FAIL" "CSP header missing"
fi

# Test 1.3: HTTP to HTTPS Redirect
echo "Testing HTTP to HTTPS redirect..."
redirect_response=$(curl -s -I http://$DOMAIN)
if echo "$redirect_response" | grep -q "301\|302"; then
    redirect_location=$(echo "$redirect_response" | grep -i "location:" | cut -d' ' -f2 | tr -d '\r')
    if [[ "$redirect_location" == https://* ]]; then
        record_test "HTTP_Redirect" "PASS" "Properly redirects to HTTPS"
    else
        record_test "HTTP_Redirect" "FAIL" "Redirects but not to HTTPS"
    fi
else
    record_test "HTTP_Redirect" "FAIL" "No redirect configured"
fi

echo ""

# Test Suite 2: Functionality Tests
echo "🌐 TEST SUITE 2: FUNCTIONALITY VALIDATION"
echo "========================================="

# Test 2.1: Main Application
echo "Testing main application..."
main_response=$(curl -s -w "%{http_code}" https://$DOMAIN -o /dev/null)
if [ "$main_response" = "200" ]; then
    record_test "Main_Application" "PASS" "Application loads successfully"
else
    record_test "Main_Application" "FAIL" "Application failed to load (HTTP $main_response)"
fi

# Test 2.2: API Endpoints
echo "Testing API endpoints..."

# Expenses API
expenses_response=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses -o /dev/null)
if [ "$expenses_response" = "200" ]; then
    # Test data structure
    expenses_data=$(curl -s https://$DOMAIN/api/expenses)
    if echo "$expenses_data" | jq -e '.success' >/dev/null 2>&1; then
        record_test "Expenses_API" "PASS" "API returns valid JSON structure"
    else
        record_test "Expenses_API" "FAIL" "API returns invalid JSON structure"
    fi
else
    record_test "Expenses_API" "FAIL" "API not accessible (HTTP $expenses_response)"
fi

# Settlements API
settlements_response=$(curl -s -w "%{http_code}" https://$DOMAIN/api/settlements -o /dev/null)
if [ "$settlements_response" = "200" ]; then
    settlements_data=$(curl -s https://$DOMAIN/api/settlements)
    if echo "$settlements_data" | jq -e '.success' >/dev/null 2>&1; then
        record_test "Settlements_API" "PASS" "API returns valid JSON structure"
    else
        record_test "Settlements_API" "FAIL" "API returns invalid JSON structure"
    fi
else
    record_test "Settlements_API" "FAIL" "API not accessible (HTTP $settlements_response)"
fi

# Test 2.3: Route Accessibility
echo "Testing route accessibility..."
routes=("/" "/add-expense" "/settlement" "/history" "/reports" "/settings")

for route in "${routes[@]}"; do
    route_response=$(curl -s -w "%{http_code}" https://$DOMAIN$route -o /dev/null)
    if [ "$route_response" = "200" ]; then
        record_test "Route_$route" "PASS" "Route accessible"
    else
        record_test "Route_$route" "FAIL" "Route not accessible (HTTP $route_response)"
    fi
done

echo ""

# Test Suite 3: Performance Tests
echo "⚡ TEST SUITE 3: PERFORMANCE VALIDATION"
echo "======================================"

# Test 3.1: Response Time
echo "Testing response times..."
times=()
for i in {1..5}; do
    time=$(curl -s -w "%{time_total}" https://$DOMAIN -o /dev/null)
    times+=($time)
done

# Calculate average
total=0
for time in "${times[@]}"; do
    total=$(echo "$total + $time" | bc -l)
done
average=$(echo "scale=3; $total / ${#times[@]}" | bc -l)

if (( $(echo "$average < 2.0" | bc -l) )); then
    record_test "Response_Time" "PASS" "Average: ${average}s (< 2s)"
else
    record_test "Response_Time" "FAIL" "Average: ${average}s (> 2s)"
fi

# Test 3.2: API Performance
echo "Testing API performance..."
api_time=$(curl -s -w "%{time_total}" https://$DOMAIN/api/expenses -o /dev/null)
if (( $(echo "$api_time < 1.0" | bc -l) )); then
    record_test "API_Performance" "PASS" "API response: ${api_time}s (< 1s)"
else
    record_test "API_Performance" "FAIL" "API response: ${api_time}s (> 1s)"
fi

echo ""

# Test Suite 4: Dashboard Components Tests
echo "📊 TEST SUITE 4: DASHBOARD COMPONENTS"
echo "===================================="

# Test 4.1: Dashboard Content
echo "Testing dashboard content..."
dashboard_content=$(curl -s https://$DOMAIN)

# Check for React app indicators
if echo "$dashboard_content" | grep -q "react\|React\|vite\|Vite"; then
    record_test "React_App" "PASS" "React application detected"
else
    record_test "React_App" "FAIL" "React application not detected"
fi

# Test 4.2: Data Integration
echo "Testing data integration..."
expenses_data=$(curl -s https://$DOMAIN/api/expenses)
if echo "$expenses_data" | jq -e '.data | length > 0' >/dev/null 2>&1; then
    expense_count=$(echo "$expenses_data" | jq '.data | length')
    record_test "Data_Integration" "PASS" "Live data available ($expense_count expenses)"
else
    record_test "Data_Integration" "FAIL" "No live data available"
fi

# Test 4.3: Balance Calculations
echo "Testing balance calculations..."
if echo "$expenses_data" | jq -e '.data' >/dev/null 2>&1; then
    # Extract expense data and calculate balance
    taha_total=$(echo "$expenses_data" | jq '[.data[] | select(.paidById == "taha") | .amount] | add // 0')
    burak_total=$(echo "$expenses_data" | jq '[.data[] | select(.paidById == "burak") | .amount] | add // 0')
    
    if [ "$taha_total" != "null" ] && [ "$burak_total" != "null" ]; then
        balance=$(echo "$taha_total - $burak_total" | bc -l)
        record_test "Balance_Calculation" "PASS" "Balance calculated: \$$(echo $balance | bc -l)"
    else
        record_test "Balance_Calculation" "FAIL" "Unable to calculate balance"
    fi
else
    record_test "Balance_Calculation" "FAIL" "No expense data for calculation"
fi

echo ""

# Test Suite 5: Mobile Responsiveness
echo "📱 TEST SUITE 5: MOBILE RESPONSIVENESS"
echo "====================================="

# Test 5.1: Mobile Viewport
echo "Testing mobile viewport..."
mobile_content=$(curl -s -H "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)" https://$DOMAIN)
if echo "$mobile_content" | grep -q "viewport.*width=device-width"; then
    record_test "Mobile_Viewport" "PASS" "Mobile viewport meta tag present"
else
    record_test "Mobile_Viewport" "FAIL" "Mobile viewport meta tag missing"
fi

# Test 5.2: Responsive CSS
echo "Testing responsive CSS..."
if echo "$mobile_content" | grep -q "sm:\|md:\|lg:\|xl:"; then
    record_test "Responsive_CSS" "PASS" "Responsive CSS classes detected"
else
    record_test "Responsive_CSS" "FAIL" "Responsive CSS classes not detected"
fi

echo ""

# Test Suite 6: Error Handling
echo "🚨 TEST SUITE 6: ERROR HANDLING"
echo "==============================="

# Test 6.1: 404 Handling
echo "Testing 404 error handling..."
not_found_response=$(curl -s -w "%{http_code}" https://$DOMAIN/nonexistent-page -o /dev/null)
if [ "$not_found_response" = "404" ] || [ "$not_found_response" = "200" ]; then
    record_test "404_Handling" "PASS" "404 errors handled properly"
else
    record_test "404_Handling" "FAIL" "404 errors not handled (HTTP $not_found_response)"
fi

# Test 6.2: API Error Handling
echo "Testing API error handling..."
api_error_response=$(curl -s -w "%{http_code}" https://$DOMAIN/api/nonexistent -o /dev/null)
if [ "$api_error_response" = "404" ] || [ "$api_error_response" = "500" ]; then
    record_test "API_Error_Handling" "PASS" "API errors handled properly"
else
    record_test "API_Error_Handling" "FAIL" "API errors not handled (HTTP $api_error_response)"
fi

echo ""

# Generate Test Report
echo "📋 COMPREHENSIVE TEST REPORT"
echo "============================"
echo ""

# Calculate success rate
success_rate=$(echo "scale=1; $passed_tests * 100 / $total_tests" | bc -l)

echo "📊 Test Summary:"
echo "   Total Tests: $total_tests"
echo "   Passed: $passed_tests"
echo "   Failed: $((total_tests - passed_tests))"
echo "   Success Rate: $success_rate%"
echo ""

# Determine overall status
if (( $(echo "$success_rate >= 90" | bc -l) )); then
    overall_status="🟢 EXCELLENT"
elif (( $(echo "$success_rate >= 80" | bc -l) )); then
    overall_status="🟡 GOOD"
elif (( $(echo "$success_rate >= 70" | bc -l) )); then
    overall_status="🟠 FAIR"
else
    overall_status="🔴 NEEDS IMPROVEMENT"
fi

echo "🎯 Overall Status: $overall_status"
echo ""

# Show failed tests
if [ $((total_tests - passed_tests)) -gt 0 ]; then
    echo "❌ Failed Tests:"
    for test_name in "${!test_results[@]}"; do
        IFS='|' read -r result details <<< "${test_results[$test_name]}"
        if [ "$result" = "FAIL" ]; then
            echo "   • $test_name: $details"
        fi
    done
    echo ""
fi

# Recommendations
echo "💡 Recommendations:"
if (( $(echo "$success_rate >= 90" | bc -l) )); then
    echo "   ✅ Application is ready for production!"
    echo "   ✅ All critical systems are functioning properly"
    echo "   ✅ Security measures are in place"
    echo "   ✅ Performance is excellent"
else
    echo "   ⚠️ Address failed tests before full production deployment"
    echo "   ⚠️ Review security implementations"
    echo "   ⚠️ Optimize performance if needed"
fi

echo ""
echo "🎉 COMPREHENSIVE TESTING COMPLETE!"
echo "================================="

# Exit with appropriate code
if (( $(echo "$success_rate >= 80" | bc -l) )); then
    exit 0
else
    exit 1
fi
