#!/bin/bash

# DigitalOcean API Management Script
# Uses DigitalOcean API v2 to manage droplets and deployment

set -e

# Configuration
DO_TOKEN="***********************************************************************"
API_BASE="https://api.digitalocean.com/v2"
DOMAIN="partner.nawrasinchina.com"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to make API calls
api_call() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    
    if [ -n "$data" ]; then
        curl -s -X "$method" \
            -H "Authorization: Bearer $DO_TOKEN" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_BASE$endpoint"
    else
        curl -s -X "$method" \
            -H "Authorization: Bearer $DO_TOKEN" \
            "$API_BASE$endpoint"
    fi
}

# Function to print colored output
print_status() {
    local color="$1"
    local message="$2"
    echo -e "${color}${message}${NC}"
}

# List all droplets
list_droplets() {
    print_status "$BLUE" "🔍 Fetching your DigitalOcean droplets..."
    
    response=$(api_call "GET" "/droplets")
    
    echo "$response" | jq -r '.droplets[] | 
        "ID: \(.id) | Name: \(.name) | Status: \(.status) | IP: \(.networks.v4[]? | select(.type=="public") | .ip_address) | Region: \(.region.name) | Size: \(.size.slug)"'
}

# Get specific droplet info
get_droplet_info() {
    local droplet_id="$1"
    print_status "$BLUE" "📊 Getting info for droplet $droplet_id..."
    
    response=$(api_call "GET" "/droplets/$droplet_id")
    echo "$response" | jq '.'
}

# Check droplet status
check_droplet_status() {
    local droplet_id="$1"
    print_status "$BLUE" "⚡ Checking status of droplet $droplet_id..."
    
    response=$(api_call "GET" "/droplets/$droplet_id")
    status=$(echo "$response" | jq -r '.droplet.status')
    ip=$(echo "$response" | jq -r '.droplet.networks.v4[]? | select(.type=="public") | .ip_address')
    
    print_status "$GREEN" "Status: $status"
    print_status "$GREEN" "Public IP: $ip"
    
    # Test if droplet is accessible
    if ping -c 1 "$ip" > /dev/null 2>&1; then
        print_status "$GREEN" "✅ Droplet is reachable"
    else
        print_status "$RED" "❌ Droplet is not reachable"
    fi
}

# Power on droplet
power_on_droplet() {
    local droplet_id="$1"
    print_status "$YELLOW" "🔌 Powering on droplet $droplet_id..."
    
    data='{"type": "power_on"}'
    response=$(api_call "POST" "/droplets/$droplet_id/actions" "$data")
    
    action_id=$(echo "$response" | jq -r '.action.id')
    print_status "$GREEN" "Power on action initiated. Action ID: $action_id"
    
    # Wait for action to complete
    wait_for_action "$action_id"
}

# Reboot droplet
reboot_droplet() {
    local droplet_id="$1"
    print_status "$YELLOW" "🔄 Rebooting droplet $droplet_id..."
    
    data='{"type": "reboot"}'
    response=$(api_call "POST" "/droplets/$droplet_id/actions" "$data")
    
    action_id=$(echo "$response" | jq -r '.action.id')
    print_status "$GREEN" "Reboot action initiated. Action ID: $action_id"
    
    # Wait for action to complete
    wait_for_action "$action_id"
}

# Wait for action to complete
wait_for_action() {
    local action_id="$1"
    print_status "$BLUE" "⏳ Waiting for action $action_id to complete..."
    
    while true; do
        response=$(api_call "GET" "/actions/$action_id")
        status=$(echo "$response" | jq -r '.action.status')
        
        case "$status" in
            "completed")
                print_status "$GREEN" "✅ Action completed successfully"
                break
                ;;
            "errored")
                print_status "$RED" "❌ Action failed"
                break
                ;;
            "in-progress")
                print_status "$YELLOW" "⏳ Action in progress..."
                sleep 5
                ;;
            *)
                print_status "$YELLOW" "Status: $status"
                sleep 5
                ;;
        esac
    done
}

# List domains
list_domains() {
    print_status "$BLUE" "🌐 Fetching your domains..."
    
    response=$(api_call "GET" "/domains")
    echo "$response" | jq -r '.domains[] | "Domain: \(.name) | TTL: \(.ttl)"'
}

# Get domain records
get_domain_records() {
    local domain="$1"
    print_status "$BLUE" "📋 Getting DNS records for $domain..."
    
    response=$(api_call "GET" "/domains/$domain/records")
    echo "$response" | jq -r '.domain_records[] | 
        "ID: \(.id) | Type: \(.type) | Name: \(.name) | Data: \(.data) | TTL: \(.ttl)"'
}

# Create A record for subdomain
create_subdomain_record() {
    local domain="$1"
    local subdomain="$2"
    local ip="$3"
    
    print_status "$BLUE" "🔗 Creating A record for $subdomain.$domain pointing to $ip..."
    
    data="{
        \"type\": \"A\",
        \"name\": \"$subdomain\",
        \"data\": \"$ip\",
        \"ttl\": 300
    }"
    
    response=$(api_call "POST" "/domains/$domain/records" "$data")
    
    if echo "$response" | jq -e '.domain_record' > /dev/null; then
        record_id=$(echo "$response" | jq -r '.domain_record.id')
        print_status "$GREEN" "✅ A record created successfully. Record ID: $record_id"
    else
        print_status "$RED" "❌ Failed to create A record"
        echo "$response" | jq '.'
    fi
}

# Main menu
show_menu() {
    echo ""
    print_status "$BLUE" "🚀 DigitalOcean API Management"
    echo "1. List all droplets"
    echo "2. Check droplet status"
    echo "3. Power on droplet"
    echo "4. Reboot droplet"
    echo "5. List domains"
    echo "6. Get domain records"
    echo "7. Create subdomain A record"
    echo "8. Quick fix deployment"
    echo "9. Exit"
    echo ""
}

# Quick fix for known droplets
quick_fix() {
    print_status "$BLUE" "🔧 Quick fix for Nawras Admin deployment..."
    
    # Check both known droplets
    DROPLET_1="498520148"  # nawras-admin-app (104.236.243.130)
    DROPLET_2="498520149"  # nawras-admin-deployed (138.197.8.183) - estimated ID
    
    print_status "$YELLOW" "Checking droplet status..."
    
    # List droplets to get actual IDs
    response=$(api_call "GET" "/droplets")
    
    # Find droplets by name
    nawras_app_id=$(echo "$response" | jq -r '.droplets[] | select(.name=="nawras-admin-app") | .id')
    nawras_deployed_id=$(echo "$response" | jq -r '.droplets[] | select(.name=="nawras-admin-deployed") | .id')
    
    if [ "$nawras_app_id" != "null" ] && [ -n "$nawras_app_id" ]; then
        print_status "$GREEN" "Found nawras-admin-app: ID $nawras_app_id"
        check_droplet_status "$nawras_app_id"
    fi
    
    if [ "$nawras_deployed_id" != "null" ] && [ -n "$nawras_deployed_id" ]; then
        print_status "$GREEN" "Found nawras-admin-deployed: ID $nawras_deployed_id"
        check_droplet_status "$nawras_deployed_id"
    fi
    
    # Suggest next steps
    print_status "$YELLOW" "💡 Suggested next steps:"
    echo "1. If droplets are down, use option 3 to power them on"
    echo "2. Use option 7 to create subdomain A record"
    echo "3. SSH to working droplet and run deployment script"
}

# Main script
main() {
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        print_status "$RED" "❌ jq is required but not installed. Please install jq first."
        exit 1
    fi
    
    while true; do
        show_menu
        read -p "Choose an option (1-9): " choice
        
        case $choice in
            1)
                list_droplets
                ;;
            2)
                read -p "Enter droplet ID: " droplet_id
                check_droplet_status "$droplet_id"
                ;;
            3)
                read -p "Enter droplet ID to power on: " droplet_id
                power_on_droplet "$droplet_id"
                ;;
            4)
                read -p "Enter droplet ID to reboot: " droplet_id
                reboot_droplet "$droplet_id"
                ;;
            5)
                list_domains
                ;;
            6)
                read -p "Enter domain name (e.g., nawrasinchina.com): " domain
                get_domain_records "$domain"
                ;;
            7)
                read -p "Enter domain (e.g., nawrasinchina.com): " domain
                read -p "Enter subdomain (e.g., partner): " subdomain
                read -p "Enter IP address: " ip
                create_subdomain_record "$domain" "$subdomain" "$ip"
                ;;
            8)
                quick_fix
                ;;
            9)
                print_status "$GREEN" "👋 Goodbye!"
                exit 0
                ;;
            *)
                print_status "$RED" "❌ Invalid option. Please choose 1-9."
                ;;
        esac
        
        echo ""
        read -p "Press Enter to continue..."
    done
}

# Run main function
main
