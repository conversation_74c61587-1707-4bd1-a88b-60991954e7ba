#!/bin/bash

# Deployment Validation Script
# Run this after deployment to validate all functionality

DOMAIN="partner.nawrasinchina.com"

echo "🧪 DEPLOYMENT VALIDATION"
echo "========================"
echo "Domain: $DOMAIN"
echo ""

# Initialize test results
total_tests=0
passed_tests=0

# Function to record test result
test_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    ((total_tests++))
    
    if [ "$result" = "PASS" ]; then
        ((passed_tests++))
        echo "✅ $test_name: PASSED - $details"
    else
        echo "❌ $test_name: FAILED - $details"
    fi
}

echo "🔒 SECURITY TESTS"
echo "================="

# Test HTTPS Certificate
echo "Testing HTTPS certificate..."
if curl -s -I https://$DOMAIN | grep -q "HTTP/2 200\|HTTP/1.1 200"; then
    test_result "HTTPS_Certificate" "PASS" "SSL certificate working"
else
    test_result "HTTPS_Certificate" "FAIL" "SSL certificate not working"
fi

# Test HTTP to HTTPS Redirect
echo "Testing HTTP to HTTPS redirect..."
redirect_response=$(curl -s -I http://$DOMAIN)
if echo "$redirect_response" | grep -q "301\|302"; then
    test_result "HTTP_Redirect" "PASS" "HTTP redirects to HTTPS"
else
    test_result "HTTP_Redirect" "FAIL" "HTTP redirect not working"
fi

# Test Security Headers
echo "Testing security headers..."
headers=$(curl -s -I https://$DOMAIN)

if echo "$headers" | grep -qi "strict-transport-security"; then
    test_result "HSTS_Header" "PASS" "HSTS header present"
else
    test_result "HSTS_Header" "FAIL" "HSTS header missing"
fi

if echo "$headers" | grep -qi "x-xss-protection"; then
    test_result "XSS_Protection" "PASS" "XSS protection header present"
else
    test_result "XSS_Protection" "FAIL" "XSS protection header missing"
fi

echo ""

echo "🔐 AUTHENTICATION TESTS"
echo "======================="

# Test Authentication Endpoints
echo "Testing authentication endpoints..."

# Test session endpoint
session_response=$(curl -s -w "%{http_code}" https://$DOMAIN/api/auth/session -o /tmp/session_test.json)
if [ "$session_response" = "200" ]; then
    test_result "Auth_Session_Endpoint" "PASS" "Session endpoint accessible"
else
    test_result "Auth_Session_Endpoint" "FAIL" "Session endpoint not accessible"
fi

# Test Taha Authentication
echo "Testing Taha authentication..."
taha_auth=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"taha2024"}' \
    -c /tmp/taha_cookies.txt \
    -o /tmp/taha_auth.json)

if [ "$taha_auth" = "200" ]; then
    test_result "Taha_Authentication" "PASS" "Taha can sign in successfully"
    TAHA_SESSION_VALID=true
else
    test_result "Taha_Authentication" "FAIL" "Taha authentication failed"
    TAHA_SESSION_VALID=false
fi

# Test Burak Authentication
echo "Testing Burak authentication..."
burak_auth=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"burak2024"}' \
    -c /tmp/burak_cookies.txt \
    -o /tmp/burak_auth.json)

if [ "$burak_auth" = "200" ]; then
    test_result "Burak_Authentication" "PASS" "Burak can sign in successfully"
    BURAK_SESSION_VALID=true
else
    test_result "Burak_Authentication" "FAIL" "Burak authentication failed"
    BURAK_SESSION_VALID=false
fi

# Test Invalid Credentials
echo "Testing invalid credential rejection..."
invalid_auth=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"invalid"}' \
    -o /tmp/invalid_auth.json)

if [ "$invalid_auth" = "401" ] || [ "$invalid_auth" = "400" ]; then
    test_result "Invalid_Credentials" "PASS" "Invalid credentials properly rejected"
else
    test_result "Invalid_Credentials" "FAIL" "Invalid credentials not rejected"
fi

echo ""

echo "🛡️ API PROTECTION TESTS"
echo "======================="

# Test Unauthenticated API Access
echo "Testing unauthenticated API access..."
unauth_api=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses -o /tmp/unauth_api.json)
if [ "$unauth_api" = "401" ]; then
    test_result "API_Protection" "PASS" "API properly requires authentication"
else
    test_result "API_Protection" "FAIL" "API allows unauthenticated access"
fi

# Test Authenticated API Access
if [ "$TAHA_SESSION_VALID" = true ]; then
    echo "Testing authenticated API access..."
    auth_api=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses \
        -b /tmp/taha_cookies.txt \
        -o /tmp/auth_api.json)
    
    if [ "$auth_api" = "200" ]; then
        test_result "Authenticated_API" "PASS" "API accessible with valid session"
    else
        test_result "Authenticated_API" "FAIL" "API not accessible with valid session"
    fi
else
    test_result "Authenticated_API" "SKIP" "Skipped - no valid session"
fi

echo ""

echo "🌐 APPLICATION TESTS"
echo "==================="

# Test Main Application
echo "Testing main application..."
main_response=$(curl -s -w "%{http_code}" https://$DOMAIN -o /tmp/main_app.html)
if [ "$main_response" = "200" ]; then
    test_result "Main_Application" "PASS" "Application loads successfully"
else
    test_result "Main_Application" "FAIL" "Application failed to load"
fi

# Test React App Loading
echo "Testing React app components..."
if grep -qi "react\|vite" /tmp/main_app.html; then
    test_result "React_App" "PASS" "React application detected"
else
    test_result "React_App" "FAIL" "React application not detected"
fi

# Test Authentication Components
if grep -qi "sign.*in\|login\|email\|password" /tmp/main_app.html; then
    test_result "Auth_Components" "PASS" "Authentication components detected"
else
    test_result "Auth_Components" "FAIL" "Authentication components not detected"
fi

echo ""

echo "⚡ PERFORMANCE TESTS"
echo "==================="

# Test Response Time
echo "Testing response times..."
times=()
for i in {1..3}; do
    time=$(curl -s -w "%{time_total}" https://$DOMAIN -o /dev/null)
    times+=($time)
done

# Calculate average
total=0
for time in "${times[@]}"; do
    total=$(echo "$total + $time" | bc -l 2>/dev/null || echo "0")
done
average=$(echo "scale=3; $total / ${#times[@]}" | bc -l 2>/dev/null || echo "0")

if (( $(echo "$average < 3.0" | bc -l 2>/dev/null || echo "0") )); then
    test_result "Response_Time" "PASS" "Average: ${average}s (< 3s)"
else
    test_result "Response_Time" "FAIL" "Average: ${average}s (> 3s)"
fi

echo ""

# Generate Final Report
echo "📋 VALIDATION REPORT"
echo "===================="
echo ""

# Calculate success rate
if [ $total_tests -gt 0 ]; then
    success_rate=$(echo "scale=1; $passed_tests * 100 / $total_tests" | bc -l 2>/dev/null || echo "0")
else
    success_rate="0"
fi

echo "📊 Test Summary:"
echo "   Total Tests: $total_tests"
echo "   Passed: $passed_tests"
echo "   Failed: $((total_tests - passed_tests))"
echo "   Success Rate: $success_rate%"
echo ""

# Determine overall status
if (( $(echo "$success_rate >= 90" | bc -l 2>/dev/null || echo "0") )); then
    overall_status="🟢 EXCELLENT"
elif (( $(echo "$success_rate >= 80" | bc -l 2>/dev/null || echo "0") )); then
    overall_status="🟡 GOOD"
elif (( $(echo "$success_rate >= 70" | bc -l 2>/dev/null || echo "0") )); then
    overall_status="🟠 FAIR"
else
    overall_status="🔴 NEEDS IMPROVEMENT"
fi

echo "🎯 Overall Status: $overall_status"
echo ""

echo "🌐 Application URLs:"
echo "   • Primary: https://$DOMAIN"
echo "   • API: https://$DOMAIN/api/"
echo "   • Health: https://$DOMAIN/api/health"
echo ""

echo "👥 User Credentials:"
echo "   • Taha: <EMAIL> / taha2024"
echo "   • Burak: <EMAIL> / burak2024"
echo ""

# Cleanup
rm -f /tmp/session_test.json /tmp/taha_auth.json /tmp/burak_auth.json
rm -f /tmp/invalid_auth.json /tmp/unauth_api.json /tmp/auth_api.json
rm -f /tmp/main_app.html /tmp/taha_cookies.txt /tmp/burak_cookies.txt

echo "🎉 VALIDATION COMPLETE!"
echo "======================"

# Exit with appropriate code
if (( $(echo "$success_rate >= 80" | bc -l 2>/dev/null || echo "0") )); then
    exit 0
else
    exit 1
fi
