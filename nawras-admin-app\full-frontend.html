<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Partner Expense Tracker</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); margin-bottom: 30px; text-align: center; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        .nav { display: flex; gap: 15px; margin-top: 25px; flex-wrap: wrap; justify-content: center; }
        .nav button { padding: 12px 24px; border: none; border-radius: 25px; background: rgba(255,255,255,0.2); color: white; cursor: pointer; font-weight: 500; transition: all 0.3s ease; backdrop-filter: blur(10px); }
        .nav button:hover { background: rgba(255,255,255,0.3); transform: translateY(-2px); }
        .nav button.active { background: rgba(255,255,255,0.4); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
        .card { background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); margin-bottom: 25px; border: 1px solid #e2e8f0; }
        .card h3 { color: #2d3748; margin-bottom: 20px; font-size: 1.3rem; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #4a5568; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 12px 16px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 1rem; transition: border-color 0.3s ease; }
        .form-group input:focus, .form-group select:focus { outline: none; border-color: #667eea; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1); }
        .btn { padding: 12px 24px; border: none; border-radius: 8px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; cursor: pointer; font-weight: 600; transition: all 0.3s ease; display: inline-block; text-decoration: none; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4); }
        .btn-secondary { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .btn-success { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .expense-item, .settlement-item { padding: 20px; border: 2px solid #e2e8f0; border-radius: 12px; margin-bottom: 15px; transition: all 0.3s ease; }
        .expense-item:hover, .settlement-item:hover { border-color: #667eea; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .amount { font-weight: bold; color: #38a169; font-size: 1.2rem; }
        .date { color: #718096; font-size: 0.9rem; margin-top: 5px; }
        .status { padding: 20px; text-align: center; border-radius: 8px; margin: 15px 0; }
        .success { color: #38a169; background: #f0fff4; border: 1px solid #9ae6b4; }
        .error { color: #e53e3e; background: #fed7d7; border: 1px solid #feb2b2; }
        .loading { color: #3182ce; background: #ebf8ff; border: 1px solid #90cdf4; }
        .hidden { display: none; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; }
        .stats-card { text-align: center; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; }
        .stats-number { font-size: 2.5rem; font-weight: bold; margin-bottom: 10px; }
        .stats-label { font-size: 1rem; opacity: 0.9; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        @media (max-width: 768px) { 
            .nav { flex-direction: column; } 
            .nav button { width: 100%; } 
            .form-row { grid-template-columns: 1fr; }
            .header h1 { font-size: 2rem; }
        }
        .loading-spinner { border: 3px solid #f3f3f3; border-top: 3px solid #667eea; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 20px auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .notification { position: fixed; top: 20px; right: 20px; padding: 15px 20px; border-radius: 8px; color: white; font-weight: 600; z-index: 1000; transform: translateX(400px); transition: transform 0.3s ease; }
        .notification.show { transform: translateX(0); }
        .notification.success { background: #38a169; }
        .notification.error { background: #e53e3e; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Nawras Admin</h1>
            <p>Partner Expense Tracking & Settlement Management System</p>
            <div class="nav">
                <button onclick="showSection('dashboard')" class="nav-btn active">📊 Dashboard</button>
                <button onclick="showSection('expenses')" class="nav-btn">💰 Expenses</button>
                <button onclick="showSection('settlements')" class="nav-btn">🤝 Settlements</button>
                <button onclick="showSection('add-expense')" class="nav-btn">➕ Add Expense</button>
                <button onclick="showSection('add-settlement')" class="nav-btn">💸 Add Settlement</button>
                <button onclick="showSection('reports')" class="nav-btn">📈 Reports</button>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard" class="section">
            <div class="grid">
                <div class="stats-card">
                    <div class="stats-number" id="total-expenses-count">-</div>
                    <div class="stats-label">Total Expenses</div>
                </div>
                <div class="stats-card">
                    <div class="stats-number" id="total-settlements-count">-</div>
                    <div class="stats-label">Total Settlements</div>
                </div>
                <div class="stats-card">
                    <div class="stats-number" id="api-status-indicator">-</div>
                    <div class="stats-label">API Status</div>
                </div>
            </div>
            
            <div class="card">
                <h3>📊 System Overview</h3>
                <div id="dashboard-details" class="status loading">
                    <div class="loading-spinner"></div>
                    Loading dashboard data...
                </div>
            </div>
        </div>

        <!-- Expenses Section -->
        <div id="expenses" class="section hidden">
            <div class="card">
                <h3>💰 Expense Management</h3>
                <div style="margin-bottom: 20px;">
                    <button onclick="loadExpenses()" class="btn">🔄 Refresh</button>
                    <button onclick="showSection('add-expense')" class="btn btn-secondary">➕ Add New</button>
                </div>
                <div id="expenses-list">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </div>

        <!-- Settlements Section -->
        <div id="settlements" class="section hidden">
            <div class="card">
                <h3>🤝 Settlement Management</h3>
                <div style="margin-bottom: 20px;">
                    <button onclick="loadSettlements()" class="btn">🔄 Refresh</button>
                    <button onclick="showSection('add-settlement')" class="btn btn-secondary">➕ Add New</button>
                </div>
                <div id="settlements-list">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </div>

        <!-- Add Expense Section -->
        <div id="add-expense" class="section hidden">
            <div class="card">
                <h3>➕ Add New Expense</h3>
                <form onsubmit="addExpense(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label>💵 Amount</label>
                            <input type="number" step="0.01" name="amount" required placeholder="0.00">
                        </div>
                        <div class="form-group">
                            <label>📝 Description</label>
                            <input type="text" name="description" required placeholder="What was this expense for?">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>🏷️ Category</label>
                            <select name="category" required>
                                <option value="">Select Category</option>
                                <option value="Food">🍽️ Food</option>
                                <option value="Groceries">🛒 Groceries</option>
                                <option value="Transportation">🚗 Transportation</option>
                                <option value="Utilities">⚡ Utilities</option>
                                <option value="Entertainment">🎬 Entertainment</option>
                                <option value="Healthcare">🏥 Healthcare</option>
                                <option value="Shopping">🛍️ Shopping</option>
                                <option value="Other">📦 Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>👤 Paid By</label>
                            <select name="paidById" required>
                                <option value="">Select Person</option>
                                <option value="taha">👨 Taha</option>
                                <option value="burak">👨 Burak</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>📅 Date</label>
                        <input type="date" name="date" required>
                    </div>
                    <button type="submit" class="btn btn-success">💾 Save Expense</button>
                </form>
                <div id="add-expense-status"></div>
            </div>
        </div>

        <!-- Add Settlement Section -->
        <div id="add-settlement" class="section hidden">
            <div class="card">
                <h3>🤝 Add New Settlement</h3>
                <form onsubmit="addSettlement(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label>💵 Amount</label>
                            <input type="number" step="0.01" name="amount" required placeholder="0.00">
                        </div>
                        <div class="form-group">
                            <label>📝 Description</label>
                            <input type="text" name="description" placeholder="Optional description">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>👤 Paid By</label>
                            <select name="paidBy" required>
                                <option value="">Select Person</option>
                                <option value="taha">👨 Taha</option>
                                <option value="burak">👨 Burak</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>👤 Paid To</label>
                            <select name="paidTo" required>
                                <option value="">Select Person</option>
                                <option value="taha">👨 Taha</option>
                                <option value="burak">👨 Burak</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>📅 Date</label>
                        <input type="date" name="date" required>
                    </div>
                    <button type="submit" class="btn btn-success">💾 Save Settlement</button>
                </form>
                <div id="add-settlement-status"></div>
            </div>
        </div>

        <!-- Reports Section -->
        <div id="reports" class="section hidden">
            <div class="card">
                <h3>📈 Financial Reports</h3>
                <div id="reports-content">
                    <div class="loading-spinner"></div>
                    Loading reports...
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notification" class="notification"></div>

    <script>
        const API_BASE = '/api';
        let currentData = { expenses: [], settlements: [] };

        // Utility Functions
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');
            setTimeout(() => notification.classList.remove('show'), 3000);
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', { 
                year: 'numeric', month: 'short', day: 'numeric' 
            });
        }

        // Navigation
        function showSection(sectionId) {
            document.querySelectorAll('.section').forEach(s => s.classList.add('hidden'));
            document.querySelectorAll('.nav-btn').forEach(b => b.classList.remove('active'));
            document.getElementById(sectionId).classList.remove('hidden');
            event.target.classList.add('active');
            
            // Load data for specific sections
            switch(sectionId) {
                case 'dashboard': loadDashboard(); break;
                case 'expenses': loadExpenses(); break;
                case 'settlements': loadSettlements(); break;
                case 'reports': loadReports(); break;
            }
        }

        // API Functions
        async function apiCall(endpoint, options = {}) {
            try {
                const response = await fetch(API_BASE + endpoint, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                showNotification(`API Error: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }

        // Dashboard Functions
        async function loadDashboard() {
            try {
                const [health, expenses, settlements] = await Promise.all([
                    apiCall('/health'),
                    apiCall('/expenses'),
                    apiCall('/settlements')
                ]);

                // Update stats cards
                document.getElementById('total-expenses-count').textContent = expenses.success ? expenses.total : '❌';
                document.getElementById('total-settlements-count').textContent = settlements.success ? settlements.total : '❌';
                document.getElementById('api-status-indicator').textContent = health.status === 'ok' ? '✅' : '❌';

                // Update dashboard details
                const detailsContainer = document.getElementById('dashboard-details');
                if (health.status === 'ok' && expenses.success && settlements.success) {
                    const totalExpenseAmount = expenses.data.reduce((sum, exp) => sum + exp.amount, 0);
                    const totalSettlementAmount = settlements.data.reduce((sum, set) => sum + set.amount, 0);
                    
                    detailsContainer.innerHTML = `
                        <div class="grid">
                            <div class="status success">
                                <h4>💰 Total Expenses</h4>
                                <p>${formatCurrency(totalExpenseAmount)} across ${expenses.total} transactions</p>
                            </div>
                            <div class="status success">
                                <h4>🤝 Total Settlements</h4>
                                <p>${formatCurrency(totalSettlementAmount)} across ${settlements.total} settlements</p>
                            </div>
                        </div>
                        <div class="status success" style="margin-top: 20px;">
                            <h4>🚀 System Status</h4>
                            <p>All systems operational • API Version: ${health.version} • Last updated: ${formatDate(health.timestamp)}</p>
                        </div>
                    `;
                } else {
                    detailsContainer.innerHTML = '<div class="status error">❌ Error loading dashboard data</div>';
                }

                currentData = { expenses: expenses.data || [], settlements: settlements.data || [] };
            } catch (error) {
                document.getElementById('dashboard-details').innerHTML = '<div class="status error">❌ Failed to load dashboard</div>';
            }
        }

        // Expense Functions
        async function loadExpenses() {
            const container = document.getElementById('expenses-list');
            container.innerHTML = '<div class="loading-spinner"></div>';
            
            const result = await apiCall('/expenses');
            
            if (result.success && result.data.length > 0) {
                container.innerHTML = result.data.map(expense => `
                    <div class="expense-item">
                        <div style="display: flex; justify-content: space-between; align-items: start;">
                            <div>
                                <h4>${expense.description}</h4>
                                <p><strong>Category:</strong> ${expense.category}</p>
                                <p><strong>Paid by:</strong> ${expense.paidById}</p>
                                <div class="date">📅 ${formatDate(expense.date)}</div>
                            </div>
                            <div class="amount">${formatCurrency(expense.amount)}</div>
                        </div>
                    </div>
                `).join('');
                currentData.expenses = result.data;
            } else if (result.success && result.data.length === 0) {
                container.innerHTML = '<div class="status">📝 No expenses found. Add your first expense!</div>';
            } else {
                container.innerHTML = '<div class="status error">❌ Error loading expenses</div>';
            }
        }

        // Settlement Functions
        async function loadSettlements() {
            const container = document.getElementById('settlements-list');
            container.innerHTML = '<div class="loading-spinner"></div>';
            
            const result = await apiCall('/settlements');
            
            if (result.success && result.data.length > 0) {
                container.innerHTML = result.data.map(settlement => `
                    <div class="settlement-item">
                        <div style="display: flex; justify-content: space-between; align-items: start;">
                            <div>
                                <h4>${settlement.description || 'Settlement Payment'}</h4>
                                <p><strong>From:</strong> ${settlement.paidBy} <strong>To:</strong> ${settlement.paidTo}</p>
                                <div class="date">📅 ${formatDate(settlement.date)}</div>
                            </div>
                            <div class="amount">${formatCurrency(settlement.amount)}</div>
                        </div>
                    </div>
                `).join('');
                currentData.settlements = result.data;
            } else if (result.success && result.data.length === 0) {
                container.innerHTML = '<div class="status">🤝 No settlements found. Add your first settlement!</div>';
            } else {
                container.innerHTML = '<div class="status error">❌ Error loading settlements</div>';
            }
        }

        // Add Expense
        async function addExpense(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData);
            
            const result = await apiCall('/expenses', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            const status = document.getElementById('add-expense-status');
            if (result.success) {
                status.innerHTML = '<div class="status success">✅ Expense added successfully!</div>';
                event.target.reset();
                showNotification('Expense added successfully!', 'success');
                // Set today's date again
                event.target.querySelector('input[name="date"]').value = new Date().toISOString().split('T')[0];
            } else {
                status.innerHTML = `<div class="status error">❌ Error: ${result.error}</div>`;
                showNotification('Failed to add expense', 'error');
            }
        }

        // Add Settlement
        async function addSettlement(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData);
            
            // Validation
            if (data.paidBy === data.paidTo) {
                const status = document.getElementById('add-settlement-status');
                status.innerHTML = '<div class="status error">❌ Error: Paid By and Paid To cannot be the same person</div>';
                showNotification('Paid By and Paid To cannot be the same person', 'error');
                return;
            }
            
            const result = await apiCall('/settlements', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            const status = document.getElementById('add-settlement-status');
            if (result.success) {
                status.innerHTML = '<div class="status success">✅ Settlement added successfully!</div>';
                event.target.reset();
                showNotification('Settlement added successfully!', 'success');
                // Set today's date again
                event.target.querySelector('input[name="date"]').value = new Date().toISOString().split('T')[0];
            } else {
                status.innerHTML = `<div class="status error">❌ Error: ${result.error}</div>`;
                showNotification('Failed to add settlement', 'error');
            }
        }

        // Reports
        async function loadReports() {
            const container = document.getElementById('reports-content');
            
            if (currentData.expenses.length === 0 && currentData.settlements.length === 0) {
                await Promise.all([loadExpenses(), loadSettlements()]);
            }
            
            const expensesByCategory = currentData.expenses.reduce((acc, exp) => {
                acc[exp.category] = (acc[exp.category] || 0) + exp.amount;
                return acc;
            }, {});
            
            const expensesByPerson = currentData.expenses.reduce((acc, exp) => {
                acc[exp.paidById] = (acc[exp.paidById] || 0) + exp.amount;
                return acc;
            }, {});
            
            container.innerHTML = `
                <div class="grid">
                    <div class="card">
                        <h4>📊 Expenses by Category</h4>
                        ${Object.entries(expensesByCategory).map(([category, amount]) => 
                            `<p><strong>${category}:</strong> ${formatCurrency(amount)}</p>`
                        ).join('') || '<p>No expense data available</p>'}
                    </div>
                    <div class="card">
                        <h4>👥 Expenses by Person</h4>
                        ${Object.entries(expensesByPerson).map(([person, amount]) => 
                            `<p><strong>${person}:</strong> ${formatCurrency(amount)}</p>`
                        ).join('') || '<p>No expense data available</p>'}
                    </div>
                </div>
            `;
        }

        // Initialize Application
        document.addEventListener('DOMContentLoaded', () => {
            // Set default dates to today
            document.querySelectorAll('input[type="date"]').forEach(input => {
                input.value = new Date().toISOString().split('T')[0];
            });
            
            // Load initial dashboard
            loadDashboard();
            
            // Show welcome notification
            setTimeout(() => {
                showNotification('Welcome to Nawras Admin! 🚀', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
