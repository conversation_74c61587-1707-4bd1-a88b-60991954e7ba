# Nawras Admin - Partner Expense Tracker

A modern, full-featured expense tracking and settlement management application built for partners to manage shared expenses efficiently.

## 🚀 Features

### Core Functionality
- **Expense Management**: Add, view, and track shared expenses with detailed categorization
- **Settlement Tracking**: Record payments between partners to settle shared costs
- **Real-time Balance Calculations**: Automatic calculation of who owes whom and by how much
- **Comprehensive History**: View all transactions with advanced filtering and search
- **Detailed Reports**: Analytics and insights with visual data representation

### User Experience
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Real-time Updates**: Live data synchronization with React Query
- **Professional UI**: Clean, modern interface with intuitive navigation
- **Settings Management**: Comprehensive user preferences and customization options
- **Error Handling**: Robust error boundaries and graceful fallbacks

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **TailwindCSS** for styling
- **React Query** for state management and data fetching
- **Wouter** for client-side routing
- **Lucide React** for icons

### Backend
- **Node.js** with Express
- **In-memory storage** for development
- **RESTful API** with proper error handling
- **CORS enabled** for cross-origin requests

## 📦 Installation

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Setup
1. Install dependencies: `npm install`
2. Start frontend: `npm run dev` (http://localhost:5173)
3. Start backend: `node server/index.js` (http://localhost:3001)

## 🎯 Usage

### Adding Expenses
1. Navigate to "Add Expense" from the sidebar
2. Fill in expense details (amount, description, category, who paid, date)
3. Submit to save the expense

### Recording Settlements
1. Go to "Settlement" page
2. Enter settlement details (amount, who is paying/receiving, description, date)
3. Submit to record the settlement

### Viewing History & Reports
- Access "History" to see all transactions with filtering and search
- View "Reports" for comprehensive analytics and visual data representation
- Customize preferences in "Settings"

## 🔧 API Endpoints

### Expenses
- `GET /api/expenses` - Get all expenses
- `POST /api/expenses` - Create new expense

### Settlements
- `GET /api/settlements` - Get all settlements
- `POST /api/settlements` - Create new settlement

## 📱 Mobile Support

Fully responsive design optimized for mobile devices with touch-friendly interface and mobile navigation drawer.

## 🔒 Security & Privacy

- Local data storage (no external data transmission)
- Settings encryption and backup
- Privacy-focused design with user controls

## 🚀 Production Deployment

### Build for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up --build

# Or build individual containers
docker build -t nawras-admin-frontend .
docker build -t nawras-admin-backend ./server
```

### Environment Configuration
- Copy `.env.example` to `.env.production`
- Update API URLs and configuration for production
- Set `VITE_API_BASE_URL` to your production API endpoint

### Performance Features
- **Code Splitting**: Automatic chunk splitting for optimal loading
- **Tree Shaking**: Unused code elimination
- **Minification**: Compressed JavaScript and CSS
- **Gzip Compression**: Nginx-level compression
- **Caching**: Optimized cache headers for static assets

### Security Features
- **CSP Headers**: Content Security Policy protection
- **XSS Protection**: Cross-site scripting prevention
- **CORS Configuration**: Proper cross-origin resource sharing
- **Secure Headers**: Complete security header implementation

---

**Built with ❤️ for efficient expense tracking and settlement management**


