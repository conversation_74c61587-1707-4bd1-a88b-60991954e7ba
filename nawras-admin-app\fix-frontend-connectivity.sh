#!/bin/bash

# Frontend Data Connectivity Fix Script
# Rebuilds and deploys frontend with correct API configuration

set -e

SERVER_IP="*************"
DOMAIN="partner.nawrasinchina.com"

echo "🔧 FRONTEND CONNECTIVITY FIX"
echo "============================"
echo "Server: $SERVER_IP"
echo "Domain: $DOMAIN"
echo ""

# Function to run commands on server
run_on_server() {
    ssh -o StrictHostKeyChecking=no root@$SERVER_IP "$1"
}

echo "📋 Step 1: Verify Current Environment"
echo "===================================="

# Check current environment configuration
echo "Current .env.production:"
cat .env.production

echo ""
echo "Current config.ts API URL:"
grep -n "apiBaseUrl" src/lib/config.ts || echo "Config file not found"

echo ""
echo "🔧 Step 2: Update Environment Configuration"
echo "==========================================="

# Create correct production environment
cat > .env.production << 'EOF'
# Production Environment Configuration - FIXED
VITE_API_BASE_URL=https://partner.nawrasinchina.com/api
VITE_APP_NAME=Nawras Admin
VITE_APP_VERSION=1.0.0
VITE_ENABLE_DEV_TOOLS=false
VITE_LOG_LEVEL=error
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false
VITE_ENABLE_CSP=true
VITE_SECURE_COOKIES=true
NODE_ENV=production
EOF

echo "✅ Updated .env.production with HTTPS API URL"

# Update config.ts to use environment variable properly
cat > src/lib/config.ts << 'EOF'
// Application configuration management - FIXED
export const config = {
  // API Configuration - Use HTTPS in production
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'https://partner.nawrasinchina.com/api',

  // App Information
  appName: import.meta.env.VITE_APP_NAME || 'Nawras Admin',
  appVersion: import.meta.env.VITE_APP_VERSION || '1.0.0',

  // Environment
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,

  // Feature Flags
  enableDevTools: import.meta.env.VITE_ENABLE_DEV_TOOLS === 'true',
  enableAnalytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
  enableErrorReporting: import.meta.env.VITE_ENABLE_ERROR_REPORTING === 'true',

  // Logging
  logLevel: import.meta.env.VITE_LOG_LEVEL || 'info',

  // Security
  enableCSP: import.meta.env.VITE_ENABLE_CSP === 'true',
  secureCookies: import.meta.env.VITE_SECURE_COOKIES === 'true',
} as const;

// Type-safe environment validation
export const validateConfig = () => {
  const requiredVars = ['VITE_API_BASE_URL'];
  const missing = requiredVars.filter(varName => !import.meta.env[varName]);

  if (missing.length > 0) {
    console.warn('Missing environment variables:', missing);
  }

  return missing.length === 0;
};

// Logger utility based on log level
export const logger = {
  debug: (...args: any[]) => {
    if (config.logLevel === 'debug' || config.isDevelopment) {
      console.debug('[DEBUG]', ...args);
    }
  },
  info: (...args: any[]) => {
    if (['debug', 'info'].includes(config.logLevel)) {
      console.info('[INFO]', ...args);
    }
  },
  warn: (...args: any[]) => {
    if (['debug', 'info', 'warn'].includes(config.logLevel)) {
      console.warn('[WARN]', ...args);
    }
  },
  error: (...args: any[]) => {
    console.error('[ERROR]', ...args);
  },
};

// Initialize configuration validation
if (config.isDevelopment) {
  validateConfig();
  logger.info('Configuration loaded:', {
    apiBaseUrl: config.apiBaseUrl,
    appName: config.appName,
    appVersion: config.appVersion,
    environment: config.isDevelopment ? 'development' : 'production',
  });
}
EOF

echo "✅ Updated config.ts with proper API URL handling"

echo ""
echo "🏗️ Step 3: Rebuild Frontend"
echo "=========================="

# Clean and rebuild
echo "Cleaning previous build..."
rm -rf dist/
rm -rf node_modules/.vite/

echo "Installing dependencies..."
npm ci

echo "Building for production..."
NODE_ENV=production npm run build

# Verify build output
if [ -d "dist" ] && [ -f "dist/index.html" ]; then
    echo "✅ Frontend build successful"
    echo "Build size:"
    du -sh dist/
else
    echo "❌ Frontend build failed"
    exit 1
fi

echo ""
echo "📤 Step 4: Deploy Updated Frontend"
echo "================================="

# Upload new frontend build
echo "Uploading frontend build..."
rsync -avz --delete dist/ root@$SERVER_IP:/opt/nawras-admin/dist/

# Verify deployment
run_on_server "
cd /opt/nawras-admin

# Check if files were uploaded
if [ -f dist/index.html ]; then
    echo '✅ Frontend files uploaded successfully'
    echo 'Frontend files:'
    ls -la dist/
else
    echo '❌ Frontend upload failed'
    exit 1
fi

# Restart nginx to clear any caches
systemctl reload nginx
echo '✅ Nginx reloaded'
"

echo ""
echo "🧪 Step 5: Test Frontend Connectivity"
echo "===================================="

# Wait for deployment to settle
sleep 5

echo "Testing frontend loading..."
if curl -s https://$DOMAIN | grep -q "Nawras Admin"; then
    echo "✅ Frontend loads correctly"
else
    echo "❌ Frontend loading failed"
fi

echo "Testing API connectivity from frontend..."
# This will test if the frontend can reach the API
api_test=$(curl -s -w "%{http_code}" https://$DOMAIN/api/health -o /tmp/api_test.json)
if [ "$api_test" = "200" ]; then
    echo "✅ API accessible from frontend domain"
    cat /tmp/api_test.json
else
    echo "❌ API not accessible (HTTP $api_test)"
fi

echo ""
echo "🔧 Step 6: Browser Cache Clearing Instructions"
echo "=============================================="

echo "⚠️  IMPORTANT: Clear browser cache to see changes!"
echo ""
echo "For users to see the fixes:"
echo "1. Press Ctrl+Shift+R (or Cmd+Shift+R on Mac) to hard refresh"
echo "2. Or clear browser cache completely"
echo "3. Or open in incognito/private browsing mode"
echo ""

echo "🎉 FRONTEND CONNECTIVITY FIX COMPLETED!"
echo "======================================="
echo "✅ Environment configuration updated"
echo "✅ Frontend rebuilt with HTTPS API URLs"
echo "✅ New build deployed to server"
echo "✅ Nginx reloaded"
echo ""
echo "🔗 Test URL: https://$DOMAIN"
echo "📊 API URL: https://$DOMAIN/api/"
echo ""
echo "📞 Next Steps:"
echo "1. Clear browser cache and test application"
echo "2. Verify data loading in dashboard"
echo "3. Test authentication flow"
echo "4. Run comprehensive validation"
