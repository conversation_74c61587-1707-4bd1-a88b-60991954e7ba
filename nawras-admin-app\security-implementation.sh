#!/bin/bash

# Security Implementation Script for Nawras Admin
# This script implements HTTPS and security headers

set -e

SERVER_IP="*************"
DOMAIN="partner.nawrasinchina.com"

echo "🔒 SECURITY IMPLEMENTATION PLAN"
echo "==============================="
echo "Server: $SERVER_IP"
echo "Domain: $DOMAIN"
echo ""

# Function to run commands on server
run_on_server() {
    ssh -o StrictHostKeyChecking=no root@$SERVER_IP "$1"
}

# Step 1: Backup current configuration
echo "📋 Step 1: Backing up current configuration..."
run_on_server "
# Create backup directory
mkdir -p /opt/nawras-admin/backups/$(date +%Y%m%d_%H%M%S)

# Backup nginx config
cp /etc/nginx/nginx.conf /opt/nawras-admin/backups/$(date +%Y%m%d_%H%M%S)/nginx.conf.backup

# Backup current site files
cp -r /opt/nawras-admin/dist /opt/nawras-admin/backups/$(date +%Y%m%d_%H%M%S)/dist.backup 2>/dev/null || true

echo '✅ Configuration backed up'
"

# Step 2: Install Certbot if not present
echo "🔧 Step 2: Installing/updating Certbot..."
run_on_server "
# Update package list
apt-get update -y

# Install certbot and nginx plugin
apt-get install -y certbot python3-certbot-nginx

# Verify installation
certbot --version
echo '✅ Certbot installed/updated'
"

# Step 3: Test nginx configuration before changes
echo "🧪 Step 3: Testing current nginx configuration..."
run_on_server "
# Test current config
nginx -t

# Show current nginx status
systemctl status nginx --no-pager -l
echo '✅ Current nginx configuration valid'
"

# Step 4: Install SSL certificate
echo "🔐 Step 4: Installing SSL certificate..."
run_on_server "
# Stop nginx temporarily to avoid conflicts
systemctl stop nginx

# Install certificate using standalone mode first
certbot certonly --standalone -d $DOMAIN --non-interactive --agree-tos --email <EMAIL>

# Start nginx again
systemctl start nginx

# Now configure nginx with the certificate
certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email <EMAIL> --redirect

echo '✅ SSL certificate installed and configured'
"

# Step 5: Add security headers to nginx configuration
echo "🛡️ Step 5: Adding security headers..."

# Create updated nginx config with security headers
cat > /tmp/nginx_security.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;
    
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 10M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Redirect HTTP to HTTPS
    server {
        listen 80;
        server_name partner.nawrasinchina.com;
        return 301 https://$server_name$request_uri;
    }

    # Main HTTPS server block
    server {
        listen 443 ssl http2;
        server_name partner.nawrasinchina.com;
        root /usr/share/nginx/html;
        index index.html;

        # SSL configuration (managed by Certbot)
        ssl_certificate /etc/letsencrypt/live/partner.nawrasinchina.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/partner.nawrasinchina.com/privkey.pem;
        include /etc/letsencrypt/options-ssl-nginx.conf;
        ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Strict-Transport-Security "max-age=********; includeSubDomains; preload" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data: https:; connect-src 'self' https:; frame-ancestors 'self';" always;
        add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

        # Handle client-side routing
        location / {
            try_files $uri $uri/ /index.html;
        }

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Content-Type-Options "nosniff" always;
        }

        # API proxy
        location /api/ {
            proxy_pass http://127.0.0.1:3001;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Security headers for API
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-Frame-Options "DENY" always;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
            add_header X-Content-Type-Options "nosniff" always;
        }

        # Security.txt
        location /.well-known/security.txt {
            return 200 "Contact: <EMAIL>\nExpires: 2025-12-31T23:59:59.000Z\n";
            add_header Content-Type text/plain;
        }
    }

    # Fallback server for direct IP access (redirect to domain)
    server {
        listen 80 default_server;
        listen 443 ssl default_server;
        server_name _;
        
        # Use same SSL cert for IP access
        ssl_certificate /etc/letsencrypt/live/partner.nawrasinchina.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/partner.nawrasinchina.com/privkey.pem;
        
        return 301 https://partner.nawrasinchina.com$request_uri;
    }
}
EOF

# Upload the new nginx configuration
echo "📤 Uploading new nginx configuration..."
scp /tmp/nginx_security.conf root@$SERVER_IP:/etc/nginx/nginx.conf

# Step 6: Test and apply new configuration
echo "🧪 Step 6: Testing and applying new configuration..."
run_on_server "
# Test new configuration
nginx -t

# If test passes, reload nginx
if [ $? -eq 0 ]; then
    systemctl reload nginx
    echo '✅ New nginx configuration applied successfully'
else
    echo '❌ Nginx configuration test failed'
    exit 1
fi
"

# Step 7: Set up automatic certificate renewal
echo "🔄 Step 7: Setting up automatic certificate renewal..."
run_on_server "
# Add cron job for certificate renewal
echo '0 12 * * * /usr/bin/certbot renew --quiet' | crontab -

# Test renewal process
certbot renew --dry-run

echo '✅ Automatic certificate renewal configured'
"

# Step 8: Verify security implementation
echo "🔍 Step 8: Verifying security implementation..."

# Test HTTPS access
echo "Testing HTTPS access..."
if curl -s -I https://$DOMAIN | grep -q "HTTP/2 200"; then
    echo "✅ HTTPS access working"
else
    echo "❌ HTTPS access failed"
fi

# Test HTTP redirect
echo "Testing HTTP to HTTPS redirect..."
if curl -s -I http://$DOMAIN | grep -q "301"; then
    echo "✅ HTTP to HTTPS redirect working"
else
    echo "❌ HTTP redirect failed"
fi

# Test security headers
echo "Testing security headers..."
headers_response=$(curl -s -I https://$DOMAIN)

if echo "$headers_response" | grep -q "strict-transport-security"; then
    echo "✅ HSTS header present"
else
    echo "❌ HSTS header missing"
fi

if echo "$headers_response" | grep -q "x-xss-protection"; then
    echo "✅ XSS Protection header present"
else
    echo "❌ XSS Protection header missing"
fi

if echo "$headers_response" | grep -q "content-security-policy"; then
    echo "✅ CSP header present"
else
    echo "❌ CSP header missing"
fi

echo ""
echo "🎉 SECURITY IMPLEMENTATION COMPLETE!"
echo "=================================="
echo ""
echo "✅ SSL Certificate: Installed and configured"
echo "✅ HTTPS Redirect: HTTP traffic redirects to HTTPS"
echo "✅ Security Headers: All critical headers implemented"
echo "✅ Auto-renewal: Certificate will auto-renew"
echo ""
echo "🌐 Your secure website: https://$DOMAIN"
echo "🔒 SSL Labs test: https://www.ssllabs.com/ssltest/analyze.html?d=$DOMAIN"
echo ""

# Cleanup
rm -f /tmp/nginx_security.conf

echo "Security implementation completed successfully!"
