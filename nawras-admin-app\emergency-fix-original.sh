#!/bin/bash

# Emergency fix for original droplet
# Quick deployment of authenticated server

echo "🚨 EMERGENCY FIX - ORIGINAL DROPLET"
echo "==================================="
echo "Deploying authenticated server to 138.197.8.183..."

# Stop any existing processes
pkill -f node || echo "No node processes found"
pm2 stop all || echo "No PM2 processes found"
pm2 delete all || echo "No PM2 processes to delete"

# Ensure we're in the right directory
cd /opt/nawras-admin

# Install Node.js if not present
if ! command -v node &> /dev/null; then
    echo "Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
fi

# Install PM2 if not present
if ! command -v pm2 &> /dev/null; then
    echo "Installing PM2..."
    npm install -g pm2
fi

# Create package.json if missing
if [ ! -f "package.json" ]; then
    cat > package.json << 'EOF'
{
  "name": "nawras-admin",
  "version": "1.0.0",
  "main": "server-auth.js",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "cookie-parser": "^1.4.6"
  }
}
EOF
fi

# Install dependencies
npm install --production

# Create emergency authenticated server
cat > server-auth.js << 'EOF'
const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors({
  origin: ['http://partner.nawrasinchina.com', 'https://partner.nawrasinchina.com'],
  credentials: true
}));
app.use(express.json());
app.use(cookieParser());

// Session storage
const sessions = new Map();

// Mock users
const mockUsers = [
  { id: "taha", email: "<EMAIL>", name: "Taha", password: "taha2024" },
  { id: "burak", email: "<EMAIL>", name: "Burak", password: "burak2024" }
];

// Authentication middleware
const requireAuth = (req, res, next) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') || req.cookies?.session;
  
  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }
  
  const session = sessions.get(sessionToken);
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    res.clearCookie('session');
    return res.status(401).json({
      success: false,
      error: 'Session expired'
    });
  }
  
  req.user = session.user;
  next();
};

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    server: 'emergency-auth'
  });
});

// Authentication routes
app.post('/api/auth/sign-in', (req, res) => {
  const { email, password } = req.body;
  const user = mockUsers.find(u => u.email === email && u.password === password);
  
  if (!user) {
    return res.status(401).json({
      success: false,
      error: 'Invalid email or password'
    });
  }
  
  const sessionToken = `session_${Date.now()}_${Math.random()}`;
  const session = {
    id: sessionToken,
    userId: user.id,
    user: {
      id: user.id,
      email: user.email,
      name: user.name
    },
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    token: sessionToken
  };
  
  sessions.set(sessionToken, session);
  
  res.cookie('session', sessionToken, {
    httpOnly: true,
    secure: false,
    sameSite: 'lax',
    maxAge: 7 * 24 * 60 * 60 * 1000
  });
  
  res.json({
    success: true,
    data: {
      session,
      user: session.user
    }
  });
});

app.get('/api/auth/session', (req, res) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') || req.cookies?.session;
  
  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.json({ success: true, data: null });
  }
  
  const session = sessions.get(sessionToken);
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    res.clearCookie('session');
    return res.json({ success: true, data: null });
  }
  
  res.json({
    success: true,
    data: session
  });
});

app.post('/api/auth/sign-out', (req, res) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') || req.cookies?.session;
  if (sessionToken) {
    sessions.delete(sessionToken);
  }
  res.clearCookie('session');
  res.json({ success: true });
});

// Protected API routes
app.get('/api/expenses', requireAuth, (req, res) => {
  const expenses = [
    {id: 1, amount: 25.5, description: "Lunch at downtown cafe", category: "Food", paidById: "taha", date: "2024-01-15", createdAt: "2024-01-15T12:30:00Z"},
    {id: 2, amount: 120, description: "Grocery shopping", category: "Groceries", paidById: "burak", date: "2024-01-14", createdAt: "2024-01-14T18:45:00Z"},
    {id: 3, amount: 45.75, description: "Gas station fill-up", category: "Transportation", paidById: "taha", date: "2024-01-13", createdAt: "2024-01-13T09:15:00Z"},
    {id: 4, amount: 89.99, description: "Monthly internet bill", category: "Utilities", paidById: "burak", date: "2024-01-12", createdAt: "2024-01-12T14:20:00Z"},
    {id: 5, amount: 15.25, description: "Coffee and pastry", category: "Food", paidById: "taha", date: "2024-01-11", createdAt: "2024-01-11T08:30:00Z"}
  ];
  
  res.json({
    success: true,
    data: expenses,
    total: expenses.length,
    totalUnfiltered: 5,
    filters: {
      sortBy: "date",
      sortOrder: "desc"
    },
    timestamp: new Date().toISOString()
  });
});

app.get('/api/settlements', requireAuth, (req, res) => {
  const settlements = [
    {id: 1, amount: 30.00, paidBy: "taha", paidTo: "burak", description: "Settlement", date: "2024-01-17", createdAt: "2024-01-17T10:00:00Z"},
    {id: 2, amount: 25.75, paidBy: "burak", paidTo: "taha", description: "Utilities", date: "2024-01-18", createdAt: "2024-01-18T15:30:00Z"}
  ];
  
  res.json({
    success: true,
    data: settlements,
    total: settlements.length,
    timestamp: new Date().toISOString()
  });
});

// 404 handler for API routes
app.use('/api/*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'API endpoint not found' 
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚨 Emergency authenticated server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔐 Authentication enabled`);
  console.log(`👥 Users: <EMAIL>, <EMAIL>`);
});
EOF

# Start the emergency server
echo "Starting emergency authenticated server..."
nohup node server-auth.js > emergency-server.log 2>&1 &

# Wait for server to start
sleep 5

# Test the server
echo ""
echo "🧪 TESTING EMERGENCY SERVER"
echo "=========================="

# Test health endpoint
echo "Testing health endpoint..."
health_response=$(curl -s http://localhost:3001/api/health)
if echo "$health_response" | grep -q '"status":"ok"'; then
    echo "✅ Health check PASSED"
    echo "Response: $health_response"
else
    echo "❌ Health check FAILED"
    echo "Response: $health_response"
    echo "Server log:"
    tail -10 emergency-server.log
fi

# Test authentication requirement
echo ""
echo "Testing authentication requirement..."
auth_response=$(curl -s -w "%{http_code}" http://localhost:3001/api/expenses -o /tmp/auth_test.json)
if [ "$auth_response" = "401" ]; then
    echo "✅ Authentication requirement PASSED"
    echo "Response: $(cat /tmp/auth_test.json)"
else
    echo "❌ Authentication requirement FAILED (HTTP $auth_response)"
    echo "Response: $(cat /tmp/auth_test.json)"
fi

echo ""
echo "🎉 EMERGENCY FIX COMPLETED!"
echo "=========================="
echo ""
echo "📊 Status:"
echo "   • Emergency server: Running on port 3001"
echo "   • Authentication: Enabled"
echo "   • Frontend: Available at http://partner.nawrasinchina.com"
echo "   • API Health: http://partner.nawrasinchina.com/api/health"
echo ""
echo "👥 User Credentials:"
echo "   • Taha: <EMAIL> / taha2024"
echo "   • Burak: <EMAIL> / burak2024"
echo ""
echo "🔧 Next Steps:"
echo "   1. Test authentication in browser"
echo "   2. Verify all API endpoints work"
echo "   3. Monitor new droplet deployment"
echo "   4. Switch to new droplet when ready"

echo ""
echo "Emergency deployment completed! 🚨"
