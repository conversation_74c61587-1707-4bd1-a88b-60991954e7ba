var e=Object.defineProperty,t=(t,n,r)=>((t,n,r)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r)(t,"symbol"!=typeof n?n+"":n,r);import{j as n,u as r,a,b as l,Q as s,c as i}from"./query-BxshoY88.js";import{r as o,u as c,L as u,R as d,S as f,a as m}from"./router-DRdqKDkt.js";import{r as h,a as p}from"./vendor-CFHJfABC.js";import{L as g,U as x,E as b,a as y,C as v,b as w,H as j,P as N,d as k,e as S,f as C,S as E,c as T,T as P,R as D,X as F,g as z,h as L,i as M,j as A,A as O,k as B,F as R,l as _,D as I,m as $,M as U,n as W,o as H,p as q,q as Y,r as Q,s as V,t as K,u as G,v as X,B as J,w as Z,x as ee,y as te}from"./ui-_TY0ykpl.js";import{R as ne,L as re,C as ae,X as le,Y as se,T as ie,a as oe,b as ce,P as ue,c as de,d as fe,B as me,e as he,A as pe,f as ge,g as xe}from"./charts-DH_UELAb.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var be,ye,ve,we,je={exports:{}},Ne={},ke={exports:{}},Se={};function Ce(){return ye||(ye=1,ke.exports=(be||(be=1,function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,l=e[r];if(!(0<a(l,t)))break e;e[r]=t,e[n]=l,n=r}}function n(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,l=e.length,s=l>>>1;r<s;){var i=2*(r+1)-1,o=e[i],c=i+1,u=e[c];if(0>a(o,n))c<l&&0>a(u,o)?(e[r]=u,e[c]=n,r=c):(e[r]=o,e[i]=n,r=i);else{if(!(c<l&&0>a(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(e.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var l=performance;e.unstable_now=function(){return l.now()}}else{var s=Date,i=s.now();e.unstable_now=function(){return s.now()-i}}var o=[],c=[],u=1,d=null,f=3,m=!1,h=!1,p=!1,g=!1,x="function"==typeof setTimeout?setTimeout:null,b="function"==typeof clearTimeout?clearTimeout:null,y="undefined"!=typeof setImmediate?setImmediate:null;function v(e){for(var a=n(c);null!==a;){if(null===a.callback)r(c);else{if(!(a.startTime<=e))break;r(c),a.sortIndex=a.expirationTime,t(o,a)}a=n(c)}}function w(e){if(p=!1,v(e),!h)if(null!==n(o))h=!0,N||(N=!0,j());else{var t=n(c);null!==t&&F(w,t.startTime-e)}}var j,N=!1,k=-1,S=5,C=-1;function E(){return!(!g&&e.unstable_now()-C<S)}function T(){if(g=!1,N){var t=e.unstable_now();C=t;var a=!0;try{e:{h=!1,p&&(p=!1,b(k),k=-1),m=!0;var l=f;try{t:{for(v(t),d=n(o);null!==d&&!(d.expirationTime>t&&E());){var s=d.callback;if("function"==typeof s){d.callback=null,f=d.priorityLevel;var i=s(d.expirationTime<=t);if(t=e.unstable_now(),"function"==typeof i){d.callback=i,v(t),a=!0;break t}d===n(o)&&r(o),v(t)}else r(o);d=n(o)}if(null!==d)a=!0;else{var u=n(c);null!==u&&F(w,u.startTime-t),a=!1}}break e}finally{d=null,f=l,m=!1}a=void 0}}finally{a?j():N=!1}}}if("function"==typeof y)j=function(){y(T)};else if("undefined"!=typeof MessageChannel){var P=new MessageChannel,D=P.port2;P.port1.onmessage=T,j=function(){D.postMessage(null)}}else j=function(){x(T,0)};function F(t,n){k=x((function(){t(e.unstable_now())}),n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_forceFrameRate=function(e){0>e||125<e||(S=0<e?Math.floor(1e3/e):5)},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_requestPaint=function(){g=!0},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(r,a,l){var s=e.unstable_now();switch(l="object"==typeof l&&null!==l&&"number"==typeof(l=l.delay)&&0<l?s+l:s,r){case 1:var i=-1;break;case 2:i=250;break;case 5:i=**********;break;case 4:i=1e4;break;default:i=5e3}return r={id:u++,callback:a,priorityLevel:r,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>s?(r.sortIndex=l,t(c,r),null===n(o)&&r===n(c)&&(p?(b(k),k=-1):p=!0,F(w,l-s))):(r.sortIndex=i,t(o,r),h||m||(h=!0,N||(N=!0,j()))),r},e.unstable_shouldYield=E,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(Se)),Se)),ke.exports}
/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function Ee(){if(ve)return Ne;ve=1;var e=Ce(),t=h(),n=p();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function s(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function i(e){if(l(e)!==e)throw Error(r(188))}function o(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=o(e)))return t;e=e.sibling}return null}var c=Object.assign,u=Symbol.for("react.element"),d=Symbol.for("react.transitional.element"),f=Symbol.for("react.portal"),m=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),x=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),y=Symbol.for("react.consumer"),v=Symbol.for("react.context"),w=Symbol.for("react.forward_ref"),j=Symbol.for("react.suspense"),N=Symbol.for("react.suspense_list"),k=Symbol.for("react.memo"),S=Symbol.for("react.lazy"),C=Symbol.for("react.activity"),E=Symbol.for("react.memo_cache_sentinel"),T=Symbol.iterator;function P(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=T&&e[T]||e["@@iterator"])?e:null}var D=Symbol.for("react.client.reference");function F(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===D?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case m:return"Fragment";case x:return"Profiler";case g:return"StrictMode";case j:return"Suspense";case N:return"SuspenseList";case C:return"Activity"}if("object"==typeof e)switch(e.$$typeof){case f:return"Portal";case v:return(e.displayName||"Context")+".Provider";case y:return(e._context.displayName||"Context")+".Consumer";case w:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case k:return null!==(t=e.displayName||null)?t:F(e.type)||"Memo";case S:t=e._payload,e=e._init;try{return F(e(t))}catch(n){}}return null}var z=Array.isArray,L=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,M=n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,A={pending:!1,data:null,method:null,action:null},O=[],B=-1;function R(e){return{current:e}}function _(e){0>B||(e.current=O[B],O[B]=null,B--)}function I(e,t){B++,O[B]=e.current,e.current=t}var $=R(null),U=R(null),W=R(null),H=R(null);function q(e,t){switch(I(W,t),I(U,e),I($,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?sd(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=id(t=sd(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}_($),I($,e)}function Y(){_($),_(U),_(W)}function Q(e){null!==e.memoizedState&&I(H,e);var t=$.current,n=id(t,e.type);t!==n&&(I(U,e),I($,n))}function V(e){U.current===e&&(_($),_(U)),H.current===e&&(_(H),Gd._currentValue=A)}var K=Object.prototype.hasOwnProperty,G=e.unstable_scheduleCallback,X=e.unstable_cancelCallback,J=e.unstable_shouldYield,Z=e.unstable_requestPaint,ee=e.unstable_now,te=e.unstable_getCurrentPriorityLevel,ne=e.unstable_ImmediatePriority,re=e.unstable_UserBlockingPriority,ae=e.unstable_NormalPriority,le=e.unstable_LowPriority,se=e.unstable_IdlePriority,ie=e.log,oe=e.unstable_setDisableYieldValue,ce=null,ue=null;function de(e){if("function"==typeof ie&&oe(e),ue&&"function"==typeof ue.setStrictMode)try{ue.setStrictMode(ce,e)}catch(t){}}var fe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(me(e)/he|0)|0},me=Math.log,he=Math.LN2;var pe=256,ge=4194304;function xe(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,l=e.suspendedLanes,s=e.pingedLanes;e=e.warmLanes;var i=134217727&r;return 0!==i?0!==(r=i&~l)?a=xe(r):0!==(s&=i)?a=xe(s):n||0!==(n=i&~e)&&(a=xe(n)):0!==(i=r&~l)?a=xe(i):0!==s?a=xe(s):n||0!==(n=r&~e)&&(a=xe(n)),0===a?0:0!==t&&t!==a&&0===(t&l)&&((l=a&-a)>=(n=t&-t)||32===l&&4194048&n)?t:a}function ye(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function we(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function je(){var e=pe;return!(4194048&(pe<<=1))&&(pe=256),e}function ke(){var e=ge;return!(62914560&(ge<<=1))&&(ge=4194304),e}function Se(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ee(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Te(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-fe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Pe(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-fe(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function De(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Fe(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function ze(){var e=M.p;return 0!==e?e:void 0===(e=window.event)?32:ff(e.type)}var Le=Math.random().toString(36).slice(2),Me="__reactFiber$"+Le,Ae="__reactProps$"+Le,Oe="__reactContainer$"+Le,Be="__reactEvents$"+Le,Re="__reactListeners$"+Le,_e="__reactHandles$"+Le,Ie="__reactResources$"+Le,$e="__reactMarker$"+Le;function Ue(e){delete e[Me],delete e[Ae],delete e[Be],delete e[Re],delete e[_e]}function We(e){var t=e[Me];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Oe]||n[Me]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=wd(e);null!==e;){if(n=e[Me])return n;e=wd(e)}return t}n=(e=n).parentNode}return null}function He(e){if(e=e[Me]||e[Oe]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function qe(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(r(33))}function Ye(e){var t=e[Ie];return t||(t=e[Ie]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Qe(e){e[$e]=!0}var Ve=new Set,Ke={};function Ge(e,t){Xe(e,t),Xe(e+"Capture",t)}function Xe(e,t){for(Ke[e]=t,e=0;e<t.length;e++)Ve.add(t[e])}var Je,Ze,et=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),tt={},nt={};function rt(e,t,n){if(a=t,K.call(nt,a)||!K.call(tt,a)&&(et.test(a)?nt[a]=!0:(tt[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function at(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function lt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function st(e){if(void 0===Je)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Je=t&&t[1]||"",Ze=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Je+e+Ze}var it=!1;function ot(e,t){if(!e||it)return"";it=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(a){var r=a}Reflect.construct(e,[],n)}else{try{n.call()}catch(l){r=l}e.call(n.prototype)}}else{try{throw Error()}catch(s){r=s}(n=e())&&"function"==typeof n.catch&&n.catch((function(){}))}}catch(i){if(i&&r&&"string"==typeof i.stack)return[i.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var l=r.DetermineComponentFrameRoot(),s=l[0],i=l[1];if(s&&i){var o=s.split("\n"),c=i.split("\n");for(a=r=0;r<o.length&&!o[r].includes("DetermineComponentFrameRoot");)r++;for(;a<c.length&&!c[a].includes("DetermineComponentFrameRoot");)a++;if(r===o.length||a===c.length)for(r=o.length-1,a=c.length-1;1<=r&&0<=a&&o[r]!==c[a];)a--;for(;1<=r&&0<=a;r--,a--)if(o[r]!==c[a]){if(1!==r||1!==a)do{if(r--,0>--a||o[r]!==c[a]){var u="\n"+o[r].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=r&&0<=a);break}}}finally{it=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?st(n):""}function ct(e){switch(e.tag){case 26:case 27:case 5:return st(e.type);case 16:return st("Lazy");case 13:return st("Suspense");case 19:return st("SuspenseList");case 0:case 15:return ot(e.type,!1);case 11:return ot(e.type.render,!1);case 1:return ot(e.type,!0);case 31:return st("Activity");default:return""}}function ut(e){try{var t="";do{t+=ct(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function dt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ft(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function mt(e){e._valueTracker||(e._valueTracker=function(e){var t=ft(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ht(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ft(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var gt=/[\n"\\]/g;function xt(e){return e.replace(gt,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function bt(e,t,n,r,a,l,s,i){e.name="",null!=s&&"function"!=typeof s&&"symbol"!=typeof s&&"boolean"!=typeof s?e.type=s:e.removeAttribute("type"),null!=t?"number"===s?(0===t&&""===e.value||e.value!=t)&&(e.value=""+dt(t)):e.value!==""+dt(t)&&(e.value=""+dt(t)):"submit"!==s&&"reset"!==s||e.removeAttribute("value"),null!=t?vt(e,s,dt(t)):null!=n?vt(e,s,dt(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=l&&(e.defaultChecked=!!l),null!=a&&(e.checked=a&&"function"!=typeof a&&"symbol"!=typeof a),null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i?e.name=""+dt(i):e.removeAttribute("name")}function yt(e,t,n,r,a,l,s,i){if(null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l&&(e.type=l),null!=t||null!=n){if(("submit"===l||"reset"===l)&&null==t)return;n=null!=n?""+dt(n):"",t=null!=t?""+dt(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}r="function"!=typeof(r=null!=r?r:a)&&"symbol"!=typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=s&&"function"!=typeof s&&"symbol"!=typeof s&&"boolean"!=typeof s&&(e.name=s)}function vt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function wt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+dt(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function jt(e,t,n){null==t||((t=""+dt(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+dt(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function Nt(e,t,n,a){if(null==t){if(null!=a){if(null!=n)throw Error(r(92));if(z(a)){if(1<a.length)throw Error(r(93));a=a[0]}n=a}null==n&&(n=""),t=n}n=dt(t),e.defaultValue=n,(a=e.textContent)===n&&""!==a&&null!==a&&(e.value=a)}function kt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ct(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!=typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Et(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(r(62));if(e=e.style,null!=n){for(var a in n)!n.hasOwnProperty(a)||null!=t&&t.hasOwnProperty(a)||(0===a.indexOf("--")?e.setProperty(a,""):"float"===a?e.cssFloat="":e[a]="");for(var l in t)a=t[l],t.hasOwnProperty(l)&&n[l]!==a&&Ct(e,l,a)}else for(var s in t)t.hasOwnProperty(s)&&Ct(e,s,t[s])}function Tt(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Dt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ft(e){return Dt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var zt=null;function Lt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Mt=null,At=null;function Ot(e){var t=He(e);if(t&&(e=t.stateNode)){var n=e[Ae]||null;e:switch(e=t.stateNode,t.type){case"input":if(bt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+xt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var l=a[Ae]||null;if(!l)throw Error(r(90));bt(a,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name)}}for(t=0;t<n.length;t++)(a=n[t]).form===e.form&&ht(a)}break e;case"textarea":jt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&wt(e,!!n.multiple,t,!1)}}}var Bt=!1;function Rt(e,t,n){if(Bt)return e(t,n);Bt=!0;try{return e(t)}finally{if(Bt=!1,(null!==Mt||null!==At)&&(Wc(),Mt&&(t=Mt,e=At,At=Mt=null,Ot(t),e)))for(t=0;t<e.length;t++)Ot(e[t])}}function _t(e,t){var n=e.stateNode;if(null===n)return null;var a=n[Ae]||null;if(null===a)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(a=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!a;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(r(231,t,typeof n));return n}var It=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),$t=!1;if(It)try{var Ut={};Object.defineProperty(Ut,"passive",{get:function(){$t=!0}}),window.addEventListener("test",Ut,Ut),window.removeEventListener("test",Ut,Ut)}catch(Of){$t=!1}var Wt=null,Ht=null,qt=null;function Yt(){if(qt)return qt;var e,t,n=Ht,r=n.length,a="value"in Wt?Wt.value:Wt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var s=r-e;for(t=1;t<=s&&n[r-t]===a[l-t];t++);return qt=a.slice(e,1<t?1-t:void 0)}function Qt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Vt(){return!0}function Kt(){return!1}function Gt(e){function t(t,n,r,a,l){for(var s in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(s)&&(t=e[s],this[s]=t?t(a):a[s]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Vt:Kt,this.isPropagationStopped=Kt,this}return c(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Vt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Vt)},persist:function(){},isPersistent:Vt}),t}var Xt,Jt,Zt,en={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},tn=Gt(en),nn=c({},en,{view:0,detail:0}),rn=Gt(nn),an=c({},nn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Zt&&(Zt&&"mousemove"===e.type?(Xt=e.screenX-Zt.screenX,Jt=e.screenY-Zt.screenY):Jt=Xt=0,Zt=e),Xt)},movementY:function(e){return"movementY"in e?e.movementY:Jt}}),ln=Gt(an),sn=Gt(c({},an,{dataTransfer:0})),on=Gt(c({},nn,{relatedTarget:0})),cn=Gt(c({},en,{animationName:0,elapsedTime:0,pseudoElement:0})),un=Gt(c({},en,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),dn=Gt(c({},en,{data:0})),fn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},mn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=hn[e])&&!!t[e]}function gn(){return pn}var xn=Gt(c({},nn,{key:function(e){if(e.key){var t=fn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Qt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?mn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gn,charCode:function(e){return"keypress"===e.type?Qt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Qt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),bn=Gt(c({},an,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),yn=Gt(c({},nn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gn})),vn=Gt(c({},en,{propertyName:0,elapsedTime:0,pseudoElement:0})),wn=Gt(c({},an,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),jn=Gt(c({},en,{newState:0,oldState:0})),Nn=[9,13,27,32],kn=It&&"CompositionEvent"in window,Sn=null;It&&"documentMode"in document&&(Sn=document.documentMode);var Cn=It&&"TextEvent"in window&&!Sn,En=It&&(!kn||Sn&&8<Sn&&11>=Sn),Tn=String.fromCharCode(32),Pn=!1;function Dn(e,t){switch(e){case"keyup":return-1!==Nn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Fn(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var zn=!1;var Ln={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Mn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Ln[e.type]:"textarea"===t}function An(e,t,n,r){Mt?At?At.push(r):At=[r]:Mt=r,0<(t=Yu(t,"onChange")).length&&(n=new tn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var On=null,Bn=null;function Rn(e){Ru(e,0)}function _n(e){if(ht(qe(e)))return e}function In(e,t){if("change"===e)return t}var $n=!1;if(It){var Un;if(It){var Wn="oninput"in document;if(!Wn){var Hn=document.createElement("div");Hn.setAttribute("oninput","return;"),Wn="function"==typeof Hn.oninput}Un=Wn}else Un=!1;$n=Un&&(!document.documentMode||9<document.documentMode)}function qn(){On&&(On.detachEvent("onpropertychange",Yn),Bn=On=null)}function Yn(e){if("value"===e.propertyName&&_n(Bn)){var t=[];An(t,Bn,e,Lt(e)),Rt(Rn,t)}}function Qn(e,t,n){"focusin"===e?(qn(),Bn=n,(On=t).attachEvent("onpropertychange",Yn)):"focusout"===e&&qn()}function Vn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return _n(Bn)}function Kn(e,t){if("click"===e)return _n(t)}function Gn(e,t){if("input"===e||"change"===e)return _n(t)}var Xn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function Jn(e,t){if(Xn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!K.call(t,a)||!Xn(e[a],t[a]))return!1}return!0}function Zn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function er(e,t){var n,r=Zn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Zn(r)}}function tr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?tr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function nr(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function rr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var ar=It&&"documentMode"in document&&11>=document.documentMode,lr=null,sr=null,ir=null,or=!1;function cr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;or||null==lr||lr!==pt(r)||("selectionStart"in(r=lr)&&rr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ir&&Jn(ir,r)||(ir=r,0<(r=Yu(sr,"onSelect")).length&&(t=new tn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=lr)))}function ur(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var dr={animationend:ur("Animation","AnimationEnd"),animationiteration:ur("Animation","AnimationIteration"),animationstart:ur("Animation","AnimationStart"),transitionrun:ur("Transition","TransitionRun"),transitionstart:ur("Transition","TransitionStart"),transitioncancel:ur("Transition","TransitionCancel"),transitionend:ur("Transition","TransitionEnd")},fr={},mr={};function hr(e){if(fr[e])return fr[e];if(!dr[e])return e;var t,n=dr[e];for(t in n)if(n.hasOwnProperty(t)&&t in mr)return fr[e]=n[t];return e}It&&(mr=document.createElement("div").style,"AnimationEvent"in window||(delete dr.animationend.animation,delete dr.animationiteration.animation,delete dr.animationstart.animation),"TransitionEvent"in window||delete dr.transitionend.transition);var pr=hr("animationend"),gr=hr("animationiteration"),xr=hr("animationstart"),br=hr("transitionrun"),yr=hr("transitionstart"),vr=hr("transitioncancel"),wr=hr("transitionend"),jr=new Map,Nr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function kr(e,t){jr.set(e,t),Ge(t,[e])}Nr.push("scrollEnd");var Sr=new WeakMap;function Cr(e,t){if("object"==typeof e&&null!==e){var n=Sr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:ut(t)},Sr.set(e,t),t)}return{value:e,source:t,stack:ut(t)}}var Er=[],Tr=0,Pr=0;function Dr(){for(var e=Tr,t=Pr=Tr=0;t<e;){var n=Er[t];Er[t++]=null;var r=Er[t];Er[t++]=null;var a=Er[t];Er[t++]=null;var l=Er[t];if(Er[t++]=null,null!==r&&null!==a){var s=r.pending;null===s?a.next=a:(a.next=s.next,s.next=a),r.pending=a}0!==l&&Mr(n,a,l)}}function Fr(e,t,n,r){Er[Tr++]=e,Er[Tr++]=t,Er[Tr++]=n,Er[Tr++]=r,Pr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function zr(e,t,n,r){return Fr(e,t,n,r),Ar(e)}function Lr(e,t){return Fr(e,null,null,t),Ar(e)}function Mr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,l=e.return;null!==l;)l.childLanes|=n,null!==(r=l.alternate)&&(r.childLanes|=n),22===l.tag&&(null===(e=l.stateNode)||1&e._visibility||(a=!0)),e=l,l=l.return;return 3===e.tag?(l=e.stateNode,a&&null!==t&&(a=31-fe(n),null===(r=(e=l.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),l):null}function Ar(e){if(50<Mc)throw Mc=0,Ac=null,Error(r(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Or={};function Br(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Rr(e,t,n,r){return new Br(e,t,n,r)}function _r(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ir(e,t){var n=e.alternate;return null===n?((n=Rr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function $r(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ur(e,t,n,a,l,s){var i=0;if(a=e,"function"==typeof e)_r(e)&&(i=1);else if("string"==typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,$.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case C:return(e=Rr(31,n,t,l)).elementType=C,e.lanes=s,e;case m:return Wr(n.children,l,s,t);case g:i=8,l|=24;break;case x:return(e=Rr(12,n,t,2|l)).elementType=x,e.lanes=s,e;case j:return(e=Rr(13,n,t,l)).elementType=j,e.lanes=s,e;case N:return(e=Rr(19,n,t,l)).elementType=N,e.lanes=s,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case b:case v:i=10;break e;case y:i=9;break e;case w:i=11;break e;case k:i=14;break e;case S:i=16,a=null;break e}i=29,n=Error(r(130,null===e?"null":typeof e,"")),a=null}return(t=Rr(i,n,t,l)).elementType=e,t.type=a,t.lanes=s,t}function Wr(e,t,n,r){return(e=Rr(7,e,r,t)).lanes=n,e}function Hr(e,t,n){return(e=Rr(6,e,null,t)).lanes=n,e}function qr(e,t,n){return(t=Rr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Yr=[],Qr=0,Vr=null,Kr=0,Gr=[],Xr=0,Jr=null,Zr=1,ea="";function ta(e,t){Yr[Qr++]=Kr,Yr[Qr++]=Vr,Vr=e,Kr=t}function na(e,t,n){Gr[Xr++]=Zr,Gr[Xr++]=ea,Gr[Xr++]=Jr,Jr=e;var r=Zr;e=ea;var a=32-fe(r)-1;r&=~(1<<a),n+=1;var l=32-fe(t)+a;if(30<l){var s=a-a%5;l=(r&(1<<s)-1).toString(32),r>>=s,a-=s,Zr=1<<32-fe(t)+a|n<<a|r,ea=l+e}else Zr=1<<l|n<<a|r,ea=e}function ra(e){null!==e.return&&(ta(e,1),na(e,1,0))}function aa(e){for(;e===Vr;)Vr=Yr[--Qr],Yr[Qr]=null,Kr=Yr[--Qr],Yr[Qr]=null;for(;e===Jr;)Jr=Gr[--Xr],Gr[Xr]=null,ea=Gr[--Xr],Gr[Xr]=null,Zr=Gr[--Xr],Gr[Xr]=null}var la=null,sa=null,ia=!1,oa=null,ca=!1,ua=Error(r(519));function da(e){throw xa(Cr(Error(r(418,"")),e)),ua}function fa(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Me]=e,t[Ae]=r,n){case"dialog":_u("cancel",t),_u("close",t);break;case"iframe":case"object":case"embed":_u("load",t);break;case"video":case"audio":for(n=0;n<Ou.length;n++)_u(Ou[n],t);break;case"source":_u("error",t);break;case"img":case"image":case"link":_u("error",t),_u("load",t);break;case"details":_u("toggle",t);break;case"input":_u("invalid",t),yt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),mt(t);break;case"select":_u("invalid",t);break;case"textarea":_u("invalid",t),Nt(t,r.value,r.defaultValue,r.children),mt(t)}"string"!=typeof(n=r.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Ju(t.textContent,n)?(null!=r.popover&&(_u("beforetoggle",t),_u("toggle",t)),null!=r.onScroll&&_u("scroll",t),null!=r.onScrollEnd&&_u("scrollend",t),null!=r.onClick&&(t.onclick=Zu),t=!0):t=!1,t||da(e)}function ma(e){for(la=e.return;la;)switch(la.tag){case 5:case 13:return void(ca=!1);case 27:case 3:return void(ca=!0);default:la=la.return}}function ha(e){if(e!==la)return!1;if(!ia)return ma(e),ia=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||od(e.type,e.memoizedProps)),t=!t),t&&sa&&da(e),ma(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(r(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){sa=yd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}sa=null}}else 27===n?(n=sa,pd(e.type)?(e=vd,vd=null,sa=e):sa=n):sa=la?yd(e.stateNode.nextSibling):null;return!0}function pa(){sa=la=null,ia=!1}function ga(){var e=oa;return null!==e&&(null===wc?wc=e:wc.push.apply(wc,e),oa=null),e}function xa(e){null===oa?oa=[e]:oa.push(e)}var ba=R(null),ya=null,va=null;function wa(e,t,n){I(ba,t._currentValue),t._currentValue=n}function ja(e){e._currentValue=ba.current,_(ba)}function Na(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ka(e,t,n,a){var l=e.child;for(null!==l&&(l.return=e);null!==l;){var s=l.dependencies;if(null!==s){var i=l.child;s=s.firstContext;e:for(;null!==s;){var o=s;s=l;for(var c=0;c<t.length;c++)if(o.context===t[c]){s.lanes|=n,null!==(o=s.alternate)&&(o.lanes|=n),Na(s.return,n,e),a||(i=null);break e}s=o.next}}else if(18===l.tag){if(null===(i=l.return))throw Error(r(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),Na(i,n,e),i=null}else i=l.child;if(null!==i)i.return=l;else for(i=l;null!==i;){if(i===e){i=null;break}if(null!==(l=i.sibling)){l.return=i.return,i=l;break}i=i.return}l=i}}function Sa(e,t,n,a){e=null;for(var l=t,s=!1;null!==l;){if(!s)if(524288&l.flags)s=!0;else if(262144&l.flags)break;if(10===l.tag){var i=l.alternate;if(null===i)throw Error(r(387));if(null!==(i=i.memoizedProps)){var o=l.type;Xn(l.pendingProps.value,i.value)||(null!==e?e.push(o):e=[o])}}else if(l===H.current){if(null===(i=l.alternate))throw Error(r(387));i.memoizedState.memoizedState!==l.memoizedState.memoizedState&&(null!==e?e.push(Gd):e=[Gd])}l=l.return}null!==e&&ka(t,e,n,a),t.flags|=262144}function Ca(e){for(e=e.firstContext;null!==e;){if(!Xn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ea(e){ya=e,va=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ta(e){return Da(ya,e)}function Pa(e,t){return null===ya&&Ea(e),Da(e,t)}function Da(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===va){if(null===e)throw Error(r(308));va=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else va=va.next=t;return n}var Fa="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},za=e.unstable_scheduleCallback,La=e.unstable_NormalPriority,Ma={$$typeof:v,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Aa(){return{controller:new Fa,data:new Map,refCount:0}}function Oa(e){e.refCount--,0===e.refCount&&za(La,(function(){e.controller.abort()}))}var Ba=null,Ra=0,_a=0,Ia=null;function $a(){if(0===--Ra&&null!==Ba){null!==Ia&&(Ia.status="fulfilled");var e=Ba;Ba=null,_a=0,Ia=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ua=L.S;L.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===Ba){var n=Ba=[];Ra=0,_a=Fu(),Ia={status:"pending",value:void 0,then:function(e){n.push(e)}}}Ra++,t.then($a,$a)}(0,t),null!==Ua&&Ua(e,t)};var Wa=R(null);function Ha(){var e=Wa.current;return null!==e?e:lc.pooledCache}function qa(e,t){I(Wa,null===t?Wa.current:t.pool)}function Ya(){var e=Ha();return null===e?null:{parent:Ma._currentValue,pool:e}}var Qa=Error(r(460)),Va=Error(r(474)),Ka=Error(r(542)),Ga={then:function(){}};function Xa(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ja(){}function Za(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Ja,Ja),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw nl(e=t.reason),e;default:if("string"==typeof t.status)t.then(Ja,Ja);else{if(null!==(e=lc)&&100<e.shellSuspendCounter)throw Error(r(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":throw nl(e=t.reason),e}throw el=t,Qa}}var el=null;function tl(){if(null===el)throw Error(r(459));var e=el;return el=null,e}function nl(e){if(e===Qa||e===Ka)throw Error(r(483))}var rl=!1;function al(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ll(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function sl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function il(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&ac){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Ar(e),Mr(e,null,n),t}return Fr(e,r,t,n),Ar(e)}function ol(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Pe(e,n)}}function cl(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var s={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===l?a=l=s:l=l.next=s,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var ul=!1;function dl(){if(ul){if(null!==Ia)throw Ia}}function fl(e,t,n,r){ul=!1;var a=e.updateQueue;rl=!1;var l=a.firstBaseUpdate,s=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var o=i,u=o.next;o.next=null,null===s?l=u:s.next=u,s=o;var d=e.alternate;null!==d&&((i=(d=d.updateQueue).lastBaseUpdate)!==s&&(null===i?d.firstBaseUpdate=u:i.next=u,d.lastBaseUpdate=o))}if(null!==l){var f=a.baseState;for(s=0,d=u=o=null,i=l;;){var m=-536870913&i.lane,h=m!==i.lane;if(h?(ic&m)===m:(r&m)===m){0!==m&&m===_a&&(ul=!0),null!==d&&(d=d.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var p=e,g=i;m=t;var x=n;switch(g.tag){case 1:if("function"==typeof(p=g.payload)){f=p.call(x,f,m);break e}f=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null==(m="function"==typeof(p=g.payload)?p.call(x,f,m):p))break e;f=c({},f,m);break e;case 2:rl=!0}}null!==(m=i.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=a.callbacks)?a.callbacks=[m]:h.push(m))}else h={lane:m,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===d?(u=d=h,o=f):d=d.next=h,s|=m;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(h=i).next,h.next=null,a.lastBaseUpdate=h,a.shared.pending=null}}null===d&&(o=f),a.baseState=o,a.firstBaseUpdate=u,a.lastBaseUpdate=d,null===l&&(a.shared.lanes=0),pc|=s,e.lanes=s,e.memoizedState=f}}function ml(e,t){if("function"!=typeof e)throw Error(r(191,e));e.call(t)}function hl(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)ml(n[e],t)}var pl=R(null),gl=R(0);function xl(e,t){I(gl,e=mc),I(pl,t),mc=e|t.baseLanes}function bl(){I(gl,mc),I(pl,pl.current)}function yl(){mc=gl.current,_(pl),_(gl)}var vl=0,wl=null,jl=null,Nl=null,kl=!1,Sl=!1,Cl=!1,El=0,Tl=0,Pl=null,Dl=0;function Fl(){throw Error(r(321))}function zl(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Xn(e[n],t[n]))return!1;return!0}function Ll(e,t,n,r,a,l){return vl=l,wl=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,L.H=null===e||null===e.memoizedState?Qs:Vs,Cl=!1,l=n(r,a),Cl=!1,Sl&&(l=Al(t,n,r,a)),Ml(e),l}function Ml(e){L.H=Ys;var t=null!==jl&&null!==jl.next;if(vl=0,Nl=jl=wl=null,kl=!1,Tl=0,Pl=null,t)throw Error(r(300));null===e||Ti||null!==(e=e.dependencies)&&Ca(e)&&(Ti=!0)}function Al(e,t,n,a){wl=e;var l=0;do{if(Sl&&(Pl=null),Tl=0,Sl=!1,25<=l)throw Error(r(301));if(l+=1,Nl=jl=null,null!=e.updateQueue){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,null!=s.memoCache&&(s.memoCache.index=0)}L.H=Ks,s=t(n,a)}while(Sl);return s}function Ol(){var e=L.H,t=e.useState()[0];return t="function"==typeof t.then?Ul(t):t,e=e.useState()[0],(null!==jl?jl.memoizedState:null)!==e&&(wl.flags|=1024),t}function Bl(){var e=0!==El;return El=0,e}function Rl(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function _l(e){if(kl){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}kl=!1}vl=0,Nl=jl=wl=null,Sl=!1,Tl=El=0,Pl=null}function Il(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Nl?wl.memoizedState=Nl=e:Nl=Nl.next=e,Nl}function $l(){if(null===jl){var e=wl.alternate;e=null!==e?e.memoizedState:null}else e=jl.next;var t=null===Nl?wl.memoizedState:Nl.next;if(null!==t)Nl=t,jl=e;else{if(null===e){if(null===wl.alternate)throw Error(r(467));throw Error(r(310))}e={memoizedState:(jl=e).memoizedState,baseState:jl.baseState,baseQueue:jl.baseQueue,queue:jl.queue,next:null},null===Nl?wl.memoizedState=Nl=e:Nl=Nl.next=e}return Nl}function Ul(e){var t=Tl;return Tl+=1,null===Pl&&(Pl=[]),e=Za(Pl,e,t),t=wl,null===(null===Nl?t.memoizedState:Nl.next)&&(t=t.alternate,L.H=null===t||null===t.memoizedState?Qs:Vs),e}function Wl(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return Ul(e);if(e.$$typeof===v)return Ta(e)}throw Error(r(438,String(e)))}function Hl(e){var t=null,n=wl.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=wl.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},wl.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=E;return t.index++,n}function ql(e,t){return"function"==typeof t?t(e):t}function Yl(e){return Ql($l(),jl,e)}function Ql(e,t,n){var a=e.queue;if(null===a)throw Error(r(311));a.lastRenderedReducer=n;var l=e.baseQueue,s=a.pending;if(null!==s){if(null!==l){var i=l.next;l.next=s.next,s.next=i}t.baseQueue=l=s,a.pending=null}if(s=e.baseState,null===l)e.memoizedState=s;else{var o=i=null,c=null,u=t=l.next,d=!1;do{var f=-536870913&u.lane;if(f!==u.lane?(ic&f)===f:(vl&f)===f){var m=u.revertLane;if(0===m)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),f===_a&&(d=!0);else{if((vl&m)===m){u=u.next,m===_a&&(d=!0);continue}f={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(o=c=f,i=s):c=c.next=f,wl.lanes|=m,pc|=m}f=u.action,Cl&&n(s,f),s=u.hasEagerState?u.eagerState:n(s,f)}else m={lane:f,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(o=c=m,i=s):c=c.next=m,wl.lanes|=f,pc|=f;u=u.next}while(null!==u&&u!==t);if(null===c?i=s:c.next=o,!Xn(s,e.memoizedState)&&(Ti=!0,d&&null!==(n=Ia)))throw n;e.memoizedState=s,e.baseState=i,e.baseQueue=c,a.lastRenderedState=s}return null===l&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Vl(e){var t=$l(),n=t.queue;if(null===n)throw Error(r(311));n.lastRenderedReducer=e;var a=n.dispatch,l=n.pending,s=t.memoizedState;if(null!==l){n.pending=null;var i=l=l.next;do{s=e(s,i.action),i=i.next}while(i!==l);Xn(s,t.memoizedState)||(Ti=!0),t.memoizedState=s,null===t.baseQueue&&(t.baseState=s),n.lastRenderedState=s}return[s,a]}function Kl(e,t,n){var a=wl,l=$l(),s=ia;if(s){if(void 0===n)throw Error(r(407));n=n()}else n=t();var i=!Xn((jl||l).memoizedState,n);if(i&&(l.memoizedState=n,Ti=!0),l=l.queue,bs(2048,8,Jl.bind(null,a,l,e),[e]),l.getSnapshot!==t||i||null!==Nl&&1&Nl.memoizedState.tag){if(a.flags|=2048,ps(9,{destroy:void 0,resource:void 0},Xl.bind(null,a,l,n,t),null),null===lc)throw Error(r(349));s||124&vl||Gl(a,t,n)}return n}function Gl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=wl.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},wl.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Xl(e,t,n,r){t.value=n,t.getSnapshot=r,Zl(t)&&es(e)}function Jl(e,t,n){return n((function(){Zl(t)&&es(e)}))}function Zl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Xn(e,n)}catch(r){return!0}}function es(e){var t=Lr(e,2);null!==t&&Rc(t,e,2)}function ts(e){var t=Il();if("function"==typeof e){var n=e;if(e=n(),Cl){de(!0);try{n()}finally{de(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ql,lastRenderedState:e},t}function ns(e,t,n,r){return e.baseState=n,Ql(e,jl,"function"==typeof r?r:ql)}function rs(e,t,n,a,l){if(Ws(e))throw Error(r(485));if(null!==(e=t.action)){var s={payload:l,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){s.listeners.push(e)}};null!==L.T?n(!0):s.isTransition=!1,a(s),null===(n=t.pending)?(s.next=t.pending=s,as(t,s)):(s.next=n.next,t.pending=n.next=s)}}function as(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var l=L.T,s={};L.T=s;try{var i=n(a,r),o=L.S;null!==o&&o(s,i),ls(e,t,i)}catch(c){is(e,t,c)}finally{L.T=l}}else try{ls(e,t,l=n(a,r))}catch(u){is(e,t,u)}}function ls(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then((function(n){ss(e,t,n)}),(function(n){return is(e,t,n)})):ss(e,t,n)}function ss(e,t,n){t.status="fulfilled",t.value=n,os(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,as(e,n)))}function is(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,os(t),t=t.next}while(t!==r)}e.action=null}function os(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function cs(e,t){return t}function us(e,t){if(ia){var n=lc.formState;if(null!==n){e:{var r=wl;if(ia){if(sa){t:{for(var a=sa,l=ca;8!==a.nodeType;){if(!l){a=null;break t}if(null===(a=yd(a.nextSibling))){a=null;break t}}a="F!"===(l=a.data)||"F"===l?a:null}if(a){sa=yd(a.nextSibling),r="F!"===a.data;break e}}da(r)}r=!1}r&&(t=n[0])}}return(n=Il()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:cs,lastRenderedState:t},n.queue=r,n=Is.bind(null,wl,r),r.dispatch=n,r=ts(!1),l=Us.bind(null,wl,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Il()).queue=a,n=rs.bind(null,wl,a,l,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function ds(e){return fs($l(),jl,e)}function fs(e,t,n){if(t=Ql(e,t,cs)[0],e=Yl(ql)[0],"object"==typeof t&&null!==t&&"function"==typeof t.then)try{var r=Ul(t)}catch(s){if(s===Qa)throw Ka;throw s}else r=t;var a=(t=$l()).queue,l=a.dispatch;return n!==t.memoizedState&&(wl.flags|=2048,ps(9,{destroy:void 0,resource:void 0},ms.bind(null,a,n),null)),[r,l,e]}function ms(e,t){e.action=t}function hs(e){var t=$l(),n=jl;if(null!==n)return fs(t,n,e);$l(),t=t.memoizedState;var r=(n=$l()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function ps(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=wl.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},wl.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function gs(){return $l().memoizedState}function xs(e,t,n,r){var a=Il();r=void 0===r?null:r,wl.flags|=e,a.memoizedState=ps(1|t,{destroy:void 0,resource:void 0},n,r)}function bs(e,t,n,r){var a=$l();r=void 0===r?null:r;var l=a.memoizedState.inst;null!==jl&&null!==r&&zl(r,jl.memoizedState.deps)?a.memoizedState=ps(t,l,n,r):(wl.flags|=e,a.memoizedState=ps(1|t,l,n,r))}function ys(e,t){xs(8390656,8,e,t)}function vs(e,t){bs(2048,8,e,t)}function ws(e,t){return bs(4,2,e,t)}function js(e,t){return bs(4,4,e,t)}function Ns(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function ks(e,t,n){n=null!=n?n.concat([e]):null,bs(4,4,Ns.bind(null,t,e),n)}function Ss(){}function Cs(e,t){var n=$l();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&zl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Es(e,t){var n=$l();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&zl(t,r[1]))return r[0];if(r=e(),Cl){de(!0);try{e()}finally{de(!1)}}return n.memoizedState=[r,t],r}function Ts(e,t,n){return void 0===n||1073741824&vl?e.memoizedState=t:(e.memoizedState=n,e=Bc(),wl.lanes|=e,pc|=e,n)}function Ps(e,t,n,r){return Xn(n,t)?n:null!==pl.current?(e=Ts(e,n,r),Xn(e,t)||(Ti=!0),e):42&vl?(e=Bc(),wl.lanes|=e,pc|=e,t):(Ti=!0,e.memoizedState=n)}function Ds(e,t,n,r,a){var l=M.p;M.p=0!==l&&8>l?l:8;var s,i,o,c=L.T,u={};L.T=u,Us(e,!1,t,n);try{var d=a(),f=L.S;if(null!==f&&f(u,d),null!==d&&"object"==typeof d&&"function"==typeof d.then)$s(e,t,(s=r,i=[],o={status:"pending",value:null,reason:null,then:function(e){i.push(e)}},d.then((function(){o.status="fulfilled",o.value=s;for(var e=0;e<i.length;e++)(0,i[e])(s)}),(function(e){for(o.status="rejected",o.reason=e,e=0;e<i.length;e++)(0,i[e])(void 0)})),o),Oc());else $s(e,t,r,Oc())}catch(m){$s(e,t,{then:function(){},status:"rejected",reason:m},Oc())}finally{M.p=l,L.T=c}}function Fs(){}function zs(e,t,n,a){if(5!==e.tag)throw Error(r(476));var l=Ls(e).queue;Ds(e,l,t,A,null===n?Fs:function(){return Ms(e),n(a)})}function Ls(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:A,baseState:A,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ql,lastRenderedState:A},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ql,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Ms(e){$s(e,Ls(e).next.queue,{},Oc())}function As(){return Ta(Gd)}function Os(){return $l().memoizedState}function Bs(){return $l().memoizedState}function Rs(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Oc(),r=il(t,e=sl(n),n);return null!==r&&(Rc(r,t,n),ol(r,t,n)),t={cache:Aa()},void(e.payload=t)}t=t.return}}function _s(e,t,n){var r=Oc();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ws(e)?Hs(t,n):null!==(n=zr(e,t,n,r))&&(Rc(n,e,r),qs(n,t,r))}function Is(e,t,n){$s(e,t,n,Oc())}function $s(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ws(e))Hs(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var s=t.lastRenderedState,i=l(s,n);if(a.hasEagerState=!0,a.eagerState=i,Xn(i,s))return Fr(e,t,a,0),null===lc&&Dr(),!1}catch(o){}if(null!==(n=zr(e,t,a,r)))return Rc(n,e,r),qs(n,t,r),!0}return!1}function Us(e,t,n,a){if(a={lane:2,revertLane:Fu(),action:a,hasEagerState:!1,eagerState:null,next:null},Ws(e)){if(t)throw Error(r(479))}else null!==(t=zr(e,n,a,2))&&Rc(t,e,2)}function Ws(e){var t=e.alternate;return e===wl||null!==t&&t===wl}function Hs(e,t){Sl=kl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function qs(e,t,n){if(4194048&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Pe(e,n)}}var Ys={readContext:Ta,use:Wl,useCallback:Fl,useContext:Fl,useEffect:Fl,useImperativeHandle:Fl,useLayoutEffect:Fl,useInsertionEffect:Fl,useMemo:Fl,useReducer:Fl,useRef:Fl,useState:Fl,useDebugValue:Fl,useDeferredValue:Fl,useTransition:Fl,useSyncExternalStore:Fl,useId:Fl,useHostTransitionStatus:Fl,useFormState:Fl,useActionState:Fl,useOptimistic:Fl,useMemoCache:Fl,useCacheRefresh:Fl},Qs={readContext:Ta,use:Wl,useCallback:function(e,t){return Il().memoizedState=[e,void 0===t?null:t],e},useContext:Ta,useEffect:ys,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,xs(4194308,4,Ns.bind(null,t,e),n)},useLayoutEffect:function(e,t){return xs(4194308,4,e,t)},useInsertionEffect:function(e,t){xs(4,2,e,t)},useMemo:function(e,t){var n=Il();t=void 0===t?null:t;var r=e();if(Cl){de(!0);try{e()}finally{de(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Il();if(void 0!==n){var a=n(t);if(Cl){de(!0);try{n(t)}finally{de(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=_s.bind(null,wl,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Il().memoizedState=e},useState:function(e){var t=(e=ts(e)).queue,n=Is.bind(null,wl,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Ss,useDeferredValue:function(e,t){return Ts(Il(),e,t)},useTransition:function(){var e=ts(!1);return e=Ds.bind(null,wl,e.queue,!0,!1),Il().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var a=wl,l=Il();if(ia){if(void 0===n)throw Error(r(407));n=n()}else{if(n=t(),null===lc)throw Error(r(349));124&ic||Gl(a,t,n)}l.memoizedState=n;var s={value:n,getSnapshot:t};return l.queue=s,ys(Jl.bind(null,a,s,e),[e]),a.flags|=2048,ps(9,{destroy:void 0,resource:void 0},Xl.bind(null,a,s,n,t),null),n},useId:function(){var e=Il(),t=lc.identifierPrefix;if(ia){var n=ea;t="«"+t+"R"+(n=(Zr&~(1<<32-fe(Zr)-1)).toString(32)+n),0<(n=El++)&&(t+="H"+n.toString(32)),t+="»"}else t="«"+t+"r"+(n=Dl++).toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:As,useFormState:us,useActionState:us,useOptimistic:function(e){var t=Il();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Us.bind(null,wl,!0,n),n.dispatch=t,[e,t]},useMemoCache:Hl,useCacheRefresh:function(){return Il().memoizedState=Rs.bind(null,wl)}},Vs={readContext:Ta,use:Wl,useCallback:Cs,useContext:Ta,useEffect:vs,useImperativeHandle:ks,useInsertionEffect:ws,useLayoutEffect:js,useMemo:Es,useReducer:Yl,useRef:gs,useState:function(){return Yl(ql)},useDebugValue:Ss,useDeferredValue:function(e,t){return Ps($l(),jl.memoizedState,e,t)},useTransition:function(){var e=Yl(ql)[0],t=$l().memoizedState;return["boolean"==typeof e?e:Ul(e),t]},useSyncExternalStore:Kl,useId:Os,useHostTransitionStatus:As,useFormState:ds,useActionState:ds,useOptimistic:function(e,t){return ns($l(),0,e,t)},useMemoCache:Hl,useCacheRefresh:Bs},Ks={readContext:Ta,use:Wl,useCallback:Cs,useContext:Ta,useEffect:vs,useImperativeHandle:ks,useInsertionEffect:ws,useLayoutEffect:js,useMemo:Es,useReducer:Vl,useRef:gs,useState:function(){return Vl(ql)},useDebugValue:Ss,useDeferredValue:function(e,t){var n=$l();return null===jl?Ts(n,e,t):Ps(n,jl.memoizedState,e,t)},useTransition:function(){var e=Vl(ql)[0],t=$l().memoizedState;return["boolean"==typeof e?e:Ul(e),t]},useSyncExternalStore:Kl,useId:Os,useHostTransitionStatus:As,useFormState:hs,useActionState:hs,useOptimistic:function(e,t){var n=$l();return null!==jl?ns(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Hl,useCacheRefresh:Bs},Gs=null,Xs=0;function Js(e){var t=Xs;return Xs+=1,null===Gs&&(Gs=[]),Za(Gs,e,t)}function Zs(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function ei(e,t){if(t.$$typeof===u)throw Error(r(525));throw e=Object.prototype.toString.call(t),Error(r(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ti(e){return(0,e._init)(e._payload)}function ni(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function a(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function l(e,t){return(e=Ir(e,t)).index=0,e.sibling=null,e}function s(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function o(e,t,n,r){return null===t||6!==t.tag?((t=Hr(n,e.mode,r)).return=e,t):((t=l(t,n)).return=e,t)}function c(e,t,n,r){var a=n.type;return a===m?h(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"==typeof a&&null!==a&&a.$$typeof===S&&ti(a)===t.type)?(Zs(t=l(t,n.props),n),t.return=e,t):(Zs(t=Ur(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=qr(n,e.mode,r)).return=e,t):((t=l(t,n.children||[])).return=e,t)}function h(e,t,n,r,a){return null===t||7!==t.tag?((t=Wr(n,e.mode,r,a)).return=e,t):((t=l(t,n)).return=e,t)}function p(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=Hr(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case d:return Zs(n=Ur(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case f:return(t=qr(t,e.mode,n)).return=e,t;case S:return p(e,t=(0,t._init)(t._payload),n)}if(z(t)||P(t))return(t=Wr(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return p(e,Js(t),n);if(t.$$typeof===v)return p(e,Pa(e,t),n);ei(e,t)}return null}function g(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==a?null:o(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case d:return n.key===a?c(e,t,n,r):null;case f:return n.key===a?u(e,t,n,r):null;case S:return g(e,t,n=(a=n._init)(n._payload),r)}if(z(n)||P(n))return null!==a?null:h(e,t,n,r,null);if("function"==typeof n.then)return g(e,t,Js(n),r);if(n.$$typeof===v)return g(e,t,Pa(e,n),r);ei(e,n)}return null}function x(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return o(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case d:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case f:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case S:return x(e,t,n,r=(0,r._init)(r._payload),a)}if(z(r)||P(r))return h(t,e=e.get(n)||null,r,a,null);if("function"==typeof r.then)return x(e,t,n,Js(r),a);if(r.$$typeof===v)return x(e,t,n,Pa(t,r),a);ei(t,r)}return null}function b(o,c,u,h){if("object"==typeof u&&null!==u&&u.type===m&&null===u.key&&(u=u.props.children),"object"==typeof u&&null!==u){switch(u.$$typeof){case d:e:{for(var y=u.key;null!==c;){if(c.key===y){if((y=u.type)===m){if(7===c.tag){n(o,c.sibling),(h=l(c,u.props.children)).return=o,o=h;break e}}else if(c.elementType===y||"object"==typeof y&&null!==y&&y.$$typeof===S&&ti(y)===c.type){n(o,c.sibling),Zs(h=l(c,u.props),u),h.return=o,o=h;break e}n(o,c);break}t(o,c),c=c.sibling}u.type===m?((h=Wr(u.props.children,o.mode,h,u.key)).return=o,o=h):(Zs(h=Ur(u.type,u.key,u.props,null,o.mode,h),u),h.return=o,o=h)}return i(o);case f:e:{for(y=u.key;null!==c;){if(c.key===y){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){n(o,c.sibling),(h=l(c,u.children||[])).return=o,o=h;break e}n(o,c);break}t(o,c),c=c.sibling}(h=qr(u,o.mode,h)).return=o,o=h}return i(o);case S:return b(o,c,u=(y=u._init)(u._payload),h)}if(z(u))return function(r,l,i,o){for(var c=null,u=null,d=l,f=l=0,m=null;null!==d&&f<i.length;f++){d.index>f?(m=d,d=null):m=d.sibling;var h=g(r,d,i[f],o);if(null===h){null===d&&(d=m);break}e&&d&&null===h.alternate&&t(r,d),l=s(h,l,f),null===u?c=h:u.sibling=h,u=h,d=m}if(f===i.length)return n(r,d),ia&&ta(r,f),c;if(null===d){for(;f<i.length;f++)null!==(d=p(r,i[f],o))&&(l=s(d,l,f),null===u?c=d:u.sibling=d,u=d);return ia&&ta(r,f),c}for(d=a(d);f<i.length;f++)null!==(m=x(d,r,f,i[f],o))&&(e&&null!==m.alternate&&d.delete(null===m.key?f:m.key),l=s(m,l,f),null===u?c=m:u.sibling=m,u=m);return e&&d.forEach((function(e){return t(r,e)})),ia&&ta(r,f),c}(o,c,u,h);if(P(u)){if("function"!=typeof(y=P(u)))throw Error(r(150));return function(l,i,o,c){if(null==o)throw Error(r(151));for(var u=null,d=null,f=i,m=i=0,h=null,b=o.next();null!==f&&!b.done;m++,b=o.next()){f.index>m?(h=f,f=null):h=f.sibling;var y=g(l,f,b.value,c);if(null===y){null===f&&(f=h);break}e&&f&&null===y.alternate&&t(l,f),i=s(y,i,m),null===d?u=y:d.sibling=y,d=y,f=h}if(b.done)return n(l,f),ia&&ta(l,m),u;if(null===f){for(;!b.done;m++,b=o.next())null!==(b=p(l,b.value,c))&&(i=s(b,i,m),null===d?u=b:d.sibling=b,d=b);return ia&&ta(l,m),u}for(f=a(f);!b.done;m++,b=o.next())null!==(b=x(f,l,m,b.value,c))&&(e&&null!==b.alternate&&f.delete(null===b.key?m:b.key),i=s(b,i,m),null===d?u=b:d.sibling=b,d=b);return e&&f.forEach((function(e){return t(l,e)})),ia&&ta(l,m),u}(o,c,u=y.call(u),h)}if("function"==typeof u.then)return b(o,c,Js(u),h);if(u.$$typeof===v)return b(o,c,Pa(o,u),h);ei(o,u)}return"string"==typeof u&&""!==u||"number"==typeof u||"bigint"==typeof u?(u=""+u,null!==c&&6===c.tag?(n(o,c.sibling),(h=l(c,u)).return=o,o=h):(n(o,c),(h=Hr(u,o.mode,h)).return=o,o=h),i(o)):n(o,c)}return function(e,t,n,r){try{Xs=0;var a=b(e,t,n,r);return Gs=null,a}catch(s){if(s===Qa||s===Ka)throw s;var l=Rr(29,s,null,e.mode);return l.lanes=r,l.return=e,l}}}var ri=ni(!0),ai=ni(!1),li=R(null),si=null;function ii(e){var t=e.alternate;I(di,1&di.current),I(li,e),null===si&&(null===t||null!==pl.current||null!==t.memoizedState)&&(si=e)}function oi(e){if(22===e.tag){if(I(di,di.current),I(li,e),null===si){var t=e.alternate;null!==t&&null!==t.memoizedState&&(si=e)}}else ci()}function ci(){I(di,di.current),I(li,li.current)}function ui(e){_(li),si===e&&(si=null),_(di)}var di=R(0);function fi(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||bd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function mi(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:c({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var hi={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Oc(),a=sl(r);a.payload=t,null!=n&&(a.callback=n),null!==(t=il(e,a,r))&&(Rc(t,e,r),ol(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Oc(),a=sl(r);a.tag=1,a.payload=t,null!=n&&(a.callback=n),null!==(t=il(e,a,r))&&(Rc(t,e,r),ol(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Oc(),r=sl(n);r.tag=2,null!=t&&(r.callback=t),null!==(t=il(e,r,n))&&(Rc(t,e,n),ol(t,e,n))}};function pi(e,t,n,r,a,l,s){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,s):!t.prototype||!t.prototype.isPureReactComponent||(!Jn(n,r)||!Jn(a,l))}function gi(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&hi.enqueueReplaceState(t,t.state,null)}function xi(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=c({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var bi="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e)};function yi(e){bi(e)}function vi(e){}function wi(e){bi(e)}function ji(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout((function(){throw n}))}}function Ni(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout((function(){throw r}))}}function ki(e,t,n){return(n=sl(n)).tag=3,n.payload={element:null},n.callback=function(){ji(e,t)},n}function Si(e){return(e=sl(e)).tag=3,e}function Ci(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"==typeof a){var l=r.value;e.payload=function(){return a(l)},e.callback=function(){Ni(t,n,r)}}var s=n.stateNode;null!==s&&"function"==typeof s.componentDidCatch&&(e.callback=function(){Ni(t,n,r),"function"!=typeof a&&(null===Cc?Cc=new Set([this]):Cc.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Ei=Error(r(461)),Ti=!1;function Pi(e,t,n,r){t.child=null===e?ai(t,null,n,r):ri(t,e.child,n,r)}function Di(e,t,n,r,a){n=n.render;var l=t.ref;if("ref"in r){var s={};for(var i in r)"ref"!==i&&(s[i]=r[i])}else s=r;return Ea(t),r=Ll(e,t,n,s,l,a),i=Bl(),null===e||Ti?(ia&&i&&ra(t),t.flags|=1,Pi(e,t,r,a),t.child):(Rl(e,t,a),Gi(e,t,a))}function Fi(e,t,n,r,a){if(null===e){var l=n.type;return"function"!=typeof l||_r(l)||void 0!==l.defaultProps||null!==n.compare?((e=Ur(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,zi(e,t,l,r,a))}if(l=e.child,!Xi(e,a)){var s=l.memoizedProps;if((n=null!==(n=n.compare)?n:Jn)(s,r)&&e.ref===t.ref)return Gi(e,t,a)}return t.flags|=1,(e=Ir(l,r)).ref=t.ref,e.return=t,t.child=e}function zi(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(Jn(l,r)&&e.ref===t.ref){if(Ti=!1,t.pendingProps=r=l,!Xi(e,a))return t.lanes=e.lanes,Gi(e,t,a);131072&e.flags&&(Ti=!0)}}return Oi(e,t,n,r,a)}function Li(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(128&t.flags){if(r=null!==l?l.baseLanes|n:n,null!==e){for(a=t.child=e.child,l=0;null!==a;)l=l|a.lanes|a.childLanes,a=a.sibling;t.childLanes=l&~r}else t.childLanes=0,t.child=null;return Mi(e,t,r,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,Mi(e,t,null!==l?l.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&qa(0,null!==l?l.cachePool:null),null!==l?xl(t,l):bl(),oi(t)}else null!==l?(qa(0,l.cachePool),xl(t,l),ci(),t.memoizedState=null):(null!==e&&qa(0,null),bl(),ci());return Pi(e,t,a,n),t.child}function Mi(e,t,n,r){var a=Ha();return a=null===a?null:{parent:Ma._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&qa(0,null),bl(),oi(t),null!==e&&Sa(e,t,r,!0),null}function Ai(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(r(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Oi(e,t,n,r,a){return Ea(t),n=Ll(e,t,n,r,void 0,a),r=Bl(),null===e||Ti?(ia&&r&&ra(t),t.flags|=1,Pi(e,t,n,a),t.child):(Rl(e,t,a),Gi(e,t,a))}function Bi(e,t,n,r,a,l){return Ea(t),t.updateQueue=null,n=Al(t,r,n,a),Ml(e),r=Bl(),null===e||Ti?(ia&&r&&ra(t),t.flags|=1,Pi(e,t,n,l),t.child):(Rl(e,t,l),Gi(e,t,l))}function Ri(e,t,n,r,a){if(Ea(t),null===t.stateNode){var l=Or,s=n.contextType;"object"==typeof s&&null!==s&&(l=Ta(s)),l=new n(r,l),t.memoizedState=null!==l.state&&void 0!==l.state?l.state:null,l.updater=hi,t.stateNode=l,l._reactInternals=t,(l=t.stateNode).props=r,l.state=t.memoizedState,l.refs={},al(t),s=n.contextType,l.context="object"==typeof s&&null!==s?Ta(s):Or,l.state=t.memoizedState,"function"==typeof(s=n.getDerivedStateFromProps)&&(mi(t,n,s,r),l.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof l.getSnapshotBeforeUpdate||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||(s=l.state,"function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount(),s!==l.state&&hi.enqueueReplaceState(l,l.state,null),fl(t,r,l,a),dl(),l.state=t.memoizedState),"function"==typeof l.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){l=t.stateNode;var i=t.memoizedProps,o=xi(n,i);l.props=o;var c=l.context,u=n.contextType;s=Or,"object"==typeof u&&null!==u&&(s=Ta(u));var d=n.getDerivedStateFromProps;u="function"==typeof d||"function"==typeof l.getSnapshotBeforeUpdate,i=t.pendingProps!==i,u||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(i||c!==s)&&gi(t,l,r,s),rl=!1;var f=t.memoizedState;l.state=f,fl(t,r,l,a),dl(),c=t.memoizedState,i||f!==c||rl?("function"==typeof d&&(mi(t,n,d,r),c=t.memoizedState),(o=rl||pi(t,n,o,r,f,c,s))?(u||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||("function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"==typeof l.componentDidMount&&(t.flags|=4194308)):("function"==typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),l.props=r,l.state=c,l.context=s,r=o):("function"==typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,ll(e,t),u=xi(n,s=t.memoizedProps),l.props=u,d=t.pendingProps,f=l.context,c=n.contextType,o=Or,"object"==typeof c&&null!==c&&(o=Ta(c)),(c="function"==typeof(i=n.getDerivedStateFromProps)||"function"==typeof l.getSnapshotBeforeUpdate)||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(s!==d||f!==o)&&gi(t,l,r,o),rl=!1,f=t.memoizedState,l.state=f,fl(t,r,l,a),dl();var m=t.memoizedState;s!==d||f!==m||rl||null!==e&&null!==e.dependencies&&Ca(e.dependencies)?("function"==typeof i&&(mi(t,n,i,r),m=t.memoizedState),(u=rl||pi(t,n,u,r,f,m,o)||null!==e&&null!==e.dependencies&&Ca(e.dependencies))?(c||"function"!=typeof l.UNSAFE_componentWillUpdate&&"function"!=typeof l.componentWillUpdate||("function"==typeof l.componentWillUpdate&&l.componentWillUpdate(r,m,o),"function"==typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,m,o)),"function"==typeof l.componentDidUpdate&&(t.flags|=4),"function"==typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof l.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),l.props=r,l.state=m,l.context=o,r=u):("function"!=typeof l.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return l=r,Ai(e,t),r=!!(128&t.flags),l||r?(l=t.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:l.render(),t.flags|=1,null!==e&&r?(t.child=ri(t,e.child,null,a),t.child=ri(t,null,n,a)):Pi(e,t,n,a),t.memoizedState=l.state,e=t.child):e=Gi(e,t,a),e}function _i(e,t,n,r){return pa(),t.flags|=256,Pi(e,t,n,r),t.child}var Ii={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function $i(e){return{baseLanes:e,cachePool:Ya()}}function Ui(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=bc),e}function Wi(e,t,n){var a,l=t.pendingProps,s=!1,i=!!(128&t.flags);if((a=i)||(a=(null===e||null!==e.memoizedState)&&!!(2&di.current)),a&&(s=!0,t.flags&=-129),a=!!(32&t.flags),t.flags&=-33,null===e){if(ia){if(s?ii(t):ci(),ia){var o,c=sa;if(o=c){e:{for(o=c,c=ca;8!==o.nodeType;){if(!c){c=null;break e}if(null===(o=yd(o.nextSibling))){c=null;break e}}c=o}null!==c?(t.memoizedState={dehydrated:c,treeContext:null!==Jr?{id:Zr,overflow:ea}:null,retryLane:536870912,hydrationErrors:null},(o=Rr(18,null,null,0)).stateNode=c,o.return=t,t.child=o,la=t,sa=null,o=!0):o=!1}o||da(t)}if(null!==(c=t.memoizedState)&&null!==(c=c.dehydrated))return bd(c)?t.lanes=32:t.lanes=536870912,null;ui(t)}return c=l.children,l=l.fallback,s?(ci(),c=qi({mode:"hidden",children:c},s=t.mode),l=Wr(l,s,n,null),c.return=t,l.return=t,c.sibling=l,t.child=c,(s=t.child).memoizedState=$i(n),s.childLanes=Ui(e,a,n),t.memoizedState=Ii,l):(ii(t),Hi(t,c))}if(null!==(o=e.memoizedState)&&null!==(c=o.dehydrated)){if(i)256&t.flags?(ii(t),t.flags&=-257,t=Yi(e,t,n)):null!==t.memoizedState?(ci(),t.child=e.child,t.flags|=128,t=null):(ci(),s=l.fallback,c=t.mode,l=qi({mode:"visible",children:l.children},c),(s=Wr(s,c,n,null)).flags|=2,l.return=t,s.return=t,l.sibling=s,t.child=l,ri(t,e.child,null,n),(l=t.child).memoizedState=$i(n),l.childLanes=Ui(e,a,n),t.memoizedState=Ii,t=s);else if(ii(t),bd(c)){if(a=c.nextSibling&&c.nextSibling.dataset)var u=a.dgst;a=u,(l=Error(r(419))).stack="",l.digest=a,xa({value:l,source:null,stack:null}),t=Yi(e,t,n)}else if(Ti||Sa(e,t,n,!1),a=0!==(n&e.childLanes),Ti||a){if(null!==(a=lc)&&(0!==(l=0!==((l=42&(l=n&-n)?1:De(l))&(a.suspendedLanes|n))?0:l)&&l!==o.retryLane))throw o.retryLane=l,Lr(e,l),Rc(a,e,l),Ei;"$?"===c.data||Kc(),t=Yi(e,t,n)}else"$?"===c.data?(t.flags|=192,t.child=e.child,t=null):(e=o.treeContext,sa=yd(c.nextSibling),la=t,ia=!0,oa=null,ca=!1,null!==e&&(Gr[Xr++]=Zr,Gr[Xr++]=ea,Gr[Xr++]=Jr,Zr=e.id,ea=e.overflow,Jr=t),(t=Hi(t,l.children)).flags|=4096);return t}return s?(ci(),s=l.fallback,c=t.mode,u=(o=e.child).sibling,(l=Ir(o,{mode:"hidden",children:l.children})).subtreeFlags=65011712&o.subtreeFlags,null!==u?s=Ir(u,s):(s=Wr(s,c,n,null)).flags|=2,s.return=t,l.return=t,l.sibling=s,t.child=l,l=s,s=t.child,null===(c=e.child.memoizedState)?c=$i(n):(null!==(o=c.cachePool)?(u=Ma._currentValue,o=o.parent!==u?{parent:u,pool:u}:o):o=Ya(),c={baseLanes:c.baseLanes|n,cachePool:o}),s.memoizedState=c,s.childLanes=Ui(e,a,n),t.memoizedState=Ii,l):(ii(t),e=(n=e.child).sibling,(n=Ir(n,{mode:"visible",children:l.children})).return=t,n.sibling=null,null!==e&&(null===(a=t.deletions)?(t.deletions=[e],t.flags|=16):a.push(e)),t.child=n,t.memoizedState=null,n)}function Hi(e,t){return(t=qi({mode:"visible",children:t},e.mode)).return=e,e.child=t}function qi(e,t){return(e=Rr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Yi(e,t,n){return ri(t,e.child,null,n),(e=Hi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Qi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Na(e.return,t,n)}function Vi(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Ki(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(Pi(e,t,r.children,n),2&(r=di.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Qi(e,n,t);else if(19===e.tag)Qi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(I(di,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===fi(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Vi(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===fi(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Vi(t,!0,n,null,l);break;case"together":Vi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Gi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),pc|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Sa(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(r(153));if(null!==t.child){for(n=Ir(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ir(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Xi(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Ca(e))}function Ji(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Ti=!0;else{if(!(Xi(e,n)||128&t.flags))return Ti=!1,function(e,t,n){switch(t.tag){case 3:q(t,t.stateNode.containerInfo),wa(0,Ma,e.memoizedState.cache),pa();break;case 27:case 5:Q(t);break;case 4:q(t,t.stateNode.containerInfo);break;case 10:wa(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(ii(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Wi(e,t,n):(ii(t),null!==(e=Gi(e,t,n))?e.sibling:null);ii(t);break;case 19:var a=!!(128&e.flags);if((r=0!==(n&t.childLanes))||(Sa(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Ki(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),I(di,di.current),r)break;return null;case 22:case 23:return t.lanes=0,Li(e,t,n);case 24:wa(0,Ma,e.memoizedState.cache)}return Gi(e,t,n)}(e,t,n);Ti=!!(131072&e.flags)}else Ti=!1,ia&&1048576&t.flags&&na(t,Kr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,l=a._init;if(a=l(a._payload),t.type=a,"function"!=typeof a){if(null!=a){if((l=a.$$typeof)===w){t.tag=11,t=Di(null,t,a,e,n);break e}if(l===k){t.tag=14,t=Fi(null,t,a,e,n);break e}}throw t=F(a)||a,Error(r(306,t,""))}_r(a)?(e=xi(a,e),t.tag=1,t=Ri(null,t,a,e,n)):(t.tag=0,t=Oi(null,t,a,e,n))}return t;case 0:return Oi(e,t,t.type,t.pendingProps,n);case 1:return Ri(e,t,a=t.type,l=xi(a,t.pendingProps),n);case 3:e:{if(q(t,t.stateNode.containerInfo),null===e)throw Error(r(387));a=t.pendingProps;var s=t.memoizedState;l=s.element,ll(e,t),fl(t,a,null,n);var i=t.memoizedState;if(a=i.cache,wa(0,Ma,a),a!==s.cache&&ka(t,[Ma],n,!0),dl(),a=i.element,s.isDehydrated){if(s={element:a,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=s,t.memoizedState=s,256&t.flags){t=_i(e,t,a,n);break e}if(a!==l){xa(l=Cr(Error(r(424)),t)),t=_i(e,t,a,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(sa=yd(e.firstChild),la=t,ia=!0,oa=null,ca=!0,n=ai(t,null,a,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pa(),a===l){t=Gi(e,t,n);break e}Pi(e,t,a,n)}t=t.child}return t;case 26:return Ai(e,t),null===e?(n=Dd(t.type,null,t.pendingProps,null))?t.memoizedState=n:ia||(n=t.type,e=t.pendingProps,(a=ld(W.current).createElement(n))[Me]=t,a[Ae]=e,nd(a,n,e),Qe(a),t.stateNode=a):t.memoizedState=Dd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Q(t),null===e&&ia&&(a=t.stateNode=jd(t.type,t.pendingProps,W.current),la=t,ca=!0,l=sa,pd(t.type)?(vd=l,sa=yd(a.firstChild)):sa=l),Pi(e,t,t.pendingProps.children,n),Ai(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&ia&&((l=a=sa)&&(null!==(a=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[$e])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(l=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(l!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((l=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&l&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var l=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===l)return e}if(null===(e=yd(e.nextSibling)))break}return null}(a,t.type,t.pendingProps,ca))?(t.stateNode=a,la=t,sa=yd(a.firstChild),ca=!1,l=!0):l=!1),l||da(t)),Q(t),l=t.type,s=t.pendingProps,i=null!==e?e.memoizedProps:null,a=s.children,od(l,s)?a=null:null!==i&&od(l,i)&&(t.flags|=32),null!==t.memoizedState&&(l=Ll(e,t,Ol,null,null,n),Gd._currentValue=l),Ai(e,t),Pi(e,t,a,n),t.child;case 6:return null===e&&ia&&((e=n=sa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yd(e.nextSibling)))return null}return e}(n,t.pendingProps,ca))?(t.stateNode=n,la=t,sa=null,e=!0):e=!1),e||da(t)),null;case 13:return Wi(e,t,n);case 4:return q(t,t.stateNode.containerInfo),a=t.pendingProps,null===e?t.child=ri(t,null,a,n):Pi(e,t,a,n),t.child;case 11:return Di(e,t,t.type,t.pendingProps,n);case 7:return Pi(e,t,t.pendingProps,n),t.child;case 8:case 12:return Pi(e,t,t.pendingProps.children,n),t.child;case 10:return a=t.pendingProps,wa(0,t.type,a.value),Pi(e,t,a.children,n),t.child;case 9:return l=t.type._context,a=t.pendingProps.children,Ea(t),a=a(l=Ta(l)),t.flags|=1,Pi(e,t,a,n),t.child;case 14:return Fi(e,t,t.type,t.pendingProps,n);case 15:return zi(e,t,t.type,t.pendingProps,n);case 19:return Ki(e,t,n);case 31:return a=t.pendingProps,n=t.mode,a={mode:a.mode,children:a.children},null===e?((n=qi(a,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Ir(e.child,a)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Li(e,t,n);case 24:return Ea(t),a=Ta(Ma),null===e?(null===(l=Ha())&&(l=lc,s=Aa(),l.pooledCache=s,s.refCount++,null!==s&&(l.pooledCacheLanes|=n),l=s),t.memoizedState={parent:a,cache:l},al(t),wa(0,Ma,l)):(0!==(e.lanes&n)&&(ll(e,t),fl(t,null,null,n),dl()),l=e.memoizedState,s=t.memoizedState,l.parent!==a?(l={parent:a,cache:a},t.memoizedState=l,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=l),wa(0,Ma,a)):(a=s.cache,wa(0,Ma,a),a!==l.cache&&ka(t,[Ma],n,!0))),Pi(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function Zi(e){e.flags|=4}function eo(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!Wd(t)){if(null!==(t=li.current)&&((4194048&ic)===ic?null!==si:(62914560&ic)!==ic&&!(536870912&ic)||t!==si))throw el=Ga,Va;e.flags|=8192}}function to(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?ke():536870912,e.lanes|=t,yc|=t)}function no(e,t){if(!ia)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ro(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ao(e,t,n){var a=t.pendingProps;switch(aa(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return ro(t),null;case 3:return n=t.stateNode,a=null,null!==e&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),ja(Ma),Y(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(ha(t)?Zi(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,ga())),ro(t),null;case 26:return n=t.memoizedState,null===e?(Zi(t),null!==n?(ro(t),eo(t,n)):(ro(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Zi(t),ro(t),eo(t,n)):(ro(t),t.flags&=-16777217):(e.memoizedProps!==a&&Zi(t),ro(t),t.flags&=-16777217),null;case 27:V(t),n=W.current;var l=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==a&&Zi(t);else{if(!a){if(null===t.stateNode)throw Error(r(166));return ro(t),null}e=$.current,ha(t)?fa(t):(e=jd(l,a,n),t.stateNode=e,Zi(t))}return ro(t),null;case 5:if(V(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==a&&Zi(t);else{if(!a){if(null===t.stateNode)throw Error(r(166));return ro(t),null}if(e=$.current,ha(t))fa(t);else{switch(l=ld(W.current),e){case 1:e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof a.is?l.createElement("select",{is:a.is}):l.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e="string"==typeof a.is?l.createElement(n,{is:a.is}):l.createElement(n)}}e[Me]=t,e[Ae]=a;e:for(l=t.child;null!==l;){if(5===l.tag||6===l.tag)e.appendChild(l.stateNode);else if(4!==l.tag&&27!==l.tag&&null!==l.child){l.child.return=l,l=l.child;continue}if(l===t)break e;for(;null===l.sibling;){if(null===l.return||l.return===t)break e;l=l.return}l.sibling.return=l.return,l=l.sibling}t.stateNode=e;e:switch(nd(e,n,a),n){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Zi(t)}}return ro(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==a&&Zi(t);else{if("string"!=typeof a&&null===t.stateNode)throw Error(r(166));if(e=W.current,ha(t)){if(e=t.stateNode,n=t.memoizedProps,a=null,null!==(l=la))switch(l.tag){case 27:case 5:a=l.memoizedProps}e[Me]=t,(e=!!(e.nodeValue===n||null!==a&&!0===a.suppressHydrationWarning||Ju(e.nodeValue,n)))||da(t)}else(e=ld(e).createTextNode(a))[Me]=t,t.stateNode=e}return ro(t),null;case 13:if(a=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(l=ha(t),null!==a&&null!==a.dehydrated){if(null===e){if(!l)throw Error(r(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(r(317));l[Me]=t}else pa(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ro(t),l=!1}else l=ga(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=l),l=!0;if(!l)return 256&t.flags?(ui(t),t):(ui(t),null)}if(ui(t),128&t.flags)return t.lanes=n,t;if(n=null!==a,e=null!==e&&null!==e.memoizedState,n){l=null,null!==(a=t.child).alternate&&null!==a.alternate.memoizedState&&null!==a.alternate.memoizedState.cachePool&&(l=a.alternate.memoizedState.cachePool.pool);var s=null;null!==a.memoizedState&&null!==a.memoizedState.cachePool&&(s=a.memoizedState.cachePool.pool),s!==l&&(a.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),to(t,t.updateQueue),ro(t),null;case 4:return Y(),null===e&&Uu(t.stateNode.containerInfo),ro(t),null;case 10:return ja(t.type),ro(t),null;case 19:if(_(di),null===(l=t.memoizedState))return ro(t),null;if(a=!!(128&t.flags),null===(s=l.rendering))if(a)no(l,!1);else{if(0!==hc||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(s=fi(e))){for(t.flags|=128,no(l,!1),e=s.updateQueue,t.updateQueue=e,to(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)$r(n,e),n=n.sibling;return I(di,1&di.current|2),t.child}e=e.sibling}null!==l.tail&&ee()>kc&&(t.flags|=128,a=!0,no(l,!1),t.lanes=4194304)}else{if(!a)if(null!==(e=fi(s))){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,to(t,e),no(l,!0),null===l.tail&&"hidden"===l.tailMode&&!s.alternate&&!ia)return ro(t),null}else 2*ee()-l.renderingStartTime>kc&&536870912!==n&&(t.flags|=128,a=!0,no(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(null!==(e=l.last)?e.sibling=s:t.child=s,l.last=s)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=ee(),t.sibling=null,e=di.current,I(di,a?1&e|2:1&e),t):(ro(t),null);case 22:case 23:return ui(t),yl(),a=null!==t.memoizedState,null!==e?null!==e.memoizedState!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?!!(536870912&n)&&!(128&t.flags)&&(ro(t),6&t.subtreeFlags&&(t.flags|=8192)):ro(t),null!==(n=t.updateQueue)&&to(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),a=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(a=t.memoizedState.cachePool.pool),a!==n&&(t.flags|=2048),null!==e&&_(Wa),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),ja(Ma),ro(t),null;case 25:case 30:return null}throw Error(r(156,t.tag))}function lo(e,t){switch(aa(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return ja(Ma),Y(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return V(t),null;case 13:if(ui(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(r(340));pa()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return _(di),null;case 4:return Y(),null;case 10:return ja(t.type),null;case 22:case 23:return ui(t),yl(),null!==e&&_(Wa),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return ja(Ma),null;default:return null}}function so(e,t){switch(aa(t),t.tag){case 3:ja(Ma),Y();break;case 26:case 27:case 5:V(t);break;case 4:Y();break;case 13:ui(t);break;case 19:_(di);break;case 10:ja(t.type);break;case 22:case 23:ui(t),yl(),null!==e&&_(Wa);break;case 24:ja(Ma)}}function io(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var l=n.create,s=n.inst;r=l(),s.destroy=r}n=n.next}while(n!==a)}}catch(i){fu(t,t.return,i)}}function oo(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var l=a.next;r=l;do{if((r.tag&e)===e){var s=r.inst,i=s.destroy;if(void 0!==i){s.destroy=void 0,a=t;var o=n,c=i;try{c()}catch(u){fu(a,o,u)}}}r=r.next}while(r!==l)}}catch(u){fu(t,t.return,u)}}function co(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{hl(t,n)}catch(r){fu(e,e.return,r)}}}function uo(e,t,n){n.props=xi(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){fu(e,t,r)}}function fo(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"==typeof n?e.refCleanup=n(r):n.current=r}}catch(a){fu(e,t,a)}}function mo(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(a){fu(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(l){fu(e,t,l)}else n.current=null}function ho(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){fu(e,e.return,a)}}function po(e,t,n){try{var a=e.stateNode;!function(e,t,n,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var l=null,s=null,i=null,o=null,c=null,u=null,d=null;for(h in n){var f=n[h];if(n.hasOwnProperty(h)&&null!=f)switch(h){case"checked":case"value":break;case"defaultValue":c=f;default:a.hasOwnProperty(h)||ed(e,t,h,null,a,f)}}for(var m in a){var h=a[m];if(f=n[m],a.hasOwnProperty(m)&&(null!=h||null!=f))switch(m){case"type":s=h;break;case"name":l=h;break;case"checked":u=h;break;case"defaultChecked":d=h;break;case"value":i=h;break;case"defaultValue":o=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(r(137,t));break;default:h!==f&&ed(e,t,m,h,a,f)}}return void bt(e,i,o,c,u,d,s,l);case"select":for(s in h=i=o=m=null,n)if(c=n[s],n.hasOwnProperty(s)&&null!=c)switch(s){case"value":break;case"multiple":h=c;default:a.hasOwnProperty(s)||ed(e,t,s,null,a,c)}for(l in a)if(s=a[l],c=n[l],a.hasOwnProperty(l)&&(null!=s||null!=c))switch(l){case"value":m=s;break;case"defaultValue":o=s;break;case"multiple":i=s;default:s!==c&&ed(e,t,l,s,a,c)}return t=o,n=i,a=h,void(null!=m?wt(e,!!n,m,!1):!!a!=!!n&&(null!=t?wt(e,!!n,t,!0):wt(e,!!n,n?[]:"",!1)));case"textarea":for(o in h=m=null,n)if(l=n[o],n.hasOwnProperty(o)&&null!=l&&!a.hasOwnProperty(o))switch(o){case"value":case"children":break;default:ed(e,t,o,null,a,l)}for(i in a)if(l=a[i],s=n[i],a.hasOwnProperty(i)&&(null!=l||null!=s))switch(i){case"value":m=l;break;case"defaultValue":h=l;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(r(91));break;default:l!==s&&ed(e,t,i,l,a,s)}return void jt(e,m,h);case"option":for(var p in n)if(m=n[p],n.hasOwnProperty(p)&&null!=m&&!a.hasOwnProperty(p))if("selected"===p)e.selected=!1;else ed(e,t,p,null,a,m);for(c in a)if(m=a[c],h=n[c],a.hasOwnProperty(c)&&m!==h&&(null!=m||null!=h))if("selected"===c)e.selected=m&&"function"!=typeof m&&"symbol"!=typeof m;else ed(e,t,c,m,a,h);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)m=n[g],n.hasOwnProperty(g)&&null!=m&&!a.hasOwnProperty(g)&&ed(e,t,g,null,a,m);for(u in a)if(m=a[u],h=n[u],a.hasOwnProperty(u)&&m!==h&&(null!=m||null!=h))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=m)throw Error(r(137,t));break;default:ed(e,t,u,m,a,h)}return;default:if(Tt(t)){for(var x in n)m=n[x],n.hasOwnProperty(x)&&void 0!==m&&!a.hasOwnProperty(x)&&td(e,t,x,void 0,a,m);for(d in a)m=a[d],h=n[d],!a.hasOwnProperty(d)||m===h||void 0===m&&void 0===h||td(e,t,d,m,a,h);return}}for(var b in n)m=n[b],n.hasOwnProperty(b)&&null!=m&&!a.hasOwnProperty(b)&&ed(e,t,b,null,a,m);for(f in a)m=a[f],h=n[f],!a.hasOwnProperty(f)||m===h||null==m&&null==h||ed(e,t,f,m,a,h)}(a,e.type,n,t),a[Ae]=t}catch(l){fu(e,e.return,l)}}function go(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pd(e.type)||4===e.tag}function xo(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||go(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function bo(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Zu));else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(bo(e,t,n),e=e.sibling;null!==e;)bo(e,t,n),e=e.sibling}function yo(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(yo(e,t,n),e=e.sibling;null!==e;)yo(e,t,n),e=e.sibling}function vo(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);nd(t,r,n),t[Me]=e,t[Ae]=n}catch(l){fu(e,e.return,l)}}var wo=!1,jo=!1,No=!1,ko="function"==typeof WeakSet?WeakSet:Set,So=null;function Co(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:_o(e,n),4&r&&io(5,n);break;case 1:if(_o(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(s){fu(n,n.return,s)}else{var a=xi(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(i){fu(n,n.return,i)}}64&r&&co(n),512&r&&fo(n,n.return);break;case 3:if(_o(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{hl(e,t)}catch(s){fu(n,n.return,s)}}break;case 27:null===t&&4&r&&vo(n);case 26:case 5:_o(e,n),null===t&&4&r&&ho(n),512&r&&fo(n,n.return);break;case 12:_o(e,n);break;case 13:_o(e,n),4&r&&zo(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=gu.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||wo)){t=null!==t&&null!==t.memoizedState||jo,a=wo;var l=jo;wo=r,(jo=t)&&!l?$o(e,n,!!(8772&n.subtreeFlags)):_o(e,n),wo=a,jo=l}break;case 30:break;default:_o(e,n)}}function Eo(e){var t=e.alternate;null!==t&&(e.alternate=null,Eo(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Ue(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var To=null,Po=!1;function Do(e,t,n){for(n=n.child;null!==n;)Fo(e,t,n),n=n.sibling}function Fo(e,t,n){if(ue&&"function"==typeof ue.onCommitFiberUnmount)try{ue.onCommitFiberUnmount(ce,n)}catch(l){}switch(n.tag){case 26:jo||mo(n,t),Do(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:jo||mo(n,t);var r=To,a=Po;pd(n.type)&&(To=n.stateNode,Po=!1),Do(e,t,n),Nd(n.stateNode),To=r,Po=a;break;case 5:jo||mo(n,t);case 6:if(r=To,a=Po,To=null,Do(e,t,n),Po=a,null!==(To=r))if(Po)try{(9===To.nodeType?To.body:"HTML"===To.nodeName?To.ownerDocument.body:To).removeChild(n.stateNode)}catch(s){fu(n,t,s)}else try{To.removeChild(n.stateNode)}catch(s){fu(n,t,s)}break;case 18:null!==To&&(Po?(gd(9===(e=To).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Df(e)):gd(To,n.stateNode));break;case 4:r=To,a=Po,To=n.stateNode.containerInfo,Po=!0,Do(e,t,n),To=r,Po=a;break;case 0:case 11:case 14:case 15:jo||oo(2,n,t),jo||oo(4,n,t),Do(e,t,n);break;case 1:jo||(mo(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount&&uo(n,t,r)),Do(e,t,n);break;case 21:Do(e,t,n);break;case 22:jo=(r=jo)||null!==n.memoizedState,Do(e,t,n),jo=r;break;default:Do(e,t,n)}}function zo(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Df(e)}catch(n){fu(t,t.return,n)}}function Lo(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new ko),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new ko),t;default:throw Error(r(435,e.tag))}}(e);t.forEach((function(t){var r=xu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function Mo(e,t){var n=t.deletions;if(null!==n)for(var a=0;a<n.length;a++){var l=n[a],s=e,i=t,o=i;e:for(;null!==o;){switch(o.tag){case 27:if(pd(o.type)){To=o.stateNode,Po=!1;break e}break;case 5:To=o.stateNode,Po=!1;break e;case 3:case 4:To=o.stateNode.containerInfo,Po=!0;break e}o=o.return}if(null===To)throw Error(r(160));Fo(s,i,l),To=null,Po=!1,null!==(s=l.alternate)&&(s.return=null),l.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Oo(t,e),t=t.sibling}var Ao=null;function Oo(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Mo(t,e),Bo(e),4&a&&(oo(3,e,e.return),io(3,e),oo(5,e,e.return));break;case 1:Mo(t,e),Bo(e),512&a&&(jo||null===n||mo(n,n.return)),64&a&&wo&&(null!==(e=e.updateQueue)&&(null!==(a=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?a:n.concat(a))));break;case 26:var l=Ao;if(Mo(t,e),Bo(e),512&a&&(jo||null===n||mo(n,n.return)),4&a){var s=null!==n?n.memoizedState:null;if(a=e.memoizedState,null===n)if(null===a)if(null===e.stateNode){e:{a=e.type,n=e.memoizedProps,l=l.ownerDocument||l;t:switch(a){case"title":(!(s=l.getElementsByTagName("title")[0])||s[$e]||s[Me]||"http://www.w3.org/2000/svg"===s.namespaceURI||s.hasAttribute("itemprop"))&&(s=l.createElement(a),l.head.insertBefore(s,l.querySelector("head > title"))),nd(s,a,n),s[Me]=e,Qe(s),a=s;break e;case"link":var i=$d("link","href",l).get(a+(n.href||""));if(i)for(var o=0;o<i.length;o++)if((s=i[o]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&s.getAttribute("rel")===(null==n.rel?null:n.rel)&&s.getAttribute("title")===(null==n.title?null:n.title)&&s.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(o,1);break t}nd(s=l.createElement(a),a,n),l.head.appendChild(s);break;case"meta":if(i=$d("meta","content",l).get(a+(n.content||"")))for(o=0;o<i.length;o++)if((s=i[o]).getAttribute("content")===(null==n.content?null:""+n.content)&&s.getAttribute("name")===(null==n.name?null:n.name)&&s.getAttribute("property")===(null==n.property?null:n.property)&&s.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&s.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(o,1);break t}nd(s=l.createElement(a),a,n),l.head.appendChild(s);break;default:throw Error(r(468,a))}s[Me]=e,Qe(s),a=s}e.stateNode=a}else Ud(l,e.type,e.stateNode);else e.stateNode=Od(l,a,e.memoizedProps);else s!==a?(null===s?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):s.count--,null===a?Ud(l,e.type,e.stateNode):Od(l,a,e.memoizedProps)):null===a&&null!==e.stateNode&&po(e,e.memoizedProps,n.memoizedProps)}break;case 27:Mo(t,e),Bo(e),512&a&&(jo||null===n||mo(n,n.return)),null!==n&&4&a&&po(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Mo(t,e),Bo(e),512&a&&(jo||null===n||mo(n,n.return)),32&e.flags){l=e.stateNode;try{kt(l,"")}catch(h){fu(e,e.return,h)}}4&a&&null!=e.stateNode&&po(e,l=e.memoizedProps,null!==n?n.memoizedProps:l),1024&a&&(No=!0);break;case 6:if(Mo(t,e),Bo(e),4&a){if(null===e.stateNode)throw Error(r(162));a=e.memoizedProps,n=e.stateNode;try{n.nodeValue=a}catch(h){fu(e,e.return,h)}}break;case 3:if(Id=null,l=Ao,Ao=Cd(t.containerInfo),Mo(t,e),Ao=l,Bo(e),4&a&&null!==n&&n.memoizedState.isDehydrated)try{Df(t.containerInfo)}catch(h){fu(e,e.return,h)}No&&(No=!1,Ro(e));break;case 4:a=Ao,Ao=Cd(e.stateNode.containerInfo),Mo(t,e),Bo(e),Ao=a;break;case 12:default:Mo(t,e),Bo(e);break;case 13:Mo(t,e),Bo(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(Nc=ee()),4&a&&(null!==(a=e.updateQueue)&&(e.updateQueue=null,Lo(e,a)));break;case 22:l=null!==e.memoizedState;var c=null!==n&&null!==n.memoizedState,u=wo,d=jo;if(wo=u||l,jo=d||c,Mo(t,e),jo=d,wo=u,Bo(e),8192&a)e:for(t=e.stateNode,t._visibility=l?-2&t._visibility:1|t._visibility,l&&(null===n||c||wo||jo||Io(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){c=n=t;try{if(s=c.stateNode,l)"function"==typeof(i=s.style).setProperty?i.setProperty("display","none","important"):i.display="none";else{o=c.stateNode;var f=c.memoizedProps.style,m=null!=f&&f.hasOwnProperty("display")?f.display:null;o.style.display=null==m||"boolean"==typeof m?"":(""+m).trim()}}catch(h){fu(c,c.return,h)}}}else if(6===t.tag){if(null===n){c=t;try{c.stateNode.nodeValue=l?"":c.memoizedProps}catch(h){fu(c,c.return,h)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&a&&(null!==(a=e.updateQueue)&&(null!==(n=a.retryQueue)&&(a.retryQueue=null,Lo(e,n))));break;case 19:Mo(t,e),Bo(e),4&a&&(null!==(a=e.updateQueue)&&(e.updateQueue=null,Lo(e,a)));case 30:case 21:}}function Bo(e){var t=e.flags;if(2&t){try{for(var n,a=e.return;null!==a;){if(go(a)){n=a;break}a=a.return}if(null==n)throw Error(r(160));switch(n.tag){case 27:var l=n.stateNode;yo(e,xo(e),l);break;case 5:var s=n.stateNode;32&n.flags&&(kt(s,""),n.flags&=-33),yo(e,xo(e),s);break;case 3:case 4:var i=n.stateNode.containerInfo;bo(e,xo(e),i);break;default:throw Error(r(161))}}catch(o){fu(e,e.return,o)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Ro(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Ro(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function _o(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Co(e,t.alternate,t),t=t.sibling}function Io(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:oo(4,t,t.return),Io(t);break;case 1:mo(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&uo(t,t.return,n),Io(t);break;case 27:Nd(t.stateNode);case 26:case 5:mo(t,t.return),Io(t);break;case 22:null===t.memoizedState&&Io(t);break;default:Io(t)}e=e.sibling}}function $o(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,l=t,s=l.flags;switch(l.tag){case 0:case 11:case 15:$o(a,l,n),io(4,l);break;case 1:if($o(a,l,n),"function"==typeof(a=(r=l).stateNode).componentDidMount)try{a.componentDidMount()}catch(c){fu(r,r.return,c)}if(null!==(a=(r=l).updateQueue)){var i=r.stateNode;try{var o=a.shared.hiddenCallbacks;if(null!==o)for(a.shared.hiddenCallbacks=null,a=0;a<o.length;a++)ml(o[a],i)}catch(c){fu(r,r.return,c)}}n&&64&s&&co(l),fo(l,l.return);break;case 27:vo(l);case 26:case 5:$o(a,l,n),n&&null===r&&4&s&&ho(l),fo(l,l.return);break;case 12:$o(a,l,n);break;case 13:$o(a,l,n),n&&4&s&&zo(a,l);break;case 22:null===l.memoizedState&&$o(a,l,n),fo(l,l.return);break;case 30:break;default:$o(a,l,n)}t=t.sibling}}function Uo(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Oa(n))}function Wo(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Oa(e))}function Ho(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)qo(e,t,n,r),t=t.sibling}function qo(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Ho(e,t,n,r),2048&a&&io(9,t);break;case 1:case 13:default:Ho(e,t,n,r);break;case 3:Ho(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Oa(e)));break;case 12:if(2048&a){Ho(e,t,n,r),e=t.stateNode;try{var l=t.memoizedProps,s=l.id,i=l.onPostCommit;"function"==typeof i&&i(s,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(o){fu(t,t.return,o)}}else Ho(e,t,n,r);break;case 23:break;case 22:l=t.stateNode,s=t.alternate,null!==t.memoizedState?2&l._visibility?Ho(e,t,n,r):Qo(e,t):2&l._visibility?Ho(e,t,n,r):(l._visibility|=2,Yo(e,t,n,r,!!(10256&t.subtreeFlags))),2048&a&&Uo(s,t);break;case 24:Ho(e,t,n,r),2048&a&&Wo(t.alternate,t)}}function Yo(e,t,n,r,a){for(a=a&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var l=e,s=t,i=n,o=r,c=s.flags;switch(s.tag){case 0:case 11:case 15:Yo(l,s,i,o,a),io(8,s);break;case 23:break;case 22:var u=s.stateNode;null!==s.memoizedState?2&u._visibility?Yo(l,s,i,o,a):Qo(l,s):(u._visibility|=2,Yo(l,s,i,o,a)),a&&2048&c&&Uo(s.alternate,s);break;case 24:Yo(l,s,i,o,a),a&&2048&c&&Wo(s.alternate,s);break;default:Yo(l,s,i,o,a)}t=t.sibling}}function Qo(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:Qo(n,r),2048&a&&Uo(r.alternate,r);break;case 24:Qo(n,r),2048&a&&Wo(r.alternate,r);break;default:Qo(n,r)}t=t.sibling}}var Vo=8192;function Ko(e){if(e.subtreeFlags&Vo)for(e=e.child;null!==e;)Go(e),e=e.sibling}function Go(e){switch(e.tag){case 26:Ko(e),e.flags&Vo&&null!==e.memoizedState&&function(e,t,n){if(null===Hd)throw Error(r(475));var a=Hd;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var l=Fd(n.href),s=e.querySelector(zd(l));if(s)return null!==(e=s._p)&&"object"==typeof e&&"function"==typeof e.then&&(a.count++,a=Yd.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=s,void Qe(s);s=e.ownerDocument||e,n=Ld(n),(l=kd.get(l))&&Rd(n,l),Qe(s=s.createElement("link"));var i=s;i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),nd(s,"link",n),t.instance=s}null===a.stylesheets&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(a.count++,t=Yd.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}(Ao,e.memoizedState,e.memoizedProps);break;case 5:default:Ko(e);break;case 3:case 4:var t=Ao;Ao=Cd(e.stateNode.containerInfo),Ko(e),Ao=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Vo,Vo=16777216,Ko(e),Vo=t):Ko(e))}}function Xo(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Jo(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];So=r,tc(r,e)}Xo(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Zo(e),e=e.sibling}function Zo(e){switch(e.tag){case 0:case 11:case 15:Jo(e),2048&e.flags&&oo(9,e,e.return);break;case 3:case 12:default:Jo(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,ec(e)):Jo(e)}}function ec(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];So=r,tc(r,e)}Xo(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:oo(8,t,t.return),ec(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,ec(t));break;default:ec(t)}e=e.sibling}}function tc(e,t){for(;null!==So;){var n=So;switch(n.tag){case 0:case 11:case 15:oo(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Oa(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,So=r;else e:for(n=e;null!==So;){var a=(r=So).sibling,l=r.return;if(Eo(r),r===n){So=null;break e}if(null!==a){a.return=l,So=a;break e}So=l}}}var nc={getCacheForType:function(e){var t=Ta(Ma),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},rc="function"==typeof WeakMap?WeakMap:Map,ac=0,lc=null,sc=null,ic=0,oc=0,cc=null,uc=!1,dc=!1,fc=!1,mc=0,hc=0,pc=0,gc=0,xc=0,bc=0,yc=0,vc=null,wc=null,jc=!1,Nc=0,kc=1/0,Sc=null,Cc=null,Ec=0,Tc=null,Pc=null,Dc=0,Fc=0,zc=null,Lc=null,Mc=0,Ac=null;function Oc(){if(2&ac&&0!==ic)return ic&-ic;if(null!==L.T){return 0!==_a?_a:Fu()}return ze()}function Bc(){0===bc&&(bc=536870912&ic&&!ia?536870912:je());var e=li.current;return null!==e&&(e.flags|=32),bc}function Rc(e,t,n){(e!==lc||2!==oc&&9!==oc)&&null===e.cancelPendingCommit||(qc(e,0),Uc(e,ic,bc,!1)),Ee(e,n),2&ac&&e===lc||(e===lc&&(!(2&ac)&&(gc|=n),4===hc&&Uc(e,ic,bc,!1)),ku(e))}function _c(e,t,n){if(6&ac)throw Error(r(327));for(var a=!n&&!(124&t)&&0===(t&e.expiredLanes)||ye(e,t),l=a?function(e,t){var n=ac;ac|=2;var a=Qc(),l=Vc();lc!==e||ic!==t?(Sc=null,kc=ee()+500,qc(e,t)):dc=ye(e,t);e:for(;;)try{if(0!==oc&&null!==sc){t=sc;var s=cc;t:switch(oc){case 1:oc=0,cc=null,tu(e,t,s,1);break;case 2:case 9:if(Xa(s)){oc=0,cc=null,eu(t);break}t=function(){2!==oc&&9!==oc||lc!==e||(oc=7),ku(e)},s.then(t,t);break e;case 3:oc=7;break e;case 4:oc=5;break e;case 7:Xa(s)?(oc=0,cc=null,eu(t)):(oc=0,cc=null,tu(e,t,s,7));break;case 5:var i=null;switch(sc.tag){case 26:i=sc.memoizedState;case 5:case 27:var o=sc;if(!i||Wd(i)){oc=0,cc=null;var c=o.sibling;if(null!==c)sc=c;else{var u=o.return;null!==u?(sc=u,nu(u)):sc=null}break t}}oc=0,cc=null,tu(e,t,s,5);break;case 6:oc=0,cc=null,tu(e,t,s,6);break;case 8:Hc(),hc=6;break e;default:throw Error(r(462))}}Jc();break}catch(d){Yc(e,d)}return va=ya=null,L.H=a,L.A=l,ac=n,null!==sc?0:(lc=null,ic=0,Dr(),hc)}(e,t):Gc(e,t,!0),s=a;;){if(0===l){dc&&!a&&Uc(e,t,0,!1);break}if(n=e.current.alternate,!s||$c(n)){if(2===l){if(s=t,e.errorRecoveryDisabledLanes&s)var i=0;else i=0!==(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var o=e;l=vc;var c=o.current.memoizedState.isDehydrated;if(c&&(qc(o,i).flags|=256),2!==(i=Gc(o,i,!1))){if(fc&&!c){o.errorRecoveryDisabledLanes|=s,gc|=s,l=4;break e}s=wc,wc=l,null!==s&&(null===wc?wc=s:wc.push.apply(wc,s))}l=i}if(s=!1,2!==l)continue}}if(1===l){qc(e,0),Uc(e,t,0,!0);break}e:{switch(a=e,s=l){case 0:case 1:throw Error(r(345));case 4:if((4194048&t)!==t)break;case 6:Uc(a,t,bc,!uc);break e;case 2:wc=null;break;case 3:case 5:break;default:throw Error(r(329))}if((62914560&t)===t&&10<(l=Nc+300-ee())){if(Uc(a,t,bc,!uc),0!==be(a,0,!0))break e;a.timeoutHandle=ud(Ic.bind(null,a,n,wc,Sc,jc,t,bc,gc,yc,uc,s,2,-0,0),l)}else Ic(a,n,wc,Sc,jc,t,bc,gc,yc,uc,s,0,-0,0)}break}l=Gc(e,t,!1),s=!1}ku(e)}function Ic(e,t,n,a,l,s,i,o,c,u,d,f,m,h){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||!(16785408&~f))&&(Hd={stylesheets:null,count:0,unsuspend:qd},Go(t),null!==(f=function(){if(null===Hd)throw Error(r(475));var e=Hd;return e.stylesheets&&0===e.count&&Vd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&Vd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(au.bind(null,e,t,s,n,a,l,i,o,c,d,1,m,h)),void Uc(e,s,i,!u);au(e,t,s,n,a,l,i,o,c)}function $c(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!Xn(l(),a))return!1}catch(s){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Uc(e,t,n,r){t&=~xc,t&=~gc,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var l=31-fe(a),s=1<<l;r[l]=-1,a&=~s}0!==n&&Te(e,n,t)}function Wc(){return!!(6&ac)||(Su(0),!1)}function Hc(){if(null!==sc){if(0===oc)var e=sc.return;else va=ya=null,_l(e=sc),Gs=null,Xs=0,e=sc;for(;null!==e;)so(e.alternate,e),e=e.return;sc=null}}function qc(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,dd(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Hc(),lc=e,sc=n=Ir(e.current,null),ic=t,oc=0,cc=null,uc=!1,dc=ye(e,t),fc=!1,yc=bc=xc=gc=pc=hc=0,wc=vc=null,jc=!1,8&t&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-fe(r),l=1<<a;t|=e[a],r&=~l}return mc=t,Dr(),n}function Yc(e,t){wl=null,L.H=Ys,t===Qa||t===Ka?(t=tl(),oc=3):t===Va?(t=tl(),oc=4):oc=t===Ei?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,cc=t,null===sc&&(hc=1,ji(e,Cr(t,e.current)))}function Qc(){var e=L.H;return L.H=Ys,null===e?Ys:e}function Vc(){var e=L.A;return L.A=nc,e}function Kc(){hc=4,uc||(4194048&ic)!==ic&&null!==li.current||(dc=!0),!(134217727&pc)&&!(134217727&gc)||null===lc||Uc(lc,ic,bc,!1)}function Gc(e,t,n){var r=ac;ac|=2;var a=Qc(),l=Vc();lc===e&&ic===t||(Sc=null,qc(e,t)),t=!1;var s=hc;e:for(;;)try{if(0!==oc&&null!==sc){var i=sc,o=cc;switch(oc){case 8:Hc(),s=6;break e;case 3:case 2:case 9:case 6:null===li.current&&(t=!0);var c=oc;if(oc=0,cc=null,tu(e,i,o,c),n&&dc){s=0;break e}break;default:c=oc,oc=0,cc=null,tu(e,i,o,c)}}Xc(),s=hc;break}catch(u){Yc(e,u)}return t&&e.shellSuspendCounter++,va=ya=null,ac=r,L.H=a,L.A=l,null===sc&&(lc=null,ic=0,Dr()),s}function Xc(){for(;null!==sc;)Zc(sc)}function Jc(){for(;null!==sc&&!J();)Zc(sc)}function Zc(e){var t=Ji(e.alternate,e,mc);e.memoizedProps=e.pendingProps,null===t?nu(e):sc=t}function eu(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Bi(n,t,t.pendingProps,t.type,void 0,ic);break;case 11:t=Bi(n,t,t.pendingProps,t.type.render,t.ref,ic);break;case 5:_l(t);default:so(n,t),t=Ji(n,t=sc=$r(t,mc),mc)}e.memoizedProps=e.pendingProps,null===t?nu(e):sc=t}function tu(e,t,n,a){va=ya=null,_l(t),Gs=null,Xs=0;var l=t.return;try{if(function(e,t,n,a,l){if(n.flags|=32768,null!==a&&"object"==typeof a&&"function"==typeof a.then){if(null!==(t=n.alternate)&&Sa(t,n,l,!0),null!==(n=li.current)){switch(n.tag){case 13:return null===si?Kc():null===n.alternate&&0===hc&&(hc=3),n.flags&=-257,n.flags|=65536,n.lanes=l,a===Ga?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([a]):t.add(a),mu(e,a,l)),!1;case 22:return n.flags|=65536,a===Ga?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([a]):n.add(a),mu(e,a,l)),!1}throw Error(r(435,n.tag))}return mu(e,a,l),Kc(),!1}if(ia)return null!==(t=li.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=l,a!==ua&&xa(Cr(e=Error(r(422),{cause:a}),n))):(a!==ua&&xa(Cr(t=Error(r(423),{cause:a}),n)),(e=e.current.alternate).flags|=65536,l&=-l,e.lanes|=l,a=Cr(a,n),cl(e,l=ki(e.stateNode,a,l)),4!==hc&&(hc=2)),!1;var s=Error(r(520),{cause:a});if(s=Cr(s,n),null===vc?vc=[s]:vc.push(s),4!==hc&&(hc=2),null===t)return!0;a=Cr(a,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=l&-l,n.lanes|=e,cl(n,e=ki(n.stateNode,a,e)),!1;case 1:if(t=n.type,s=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===s||"function"!=typeof s.componentDidCatch||null!==Cc&&Cc.has(s))))return n.flags|=65536,l&=-l,n.lanes|=l,Ci(l=Si(l),e,n,a),cl(n,l),!1}n=n.return}while(null!==n);return!1}(e,l,t,n,ic))return hc=1,ji(e,Cr(n,e.current)),void(sc=null)}catch(s){if(null!==l)throw sc=l,s;return hc=1,ji(e,Cr(n,e.current)),void(sc=null)}32768&t.flags?(ia||1===a?e=!0:dc||536870912&ic?e=!1:(uc=e=!0,(2===a||9===a||3===a||6===a)&&(null!==(a=li.current)&&13===a.tag&&(a.flags|=16384))),ru(t,e)):nu(t)}function nu(e){var t=e;do{if(32768&t.flags)return void ru(t,uc);e=t.return;var n=ao(t.alternate,t,mc);if(null!==n)return void(sc=n);if(null!==(t=t.sibling))return void(sc=t);sc=t=e}while(null!==t);0===hc&&(hc=5)}function ru(e,t){do{var n=lo(e.alternate,e);if(null!==n)return n.flags&=32767,void(sc=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(sc=e);sc=e=n}while(null!==e);hc=6,sc=null}function au(e,t,n,a,l,s,i,o,c){e.cancelPendingCommit=null;do{cu()}while(0!==Ec);if(6&ac)throw Error(r(327));if(null!==t){if(t===e.current)throw Error(r(177));if(s=t.lanes|t.childLanes,function(e,t,n,r,a,l){var s=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,o=e.expirationTimes,c=e.hiddenUpdates;for(n=s&~n;0<n;){var u=31-fe(n),d=1<<u;i[u]=0,o[u]=-1;var f=c[u];if(null!==f)for(c[u]=null,u=0;u<f.length;u++){var m=f[u];null!==m&&(m.lane&=-536870913)}n&=~d}0!==r&&Te(e,r,0),0!==l&&0===a&&0!==e.tag&&(e.suspendedLanes|=l&~(s&~t))}(e,n,s|=Pr,i,o,c),e===lc&&(sc=lc=null,ic=0),Pc=t,Tc=e,Dc=n,Fc=s,zc=l,Lc=a,10256&t.subtreeFlags||10256&t.flags?(e.callbackNode=null,e.callbackPriority=0,G(ae,(function(){return uu(),null}))):(e.callbackNode=null,e.callbackPriority=0),a=!!(13878&t.flags),13878&t.subtreeFlags||a){a=L.T,L.T=null,l=M.p,M.p=2,i=ac,ac|=4;try{!function(e,t){if(e=e.containerInfo,rd=af,rr(e=nr(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var a=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(a&&0!==a.rangeCount){n=a.anchorNode;var l=a.anchorOffset,s=a.focusNode;a=a.focusOffset;try{n.nodeType,s.nodeType}catch(g){n=null;break e}var i=0,o=-1,c=-1,u=0,d=0,f=e,m=null;t:for(;;){for(var h;f!==n||0!==l&&3!==f.nodeType||(o=i+l),f!==s||0!==a&&3!==f.nodeType||(c=i+a),3===f.nodeType&&(i+=f.nodeValue.length),null!==(h=f.firstChild);)m=f,f=h;for(;;){if(f===e)break t;if(m===n&&++u===l&&(o=i),m===s&&++d===a&&(c=i),null!==(h=f.nextSibling))break;m=(f=m).parentNode}f=h}n=-1===o||-1===c?null:{start:o,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ad={focusedElem:e,selectionRange:n},af=!1,So=t;null!==So;)if(e=(t=So).child,1024&t.subtreeFlags&&null!==e)e.return=t,So=e;else for(;null!==So;){switch(s=(t=So).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==s){e=void 0,n=t,l=s.memoizedProps,s=s.memoizedState,a=n.stateNode;try{var p=xi(n.type,l,(n.elementType,n.type));e=a.getSnapshotBeforeUpdate(p,s),a.__reactInternalSnapshotBeforeUpdate=e}catch(x){fu(n,n.return,x)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))xd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":xd(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(r(163))}if(null!==(e=t.sibling)){e.return=t.return,So=e;break}So=t.return}}(e,t)}finally{ac=i,M.p=l,L.T=a}}Ec=1,lu(),su(),iu()}}function lu(){if(1===Ec){Ec=0;var e=Tc,t=Pc,n=!!(13878&t.flags);if(13878&t.subtreeFlags||n){n=L.T,L.T=null;var r=M.p;M.p=2;var a=ac;ac|=4;try{Oo(t,e);var l=ad,s=nr(e.containerInfo),i=l.focusedElem,o=l.selectionRange;if(s!==i&&i&&i.ownerDocument&&tr(i.ownerDocument.documentElement,i)){if(null!==o&&rr(i)){var c=o.start,u=o.end;if(void 0===u&&(u=c),"selectionStart"in i)i.selectionStart=c,i.selectionEnd=Math.min(u,i.value.length);else{var d=i.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var m=f.getSelection(),h=i.textContent.length,p=Math.min(o.start,h),g=void 0===o.end?p:Math.min(o.end,h);!m.extend&&p>g&&(s=g,g=p,p=s);var x=er(i,p),b=er(i,g);if(x&&b&&(1!==m.rangeCount||m.anchorNode!==x.node||m.anchorOffset!==x.offset||m.focusNode!==b.node||m.focusOffset!==b.offset)){var y=d.createRange();y.setStart(x.node,x.offset),m.removeAllRanges(),p>g?(m.addRange(y),m.extend(b.node,b.offset)):(y.setEnd(b.node,b.offset),m.addRange(y))}}}}for(d=[],m=i;m=m.parentNode;)1===m.nodeType&&d.push({element:m,left:m.scrollLeft,top:m.scrollTop});for("function"==typeof i.focus&&i.focus(),i=0;i<d.length;i++){var v=d[i];v.element.scrollLeft=v.left,v.element.scrollTop=v.top}}af=!!rd,ad=rd=null}finally{ac=a,M.p=r,L.T=n}}e.current=t,Ec=2}}function su(){if(2===Ec){Ec=0;var e=Tc,t=Pc,n=!!(8772&t.flags);if(8772&t.subtreeFlags||n){n=L.T,L.T=null;var r=M.p;M.p=2;var a=ac;ac|=4;try{Co(e,t.alternate,t)}finally{ac=a,M.p=r,L.T=n}}Ec=3}}function iu(){if(4===Ec||3===Ec){Ec=0,Z();var e=Tc,t=Pc,n=Dc,r=Lc;10256&t.subtreeFlags||10256&t.flags?Ec=5:(Ec=0,Pc=Tc=null,ou(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(Cc=null),Fe(n),t=t.stateNode,ue&&"function"==typeof ue.onCommitFiberRoot)try{ue.onCommitFiberRoot(ce,t,void 0,!(128&~t.current.flags))}catch(o){}if(null!==r){t=L.T,a=M.p,M.p=2,L.T=null;try{for(var l=e.onRecoverableError,s=0;s<r.length;s++){var i=r[s];l(i.value,{componentStack:i.stack})}}finally{L.T=t,M.p=a}}3&Dc&&cu(),ku(e),a=e.pendingLanes,4194090&n&&42&a?e===Ac?Mc++:(Mc=0,Ac=e):Mc=0,Su(0)}}function ou(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Oa(t)))}function cu(e){return lu(),su(),iu(),uu()}function uu(){if(5!==Ec)return!1;var e=Tc,t=Fc;Fc=0;var n=Fe(Dc),a=L.T,l=M.p;try{M.p=32>n?32:n,L.T=null,n=zc,zc=null;var s=Tc,i=Dc;if(Ec=0,Pc=Tc=null,Dc=0,6&ac)throw Error(r(331));var o=ac;if(ac|=4,Zo(s.current),qo(s,s.current,i,n),ac=o,Su(0,!1),ue&&"function"==typeof ue.onPostCommitFiberRoot)try{ue.onPostCommitFiberRoot(ce,s)}catch(c){}return!0}finally{M.p=l,L.T=a,ou(e,t)}}function du(e,t,n){t=Cr(n,t),null!==(e=il(e,t=ki(e.stateNode,t,2),2))&&(Ee(e,2),ku(e))}function fu(e,t,n){if(3===e.tag)du(e,e,n);else for(;null!==t;){if(3===t.tag){du(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Cc||!Cc.has(r))){e=Cr(n,e),null!==(r=il(t,n=Si(2),2))&&(Ci(n,r,t,e),Ee(r,2),ku(r));break}}t=t.return}}function mu(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new rc;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(fc=!0,a.add(n),e=hu.bind(null,e,t,n),t.then(e,e))}function hu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,lc===e&&(ic&n)===n&&(4===hc||3===hc&&(62914560&ic)===ic&&300>ee()-Nc?!(2&ac)&&qc(e,0):xc|=n,yc===ic&&(yc=0)),ku(e)}function pu(e,t){0===t&&(t=ke()),null!==(e=Lr(e,t))&&(Ee(e,t),ku(e))}function gu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),pu(e,n)}function xu(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,l=e.memoizedState;null!==l&&(n=l.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(r(314))}null!==a&&a.delete(t),pu(e,n)}var bu=null,yu=null,vu=!1,wu=!1,ju=!1,Nu=0;function ku(e){e!==yu&&null===e.next&&(null===yu?bu=yu=e:yu=yu.next=e),wu=!0,vu||(vu=!0,md((function(){6&ac?G(ne,Cu):Eu()})))}function Su(e,t){if(!ju&&wu){ju=!0;do{for(var n=!1,r=bu;null!==r;){if(0!==e){var a=r.pendingLanes;if(0===a)var l=0;else{var s=r.suspendedLanes,i=r.pingedLanes;l=(1<<31-fe(42|e)+1)-1,l=201326741&(l&=a&~(s&~i))?201326741&l|1:l?2|l:0}0!==l&&(n=!0,Du(r,l))}else l=ic,!(3&(l=be(r,r===lc?l:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||ye(r,l)||(n=!0,Du(r,l));r=r.next}}while(n);ju=!1}}function Cu(){Eu()}function Eu(){wu=vu=!1;var e=0;0!==Nu&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==cd&&(cd=e,!0);return cd=null,!1}()&&(e=Nu),Nu=0);for(var t=ee(),n=null,r=bu;null!==r;){var a=r.next,l=Tu(r,t);0===l?(r.next=null,null===n?bu=a:n.next=a,null===a&&(yu=n)):(n=r,(0!==e||3&l)&&(wu=!0)),r=a}Su(e)}function Tu(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=-62914561&e.pendingLanes;0<l;){var s=31-fe(l),i=1<<s,o=a[s];-1===o?0!==(i&n)&&0===(i&r)||(a[s]=we(i,t)):o<=t&&(e.expiredLanes|=i),l&=~i}if(n=ic,n=be(e,e===(t=lc)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===oc||9===oc)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&X(r),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||ye(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&X(r),Fe(n)){case 2:case 8:n=re;break;case 32:default:n=ae;break;case 268435456:n=se}return r=Pu.bind(null,e),n=G(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&X(r),e.callbackPriority=2,e.callbackNode=null,2}function Pu(e,t){if(0!==Ec&&5!==Ec)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(cu()&&e.callbackNode!==n)return null;var r=ic;return 0===(r=be(e,e===lc?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(_c(e,r,t),Tu(e,ee()),null!=e.callbackNode&&e.callbackNode===n?Pu.bind(null,e):null)}function Du(e,t){if(cu())return null;_c(e,t,!0)}function Fu(){return 0===Nu&&(Nu=je()),Nu}function zu(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:Ft(""+e)}function Lu(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Mu=0;Mu<Nr.length;Mu++){var Au=Nr[Mu];kr(Au.toLowerCase(),"on"+(Au[0].toUpperCase()+Au.slice(1)))}kr(pr,"onAnimationEnd"),kr(gr,"onAnimationIteration"),kr(xr,"onAnimationStart"),kr("dblclick","onDoubleClick"),kr("focusin","onFocus"),kr("focusout","onBlur"),kr(br,"onTransitionRun"),kr(yr,"onTransitionStart"),kr(vr,"onTransitionCancel"),kr(wr,"onTransitionEnd"),Xe("onMouseEnter",["mouseout","mouseover"]),Xe("onMouseLeave",["mouseout","mouseover"]),Xe("onPointerEnter",["pointerout","pointerover"]),Xe("onPointerLeave",["pointerout","pointerover"]),Ge("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ge("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ge("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ge("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ge("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ge("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ou="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Bu=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ou));function Ru(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var s=r.length-1;0<=s;s--){var i=r[s],o=i.instance,c=i.currentTarget;if(i=i.listener,o!==l&&a.isPropagationStopped())break e;l=i,a.currentTarget=c;try{l(a)}catch(u){bi(u)}a.currentTarget=null,l=o}else for(s=0;s<r.length;s++){if(o=(i=r[s]).instance,c=i.currentTarget,i=i.listener,o!==l&&a.isPropagationStopped())break e;l=i,a.currentTarget=c;try{l(a)}catch(u){bi(u)}a.currentTarget=null,l=o}}}}function _u(e,t){var n=t[Be];void 0===n&&(n=t[Be]=new Set);var r=e+"__bubble";n.has(r)||(Wu(t,e,2,!1),n.add(r))}function Iu(e,t,n){var r=0;t&&(r|=4),Wu(n,e,r,t)}var $u="_reactListening"+Math.random().toString(36).slice(2);function Uu(e){if(!e[$u]){e[$u]=!0,Ve.forEach((function(t){"selectionchange"!==t&&(Bu.has(t)||Iu(t,!1,e),Iu(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[$u]||(t[$u]=!0,Iu("selectionchange",!1,t))}}function Wu(e,t,n,r){switch(ff(t)){case 2:var a=lf;break;case 8:a=sf;break;default:a=of}n=a.bind(null,t,n,e),a=void 0,!$t||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hu(e,t,n,r,a){var s=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var o=r.stateNode.containerInfo;if(o===a)break;if(4===i)for(i=r.return;null!==i;){var c=i.tag;if((3===c||4===c)&&i.stateNode.containerInfo===a)return;i=i.return}for(;null!==o;){if(null===(i=We(o)))return;if(5===(c=i.tag)||6===c||26===c||27===c){r=s=i;continue e}o=o.parentNode}}r=r.return}Rt((function(){var r=s,a=Lt(n),i=[];e:{var o=jr.get(e);if(void 0!==o){var c=tn,u=e;switch(e){case"keypress":if(0===Qt(n))break e;case"keydown":case"keyup":c=xn;break;case"focusin":u="focus",c=on;break;case"focusout":u="blur",c=on;break;case"beforeblur":case"afterblur":c=on;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=ln;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=sn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=yn;break;case pr:case gr:case xr:c=cn;break;case wr:c=vn;break;case"scroll":case"scrollend":c=rn;break;case"wheel":c=wn;break;case"copy":case"cut":case"paste":c=un;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=bn;break;case"toggle":case"beforetoggle":c=jn}var d=!!(4&t),f=!d&&("scroll"===e||"scrollend"===e),m=d?null!==o?o+"Capture":null:o;d=[];for(var h,p=r;null!==p;){var g=p;if(h=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===h||null===m||null!=(g=_t(p,m))&&d.push(qu(p,g,h)),f)break;p=p.return}0<d.length&&(o=new c(o,u,null,n,a),i.push({event:o,listeners:d}))}}if(!(7&t)){if(c="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===zt||!(u=n.relatedTarget||n.fromElement)||!We(u)&&!u[Oe])&&(c||o)&&(o=a.window===a?a:(o=a.ownerDocument)?o.defaultView||o.parentWindow:window,c?(c=r,null!==(u=(u=n.relatedTarget||n.toElement)?We(u):null)&&(f=l(u),d=u.tag,u!==f||5!==d&&27!==d&&6!==d)&&(u=null)):(c=null,u=r),c!==u)){if(d=ln,g="onMouseLeave",m="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(d=bn,g="onPointerLeave",m="onPointerEnter",p="pointer"),f=null==c?o:qe(c),h=null==u?o:qe(u),(o=new d(g,p+"leave",c,n,a)).target=f,o.relatedTarget=h,g=null,We(a)===r&&((d=new d(m,p+"enter",u,n,a)).target=h,d.relatedTarget=f,g=d),f=g,c&&u)e:{for(m=u,p=0,h=d=c;h;h=Qu(h))p++;for(h=0,g=m;g;g=Qu(g))h++;for(;0<p-h;)d=Qu(d),p--;for(;0<h-p;)m=Qu(m),h--;for(;p--;){if(d===m||null!==m&&d===m.alternate)break e;d=Qu(d),m=Qu(m)}d=null}else d=null;null!==c&&Vu(i,o,c,d,!1),null!==u&&null!==f&&Vu(i,f,u,d,!0)}if("select"===(c=(o=r?qe(r):window).nodeName&&o.nodeName.toLowerCase())||"input"===c&&"file"===o.type)var x=In;else if(Mn(o))if($n)x=Gn;else{x=Vn;var b=Qn}else!(c=o.nodeName)||"input"!==c.toLowerCase()||"checkbox"!==o.type&&"radio"!==o.type?r&&Tt(r.elementType)&&(x=In):x=Kn;switch(x&&(x=x(e,r))?An(i,x,n,a):(b&&b(e,o,r),"focusout"===e&&r&&"number"===o.type&&null!=r.memoizedProps.value&&vt(o,"number",o.value)),b=r?qe(r):window,e){case"focusin":(Mn(b)||"true"===b.contentEditable)&&(lr=b,sr=r,ir=null);break;case"focusout":ir=sr=lr=null;break;case"mousedown":or=!0;break;case"contextmenu":case"mouseup":case"dragend":or=!1,cr(i,n,a);break;case"selectionchange":if(ar)break;case"keydown":case"keyup":cr(i,n,a)}var y;if(kn)e:{switch(e){case"compositionstart":var v="onCompositionStart";break e;case"compositionend":v="onCompositionEnd";break e;case"compositionupdate":v="onCompositionUpdate";break e}v=void 0}else zn?Dn(e,n)&&(v="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(v="onCompositionStart");v&&(En&&"ko"!==n.locale&&(zn||"onCompositionStart"!==v?"onCompositionEnd"===v&&zn&&(y=Yt()):(Ht="value"in(Wt=a)?Wt.value:Wt.textContent,zn=!0)),0<(b=Yu(r,v)).length&&(v=new dn(v,e,null,n,a),i.push({event:v,listeners:b}),y?v.data=y:null!==(y=Fn(n))&&(v.data=y))),(y=Cn?function(e,t){switch(e){case"compositionend":return Fn(t);case"keypress":return 32!==t.which?null:(Pn=!0,Tn);case"textInput":return(e=t.data)===Tn&&Pn?null:e;default:return null}}(e,n):function(e,t){if(zn)return"compositionend"===e||!kn&&Dn(e,t)?(e=Yt(),qt=Ht=Wt=null,zn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return En&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(v=Yu(r,"onBeforeInput")).length&&(b=new dn("onBeforeInput","beforeinput",null,n,a),i.push({event:b,listeners:v}),b.data=y)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var l=zu((a[Ae]||null).action),s=r.submitter;s&&null!==(t=(t=s[Ae]||null)?zu(t.formAction):s.getAttribute("formAction"))&&(l=t,s=null);var i=new tn("action","action",null,r,a);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==Nu){var e=s?Lu(a,s):new FormData(a);zs(n,{pending:!0,data:e,method:a.method,action:l},null,e)}}else"function"==typeof l&&(i.preventDefault(),e=s?Lu(a,s):new FormData(a),zs(n,{pending:!0,data:e,method:a.method,action:l},l,e))},currentTarget:a}]})}}(i,e,r,n,a)}Ru(i,t)}))}function qu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Yu(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===l||(null!=(a=_t(e,n))&&r.unshift(qu(e,a,l)),null!=(a=_t(e,t))&&r.push(qu(e,a,l))),3===e.tag)return r;e=e.return}return[]}function Qu(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Vu(e,t,n,r,a){for(var l=t._reactName,s=[];null!==n&&n!==r;){var i=n,o=i.alternate,c=i.stateNode;if(i=i.tag,null!==o&&o===r)break;5!==i&&26!==i&&27!==i||null===c||(o=c,a?null!=(c=_t(n,l))&&s.unshift(qu(n,c,o)):a||null!=(c=_t(n,l))&&s.push(qu(n,c,o))),n=n.return}0!==s.length&&e.push({event:t,listeners:s})}var Ku=/\r\n?/g,Gu=/\u0000|\uFFFD/g;function Xu(e){return("string"==typeof e?e:""+e).replace(Ku,"\n").replace(Gu,"")}function Ju(e,t){return t=Xu(t),Xu(e)===t}function Zu(){}function ed(e,t,n,a,l,s){switch(n){case"children":"string"==typeof a?"body"===t||"textarea"===t&&""===a||kt(e,a):("number"==typeof a||"bigint"==typeof a)&&"body"!==t&&kt(e,""+a);break;case"className":at(e,"class",a);break;case"tabIndex":at(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":at(e,n,a);break;case"style":Et(e,a,s);break;case"data":if("object"!==t){at(e,"data",a);break}case"src":case"href":if(""===a&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==a||"function"==typeof a||"symbol"==typeof a||"boolean"==typeof a){e.removeAttribute(n);break}a=Ft(""+a),e.setAttribute(n,a);break;case"action":case"formAction":if("function"==typeof a){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof s&&("formAction"===n?("input"!==t&&ed(e,t,"name",l.name,l,null),ed(e,t,"formEncType",l.formEncType,l,null),ed(e,t,"formMethod",l.formMethod,l,null),ed(e,t,"formTarget",l.formTarget,l,null)):(ed(e,t,"encType",l.encType,l,null),ed(e,t,"method",l.method,l,null),ed(e,t,"target",l.target,l,null))),null==a||"symbol"==typeof a||"boolean"==typeof a){e.removeAttribute(n);break}a=Ft(""+a),e.setAttribute(n,a);break;case"onClick":null!=a&&(e.onclick=Zu);break;case"onScroll":null!=a&&_u("scroll",e);break;case"onScrollEnd":null!=a&&_u("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=a){if("object"!=typeof a||!("__html"in a))throw Error(r(61));if(null!=(n=a.__html)){if(null!=l.children)throw Error(r(60));e.innerHTML=n}}break;case"multiple":e.multiple=a&&"function"!=typeof a&&"symbol"!=typeof a;break;case"muted":e.muted=a&&"function"!=typeof a&&"symbol"!=typeof a;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==a||"function"==typeof a||"boolean"==typeof a||"symbol"==typeof a){e.removeAttribute("xlink:href");break}n=Ft(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=a&&"function"!=typeof a&&"symbol"!=typeof a?e.setAttribute(n,""+a):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&"function"!=typeof a&&"symbol"!=typeof a?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===a?e.setAttribute(n,""):!1!==a&&null!=a&&"function"!=typeof a&&"symbol"!=typeof a?e.setAttribute(n,a):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=a&&"function"!=typeof a&&"symbol"!=typeof a&&!isNaN(a)&&1<=a?e.setAttribute(n,a):e.removeAttribute(n);break;case"rowSpan":case"start":null==a||"function"==typeof a||"symbol"==typeof a||isNaN(a)?e.removeAttribute(n):e.setAttribute(n,a);break;case"popover":_u("beforetoggle",e),_u("toggle",e),rt(e,"popover",a);break;case"xlinkActuate":lt(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":lt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":lt(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":lt(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":lt(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":lt(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":lt(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":lt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":lt(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":rt(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&rt(e,n=Pt.get(n)||n,a)}}function td(e,t,n,a,l,s){switch(n){case"style":Et(e,a,s);break;case"dangerouslySetInnerHTML":if(null!=a){if("object"!=typeof a||!("__html"in a))throw Error(r(61));if(null!=(n=a.__html)){if(null!=l.children)throw Error(r(60));e.innerHTML=n}}break;case"children":"string"==typeof a?kt(e,a):("number"==typeof a||"bigint"==typeof a)&&kt(e,""+a);break;case"onScroll":null!=a&&_u("scroll",e);break;case"onScrollEnd":null!=a&&_u("scrollend",e);break;case"onClick":null!=a&&(e.onclick=Zu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ke.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(l=n.endsWith("Capture"),t=n.slice(2,l?n.length-7:void 0),"function"==typeof(s=null!=(s=e[Ae]||null)?s[n]:null)&&e.removeEventListener(t,s,l),"function"!=typeof a)?n in e?e[n]=a:!0===a?e.setAttribute(n,""):rt(e,n,a):("function"!=typeof s&&null!==s&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,a,l)))}}function nd(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":_u("error",e),_u("load",e);var a,l=!1,s=!1;for(a in n)if(n.hasOwnProperty(a)){var i=n[a];if(null!=i)switch(a){case"src":l=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:ed(e,t,a,i,n,null)}}return s&&ed(e,t,"srcSet",n.srcSet,n,null),void(l&&ed(e,t,"src",n.src,n,null));case"input":_u("invalid",e);var o=a=i=s=null,c=null,u=null;for(l in n)if(n.hasOwnProperty(l)){var d=n[l];if(null!=d)switch(l){case"name":s=d;break;case"type":i=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":a=d;break;case"defaultValue":o=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(r(137,t));break;default:ed(e,t,l,d,n,null)}}return yt(e,a,o,c,u,i,s,!1),void mt(e);case"select":for(s in _u("invalid",e),l=i=a=null,n)if(n.hasOwnProperty(s)&&null!=(o=n[s]))switch(s){case"value":a=o;break;case"defaultValue":i=o;break;case"multiple":l=o;default:ed(e,t,s,o,n,null)}return t=a,n=i,e.multiple=!!l,void(null!=t?wt(e,!!l,t,!1):null!=n&&wt(e,!!l,n,!0));case"textarea":for(i in _u("invalid",e),a=s=l=null,n)if(n.hasOwnProperty(i)&&null!=(o=n[i]))switch(i){case"value":l=o;break;case"defaultValue":s=o;break;case"children":a=o;break;case"dangerouslySetInnerHTML":if(null!=o)throw Error(r(91));break;default:ed(e,t,i,o,n,null)}return Nt(e,l,s,a),void mt(e);case"option":for(c in n)if(n.hasOwnProperty(c)&&null!=(l=n[c]))if("selected"===c)e.selected=l&&"function"!=typeof l&&"symbol"!=typeof l;else ed(e,t,c,l,n,null);return;case"dialog":_u("beforetoggle",e),_u("toggle",e),_u("cancel",e),_u("close",e);break;case"iframe":case"object":_u("load",e);break;case"video":case"audio":for(l=0;l<Ou.length;l++)_u(Ou[l],e);break;case"image":_u("error",e),_u("load",e);break;case"details":_u("toggle",e);break;case"embed":case"source":case"link":_u("error",e),_u("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(l=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:ed(e,t,u,l,n,null)}return;default:if(Tt(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(l=n[d])&&td(e,t,d,l,n,void 0));return}}for(o in n)n.hasOwnProperty(o)&&(null!=(l=n[o])&&ed(e,t,o,l,n,null))}var rd=null,ad=null;function ld(e){return 9===e.nodeType?e:e.ownerDocument}function sd(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function id(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function od(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var cd=null;var ud="function"==typeof setTimeout?setTimeout:void 0,dd="function"==typeof clearTimeout?clearTimeout:void 0,fd="function"==typeof Promise?Promise:void 0,md="function"==typeof queueMicrotask?queueMicrotask:void 0!==fd?function(e){return fd.resolve(null).then(e).catch(hd)}:ud;function hd(e){setTimeout((function(){throw e}))}function pd(e){return"head"===e}function gd(e,t){var n=t,r=0,a=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&8===l.nodeType)if("/$"===(n=l.data)){if(0<r&&8>r){n=r;var s=e.ownerDocument;if(1&n&&Nd(s.documentElement),2&n&&Nd(s.body),4&n)for(Nd(n=s.head),s=n.firstChild;s;){var i=s.nextSibling,o=s.nodeName;s[$e]||"SCRIPT"===o||"STYLE"===o||"LINK"===o&&"stylesheet"===s.rel.toLowerCase()||n.removeChild(s),s=i}}if(0===a)return e.removeChild(l),void Df(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=l}while(n);Df(t)}function xd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":xd(n),Ue(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function bd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var vd=null;function wd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function jd(e,t,n){switch(t=ld(n),e){case"html":if(!(e=t.documentElement))throw Error(r(452));return e;case"head":if(!(e=t.head))throw Error(r(453));return e;case"body":if(!(e=t.body))throw Error(r(454));return e;default:throw Error(r(451))}}function Nd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ue(e)}var kd=new Map,Sd=new Set;function Cd(e){return"function"==typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Ed=M.d;M.d={f:function(){var e=Ed.f(),t=Wc();return e||t},r:function(e){var t=He(e);null!==t&&5===t.tag&&"form"===t.type?Ms(t):Ed.r(e)},D:function(e){Ed.D(e),Pd("dns-prefetch",e,null)},C:function(e,t){Ed.C(e,t),Pd("preconnect",e,t)},L:function(e,t,n){Ed.L(e,t,n);var r=Td;if(r&&e&&t){var a='link[rel="preload"][as="'+xt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+xt(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(a+='[imagesizes="'+xt(n.imageSizes)+'"]')):a+='[href="'+xt(e)+'"]';var l=a;switch(t){case"style":l=Fd(e);break;case"script":l=Md(e)}kd.has(l)||(e=c({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),kd.set(l,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(zd(l))||"script"===t&&r.querySelector(Ad(l))||(nd(t=r.createElement("link"),"link",e),Qe(t),r.head.appendChild(t)))}},m:function(e,t){Ed.m(e,t);var n=Td;if(n&&e){var r=t&&"string"==typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+xt(r)+'"][href="'+xt(e)+'"]',l=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":l=Md(e)}if(!kd.has(l)&&(e=c({rel:"modulepreload",href:e},t),kd.set(l,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ad(l)))return}nd(r=n.createElement("link"),"link",e),Qe(r),n.head.appendChild(r)}}},X:function(e,t){Ed.X(e,t);var n=Td;if(n&&e){var r=Ye(n).hoistableScripts,a=Md(e),l=r.get(a);l||((l=n.querySelector(Ad(a)))||(e=c({src:e,async:!0},t),(t=kd.get(a))&&_d(e,t),Qe(l=n.createElement("script")),nd(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}},S:function(e,t,n){Ed.S(e,t,n);var r=Td;if(r&&e){var a=Ye(r).hoistableStyles,l=Fd(e);t=t||"default";var s=a.get(l);if(!s){var i={loading:0,preload:null};if(s=r.querySelector(zd(l)))i.loading=5;else{e=c({rel:"stylesheet",href:e,"data-precedence":t},n),(n=kd.get(l))&&Rd(e,n);var o=s=r.createElement("link");Qe(o),nd(o,"link",e),o._p=new Promise((function(e,t){o.onload=e,o.onerror=t})),o.addEventListener("load",(function(){i.loading|=1})),o.addEventListener("error",(function(){i.loading|=2})),i.loading|=4,Bd(s,t,r)}s={type:"stylesheet",instance:s,count:1,state:i},a.set(l,s)}}},M:function(e,t){Ed.M(e,t);var n=Td;if(n&&e){var r=Ye(n).hoistableScripts,a=Md(e),l=r.get(a);l||((l=n.querySelector(Ad(a)))||(e=c({src:e,async:!0,type:"module"},t),(t=kd.get(a))&&_d(e,t),Qe(l=n.createElement("script")),nd(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}}};var Td="undefined"==typeof document?null:document;function Pd(e,t,n){var r=Td;if(r&&"string"==typeof t&&t){var a=xt(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"==typeof n&&(a+='[crossorigin="'+n+'"]'),Sd.has(a)||(Sd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(nd(t=r.createElement("link"),"link",e),Qe(t),r.head.appendChild(t)))}}function Dd(e,t,n,a){var l,s,i,o,c=(c=W.current)?Cd(c):null;if(!c)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=Fd(n.href),(a=(n=Ye(c).hoistableStyles).get(t))||(a={type:"style",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=Fd(n.href);var u=Ye(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(zd(e)))&&!u._p&&(d.instance=u,d.state.loading=5),kd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},kd.set(e,n),u||(l=c,s=e,i=n,o=d.state,l.querySelector('link[rel="preload"][as="style"]['+s+"]")?o.loading=1:(s=l.createElement("link"),o.preload=s,s.addEventListener("load",(function(){return o.loading|=1})),s.addEventListener("error",(function(){return o.loading|=2})),nd(s,"link",i),Qe(s),l.head.appendChild(s))))),t&&null===a)throw Error(r(528,""));return d}if(t&&null!==a)throw Error(r(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=Md(n),(a=(n=Ye(c).hoistableScripts).get(t))||(a={type:"script",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function Fd(e){return'href="'+xt(e)+'"'}function zd(e){return'link[rel="stylesheet"]['+e+"]"}function Ld(e){return c({},e,{"data-precedence":e.precedence,precedence:null})}function Md(e){return'[src="'+xt(e)+'"]'}function Ad(e){return"script[async]"+e}function Od(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+xt(n.href)+'"]');if(a)return t.instance=a,Qe(a),a;var l=c({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Qe(a=(e.ownerDocument||e).createElement("style")),nd(a,"style",l),Bd(a,n.precedence,e),t.instance=a;case"stylesheet":l=Fd(n.href);var s=e.querySelector(zd(l));if(s)return t.state.loading|=4,t.instance=s,Qe(s),s;a=Ld(n),(l=kd.get(l))&&Rd(a,l),Qe(s=(e.ownerDocument||e).createElement("link"));var i=s;return i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),nd(s,"link",a),t.state.loading|=4,Bd(s,n.precedence,e),t.instance=s;case"script":return s=Md(n.src),(l=e.querySelector(Ad(s)))?(t.instance=l,Qe(l),l):(a=n,(l=kd.get(s))&&_d(a=c({},n),l),Qe(l=(e=e.ownerDocument||e).createElement("script")),nd(l,"link",a),e.head.appendChild(l),t.instance=l);case"void":return null;default:throw Error(r(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(a=t.instance,t.state.loading|=4,Bd(a,n.precedence,e));return t.instance}function Bd(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,l=a,s=0;s<r.length;s++){var i=r[s];if(i.dataset.precedence===t)l=i;else if(l!==a)break}l?l.parentNode.insertBefore(e,l.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Rd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function _d(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Id=null;function $d(e,t,n){if(null===Id){var r=new Map,a=Id=new Map;a.set(n,r)}else(r=(a=Id).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var l=n[a];if(!(l[$e]||l[Me]||"link"===e&&"stylesheet"===l.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==l.namespaceURI){var s=l.getAttribute(t)||"";s=e+s;var i=r.get(s);i?i.push(l):r.set(s,[l])}}return r}function Ud(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Wd(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var Hd=null;function qd(){}function Yd(){if(this.count--,0===this.count)if(this.stylesheets)Vd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Qd=null;function Vd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Qd=new Map,t.forEach(Kd,e),Qd=null,Yd.call(e))}function Kd(e,t){if(!(4&t.state.loading)){var n=Qd.get(e);if(n)var r=n.get(null);else{n=new Map,Qd.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),l=0;l<a.length;l++){var s=a[l];"LINK"!==s.nodeName&&"not all"===s.getAttribute("media")||(n.set(s.dataset.precedence,s),r=s)}r&&n.set(null,r)}s=(a=t.instance).getAttribute("data-precedence"),(l=n.get(s)||r)===r&&n.set(null,a),n.set(s,a),this.count++,r=Yd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),l?l.parentNode.insertBefore(a,l.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Gd={$$typeof:v,Provider:null,Consumer:null,_currentValue:A,_currentValue2:A,_threadCount:0};function Xd(e,t,n,r,a,l,s,i){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Se(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Se(0),this.hiddenUpdates=Se(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=l,this.onRecoverableError=s,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Jd(e,t,n,r,a,l,s,i,o,c,u,d){return e=new Xd(e,t,n,s,i,o,c,d),t=1,!0===l&&(t|=24),l=Rr(3,null,null,t),e.current=l,l.stateNode=e,(t=Aa()).refCount++,e.pooledCache=t,t.refCount++,l.memoizedState={element:r,isDehydrated:n,cache:t},al(l),e}function Zd(e){return e?e=Or:Or}function ef(e,t,n,r,a,l){a=Zd(a),null===r.context?r.context=a:r.pendingContext=a,(r=sl(t)).payload={element:n},null!==(l=void 0===l?null:l)&&(r.callback=l),null!==(n=il(e,r,t))&&(Rc(n,0,t),ol(n,e,t))}function tf(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function nf(e,t){tf(e,t),(e=e.alternate)&&tf(e,t)}function rf(e){if(13===e.tag){var t=Lr(e,67108864);null!==t&&Rc(t,0,67108864),nf(e,67108864)}}var af=!0;function lf(e,t,n,r){var a=L.T;L.T=null;var l=M.p;try{M.p=2,of(e,t,n,r)}finally{M.p=l,L.T=a}}function sf(e,t,n,r){var a=L.T;L.T=null;var l=M.p;try{M.p=8,of(e,t,n,r)}finally{M.p=l,L.T=a}}function of(e,t,n,r){if(af){var a=cf(r);if(null===a)Hu(e,t,r,uf,n),wf(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return hf=jf(hf,e,t,n,r,a),!0;case"dragenter":return pf=jf(pf,e,t,n,r,a),!0;case"mouseover":return gf=jf(gf,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return xf.set(l,jf(xf.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,bf.set(l,jf(bf.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(wf(e,r),4&t&&-1<vf.indexOf(e)){for(;null!==a;){var l=He(a);if(null!==l)switch(l.tag){case 3:if((l=l.stateNode).current.memoizedState.isDehydrated){var s=xe(l.pendingLanes);if(0!==s){var i=l;for(i.pendingLanes|=2,i.entangledLanes|=2;s;){var o=1<<31-fe(s);i.entanglements[1]|=o,s&=~o}ku(l),!(6&ac)&&(kc=ee()+500,Su(0))}}break;case 13:null!==(i=Lr(l,2))&&Rc(i,0,2),Wc(),nf(l,2)}if(null===(l=cf(r))&&Hu(e,t,r,uf,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Hu(e,t,r,null,n)}}function cf(e){return df(e=Lt(e))}var uf=null;function df(e){if(uf=null,null!==(e=We(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=s(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return uf=e,null}function ff(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(te()){case ne:return 2;case re:return 8;case ae:case le:return 32;case se:return 268435456;default:return 32}default:return 32}}var mf=!1,hf=null,pf=null,gf=null,xf=new Map,bf=new Map,yf=[],vf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function wf(e,t){switch(e){case"focusin":case"focusout":hf=null;break;case"dragenter":case"dragleave":pf=null;break;case"mouseover":case"mouseout":gf=null;break;case"pointerover":case"pointerout":xf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":bf.delete(t.pointerId)}}function jf(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=He(t))&&rf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Nf(e){var t=We(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=s(n)))return e.blockedOn=t,void function(e,t){var n=M.p;try{return M.p=e,t()}finally{M.p=n}}(e.priority,(function(){if(13===n.tag){var e=Oc();e=De(e);var t=Lr(n,e);null!==t&&Rc(t,0,e),nf(n,e)}}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function kf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=cf(e.nativeEvent);if(null!==n)return null!==(t=He(n))&&rf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);zt=r,n.target.dispatchEvent(r),zt=null,t.shift()}return!0}function Sf(e,t,n){kf(e)&&n.delete(t)}function Cf(){mf=!1,null!==hf&&kf(hf)&&(hf=null),null!==pf&&kf(pf)&&(pf=null),null!==gf&&kf(gf)&&(gf=null),xf.forEach(Sf),bf.forEach(Sf)}function Ef(t,n){t.blockedOn===n&&(t.blockedOn=null,mf||(mf=!0,e.unstable_scheduleCallback(e.unstable_NormalPriority,Cf)))}var Tf=null;function Pf(t){Tf!==t&&(Tf=t,e.unstable_scheduleCallback(e.unstable_NormalPriority,(function(){Tf===t&&(Tf=null);for(var e=0;e<t.length;e+=3){var n=t[e],r=t[e+1],a=t[e+2];if("function"!=typeof r){if(null===df(r||n))continue;break}var l=He(n);null!==l&&(t.splice(e,3),e-=3,zs(l,{pending:!0,data:a,method:n.method,action:r},r,a))}})))}function Df(e){function t(t){return Ef(t,e)}null!==hf&&Ef(hf,e),null!==pf&&Ef(pf,e),null!==gf&&Ef(gf,e),xf.forEach(t),bf.forEach(t);for(var n=0;n<yf.length;n++){var r=yf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<yf.length&&null===(n=yf[0]).blockedOn;)Nf(n),null===n.blockedOn&&yf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],l=n[r+1],s=a[Ae]||null;if("function"==typeof l)s||Pf(n);else if(s){var i=null;if(l&&l.hasAttribute("formAction")){if(a=l,s=l[Ae]||null)i=s.formAction;else if(null!==df(a))continue}else i=s.action;"function"==typeof i?n[r+1]=i:(n.splice(r,3),r-=3),Pf(n)}}}function Ff(e){this._internalRoot=e}function zf(e){this._internalRoot=e}zf.prototype.render=Ff.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(r(409));ef(t.current,Oc(),e,t,null,null)},zf.prototype.unmount=Ff.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;ef(e.current,2,null,e,null,null),Wc(),t[Oe]=null}},zf.prototype.unstable_scheduleHydration=function(e){if(e){var t=ze();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yf.length&&0!==t&&t<yf[n].priority;n++);yf.splice(n,0,e),0===n&&Nf(e)}};var Lf=t.version;if("19.1.0"!==Lf)throw Error(r(527,Lf,"19.1.0"));M.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(r(188));throw e=Object.keys(e).join(","),Error(r(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(r(188));return t!==e?null:e}for(var n=e,a=t;;){var s=n.return;if(null===s)break;var o=s.alternate;if(null===o){if(null!==(a=s.return)){n=a;continue}break}if(s.child===o.child){for(o=s.child;o;){if(o===n)return i(s),e;if(o===a)return i(s),t;o=o.sibling}throw Error(r(188))}if(n.return!==a.return)n=s,a=o;else{for(var c=!1,u=s.child;u;){if(u===n){c=!0,n=s,a=o;break}if(u===a){c=!0,a=s,n=o;break}u=u.sibling}if(!c){for(u=o.child;u;){if(u===n){c=!0,n=o,a=s;break}if(u===a){c=!0,a=o,n=s;break}u=u.sibling}if(!c)throw Error(r(189))}}if(n.alternate!==a)throw Error(r(190))}if(3!==n.tag)throw Error(r(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?o(e):null)?null:e.stateNode};var Mf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:L,reconcilerVersion:"19.1.0"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Af=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Af.isDisabled&&Af.supportsFiber)try{ce=Af.inject(Mf),ue=Af}catch(Bf){}}return Ne.createRoot=function(e,t){if(!a(e))throw Error(r(299));var n=!1,l="",s=yi,i=vi,o=wi;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(l=t.identifierPrefix),void 0!==t.onUncaughtError&&(s=t.onUncaughtError),void 0!==t.onCaughtError&&(i=t.onCaughtError),void 0!==t.onRecoverableError&&(o=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Jd(e,1,!1,null,0,n,l,s,i,o,0,null),e[Oe]=t.current,Uu(e),new Ff(t)},Ne.hydrateRoot=function(e,t,n){if(!a(e))throw Error(r(299));var l=!1,s="",i=yi,o=vi,c=wi,u=null;return null!=n&&(!0===n.unstable_strictMode&&(l=!0),void 0!==n.identifierPrefix&&(s=n.identifierPrefix),void 0!==n.onUncaughtError&&(i=n.onUncaughtError),void 0!==n.onCaughtError&&(o=n.onCaughtError),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(u=n.formState)),(t=Jd(e,1,!0,t,0,l,s,i,o,c,0,u)).context=Zd(null),n=t.current,(s=sl(l=De(l=Oc()))).callback=null,il(n,s,l),n=l,t.current.lanes=n,Ee(t,n),ku(t),e[Oe]=t.current,Uu(e),new zf(t)},Ne.version="19.1.0",Ne}var Te=(we||(we=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){}}(),je.exports=Ee()),je.exports),Pe=function(){return null};const De=new class{constructor(e="https://partner.nawrasinchina.com"){t(this,"baseURL"),this.baseURL=e}async signIn(e){const t=await fetch(`${this.baseURL}/api/auth/sign-in`,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(!t.ok){const e=await t.json();throw new Error(e.error||"Authentication failed")}const n=await t.json();if(!n.success)throw new Error(n.error||"Authentication failed");return n.data}async signOut(){if(!(await fetch(`${this.baseURL}/api/auth/sign-out`,{method:"POST",credentials:"include"})).ok)throw new Error("Sign out failed")}async getSession(){try{const e=await fetch(`${this.baseURL}/api/auth/session`,{credentials:"include"});if(!e.ok)return null;const t=await e.json();return t.success?t.data:null}catch(e){return null}}},Fe=o.createContext(void 0),ze=({children:e})=>{const[t,r]=o.useState({user:null,session:null,isLoading:!0,isAuthenticated:!1});o.useEffect((()=>{(async()=>{try{const e=await De.getSession();r(e?{user:e.user,session:e,isLoading:!1,isAuthenticated:!0}:{user:null,session:null,isLoading:!1,isAuthenticated:!1})}catch(e){r({user:null,session:null,isLoading:!1,isAuthenticated:!1})}})()}),[]);const a={...t,signIn:async e=>{r((e=>({...e,isLoading:!0})));try{const{user:t,session:n}=await De.signIn(e);r({user:t,session:n,isLoading:!1,isAuthenticated:!0})}catch(t){throw r((e=>({...e,isLoading:!1}))),t}},signOut:async()=>{r((e=>({...e,isLoading:!0})));try{await De.signOut(),r({user:null,session:null,isLoading:!1,isAuthenticated:!1})}catch(e){r({user:null,session:null,isLoading:!1,isAuthenticated:!1})}}};return n.jsx(Fe.Provider,{value:a,children:e})},Le=()=>{const e=o.useContext(Fe);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},Me=()=>{const[,e]=c(),[t,r]=o.useState({email:"",password:""}),[a,l]=o.useState(!1),[s,i]=o.useState(!1),[u,d]=o.useState(null),f=(e,t)=>{r((n=>({...n,[e]:t}))),u&&d(null)},m=e=>{r({email:`${e}@nawrasinchina.com`,password:`${e}2024`})};return n.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:n.jsxs("div",{className:"max-w-md w-full",children:[n.jsxs("div",{className:"text-center mb-8",children:[n.jsx("div",{className:"mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4",children:n.jsx(g,{className:"h-8 w-8 text-white"})}),n.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Nawras Admin"}),n.jsx("p",{className:"text-gray-600",children:"Sign in to access your expense tracker"})]}),n.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[n.jsxs("form",{onSubmit:async n=>{n.preventDefault(),i(!0),d(null);try{await De.signIn(t),e("/")}catch(r){d(r.message||"Invalid email or password")}finally{i(!1)}},className:"space-y-6",children:[n.jsxs("div",{children:[n.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),n.jsxs("div",{className:"relative",children:[n.jsx(x,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),n.jsx("input",{type:"email",id:"email",value:t.email,onChange:e=>f("email",e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your email",required:!0})]})]}),n.jsxs("div",{children:[n.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),n.jsxs("div",{className:"relative",children:[n.jsx(g,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),n.jsx("input",{type:a?"text":"password",id:"password",value:t.password,onChange:e=>f("password",e.target.value),className:"w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your password",required:!0}),n.jsx("button",{type:"button",onClick:()=>l(!a),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:a?n.jsx(b,{className:"h-5 w-5"}):n.jsx(y,{className:"h-5 w-5"})})]})]}),u&&n.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 flex items-center space-x-2",children:[n.jsx(v,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),n.jsx("span",{className:"text-red-700 text-sm",children:u})]}),n.jsx("button",{type:"submit",disabled:s,className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center",children:s?n.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):"Sign In"})]}),n.jsxs("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[n.jsx("p",{className:"text-sm text-gray-600 text-center mb-3",children:"Quick Login:"}),n.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[n.jsx("button",{onClick:()=>m("taha"),className:"px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg text-sm font-medium transition-colors",children:"Login as Taha"}),n.jsx("button",{onClick:()=>m("burak"),className:"px-4 py-2 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg text-sm font-medium transition-colors",children:"Login as Burak"})]})]})]}),n.jsx("div",{className:"text-center mt-8",children:n.jsx("p",{className:"text-sm text-gray-500",children:"Secure expense tracking for partners"})})]})})},Ae=({children:e})=>{const{isAuthenticated:t,isLoading:r}=Le();return r?n.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:n.jsxs("div",{className:"text-center",children:[n.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),n.jsx("p",{className:"text-gray-600",children:"Loading..."})]})}):t?n.jsx(n.Fragment,{children:e}):n.jsx(Me,{})},Oe=({className:e="",showText:t=!0})=>{const[r,a]=o.useState(!1),{user:l,signOut:s}=Le();return n.jsxs("button",{onClick:async()=>{a(!0);try{await s()}catch(e){}finally{a(!1)}},disabled:r,className:`inline-flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors ${e}`,title:`Sign out ${l?.name||""}`,children:[r?n.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"}):n.jsx(w,{className:"h-4 w-4"}),t&&n.jsx("span",{children:r?"Signing out...":"Sign Out"})]})},Be=[{path:"/",label:"Dashboard",icon:j},{path:"/add-expense",label:"Add Expense",icon:N},{path:"/settlement",label:"Settlement",icon:k},{path:"/history",label:"History",icon:S},{path:"/reports",label:"Reports",icon:C},{path:"/settings",label:"Settings",icon:E}],Re=()=>{const[e]=c(),{user:t}=Le();return n.jsx("div",{className:"hidden md:flex md:w-64 md:flex-col",children:n.jsxs("div",{className:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200",children:[n.jsx("div",{className:"flex items-center flex-shrink-0 px-4 mb-8",children:n.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"Nawras Admin"})}),n.jsx("nav",{className:"mt-5 flex-1 px-2 space-y-1",children:Be.map((t=>{const r=t.icon,a=e===t.path;return n.jsx(u,{href:t.path,children:n.jsxs("a",{className:T("group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors",a?"bg-blue-100 text-blue-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[n.jsx(r,{className:T("mr-3 flex-shrink-0 h-5 w-5",a?"text-blue-500":"text-gray-400 group-hover:text-gray-500")}),t.label]})},t.path)}))}),n.jsxs("div",{className:"flex-shrink-0 border-t border-gray-200",children:[t&&n.jsx("div",{className:"p-4 border-b border-gray-200 bg-gray-50",children:n.jsxs("div",{className:"flex items-center space-x-3",children:[n.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:n.jsx(x,{className:"h-5 w-5 text-blue-600"})}),n.jsxs("div",{className:"flex-1 min-w-0",children:[n.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:t.name}),n.jsx("p",{className:"text-xs text-gray-500 truncate",children:t.email})]})]})}),n.jsx("div",{className:"p-4",children:n.jsx(Oe,{className:"w-full justify-center"})}),n.jsx("div",{className:"px-4 pb-4",children:n.jsx("div",{className:"text-xs text-gray-500",children:"Nawras Partner Expenses"})})]})]})})},_e=[{path:"/",label:"Home",icon:j},{path:"/add-expense",label:"Add",icon:N},{path:"/settlement",label:"Settle",icon:k},{path:"/history",label:"History",icon:S},{path:"/reports",label:"Reports",icon:C},{path:"/settings",label:"Settings",icon:E}],Ie=()=>{const[e]=c();return n.jsx("div",{className:"md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-inset-bottom",children:n.jsx("nav",{className:"flex justify-around items-center py-1",children:_e.map((t=>{const r=t.icon,a=e===t.path;return n.jsx(u,{href:t.path,children:n.jsxs("a",{className:T("flex flex-col items-center justify-center px-1 py-2 min-w-0 transition-colors min-h-[60px]",a?"text-blue-600":"text-gray-500 hover:text-gray-700"),children:[n.jsx(r,{className:T("h-6 w-6 mb-1",a?"text-blue-600":"text-gray-400")}),n.jsx("span",{className:T("text-xs font-medium truncate",a?"text-blue-600":"text-gray-500"),children:t.label})]})},t.path)}))})})},$e=()=>{const[e]=c(),t=(e=>{switch(e){case"/":return"Dashboard";case"/add-expense":return"Add Expense";case"/settlement":return"Settlement";case"/history":return"History";case"/reports":return"Reports";case"/settings":return"Settings";default:return"Nawras Admin"}})(e);return n.jsx("div",{className:"md:hidden bg-white border-b border-gray-200 px-4 py-3",children:n.jsx("h1",{className:"text-lg font-semibold text-gray-900 text-center",children:t})})},Ue="http://138.197.8.183:3001/api",We=(...e)=>{},He=(...e)=>{},qe=Ue;async function Ye(e){const t=await fetch(`${qe}/expenses`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw new Error(`Failed to create expense: ${t.status} ${t.statusText}`);const n=await t.json();if(!n.success)throw new Error(n.error||"Failed to create expense");return n.data}async function Qe(e){const t=await fetch(`${qe}/expenses/${e}`,{method:"DELETE"});if(!t.ok)throw new Error(`Failed to delete expense: ${t.status} ${t.statusText}`);const n=await t.json();if(!n.success)throw new Error(n.error||"Failed to delete expense")}const Ve={all:["expenses"],lists:()=>[...Ve.all,"list"],list:e=>[...Ve.lists(),e],details:()=>[...Ve.all,"detail"],detail:e=>[...Ve.details(),e]};function Ke(e){return r({queryKey:Ve.list(e),queryFn:()=>async function(e){const t=new URLSearchParams;e?.category&&t.append("category",e.category),e?.paidBy&&t.append("paidBy",e.paidBy),e?.limit&&t.append("limit",e.limit.toString()),e?.startDate&&t.append("startDate",e.startDate),e?.endDate&&t.append("endDate",e.endDate);const n=`${qe}/expenses${t.toString()?`?${t.toString()}`:""}`;try{const e=await fetch(n);if(!e.ok)throw He("Failed to fetch expenses:",e.status,e.statusText),new Error(`Failed to fetch expenses: ${e.status} ${e.statusText}`);const t=await e.json();if(!t.success)throw He("API returned error:",t.error),new Error(t.error||"Failed to fetch expenses");return We("Successfully fetched expenses:",t.data.length,"items"),t.data}catch(r){throw He("Error fetching expenses:",r),r}}(e),staleTime:3e5,gcTime:6e5,refetchOnWindowFocus:!1,retry:2,retryDelay:e=>Math.min(1e3*2**e,3e4)})}function Ge(){const e=a();return l({mutationFn:({id:e,expense:t})=>async function(e,t){const n=await fetch(`${qe}/expenses/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!n.ok)throw new Error(`Failed to update expense: ${n.status} ${n.statusText}`);const r=await n.json();if(!r.success)throw new Error(r.error||"Failed to update expense");return r.data}(e,t),onSuccess:t=>{e.setQueryData(Ve.detail(t.id),t),e.invalidateQueries({queryKey:Ve.lists()})},onError:e=>{}})}const Xe=Ue;async function Je(e){const t=await fetch(`${Xe}/settlements`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw new Error(`Failed to create settlement: ${t.status} ${t.statusText}`);const n=await t.json();if(!n.success)throw new Error(n.error||"Failed to create settlement");return n.data}const Ze={all:["settlements"],lists:()=>[...Ze.all,"list"],list:e=>[...Ze.lists(),e],details:()=>[...Ze.all,"detail"],detail:e=>[...Ze.details(),e]};function et(e){return r({queryKey:Ze.list(e),queryFn:()=>async function(){const e=new URLSearchParams,t=`${Xe}/settlements${e.toString()?`?${e.toString()}`:""}`,n=await fetch(t);if(!n.ok)throw new Error(`Failed to fetch settlements: ${n.status} ${n.statusText}`);const r=await n.json();if(!r.success)throw new Error(r.error||"Failed to fetch settlements");return r.data}(),staleTime:3e5})}const tt={FOOD:"Food",GROCERIES:"Groceries",TRANSPORTATION:"Transportation",UTILITIES:"Utilities",ENTERTAINMENT:"Entertainment",HEALTHCARE:"Healthcare",SHOPPING:"Shopping",TRAVEL:"Travel",OTHER:"Other"},nt="taha",rt="burak",at=[{name:"Food",color:"#ef4444",icon:"🍽️",isDefault:!0},{name:"Groceries",color:"#22c55e",icon:"🛒",isDefault:!0},{name:"Transportation",color:"#3b82f6",icon:"🚗",isDefault:!0},{name:"Utilities",color:"#f59e0b",icon:"⚡",isDefault:!0},{name:"Entertainment",color:"#8b5cf6",icon:"🎬",isDefault:!0},{name:"Healthcare",color:"#ec4899",icon:"🏥",isDefault:!0},{name:"Shopping",color:"#06b6d4",icon:"🛍️",isDefault:!0},{name:"Travel",color:"#84cc16",icon:"✈️",isDefault:!0},{name:"Other",color:"#6b7280",icon:"📝",isDefault:!0}],lt={currency:"USD",currencySymbol:"$",dateFormat:"MM/DD/YYYY",timeFormat:"12h",notifications:{emailNotifications:!0,pushNotifications:!1,expenseReminders:!0,settlementReminders:!0,weeklyReports:!1,monthlyReports:!0},theme:"system",language:"en",defaultExpenseCategory:"Food",autoSaveExpenses:!0,showDecimalPlaces:!0,dataRetention:"2years",shareAnalytics:!1,profile:{name:"User",email:"",timezone:"America/New_York"}};function st(e,t){const n=e.filter((e=>e.paidById===t)),r=n.reduce(((e,t)=>e+t.amount),0),a=n.length;return{totalAmount:r,expenseCount:a,averageAmount:a>0?r/a:0,lastExpenseDate:n.length>0?n.sort(((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime()))[0].date:void 0}}function it(e,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e)}class ot extends o.Component{constructor(e){super(e),t(this,"handleReload",(()=>{window.location.reload()})),t(this,"handleGoHome",(()=>{window.location.href="/"})),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?n.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:n.jsxs("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center",children:[n.jsx("div",{className:"flex justify-center mb-4",children:n.jsx("div",{className:"h-16 w-16 bg-red-100 rounded-full flex items-center justify-center",children:n.jsx(P,{className:"h-8 w-8 text-red-600"})})}),n.jsx("h1",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Something went wrong"}),n.jsx("p",{className:"text-gray-600 mb-6",children:"We're sorry, but something unexpected happened. Please try refreshing the page or go back to the home page."}),!1,n.jsxs("div",{className:"flex gap-3",children:[n.jsxs("button",{onClick:this.handleReload,className:"flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors",children:[n.jsx(D,{className:"h-4 w-4 mr-2"}),"Reload Page"]}),n.jsxs("button",{onClick:this.handleGoHome,className:"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors",children:[n.jsx(j,{className:"h-4 w-4 mr-2"}),"Go Home"]})]})]})}):this.props.children}}function ct({expense:e,isOpen:t,onClose:r,onSuccess:a}){const[l,s]=o.useState({amount:e.amount,description:e.description,category:e.category,paidById:e.paidById,date:e.date}),[i,c]=o.useState({}),u=Ge();o.useEffect((()=>{s({amount:e.amount,description:e.description,category:e.category,paidById:e.paidById,date:e.date}),c({})}),[e]);const d=(e,t)=>{s((n=>({...n,[e]:t}))),i[e]&&c((t=>({...t,[e]:""})))};return t?n.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:n.jsxs("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-md",children:[n.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[n.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Edit Expense"}),n.jsx("button",{onClick:r,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:u.isPending,children:n.jsx(F,{className:"h-6 w-6"})})]}),n.jsxs("form",{onSubmit:async t=>{if(t.preventDefault(),(()=>{const e={};return(!l.amount||l.amount<=0)&&(e.amount="Amount must be greater than 0"),l.description?.trim()||(e.description="Description is required"),l.category?.trim()||(e.category="Category is required"),l.paidById||(e.paidById="Please select who paid"),l.date||(e.date="Date is required"),c(e),0===Object.keys(e).length})())try{await u.mutateAsync({id:e.id,expense:l}),a?.(),r()}catch(n){}},className:"p-6 space-y-4",children:[n.jsxs("div",{children:[n.jsx("label",{htmlFor:"amount",className:"block text-sm font-medium text-gray-700 mb-1",children:"Amount ($)"}),n.jsx("input",{type:"number",id:"amount",step:"0.01",min:"0",value:l.amount||"",onChange:e=>d("amount",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.amount?"border-red-500":"border-gray-300"),disabled:u.isPending}),i.amount&&n.jsx("p",{className:"text-red-500 text-sm mt-1",children:i.amount})]}),n.jsxs("div",{children:[n.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),n.jsx("input",{type:"text",id:"description",value:l.description||"",onChange:e=>d("description",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.description?"border-red-500":"border-gray-300"),disabled:u.isPending}),i.description&&n.jsx("p",{className:"text-red-500 text-sm mt-1",children:i.description})]}),n.jsxs("div",{children:[n.jsx("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-1",children:"Category"}),n.jsxs("select",{id:"category",value:l.category||"",onChange:e=>d("category",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.category?"border-red-500":"border-gray-300"),disabled:u.isPending,children:[n.jsx("option",{value:"",children:"Select category"}),n.jsx("option",{value:"Food",children:"Food"}),n.jsx("option",{value:"Groceries",children:"Groceries"}),n.jsx("option",{value:"Transportation",children:"Transportation"}),n.jsx("option",{value:"Utilities",children:"Utilities"}),n.jsx("option",{value:"Entertainment",children:"Entertainment"}),n.jsx("option",{value:"Healthcare",children:"Healthcare"}),n.jsx("option",{value:"Shopping",children:"Shopping"}),n.jsx("option",{value:"Other",children:"Other"})]}),i.category&&n.jsx("p",{className:"text-red-500 text-sm mt-1",children:i.category})]}),n.jsxs("div",{children:[n.jsx("label",{htmlFor:"paidById",className:"block text-sm font-medium text-gray-700 mb-1",children:"Paid By"}),n.jsxs("select",{id:"paidById",value:l.paidById||"",onChange:e=>d("paidById",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.paidById?"border-red-500":"border-gray-300"),disabled:u.isPending,children:[n.jsx("option",{value:"",children:"Select person"}),n.jsx("option",{value:"taha",children:"Taha"}),n.jsx("option",{value:"burak",children:"Burak"})]}),i.paidById&&n.jsx("p",{className:"text-red-500 text-sm mt-1",children:i.paidById})]}),n.jsxs("div",{children:[n.jsx("label",{htmlFor:"date",className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),n.jsx("input",{type:"date",id:"date",value:l.date||"",onChange:e=>d("date",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.date?"border-red-500":"border-gray-300"),disabled:u.isPending}),i.date&&n.jsx("p",{className:"text-red-500 text-sm mt-1",children:i.date})]}),u.isError&&n.jsx("div",{className:"text-red-500 text-sm",children:"Failed to update expense. Please try again."}),n.jsxs("div",{className:"flex gap-3 pt-4",children:[n.jsx("button",{type:"button",onClick:r,className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors",disabled:u.isPending,children:"Cancel"}),n.jsx("button",{type:"submit",className:"flex-1 px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors flex items-center justify-center gap-2",disabled:u.isPending,children:u.isPending?n.jsxs(n.Fragment,{children:[n.jsx(z,{className:"h-4 w-4 animate-spin"}),"Updating..."]}):n.jsxs(n.Fragment,{children:[n.jsx(L,{className:"h-4 w-4"}),"Update Expense"]})})]})]})]})}):null}function ut({expense:e,isOpen:t,onClose:r,onSuccess:s}){const i=function(){const e=a();return l({mutationFn:Qe,onSuccess:(t,n)=>{e.removeQueries({queryKey:Ve.detail(n)}),e.invalidateQueries({queryKey:Ve.lists()})},onError:e=>{}})}();return t?n.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:n.jsxs("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-md",children:[n.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center",children:n.jsx(P,{className:"h-5 w-5 text-red-600"})}),n.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Delete Expense"})]}),n.jsx("button",{onClick:r,className:"text-gray-400 hover:text-gray-600 transition-colors",disabled:i.isPending,children:n.jsx(F,{className:"h-6 w-6"})})]}),n.jsxs("div",{className:"p-6",children:[n.jsx("p",{className:"text-gray-600 mb-4",children:"Are you sure you want to delete this expense? This action cannot be undone."}),n.jsx("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:n.jsxs("div",{className:"space-y-2",children:[n.jsxs("div",{className:"flex justify-between",children:[n.jsx("span",{className:"text-sm text-gray-500",children:"Amount:"}),n.jsx("span",{className:"font-medium text-gray-900",children:it(e.amount)})]}),n.jsxs("div",{className:"flex justify-between",children:[n.jsx("span",{className:"text-sm text-gray-500",children:"Description:"}),n.jsx("span",{className:"font-medium text-gray-900 text-right max-w-48 truncate",children:e.description})]}),n.jsxs("div",{className:"flex justify-between",children:[n.jsx("span",{className:"text-sm text-gray-500",children:"Category:"}),n.jsx("span",{className:"font-medium text-gray-900",children:e.category})]}),n.jsxs("div",{className:"flex justify-between",children:[n.jsx("span",{className:"text-sm text-gray-500",children:"Paid by:"}),n.jsx("span",{className:"font-medium text-gray-900 capitalize",children:e.paidById})]}),n.jsxs("div",{className:"flex justify-between",children:[n.jsx("span",{className:"text-sm text-gray-500",children:"Date:"}),n.jsx("span",{className:"font-medium text-gray-900",children:new Date(e.date).toLocaleDateString()})]})]})}),i.isError&&n.jsx("div",{className:"text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-lg",children:"Failed to delete expense. Please try again."}),n.jsxs("div",{className:"flex gap-3",children:[n.jsx("button",{type:"button",onClick:r,className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors",disabled:i.isPending,children:"Cancel"}),n.jsx("button",{onClick:async()=>{try{await i.mutateAsync(e.id),s?.(),r()}catch(t){}},className:"flex-1 px-4 py-2 bg-red-600 text-white hover:bg-red-700 rounded-lg transition-colors flex items-center justify-center gap-2",disabled:i.isPending,children:i.isPending?n.jsxs(n.Fragment,{children:[n.jsx(z,{className:"h-4 w-4 animate-spin"}),"Deleting..."]}):n.jsxs(n.Fragment,{children:[n.jsx(M,{className:"h-4 w-4"}),"Delete Expense"]})})]})]})]})}):null}function dt({filters:e,onFiltersChange:t,onClearFilters:r,showAdvanced:a=!0,className:l=""}){const[s,i]=o.useState(!1),c=o.useCallback(((n,r)=>{t({...e,[n]:r||void 0})}),[e,t]),u=Object.values(e).some((e=>void 0!==e&&""!==e&&null!==e));return n.jsxs("div",{className:`bg-white rounded-lg shadow-sm border border-gray-200 ${l}`,children:[n.jsx("div",{className:"p-4",children:n.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4",children:[n.jsxs("div",{className:"flex-1 relative",children:[n.jsx(A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),n.jsx("input",{type:"text",placeholder:"Search expenses...",value:e.search||"",onChange:e=>c("search",e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),n.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[n.jsxs("select",{value:e.category||"",onChange:e=>c("category",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",children:[n.jsx("option",{value:"",children:"All Categories"}),Object.values(tt).map((e=>n.jsx("option",{value:e,children:e},e)))]}),n.jsxs("select",{value:e.paidBy||"",onChange:e=>c("paidBy",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",children:[n.jsx("option",{value:"",children:"All Users"}),n.jsx("option",{value:nt,children:"Taha"}),n.jsx("option",{value:rt,children:"Burak"})]}),n.jsxs("select",{value:e.sortBy||"date",onChange:e=>c("sortBy",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",children:[n.jsx("option",{value:"date",children:"Sort by Date"}),n.jsx("option",{value:"amount",children:"Sort by Amount"}),n.jsx("option",{value:"description",children:"Sort by Description"}),n.jsx("option",{value:"category",children:"Sort by Category"}),n.jsx("option",{value:"paidBy",children:"Sort by Paid By"})]}),n.jsx("button",{onClick:()=>{c("sortOrder","asc"===e.sortOrder?"desc":"asc")},className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm flex items-center gap-1",title:"Sort "+("asc"===e.sortOrder?"Ascending":"Descending"),children:"asc"===e.sortOrder?n.jsx(O,{className:"h-4 w-4"}):n.jsx(B,{className:"h-4 w-4"})}),a&&n.jsxs("button",{onClick:()=>i(!s),className:"px-3 py-2 border rounded-lg text-sm flex items-center gap-1 transition-colors "+(s?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:bg-gray-50"),children:[n.jsx(R,{className:"h-4 w-4"}),"Advanced"]}),u&&n.jsxs("button",{onClick:r,className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm flex items-center gap-1 text-gray-600",children:[n.jsx(F,{className:"h-4 w-4"}),"Clear"]})]})]})}),a&&s&&n.jsxs("div",{className:"border-t border-gray-200 p-4 bg-gray-50",children:[n.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[n.jsxs("div",{children:[n.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[n.jsx(_,{className:"inline h-4 w-4 mr-1"}),"Start Date"]}),n.jsx("input",{type:"date",value:e.startDate||"",onChange:e=>c("startDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"})]}),n.jsxs("div",{children:[n.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[n.jsx(_,{className:"inline h-4 w-4 mr-1"}),"End Date"]}),n.jsx("input",{type:"date",value:e.endDate||"",onChange:e=>c("endDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"})]}),n.jsxs("div",{children:[n.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[n.jsx(I,{className:"inline h-4 w-4 mr-1"}),"Min Amount"]}),n.jsx("input",{type:"number",step:"0.01",min:"0",placeholder:"0.00",value:e.minAmount||"",onChange:e=>c("minAmount",parseFloat(e.target.value)||void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"})]}),n.jsxs("div",{children:[n.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[n.jsx(I,{className:"inline h-4 w-4 mr-1"}),"Max Amount"]}),n.jsx("input",{type:"number",step:"0.01",min:"0",placeholder:"999.99",value:e.maxAmount||"",onChange:e=>c("maxAmount",parseFloat(e.target.value)||void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"})]})]}),u&&n.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:n.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[n.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Active filters:"}),e.search&&n.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800",children:['Search: "',e.search,'"']}),e.category&&n.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800",children:["Category: ",e.category]}),e.paidBy&&n.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800",children:["Paid by: ","taha"===e.paidBy?"Taha":"Burak"]}),(e.startDate||e.endDate)&&n.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800",children:["Date: ",e.startDate||"..."," to ",e.endDate||"..."]}),(e.minAmount||e.maxAmount)&&n.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800",children:["Amount: $",e.minAmount||"0"," - $",e.maxAmount||"∞"]})]})})]})]})}function ft({filters:e,onFiltersChange:t,onClearFilters:r,placeholder:a="Search expenses...",className:l="",showQuickFilters:s=!0}){const[i,c]=o.useState(e.search||"");o.useEffect((()=>{const n=setTimeout((()=>{i!==e.search&&t({...e,search:i||void 0})}),300);return()=>clearTimeout(n)}),[i,e,t]);const u=o.useCallback(((n,r)=>{t({...e,[n]:r||void 0})}),[e,t]),d=Object.values(e).some((e=>void 0!==e&&""!==e&&null!==e));return n.jsxs("div",{className:`space-y-3 ${l}`,children:[n.jsxs("div",{className:"relative",children:[n.jsx(A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),n.jsx("input",{type:"text",placeholder:a,value:i,onChange:e=>c(e.target.value),className:"w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),i&&n.jsx("button",{onClick:()=>{c(""),t({...e,search:void 0})},className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:n.jsx(F,{className:"h-4 w-4"})})]}),s&&n.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[n.jsxs("div",{className:"flex items-center gap-1",children:[n.jsx("span",{className:"text-xs text-gray-500",children:"Category:"}),n.jsx("button",{onClick:()=>u("category","Food"===e.category?void 0:"Food"),className:"px-2 py-1 text-xs rounded-full transition-colors "+("Food"===e.category?"bg-blue-100 text-blue-700 border border-blue-200":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:"Food"}),n.jsx("button",{onClick:()=>u("category","Groceries"===e.category?void 0:"Groceries"),className:"px-2 py-1 text-xs rounded-full transition-colors "+("Groceries"===e.category?"bg-blue-100 text-blue-700 border border-blue-200":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:"Groceries"}),n.jsx("button",{onClick:()=>u("category","Transportation"===e.category?void 0:"Transportation"),className:"px-2 py-1 text-xs rounded-full transition-colors "+("Transportation"===e.category?"bg-blue-100 text-blue-700 border border-blue-200":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:"Transport"})]}),n.jsxs("div",{className:"flex items-center gap-1",children:[n.jsx("span",{className:"text-xs text-gray-500",children:"Paid by:"}),n.jsx("button",{onClick:()=>u("paidBy",e.paidBy===nt?void 0:nt),className:"px-2 py-1 text-xs rounded-full transition-colors "+(e.paidBy===nt?"bg-green-100 text-green-700 border border-green-200":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:"Taha"}),n.jsx("button",{onClick:()=>u("paidBy",e.paidBy===rt?void 0:rt),className:"px-2 py-1 text-xs rounded-full transition-colors "+(e.paidBy===rt?"bg-purple-100 text-purple-700 border border-purple-200":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:"Burak"})]}),d&&n.jsxs("button",{onClick:r,className:"px-2 py-1 text-xs text-red-600 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors flex items-center gap-1",children:[n.jsx(F,{className:"h-3 w-3"}),"Clear all"]})]}),d&&n.jsxs("div",{className:"text-xs text-gray-500",children:[Object.values(e).filter((e=>void 0!==e&&""!==e&&null!==e)).length," filter(s) active"]})]})}function mt({title:e,subtitle:t,loading:r=!1,error:a,height:l=400,responsive:s=!0,exportable:i=!1,className:c="",children:u}){const[d,f]=o.useState(!1),m=()=>{};return n.jsxs("div",{className:`bg-white rounded-lg shadow-sm border border-gray-200 ${c}`,children:[n.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-100",children:[n.jsxs("div",{className:"flex-1",children:[n.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:e}),t&&n.jsx("p",{className:"text-sm text-gray-500 mt-1",children:t})]}),n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("button",{onClick:m,className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors",title:"Refresh chart",children:n.jsx(D,{className:"h-4 w-4"})}),i&&n.jsx("button",{onClick:async()=>{if(i){f(!0);try{await new Promise((e=>setTimeout(e,1e3)))}catch(e){}finally{f(!1)}}},disabled:d||r||!!a,className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Export chart",children:n.jsx($,{className:"h-4 w-4 "+(d?"animate-pulse":"")})})]})]}),n.jsx("div",{className:"p-4",style:{height:s?"auto":l,minHeight:s?l:"auto"},children:r?n.jsx(ht,{height:l}):a?n.jsx(pt,{error:a,onRetry:m}):n.jsx("div",{className:"w-full h-full",children:u})})]})}function ht({height:e}){return n.jsx("div",{className:"flex items-center justify-center",style:{height:e},children:n.jsxs("div",{className:"text-center",children:[n.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Loading chart data..."})]})})}function pt({error:e,onRetry:t}){return n.jsx("div",{className:"flex items-center justify-center h-full min-h-[200px]",children:n.jsxs("div",{className:"text-center max-w-sm",children:[n.jsx("div",{className:"h-12 w-12 bg-red-100 rounded-lg mx-auto mb-4 flex items-center justify-center",children:n.jsx(v,{className:"h-6 w-6 text-red-600"})}),n.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Failed to load chart"}),n.jsx("p",{className:"text-sm text-gray-500 mb-4",children:e}),n.jsxs("button",{onClick:t,className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[n.jsx(D,{className:"h-4 w-4 mr-2"}),"Try again"]})]})})}const gt={primary:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6","#06B6D4","#F97316","#84CC16"],neutral:["#F9FAFB","#F3F4F6","#E5E7EB","#D1D5DB","#9CA3AF","#6B7280","#4B5563","#374151","#1F2937","#111827"]},xt={family:'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',sizes:{small:12}},bt={categories:{Food:"#3B82F6",Groceries:"#10B981",Transportation:"#F59E0B",Utilities:"#EF4444",Entertainment:"#8B5CF6",Healthcare:"#06B6D4",Shopping:"#F97316",Travel:"#84CC16",Other:"#6B7280"},users:{taha:"#3B82F6",burak:"#10B981"},balance:{positive:"#10B981",negative:"#EF4444",zero:"#6B7280"}},yt=e=>bt.categories[e]||gt.neutral[5],vt=e=>bt.users[e]||gt.primary[0],wt=e=>e>0?bt.balance.positive:e<0?bt.balance.negative:bt.balance.zero,jt=6048e5,Nt=6e4,kt=36e5,St=Symbol.for("constructDateFrom");function Ct(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&St in e?e[St](t):e instanceof Date?new e.constructor(t):new Date(t)}function Et(e,t){return Ct(t||e,e)}let Tt={};function Pt(){return Tt}function Dt(e,t){const n=Pt(),r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,a=Et(e,t?.in),l=a.getDay(),s=(l<r?7:0)+l-r;return a.setDate(a.getDate()-s),a.setHours(0,0,0,0),a}function Ft(e,t){return Dt(e,{...t,weekStartsOn:1})}function zt(e,t){const n=Et(e,t?.in),r=n.getFullYear(),a=Ct(n,0);a.setFullYear(r+1,0,4),a.setHours(0,0,0,0);const l=Ft(a),s=Ct(n,0);s.setFullYear(r,0,4),s.setHours(0,0,0,0);const i=Ft(s);return n.getTime()>=l.getTime()?r+1:n.getTime()>=i.getTime()?r:r-1}function Lt(e){const t=Et(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function Mt(e,t){const n=Et(e,t?.in);return n.setHours(0,0,0,0),n}function At(e,t,n){const[r,a]=function(e,...t){const n=Ct.bind(null,t.find((e=>"object"==typeof e)));return t.map(n)}(0,e,t),l=Mt(r),s=Mt(a),i=+l-Lt(l),o=+s-Lt(s);return Math.round((i-o)/864e5)}function Ot(e){return!(!((t=e)instanceof Date||"object"==typeof t&&"[object Date]"===Object.prototype.toString.call(t))&&"number"!=typeof e||isNaN(+Et(e)));var t}const Bt={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function Rt(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const _t={date:Rt({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:Rt({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:Rt({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},It={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function $t(e){return(t,n)=>{let r;if("formatting"===(n?.context?String(n.context):"standalone")&&e.formattingValues){const t=e.defaultFormattingWidth||e.defaultWidth,a=n?.width?String(n.width):t;r=e.formattingValues[a]||e.formattingValues[t]}else{const t=e.defaultWidth,a=n?.width?String(n.width):e.defaultWidth;r=e.values[a]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function Ut(e){return(t,n={})=>{const r=n.width,a=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],l=t.match(a);if(!l)return null;const s=l[0],i=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],o=Array.isArray(i)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n;return}(i,(e=>e.test(s))):function(e,t){for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n;return}(i,(e=>e.test(s)));let c;c=e.valueCallback?e.valueCallback(o):o,c=n.valueCallback?n.valueCallback(c):c;return{value:c,rest:t.slice(s.length)}}}var Wt;const Ht={code:"en-US",formatDistance:(e,t,n)=>{let r;const a=Bt[e];return r="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",t.toString()),n?.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r},formatLong:_t,formatRelative:(e,t,n,r)=>It[e],localize:{ordinalNumber:(e,t)=>{const n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:$t({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:$t({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:$t({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:$t({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:$t({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(Wt={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)},(e,t={})=>{const n=e.match(Wt.matchPattern);if(!n)return null;const r=n[0],a=e.match(Wt.parsePattern);if(!a)return null;let l=Wt.valueCallback?Wt.valueCallback(a[0]):a[0];return l=t.valueCallback?t.valueCallback(l):l,{value:l,rest:e.slice(r.length)}}),era:Ut({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:Ut({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:Ut({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Ut({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Ut({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function qt(e,t){const n=Et(e,t?.in),r=At(n,function(e,t){const n=Et(e,t?.in);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}(n));return r+1}function Yt(e,t){const n=Et(e,t?.in),r=+Ft(n)-+function(e,t){const n=zt(e,t),r=Ct(e,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),Ft(r)}(n);return Math.round(r/jt)+1}function Qt(e,t){const n=Et(e,t?.in),r=n.getFullYear(),a=Pt(),l=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??a.firstWeekContainsDate??a.locale?.options?.firstWeekContainsDate??1,s=Ct(t?.in||e,0);s.setFullYear(r+1,0,l),s.setHours(0,0,0,0);const i=Dt(s,t),o=Ct(t?.in||e,0);o.setFullYear(r,0,l),o.setHours(0,0,0,0);const c=Dt(o,t);return+n>=+i?r+1:+n>=+c?r:r-1}function Vt(e,t){const n=Et(e,t?.in),r=+Dt(n,t)-+function(e,t){const n=Pt(),r=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,a=Qt(e,t),l=Ct(t?.in||e,0);return l.setFullYear(a,0,r),l.setHours(0,0,0,0),Dt(l,t)}(n,t);return Math.round(r/jt)+1}function Kt(e,t){return(e<0?"-":"")+Math.abs(e).toString().padStart(t,"0")}const Gt={y(e,t){const n=e.getFullYear(),r=n>0?n:1-n;return Kt("yy"===t?r%100:r,t.length)},M(e,t){const n=e.getMonth();return"M"===t?String(n+1):Kt(n+1,2)},d:(e,t)=>Kt(e.getDate(),t.length),a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(e,t)=>Kt(e.getHours()%12||12,t.length),H:(e,t)=>Kt(e.getHours(),t.length),m:(e,t)=>Kt(e.getMinutes(),t.length),s:(e,t)=>Kt(e.getSeconds(),t.length),S(e,t){const n=t.length,r=e.getMilliseconds();return Kt(Math.trunc(r*Math.pow(10,n-3)),t.length)}},Xt="midnight",Jt="noon",Zt="morning",en="afternoon",tn="evening",nn="night",rn={G:function(e,t,n){const r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if("yo"===t){const t=e.getFullYear(),r=t>0?t:1-t;return n.ordinalNumber(r,{unit:"year"})}return Gt.y(e,t)},Y:function(e,t,n,r){const a=Qt(e,r),l=a>0?a:1-a;if("YY"===t){return Kt(l%100,2)}return"Yo"===t?n.ordinalNumber(l,{unit:"year"}):Kt(l,t.length)},R:function(e,t){return Kt(zt(e),t.length)},u:function(e,t){return Kt(e.getFullYear(),t.length)},Q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return Kt(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return Kt(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){const r=e.getMonth();switch(t){case"M":case"MM":return Gt.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){const r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return Kt(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){const a=Vt(e,r);return"wo"===t?n.ordinalNumber(a,{unit:"week"}):Kt(a,t.length)},I:function(e,t,n){const r=Yt(e);return"Io"===t?n.ordinalNumber(r,{unit:"week"}):Kt(r,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getDate(),{unit:"date"}):Gt.d(e,t)},D:function(e,t,n){const r=qt(e);return"Do"===t?n.ordinalNumber(r,{unit:"dayOfYear"}):Kt(r,t.length)},E:function(e,t,n){const r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){const a=e.getDay(),l=(a-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(l);case"ee":return Kt(l,2);case"eo":return n.ordinalNumber(l,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){const a=e.getDay(),l=(a-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(l);case"cc":return Kt(l,t.length);case"co":return n.ordinalNumber(l,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,n){const r=e.getDay(),a=0===r?7:r;switch(t){case"i":return String(a);case"ii":return Kt(a,t.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){const r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){const r=e.getHours();let a;switch(a=12===r?Jt:0===r?Xt:r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(e,t,n){const r=e.getHours();let a;switch(a=r>=17?tn:r>=12?en:r>=4?Zt:nn,t){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),n.ordinalNumber(t,{unit:"hour"})}return Gt.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getHours(),{unit:"hour"}):Gt.H(e,t)},K:function(e,t,n){const r=e.getHours()%12;return"Ko"===t?n.ordinalNumber(r,{unit:"hour"}):Kt(r,t.length)},k:function(e,t,n){let r=e.getHours();return 0===r&&(r=24),"ko"===t?n.ordinalNumber(r,{unit:"hour"}):Kt(r,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):Gt.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getSeconds(),{unit:"second"}):Gt.s(e,t)},S:function(e,t){return Gt.S(e,t)},X:function(e,t,n){const r=e.getTimezoneOffset();if(0===r)return"Z";switch(t){case"X":return ln(r);case"XXXX":case"XX":return sn(r);default:return sn(r,":")}},x:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"x":return ln(r);case"xxxx":case"xx":return sn(r);default:return sn(r,":")}},O:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+an(r,":");default:return"GMT"+sn(r,":")}},z:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+an(r,":");default:return"GMT"+sn(r,":")}},t:function(e,t,n){return Kt(Math.trunc(+e/1e3),t.length)},T:function(e,t,n){return Kt(+e,t.length)}};function an(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),a=Math.trunc(r/60),l=r%60;return 0===l?n+String(a):n+String(a)+t+Kt(l,2)}function ln(e,t){if(e%60==0){return(e>0?"-":"+")+Kt(Math.abs(e)/60,2)}return sn(e,t)}function sn(e,t=""){const n=e>0?"-":"+",r=Math.abs(e);return n+Kt(Math.trunc(r/60),2)+t+Kt(r%60,2)}const on=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},cn=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},un={p:cn,P:(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],r=n[1],a=n[2];if(!a)return on(e,t);let l;switch(r){case"P":l=t.dateTime({width:"short"});break;case"PP":l=t.dateTime({width:"medium"});break;case"PPP":l=t.dateTime({width:"long"});break;default:l=t.dateTime({width:"full"})}return l.replace("{{date}}",on(r,t)).replace("{{time}}",cn(a,t))}},dn=/^D+$/,fn=/^Y+$/,mn=["D","DD","YY","YYYY"];const hn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,pn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,gn=/^'([^]*?)'?$/,xn=/''/g,bn=/[a-zA-Z]/;function yn(e,t,n){const r=Pt(),a=r.locale??Ht,l=r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,s=r.weekStartsOn??r.locale?.options?.weekStartsOn??0,i=Et(e,n?.in);if(!Ot(i))throw new RangeError("Invalid time value");let o=t.match(pn).map((e=>{const t=e[0];if("p"===t||"P"===t){return(0,un[t])(e,a.formatLong)}return e})).join("").match(hn).map((e=>{if("''"===e)return{isToken:!1,value:"'"};const t=e[0];if("'"===t)return{isToken:!1,value:vn(e)};if(rn[t])return{isToken:!0,value:e};if(t.match(bn))throw new RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}}));a.localize.preprocessor&&(o=a.localize.preprocessor(i,o));const c={firstWeekContainsDate:l,weekStartsOn:s,locale:a};return o.map((n=>{if(!n.isToken)return n.value;const r=n.value;(function(e){return fn.test(e)}(r)||function(e){return dn.test(e)}(r))&&function(e,t,n){const r=function(e,t,n){const r="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,n);if(mn.includes(e))throw new RangeError(r)}(r,t,String(e));return(0,rn[r[0]])(i,r,a.localize,c)})).join("")}function vn(e){const t=e.match(gn);return t?t[1].replace(xn,"'"):e}function wn(e,t){const n=()=>Ct(t?.in,NaN),r=function(e){const t={},n=e.split(jn.dateTimeDelimiter);let r;if(n.length>2)return t;/:/.test(n[0])?r=n[0]:(t.date=n[0],r=n[1],jn.timeZoneDelimiter.test(t.date)&&(t.date=e.split(jn.timeZoneDelimiter)[0],r=e.substr(t.date.length,e.length)));if(r){const e=jn.timezone.exec(r);e?(t.time=r.replace(e[1],""),t.timezone=e[1]):t.time=r}return t}(e);let a;if(r.date){const e=function(e,t){const n=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),r=e.match(n);if(!r)return{year:NaN,restDateString:""};const a=r[1]?parseInt(r[1]):null,l=r[2]?parseInt(r[2]):null;return{year:null===l?a:100*l,restDateString:e.slice((r[1]||r[2]).length)}}(r.date,2);a=function(e,t){if(null===t)return new Date(NaN);const n=e.match(Nn);if(!n)return new Date(NaN);const r=!!n[4],a=Cn(n[1]),l=Cn(n[2])-1,s=Cn(n[3]),i=Cn(n[4]),o=Cn(n[5])-1;if(r)return function(e,t,n){return t>=1&&t<=53&&n>=0&&n<=6}(0,i,o)?function(e,t,n){const r=new Date(0);r.setUTCFullYear(e,0,4);const a=r.getUTCDay()||7,l=7*(t-1)+n+1-a;return r.setUTCDate(r.getUTCDate()+l),r}(t,i,o):new Date(NaN);{const e=new Date(0);return function(e,t,n){return t>=0&&t<=11&&n>=1&&n<=(Tn[t]||(Pn(e)?29:28))}(t,l,s)&&function(e,t){return t>=1&&t<=(Pn(e)?366:365)}(t,a)?(e.setUTCFullYear(t,l,Math.max(a,s)),e):new Date(NaN)}}(e.restDateString,e.year)}if(!a||isNaN(+a))return n();const l=+a;let s,i=0;if(r.time&&(i=function(e){const t=e.match(kn);if(!t)return NaN;const n=En(t[1]),r=En(t[2]),a=En(t[3]);if(!function(e,t,n){if(24===e)return 0===t&&0===n;return n>=0&&n<60&&t>=0&&t<60&&e>=0&&e<25}(n,r,a))return NaN;return n*kt+r*Nt+1e3*a}(r.time),isNaN(i)))return n();if(!r.timezone){const e=new Date(l+i),n=Et(0,t?.in);return n.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),n.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),n}return s=function(e){if("Z"===e)return 0;const t=e.match(Sn);if(!t)return 0;const n="+"===t[1]?-1:1,r=parseInt(t[2]),a=t[3]&&parseInt(t[3])||0;if(!function(e,t){return t>=0&&t<=59}(0,a))return NaN;return n*(r*kt+a*Nt)}(r.timezone),isNaN(s)?n():Et(l+i+s,t?.in)}const jn={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Nn=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,kn=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,Sn=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Cn(e){return e?parseInt(e):1}function En(e){return e&&parseFloat(e.replace(",","."))||0}const Tn=[31,null,31,30,31,30,31,31,30,31,30,31];function Pn(e){return e%400==0||e%4==0&&e%100!=0}const Dn=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),Fn=e=>`${e.toFixed(1)}%`,zn=(e,t="month")=>{const n=wn(e);switch(t){case"month":default:return yn(n,"MMM yyyy");case"day":return yn(n,"MMM dd");case"year":return yn(n,"yyyy")}};function Ln({active:e,payload:t,label:r,formatter:a,labelFormatter:l}){return e&&t&&t.length?n.jsxs("div",{className:"bg-white p-3 border border-gray-200 rounded-lg shadow-lg",children:[r&&n.jsx("p",{className:"text-sm font-medium text-gray-900 mb-2",children:l?l(r):r}),t.map(((e,t)=>n.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[n.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),n.jsxs("span",{className:"text-gray-600",children:[e.name,":"]}),n.jsx("span",{className:"font-medium text-gray-900",children:a?a(e.value,e.name)[0]:e.value})]},t)))]}):null}function Mn({data:e,height:t=400,showComparison:r=!0,onDataPointClick:a}){const l=e.map((e=>({...e,month:zn(e.month+"-01","month"),totalExpenses:Number(e.totalExpenses.toFixed(2)),tahaExpenses:Number(e.tahaExpenses.toFixed(2)),burakExpenses:Number(e.burakExpenses.toFixed(2))})));return n.jsx(mt,{title:"Monthly Expense Trends",subtitle:"Track spending patterns over time",height:t,exportable:!0,children:n.jsx(ne,{width:"100%",height:"100%",children:n.jsxs(re,{data:l,margin:{top:20,right:30,left:20,bottom:60},onClick:e=>{a&&a(e)},children:[n.jsx(ae,{strokeDasharray:"3 3",stroke:gt.neutral[2],strokeWidth:1}),n.jsx(le,{dataKey:"month",fontSize:xt.sizes.small,fontFamily:xt.family,fill:gt.neutral[6],angle:-45,textAnchor:"end",height:80}),n.jsx(se,{fontSize:xt.sizes.small,fontFamily:xt.family,fill:gt.neutral[6],tickFormatter:e=>Dn(e)}),n.jsx(ie,{content:n.jsx(Ln,{formatter:(e,t)=>[Dn(e),"totalExpenses"===t?"Total":"tahaExpenses"===t?"Taha":"burakExpenses"===t?"Burak":t]})}),n.jsx(oe,{wrapperStyle:{fontSize:xt.sizes.small,fontFamily:xt.family,paddingTop:"20px"}}),n.jsx(ce,{type:"monotone",dataKey:"totalExpenses",stroke:gt.primary[0],strokeWidth:3,dot:{fill:gt.primary[0],strokeWidth:2,r:4},activeDot:{r:6,stroke:gt.primary[0],strokeWidth:2},name:"Total Expenses"}),r&&n.jsxs(n.Fragment,{children:[n.jsx(ce,{type:"monotone",dataKey:"tahaExpenses",stroke:vt("taha"),strokeWidth:2,strokeDasharray:"5 5",dot:{fill:vt("taha"),strokeWidth:2,r:3},activeDot:{r:5,stroke:vt("taha"),strokeWidth:2},name:"Taha's Expenses"}),n.jsx(ce,{type:"monotone",dataKey:"burakExpenses",stroke:vt("burak"),strokeWidth:2,strokeDasharray:"5 5",dot:{fill:vt("burak"),strokeWidth:2,r:3},activeDot:{r:5,stroke:vt("burak"),strokeWidth:2},name:"Burak's Expenses"})]})]})})})}function An({data:e,height:t=400,showPercentages:r=!0,innerRadius:a=60,outerRadius:l=120,onSliceClick:s}){const i=e.map((e=>({...e,amount:Number(e.amount.toFixed(2)),percentage:Number(e.percentage.toFixed(1))})));return n.jsxs(mt,{title:"Expense Categories",subtitle:"Breakdown of spending by category",height:t,exportable:!0,children:[n.jsx(ne,{width:"100%",height:"100%",children:n.jsxs(ue,{children:[n.jsx(de,{data:i,cx:"50%",cy:"50%",labelLine:!1,label:e=>r&&e.percentage>5?`${e.percentage.toFixed(1)}%`:"",outerRadius:l,innerRadius:a,fill:"#8884d8",dataKey:"amount",onClick:e=>{s&&s(e)},style:{cursor:s?"pointer":"default"},children:i.map(((e,t)=>n.jsx(fe,{fill:e.color,stroke:gt.neutral[1],strokeWidth:2},`cell-${t}`)))}),n.jsx(ie,{content:n.jsx(Ln,{formatter:(e,t,n)=>"amount"===t?[Dn(e),`${n.payload.category} (${Fn(n.payload.percentage)})`]:[e,t]})}),n.jsx(oe,{content:e=>{const{payload:t}=e;return n.jsx("div",{className:"flex flex-wrap justify-center gap-4 mt-4",children:t.map(((e,t)=>n.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[n.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),n.jsxs("span",{className:"text-gray-700",children:[e.value," (",Dn(i[t]?.amount||0),")"]})]},t)))})}})]})}),n.jsxs("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[n.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-gray-500",children:"Categories"}),n.jsx("div",{className:"text-lg font-semibold text-gray-900",children:i.length})]}),n.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-gray-500",children:"Total Amount"}),n.jsx("div",{className:"text-lg font-semibold text-gray-900",children:Dn(i.reduce(((e,t)=>e+t.amount),0))})]}),n.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-gray-500",children:"Top Category"}),n.jsx("div",{className:"text-lg font-semibold text-gray-900",children:i[0]?.category||"None"})]}),n.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-gray-500",children:"Avg per Category"}),n.jsx("div",{className:"text-lg font-semibold text-gray-900",children:Dn(i.reduce(((e,t)=>e+t.amount),0)/i.length||0)})]})]})]})}function On({data:e,height:t=400,showDifference:r=!0,stackedView:a=!1,onBarClick:l}){const s=e.map((e=>({...e,period:zn(e.period+"-01","month"),taha:Number(e.taha.toFixed(2)),burak:Number(e.burak.toFixed(2)),difference:Number(e.difference.toFixed(2)),absDifference:Math.abs(Number(e.difference.toFixed(2)))})));return n.jsxs(mt,{title:"User Spending Comparison",subtitle:"Compare expenses between Taha and Burak",height:t,exportable:!0,children:[n.jsx(ne,{width:"100%",height:"100%",children:n.jsxs(me,{data:s,margin:{top:20,right:30,left:20,bottom:60},onClick:e=>{l&&l(e)},children:[n.jsx(ae,{strokeDasharray:"3 3",stroke:gt.neutral[2],strokeWidth:1}),n.jsx(le,{dataKey:"period",fontSize:xt.sizes.small,fontFamily:xt.family,fill:gt.neutral[6],angle:-45,textAnchor:"end",height:80}),n.jsx(se,{fontSize:xt.sizes.small,fontFamily:xt.family,fill:gt.neutral[6],tickFormatter:e=>Dn(e)}),n.jsx(ie,{content:n.jsx(Ln,{formatter:(e,t)=>{const n=Dn(e);let r=t;switch(t){case"taha":r="Taha";break;case"burak":r="Burak";break;case"difference":case"absDifference":r="Difference"}return[n,r]}})}),n.jsx(oe,{wrapperStyle:{fontSize:xt.sizes.small,fontFamily:xt.family,paddingTop:"20px"}}),n.jsx(he,{dataKey:"taha",fill:vt("taha"),name:"Taha",radius:[2,2,0,0],stackId:a?"stack":void 0}),n.jsx(he,{dataKey:"burak",fill:vt("burak"),name:"Burak",radius:[2,2,0,0],stackId:a?"stack":void 0}),r&&!a&&n.jsx(he,{dataKey:"absDifference",fill:gt.neutral[4],name:"Difference",radius:[2,2,0,0]})]})}),n.jsxs("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[n.jsxs("div",{className:"bg-blue-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-blue-600",children:"Taha Total"}),n.jsx("div",{className:"text-lg font-semibold text-blue-900",children:Dn(s.reduce(((e,t)=>e+t.taha),0))})]}),n.jsxs("div",{className:"bg-green-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-green-600",children:"Burak Total"}),n.jsx("div",{className:"text-lg font-semibold text-green-900",children:Dn(s.reduce(((e,t)=>e+t.burak),0))})]}),n.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-gray-500",children:"Net Difference"}),n.jsx("div",{className:"text-lg font-semibold text-gray-900",children:Dn(s.reduce(((e,t)=>e+t.difference),0))})]}),n.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-gray-500",children:"Periods"}),n.jsx("div",{className:"text-lg font-semibold text-gray-900",children:s.length})]})]})]})}function Bn({data:e,height:t=400,showSettlements:r=!0,onPointClick:a}){const l=e.map((e=>({...e,date:zn(e.date,"day"),balance:Number(e.balance.toFixed(2)),cumulativeExpenses:Number(e.cumulativeExpenses.toFixed(2)),settlements:Number(e.settlements.toFixed(2)),runningBalance:Number(e.runningBalance.toFixed(2))}))),s=l[l.length-1]?.runningBalance||0,i=s>0?"Taha owes Burak":s<0?"Burak owes Taha":"Balanced";return n.jsxs(mt,{title:"Balance History",subtitle:"Track balance changes over time",height:t,exportable:!0,children:[n.jsx(ne,{width:"100%",height:"100%",children:n.jsxs(pe,{data:l,margin:{top:20,right:30,left:20,bottom:60},onClick:e=>{a&&a(e)},children:[n.jsx(ae,{strokeDasharray:"3 3",stroke:gt.neutral[2],strokeWidth:1}),n.jsx(le,{dataKey:"date",fontSize:xt.sizes.small,fontFamily:xt.family,fill:gt.neutral[6],angle:-45,textAnchor:"end",height:80}),n.jsx(se,{fontSize:xt.sizes.small,fontFamily:xt.family,fill:gt.neutral[6],tickFormatter:e=>Dn(e)}),n.jsx(ie,{content:n.jsx(Ln,{formatter:(e,t)=>{const n=Dn(e);let r=t;switch(t){case"balance":r="Balance";break;case"cumulativeExpenses":r="Total Expenses";break;case"settlements":r="Settlements";break;case"runningBalance":r="Running Balance"}return[n,r]},labelFormatter:e=>`Date: ${e}`})}),n.jsx(oe,{wrapperStyle:{fontSize:xt.sizes.small,fontFamily:xt.family,paddingTop:"20px"}}),n.jsx(ge,{y:0,stroke:gt.neutral[4],strokeDasharray:"2 2",strokeWidth:2}),n.jsx(xe,{type:"monotone",dataKey:"runningBalance",stroke:wt(s),fill:wt(s),fillOpacity:.3,strokeWidth:3,name:"Running Balance",dot:{fill:wt(s),strokeWidth:2,r:4},activeDot:{r:6,stroke:wt(s),strokeWidth:2}}),r&&n.jsx(xe,{type:"monotone",dataKey:"settlements",stroke:gt.primary[2],fill:gt.primary[2],fillOpacity:.1,strokeWidth:2,strokeDasharray:"5 5",name:"Settlements",dot:{fill:gt.primary[2],strokeWidth:2,r:3}})]})}),n.jsxs("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[n.jsxs("div",{className:"rounded-lg p-3 "+(s>0?"bg-red-50":s<0?"bg-green-50":"bg-gray-50"),children:[n.jsx("div",{className:"text-sm "+(s>0?"text-red-600":s<0?"text-green-600":"text-gray-500"),children:"Current Status"}),n.jsx("div",{className:"text-lg font-semibold "+(s>0?"text-red-900":s<0?"text-green-900":"text-gray-900"),children:i})]}),n.jsxs("div",{className:"bg-blue-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-blue-600",children:"Current Balance"}),n.jsx("div",{className:"text-lg font-semibold text-blue-900",children:Dn(Math.abs(s))})]}),n.jsxs("div",{className:"bg-purple-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-purple-600",children:"Total Expenses"}),n.jsx("div",{className:"text-lg font-semibold text-purple-900",children:Dn(l[l.length-1]?.cumulativeExpenses||0)})]}),n.jsxs("div",{className:"bg-orange-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-orange-600",children:"Total Settlements"}),n.jsx("div",{className:"text-lg font-semibold text-orange-900",children:Dn(l[l.length-1]?.settlements||0)})]})]})]})}function Rn({data:e,height:t=400,maxCategories:r=5,showTrends:a=!0,onCategoryClick:l}){const s=e.slice(0,r).map((e=>({...e,amount:Number(e.amount.toFixed(2)),percentage:Number(e.percentage.toFixed(1)),avgAmount:Number(e.avgAmount.toFixed(2))}))),i=e=>{l&&l(e)},o=e=>{switch(e){case"up":return n.jsx(H,{className:"h-4 w-4 text-green-600"});case"down":return n.jsx(W,{className:"h-4 w-4 text-red-600"});default:return n.jsx(U,{className:"h-4 w-4 text-gray-400"})}};return n.jsxs(mt,{title:`Top ${r} Categories`,subtitle:"Highest spending categories",height:t,exportable:!0,children:[n.jsx(ne,{width:"100%",height:"100%",children:n.jsxs(me,{data:s,layout:"horizontal",margin:{top:20,right:30,left:80,bottom:20},onClick:i,children:[n.jsx(ae,{strokeDasharray:"3 3",stroke:gt.neutral[2],strokeWidth:1}),n.jsx(le,{type:"number",fontSize:xt.sizes.small,fontFamily:xt.family,fill:gt.neutral[6],tickFormatter:e=>Dn(e)}),n.jsx(se,{type:"category",dataKey:"category",fontSize:xt.sizes.small,fontFamily:xt.family,fill:gt.neutral[6],width:70}),n.jsx(ie,{content:n.jsx(Ln,{formatter:(e,t,n)=>{const r=Dn(e),a=n.payload;return[r,`${a.category} (${Fn(a.percentage)})`]}})}),n.jsx(he,{dataKey:"amount",radius:[0,4,4,0],name:"Amount",children:s.map(((e,t)=>n.jsx(fe,{fill:yt(e.category)},`cell-${t}`)))})]})}),n.jsxs("div",{className:"mt-4 space-y-3",children:[n.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Category Breakdown"}),s.map((e=>n.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer",onClick:()=>i(e),children:[n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:yt(e.category)}}),n.jsx("span",{className:"font-medium text-gray-900",children:e.category})]}),a&&n.jsx("div",{className:"flex items-center gap-1",children:o(e.trend||"stable")})]}),n.jsxs("div",{className:"flex items-center gap-4 text-sm",children:[n.jsxs("div",{className:"text-right",children:[n.jsx("div",{className:"font-semibold text-gray-900",children:Dn(e.amount)}),n.jsx("div",{className:"text-gray-500",children:Fn(e.percentage)})]}),n.jsxs("div",{className:"text-right",children:[n.jsxs("div",{className:"text-gray-600",children:[e.count," expense",1!==e.count?"s":""]}),n.jsxs("div",{className:"text-gray-500",children:["Avg: ",Dn(e.avgAmount)]})]})]})]},e.category)))]}),n.jsxs("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[n.jsxs("div",{className:"bg-blue-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-blue-600",children:"Top Category"}),n.jsx("div",{className:"text-lg font-semibold text-blue-900",children:s[0]?.category||"None"})]}),n.jsxs("div",{className:"bg-green-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-green-600",children:"Total Amount"}),n.jsx("div",{className:"text-lg font-semibold text-green-900",children:Dn(s.reduce(((e,t)=>e+t.amount),0))})]}),n.jsxs("div",{className:"bg-purple-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-purple-600",children:"Categories"}),n.jsx("div",{className:"text-lg font-semibold text-purple-900",children:s.length})]}),n.jsxs("div",{className:"bg-orange-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-orange-600",children:"Avg per Category"}),n.jsx("div",{className:"text-lg font-semibold text-orange-900",children:Dn(s.reduce(((e,t)=>e+t.amount),0)/s.length||0)})]})]})]})}function _n({data:e,height:t=400,onCellClick:r}){const a=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],l=[{start:0,end:5,label:"Night\n(12-6 AM)"},{start:6,end:11,label:"Morning\n(6-12 PM)"},{start:12,end:17,label:"Afternoon\n(12-6 PM)"},{start:18,end:23,label:"Evening\n(6-12 AM)"}],s=(()=>{const t={};return a.forEach(((e,n)=>{l.forEach(((e,r)=>{t[`${n}-${r}`]={dayOfWeek:n,hour:r,amount:0,count:0,dayName:a[n],timeSlot:l[r].label.split("\n")[0]}}))})),e.forEach((e=>{const n=l.findIndex((t=>e.hour>=t.start&&e.hour<=t.end));if(-1!==n){const r=`${e.dayOfWeek}-${n}`;t[r]&&(t[r].amount+=e.amount,t[r].count+=e.count)}})),t})(),i=Math.max(...Object.values(s).map((e=>e.amount))),o=e=>{if(0===e)return gt.neutral[1];return`rgba(59, 130, 246, ${Math.max(.1,e)})`};return n.jsx(mt,{title:"Spending Time Patterns",subtitle:"When do you spend the most?",height:t,exportable:!0,children:n.jsxs("div",{className:"p-4",children:[n.jsxs("div",{className:"grid grid-cols-5 gap-2 mb-4",children:[n.jsx("div",{className:"text-xs font-medium text-gray-500 text-center"}),l.map(((e,t)=>n.jsxs("div",{className:"text-xs font-medium text-gray-500 text-center",children:[e.label.split("\n")[0],n.jsx("br",{}),n.jsx("span",{className:"text-gray-400",children:e.label.split("\n")[1]})]},t))),a.map(((e,t)=>n.jsxs(d.Fragment,{children:[n.jsx("div",{className:"text-xs font-medium text-gray-500 flex items-center",children:e}),l.map(((e,a)=>{const l=s[`${t}-${a}`],c=(u=l.amount,0===i?0:u/i);var u;return n.jsxs("div",{className:"aspect-square rounded-lg border border-gray-200 flex items-center justify-center cursor-pointer hover:border-blue-400 transition-colors group relative",style:{backgroundColor:o(c)},onClick:()=>{return e=l,void(r&&r(e));var e},children:[n.jsxs("div",{className:"text-center",children:[n.jsx("div",{className:"text-xs font-medium text-gray-900",children:l.count>0?l.count:""}),l.amount>0&&n.jsx("div",{className:"text-xs text-gray-600",children:Dn(l.amount)})]}),n.jsxs("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10",children:[l.dayName," ",l.timeSlot,n.jsx("br",{}),l.count," expense",1!==l.count?"s":"",": ",Dn(l.amount)]})]},`${t}-${a}`)}))]},t)))]}),n.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 mb-4",children:[n.jsx("span",{children:"Less"}),n.jsx("div",{className:"flex items-center gap-1",children:[0,.2,.4,.6,.8,1].map(((e,t)=>n.jsx("div",{className:"w-3 h-3 rounded border border-gray-200",style:{backgroundColor:o(e)}},t)))}),n.jsx("span",{children:"More"})]}),n.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[n.jsxs("div",{className:"bg-blue-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-blue-600",children:"Peak Day"}),n.jsx("div",{className:"text-lg font-semibold text-blue-900",children:Object.values(s).reduce(((e,t)=>t.amount>e.amount?t:e),{amount:0,dayName:"None"}).dayName})]}),n.jsxs("div",{className:"bg-green-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-green-600",children:"Peak Time"}),n.jsx("div",{className:"text-lg font-semibold text-green-900",children:Object.values(s).reduce(((e,t)=>t.amount>e.amount?t:e),{amount:0,timeSlot:"None"}).timeSlot})]}),n.jsxs("div",{className:"bg-purple-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-purple-600",children:"Total Expenses"}),n.jsx("div",{className:"text-lg font-semibold text-purple-900",children:Object.values(s).reduce(((e,t)=>e+t.count),0)})]}),n.jsxs("div",{className:"bg-orange-50 rounded-lg p-3",children:[n.jsx("div",{className:"text-sm text-orange-600",children:"Total Amount"}),n.jsx("div",{className:"text-lg font-semibold text-orange-900",children:Dn(Object.values(s).reduce(((e,t)=>e+t.amount),0))})]})]})]})})}const In="http://138.197.8.183:3001";function $n(e){return r({queryKey:["analytics","monthly",e],queryFn:async()=>{const t=new URLSearchParams;e?.startDate&&t.append("startDate",e.startDate),e?.endDate&&t.append("endDate",e.endDate),e?.granularity&&t.append("granularity",e.granularity);const n=await fetch(`${In}/api/analytics/monthly?${t}`);if(!n.ok)throw new Error("Failed to fetch monthly analytics");const r=await n.json();if(!r.success)throw new Error(r.error||"Failed to fetch monthly analytics");return r.data},staleTime:3e5,refetchOnWindowFocus:!1})}function Un(e){return r({queryKey:["analytics","categories",e],queryFn:async()=>{const t=new URLSearchParams;e?.period&&t.append("period",e.period),e?.groupBy&&t.append("groupBy",e.groupBy);const n=await fetch(`${In}/api/analytics/categories?${t}`);if(!n.ok)throw new Error("Failed to fetch category analytics");const r=await n.json();if(!r.success)throw new Error(r.error||"Failed to fetch category analytics");return r.data},staleTime:3e5,refetchOnWindowFocus:!1})}function Wn(e){return r({queryKey:["analytics","users",e],queryFn:async()=>{const t=new URLSearchParams;e?.period&&t.append("period",e.period),e?.granularity&&t.append("granularity",e.granularity);const n=await fetch(`${In}/api/analytics/users?${t}`);if(!n.ok)throw new Error("Failed to fetch user analytics");const r=await n.json();if(!r.success)throw new Error(r.error||"Failed to fetch user analytics");return r.data},staleTime:3e5,refetchOnWindowFocus:!1})}function Hn({className:e=""}){const[t,r]=o.useState({period:"all",granularity:"month"}),{monthly:a,categories:l,users:s,isLoading:i,error:c}=function(e){const t=$n(e),n=Un(e),r=Wn(e);return{monthly:{data:t.data||[],isLoading:t.isLoading,error:t.error,refetch:t.refetch},categories:{data:n.data||[],isLoading:n.isLoading,error:n.error,refetch:n.refetch},users:{data:r.data||[],isLoading:r.isLoading,error:r.error,refetch:r.refetch},isLoading:t.isLoading||n.isLoading||r.isLoading,error:t.error||n.error||r.error,refetchAll:()=>{t.refetch(),n.refetch(),r.refetch()}}}(t);return c?n.jsx("div",{className:`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${e}`,children:n.jsxs("div",{className:"text-center",children:[n.jsx("div",{className:"h-12 w-12 bg-red-100 rounded-lg mx-auto mb-4 flex items-center justify-center",children:n.jsx(H,{className:"h-6 w-6 text-red-600"})}),n.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Failed to load analytics"}),n.jsx("p",{className:"text-sm text-gray-500 mb-4",children:c instanceof Error?c.message:"An error occurred while loading chart data"}),n.jsx("button",{onClick:()=>window.location.reload(),className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Try again"})]})}):n.jsxs("div",{className:`space-y-6 ${e}`,children:[n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Analytics Dashboard"}),n.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Visual insights into your expense patterns"})]}),n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx(_,{className:"h-4 w-4 text-gray-500"}),n.jsx("select",{value:t.period,onChange:e=>{return t=e.target.value,void r((e=>({...e,period:t})));var t},className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",children:[{value:"1month",label:"1 Month"},{value:"3months",label:"3 Months"},{value:"6months",label:"6 Months"},{value:"1year",label:"1 Year"},{value:"all",label:"All Time"}].map((e=>n.jsx("option",{value:e.value,children:e.label},e.value)))})]})]}),n.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[n.jsx("div",{className:"lg:col-span-2",children:i?n.jsx(qn,{title:"Monthly Expense Trends",icon:n.jsx(H,{className:"h-5 w-5"})}):n.jsx(Mn,{data:a.data,height:350,showComparison:!0,onDataPointClick:e=>{}})}),n.jsx("div",{children:i?n.jsx(qn,{title:"Expense Categories",icon:n.jsx(q,{className:"h-5 w-5"})}):n.jsx(An,{data:l.data,height:400,showPercentages:!0,onSliceClick:e=>{}})}),n.jsx("div",{children:i?n.jsx(qn,{title:"User Spending Comparison",icon:n.jsx(C,{className:"h-5 w-5"})}):n.jsx(On,{data:s.data,height:400,showDifference:!0,onBarClick:e=>{}})})]}),!i&&n.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[n.jsxs("div",{className:"bg-blue-50 rounded-lg p-4 text-center",children:[n.jsx("div",{className:"text-2xl font-bold text-blue-900",children:a.data.length}),n.jsx("div",{className:"text-sm text-blue-600",children:"Months Tracked"})]}),n.jsxs("div",{className:"bg-green-50 rounded-lg p-4 text-center",children:[n.jsx("div",{className:"text-2xl font-bold text-green-900",children:l.data.length}),n.jsx("div",{className:"text-sm text-green-600",children:"Categories"})]}),n.jsxs("div",{className:"bg-purple-50 rounded-lg p-4 text-center",children:[n.jsx("div",{className:"text-2xl font-bold text-purple-900",children:l.data[0]?.category||"None"}),n.jsx("div",{className:"text-sm text-purple-600",children:"Top Category"})]}),n.jsxs("div",{className:"bg-orange-50 rounded-lg p-4 text-center",children:[n.jsx("div",{className:"text-2xl font-bold text-orange-900",children:s.data.length}),n.jsx("div",{className:"text-sm text-orange-600",children:"Periods"})]})]})]})}function qn({title:e,icon:t}){return n.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[n.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[t,n.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:e})]}),n.jsxs("div",{className:"animate-pulse",children:[n.jsx("div",{className:"h-64 bg-gray-200 rounded-lg"}),n.jsxs("div",{className:"mt-4 grid grid-cols-4 gap-4",children:[n.jsx("div",{className:"h-16 bg-gray-100 rounded-lg"}),n.jsx("div",{className:"h-16 bg-gray-100 rounded-lg"}),n.jsx("div",{className:"h-16 bg-gray-100 rounded-lg"}),n.jsx("div",{className:"h-16 bg-gray-100 rounded-lg"})]})]})]})}const Yn=()=>{const[e,t]=o.useState({limit:4,sortBy:"date",sortOrder:"desc"}),{data:r=[],isLoading:a,error:l}=Ke(),{data:s=[],isLoading:i,error:c}=Ke(e),[f,m]=o.useState(null),[h,p]=o.useState(null),g=d.useMemo((()=>function(e){const t=e.filter((e=>e.paidById===nt)).reduce(((e,t)=>e+t.amount),0),n=e.filter((e=>e.paidById===rt)).reduce(((e,t)=>e+t.amount),0),r=t+n;let a,l=0;return e.forEach((e=>{e.paidById===nt?l+=e.amount:e.paidById===rt&&(l-=e.amount)})),a=Math.abs(l)<.01?"balanced":l>0?"burak_owes_taha":"taha_owes_burak",{tahaTotal:t,burakTotal:n,combinedTotal:r,netBalance:Math.abs(l),whoOwesWhom:a}}(r)),[r]),x=d.useMemo((()=>st(r,nt)),[r]),b=d.useMemo((()=>st(r,rt)),[r]),y=a||i,v=l||c;return n.jsxs("div",{className:"p-4 md:p-8",children:[n.jsxs("div",{className:"max-w-7xl mx-auto",children:[n.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-8",children:[n.jsxs("div",{className:"mb-4 sm:mb-0",children:[n.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900",children:"Dashboard"}),n.jsx("p",{className:"text-gray-600 mt-1",children:"Welcome back! Here's your expense overview."})]}),n.jsx(u,{href:"/add-expense",children:n.jsxs("a",{className:"inline-flex items-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors",children:[n.jsx(N,{className:"h-4 w-4 mr-2"}),"Add Expense"]})})]}),n.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8",children:[v&&n.jsx("div",{className:"col-span-full bg-red-50 border border-red-200 rounded-lg p-4",children:n.jsxs("p",{className:"text-red-600 text-sm",children:["Failed to load expense data: ",v.message]})}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-1",children:"Taha's Expenses"}),n.jsx("p",{className:"text-2xl font-bold text-blue-600",children:y?n.jsx("div",{className:"h-8 bg-blue-100 rounded animate-pulse w-20"}):it(g.tahaTotal)}),n.jsx("p",{className:"text-xs text-gray-400 mt-1",children:y?n.jsx("div",{className:"h-3 bg-gray-200 rounded animate-pulse w-16"}):`${x.expenseCount} expense${1!==x.expenseCount?"s":""}`})]}),n.jsx("div",{className:"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center",children:n.jsx(Y,{className:"h-5 w-5 text-blue-600"})})]})}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-1",children:"Burak's Expenses"}),n.jsx("p",{className:"text-2xl font-bold text-green-600",children:y?n.jsx("div",{className:"h-8 bg-green-100 rounded animate-pulse w-20"}):it(g.burakTotal)}),n.jsx("p",{className:"text-xs text-gray-400 mt-1",children:y?n.jsx("div",{className:"h-3 bg-gray-200 rounded animate-pulse w-16"}):`${b.expenseCount} expense${1!==b.expenseCount?"s":""}`})]}),n.jsx("div",{className:"h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center",children:n.jsx(Y,{className:"h-5 w-5 text-green-600"})})]})}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-1",children:"Combined Total"}),n.jsx("p",{className:"text-2xl font-bold text-purple-600",children:y?n.jsx("div",{className:"h-8 bg-purple-100 rounded animate-pulse w-20"}):it(g.combinedTotal)}),n.jsx("p",{className:"text-xs text-gray-400 mt-1",children:y?n.jsx("div",{className:"h-3 bg-gray-200 rounded animate-pulse w-20"}):`${r.length} total expense${1!==r.length?"s":""}`})]}),n.jsx("div",{className:"h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center",children:n.jsx(H,{className:"h-5 w-5 text-purple-600"})})]})}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-1",children:"Net Balance"}),n.jsx("p",{className:"text-2xl font-bold text-orange-600",children:y?n.jsx("div",{className:"h-8 bg-orange-100 rounded animate-pulse w-20"}):it(g.netBalance)}),n.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"balanced"===g.whoOwesWhom?"All settled!":"taha_owes_burak"===g.whoOwesWhom?"Taha owes Burak":"Burak owes Taha"})]}),n.jsx("div",{className:"h-10 w-10 bg-orange-100 rounded-lg flex items-center justify-center",children:n.jsx(_,{className:"h-5 w-5 text-orange-600"})})]})})]}),n.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[n.jsxs("div",{className:"lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[n.jsxs("div",{className:"flex items-center justify-between mb-4",children:[n.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:"Recent Expenses"}),n.jsx(u,{href:"/history",children:n.jsx("a",{className:"text-sm text-blue-600 hover:text-blue-700",children:"View all"})})]}),n.jsx("div",{className:"mb-4",children:n.jsx(ft,{filters:e,onFiltersChange:t,onClearFilters:()=>t({limit:4,sortBy:"date",sortOrder:"desc"}),placeholder:"Search recent expenses...",showQuickFilters:!0})}),n.jsx("div",{className:"space-y-4",children:y?n.jsx("div",{className:"space-y-3",children:[1,2,3].map((e=>n.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[n.jsxs("div",{className:"flex-1",children:[n.jsx("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-3/4 mb-2"}),n.jsx("div",{className:"h-3 bg-gray-200 rounded animate-pulse w-1/2"})]}),n.jsx("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-16"})]},e)))}):v?n.jsxs("div",{className:"text-center py-8",children:[n.jsx("div",{className:"h-12 w-12 bg-red-100 rounded-lg mx-auto mb-3 flex items-center justify-center",children:n.jsx("span",{className:"text-red-500 text-xl",children:"⚠️"})}),n.jsx("p",{className:"text-red-600 mb-3",children:"Failed to load expenses"}),n.jsx("p",{className:"text-gray-500 text-sm",children:v.message})]}):0===s.length?n.jsxs("div",{className:"text-center py-8",children:[n.jsx("div",{className:"h-12 w-12 bg-gray-100 rounded-lg mx-auto mb-3 flex items-center justify-center",children:n.jsx(N,{className:"h-6 w-6 text-gray-400"})}),n.jsx("p",{className:"text-gray-500 mb-3",children:"No expenses found"}),n.jsx(u,{href:"/add-expense",children:n.jsx("a",{className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"Add your first expense"})})]}):n.jsxs("div",{className:"space-y-3",children:[s.map((e=>n.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group",children:[n.jsxs("div",{className:"flex-1",children:[n.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[n.jsx("p",{className:"font-medium text-gray-900",children:e.description}),n.jsx("span",{className:"px-2 py-1 text-xs rounded-full "+("taha"===e.paidById?"bg-blue-100 text-blue-700":"bg-green-100 text-green-700"),children:"taha"===e.paidById?"Taha":"Burak"})]}),n.jsxs("p",{className:"text-sm text-gray-500",children:[e.category," • ",new Date(e.date).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})]})]}),n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"text-right",children:n.jsx("p",{className:"font-semibold text-gray-900",children:it(e.amount)})}),n.jsxs("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[n.jsx("button",{onClick:()=>m(e),className:"p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors",title:"Edit expense",children:n.jsx(Q,{className:"h-4 w-4"})}),n.jsx("button",{onClick:()=>p(e),className:"p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors",title:"Delete expense",children:n.jsx(M,{className:"h-4 w-4"})})]})]})]},e.id))),r.length>s.length&&n.jsx("div",{className:"text-center pt-2",children:n.jsx(u,{href:"/history",children:n.jsxs("a",{className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:["View ",r.length-s.length," more expenses"]})})})]})})]}),n.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[n.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Quick Actions"}),n.jsxs("div",{className:"space-y-3",children:[n.jsx(u,{href:"/add-expense",children:n.jsxs("a",{className:"flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:[n.jsx(N,{className:"h-5 w-5 text-blue-600 mr-3"}),n.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Add Expense"})]})}),n.jsx(u,{href:"/settlement",children:n.jsxs("a",{className:"flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:[n.jsx(_,{className:"h-5 w-5 text-green-600 mr-3"}),n.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Settle Balance"})]})}),n.jsx(u,{href:"/reports",children:n.jsxs("a",{className:"flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:[n.jsx(H,{className:"h-5 w-5 text-purple-600 mr-3"}),n.jsx("span",{className:"text-sm font-medium text-gray-700",children:"View Reports"})]})})]})]})]}),n.jsx(Hn,{})]}),f&&n.jsx(ct,{expense:f,isOpen:!!f,onClose:()=>m(null),onSuccess:()=>{m(null)}}),h&&n.jsx(ut,{expense:h,isOpen:!!h,onClose:()=>p(null),onSuccess:()=>{p(null)}})]})},Qn=()=>{const[,e]=c(),t=function(){const e=a();return l({mutationFn:Ye,onSuccess:t=>{e.invalidateQueries({queryKey:Ve.lists()}),e.setQueryData(Ve.detail(t.id),t)},onError:e=>{}})}(),[r,s]=o.useState({amount:"",description:"",category:"",paidById:"",date:(new Date).toISOString().split("T")[0]}),[i,u]=o.useState({}),[d,f]=o.useState(!1),m=(e,t)=>{s((n=>({...n,[e]:t}))),i[e]&&u((t=>({...t,[e]:void 0})))};return n.jsx("div",{className:"max-w-2xl mx-auto",children:n.jsx("form",{onSubmit:async n=>{if(n.preventDefault(),(()=>{const e={},t=parseFloat(r.amount);if(r.amount.trim()?isNaN(t)||t<=0?e.amount="Amount must be a positive number":t>1e4&&(e.amount="Amount cannot exceed $10,000"):e.amount="Amount is required",r.description.trim()?r.description.trim().length<3?e.description="Description must be at least 3 characters":r.description.trim().length>100&&(e.description="Description cannot exceed 100 characters"):e.description="Description is required",r.category||(e.category="Category is required"),r.paidById||(e.paidById="Please select who paid for this expense"),r.date){const t=new Date(r.date),n=new Date,a=new Date;a.setFullYear(n.getFullYear()-1),t>n?e.date="Date cannot be in the future":t<a&&(e.date="Date cannot be more than a year ago")}else e.date="Date is required";return u(e),0===Object.keys(e).length})()){f(!0);try{const n={amount:parseFloat(r.amount),description:r.description.trim(),category:r.category,paidById:r.paidById,date:r.date};await t.mutateAsync(n),e("/")}catch(a){}finally{f(!1)}}},className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"space-y-6",children:[n.jsxs("div",{children:[n.jsxs("label",{htmlFor:"amount",className:"block text-sm font-medium text-gray-700 mb-2",children:[n.jsx(I,{className:"inline h-4 w-4 mr-1"}),"Amount"]}),n.jsxs("div",{className:"relative",children:[n.jsx("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500",children:"$"}),n.jsx("input",{type:"number",id:"amount",step:"0.01",min:"0",max:"10000",placeholder:"0.00",value:r.amount,onChange:e=>m("amount",e.target.value),className:"w-full pl-8 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.amount?"border-red-300":"border-gray-300")})]}),i.amount&&n.jsx("p",{className:"mt-1 text-sm text-red-600",children:i.amount})]}),n.jsxs("div",{children:[n.jsxs("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[n.jsx(V,{className:"inline h-4 w-4 mr-1"}),"Description"]}),n.jsx("input",{type:"text",id:"description",placeholder:"What was this expense for?",value:r.description,onChange:e=>m("description",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.description?"border-red-300":"border-gray-300"),maxLength:100}),n.jsxs("div",{className:"flex justify-between mt-1",children:[i.description?n.jsx("p",{className:"text-sm text-red-600",children:i.description}):n.jsx("p",{className:"text-sm text-gray-500",children:"Brief description of the expense"}),n.jsxs("p",{className:"text-sm text-gray-400",children:[r.description.length,"/100"]})]})]}),n.jsxs("div",{children:[n.jsx("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),n.jsxs("select",{id:"category",value:r.category,onChange:e=>m("category",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.category?"border-red-300":"border-gray-300"),children:[n.jsx("option",{value:"",children:"Select a category"}),at.map((e=>n.jsxs("option",{value:e.name,children:[e.icon," ",e.name]},e.name)))]}),i.category&&n.jsx("p",{className:"mt-1 text-sm text-red-600",children:i.category})]}),n.jsxs("div",{children:[n.jsxs("label",{htmlFor:"paidById",className:"block text-sm font-medium text-gray-700 mb-2",children:[n.jsx(x,{className:"inline h-4 w-4 mr-1"}),"Paid By"]}),n.jsxs("select",{id:"paidById",value:r.paidById,onChange:e=>m("paidById",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.paidById?"border-red-300":"border-gray-300"),children:[n.jsx("option",{value:"",children:"Who paid for this?"}),n.jsx("option",{value:nt,children:"Taha"}),n.jsx("option",{value:rt,children:"Burak"})]}),i.paidById&&n.jsx("p",{className:"mt-1 text-sm text-red-600",children:i.paidById})]}),n.jsxs("div",{children:[n.jsxs("label",{htmlFor:"date",className:"block text-sm font-medium text-gray-700 mb-2",children:[n.jsx(_,{className:"inline h-4 w-4 mr-1"}),"Date"]}),n.jsx("input",{type:"date",id:"date",value:r.date,onChange:e=>m("date",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.date?"border-red-300":"border-gray-300")}),i.date&&n.jsx("p",{className:"mt-1 text-sm text-red-600",children:i.date})]}),t.error&&n.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:n.jsxs("p",{className:"text-red-600 text-sm",children:["Failed to create expense: ",t.error.message]})}),n.jsxs("div",{className:"flex gap-3 pt-4",children:[n.jsx("button",{type:"submit",disabled:d||t.isPending,className:"flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:d||t.isPending?n.jsxs(n.Fragment,{children:[n.jsx("div",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Saving..."]}):n.jsxs(n.Fragment,{children:[n.jsx(L,{className:"h-4 w-4 mr-2"}),"Save Expense"]})}),n.jsxs("button",{type:"button",onClick:()=>{e("/")},disabled:d||t.isPending,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[n.jsx(F,{className:"h-4 w-4 mr-2 inline"}),"Cancel"]})]})]})})})},Vn=()=>{const[,e]=c(),t=function(){const e=a();return l({mutationFn:Je,onSuccess:t=>{e.invalidateQueries({queryKey:Ze.lists()}),e.setQueryData(Ze.detail(t.id),t),e.invalidateQueries({queryKey:["expenses"]})},onError:e=>{}})}(),[r,s]=o.useState({amount:"",paidBy:"",paidTo:"",description:"",date:(new Date).toISOString().split("T")[0]}),[i,u]=o.useState({}),[d,f]=o.useState(!1),m=(e,t)=>{s((n=>({...n,[e]:t}))),i[e]&&u((t=>({...t,[e]:void 0}))),("paidBy"===e||"paidTo"===e)&&i.paidBy&&i.paidTo&&u((e=>({...e,paidBy:void 0,paidTo:void 0})))};return n.jsx("div",{className:"max-w-2xl mx-auto",children:n.jsx("form",{onSubmit:async n=>{if(n.preventDefault(),(()=>{const e={},t=parseFloat(r.amount);if(r.amount.trim()?isNaN(t)||t<=0?e.amount="Amount must be a positive number":t>1e4&&(e.amount="Amount cannot exceed $10,000"):e.amount="Amount is required",r.paidBy||(e.paidBy="Please select who is making the payment"),r.paidTo||(e.paidTo="Please select who is receiving the payment"),r.paidBy&&r.paidTo&&r.paidBy===r.paidTo&&(e.paidBy="Payer and recipient cannot be the same person",e.paidTo="Payer and recipient cannot be the same person"),r.description.trim().length>200&&(e.description="Description cannot exceed 200 characters"),r.date){const t=new Date(r.date),n=new Date,a=new Date;a.setFullYear(n.getFullYear()-1),t>n?e.date="Date cannot be in the future":t<a&&(e.date="Date cannot be more than a year ago")}else e.date="Date is required";return u(e),0===Object.keys(e).length})()){f(!0);try{const n={amount:parseFloat(r.amount),paidBy:r.paidBy,paidTo:r.paidTo,description:r.description.trim(),date:r.date};await t.mutateAsync(n),e("/")}catch(a){}finally{f(!1)}}},className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"space-y-6",children:[n.jsxs("div",{children:[n.jsxs("label",{htmlFor:"amount",className:"block text-sm font-medium text-gray-700 mb-2",children:[n.jsx(I,{className:"inline h-4 w-4 mr-1"}),"Settlement Amount"]}),n.jsxs("div",{className:"relative",children:[n.jsx("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500",children:"$"}),n.jsx("input",{type:"number",id:"amount",step:"0.01",min:"0",max:"10000",placeholder:"0.00",value:r.amount,onChange:e=>m("amount",e.target.value),className:"w-full pl-8 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.amount?"border-red-300":"border-gray-300")})]}),i.amount&&n.jsx("p",{className:"mt-1 text-sm text-red-600",children:i.amount})]}),n.jsxs("div",{children:[n.jsxs("label",{htmlFor:"paidBy",className:"block text-sm font-medium text-gray-700 mb-2",children:[n.jsx(Y,{className:"inline h-4 w-4 mr-1"}),"Who is Paying"]}),n.jsxs("select",{id:"paidBy",value:r.paidBy,onChange:e=>m("paidBy",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.paidBy?"border-red-300":"border-gray-300"),children:[n.jsx("option",{value:"",children:"Select who is making the payment"}),n.jsx("option",{value:nt,children:"Taha"}),n.jsx("option",{value:rt,children:"Burak"})]}),i.paidBy&&n.jsx("p",{className:"mt-1 text-sm text-red-600",children:i.paidBy})]}),n.jsxs("div",{children:[n.jsxs("label",{htmlFor:"paidTo",className:"block text-sm font-medium text-gray-700 mb-2",children:[n.jsx(Y,{className:"inline h-4 w-4 mr-1"}),"Who is Receiving"]}),n.jsxs("select",{id:"paidTo",value:r.paidTo,onChange:e=>m("paidTo",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.paidTo?"border-red-300":"border-gray-300"),children:[n.jsx("option",{value:"",children:"Select who is receiving the payment"}),n.jsx("option",{value:nt,children:"Taha"}),n.jsx("option",{value:rt,children:"Burak"})]}),i.paidTo&&n.jsx("p",{className:"mt-1 text-sm text-red-600",children:i.paidTo})]}),n.jsxs("div",{children:[n.jsxs("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[n.jsx(K,{className:"inline h-4 w-4 mr-1"}),"Description (Optional)"]}),n.jsx("textarea",{id:"description",rows:3,placeholder:"What is this settlement for? (optional)",value:r.description,onChange:e=>m("description",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.description?"border-red-300":"border-gray-300"),maxLength:200}),n.jsxs("div",{className:"flex justify-between mt-1",children:[i.description?n.jsx("p",{className:"text-sm text-red-600",children:i.description}):n.jsx("p",{className:"text-sm text-gray-500",children:"Brief description of the settlement"}),n.jsxs("p",{className:"text-sm text-gray-400",children:[r.description.length,"/200"]})]})]}),n.jsxs("div",{children:[n.jsxs("label",{htmlFor:"date",className:"block text-sm font-medium text-gray-700 mb-2",children:[n.jsx(_,{className:"inline h-4 w-4 mr-1"}),"Settlement Date"]}),n.jsx("input",{type:"date",id:"date",value:r.date,onChange:e=>m("date",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "+(i.date?"border-red-300":"border-gray-300")}),i.date&&n.jsx("p",{className:"mt-1 text-sm text-red-600",children:i.date})]}),t.error&&n.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:n.jsxs("p",{className:"text-red-600 text-sm",children:["Failed to create settlement: ",t.error.message]})}),n.jsxs("div",{className:"flex gap-3 pt-4",children:[n.jsx("button",{type:"submit",disabled:d||t.isPending,className:"flex-1 flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:d||t.isPending?n.jsxs(n.Fragment,{children:[n.jsx("div",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Processing..."]}):n.jsxs(n.Fragment,{children:[n.jsx(L,{className:"h-4 w-4 mr-2"}),"Record Settlement"]})}),n.jsxs("button",{type:"button",onClick:()=>{e("/")},disabled:d||t.isPending,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[n.jsx(F,{className:"h-4 w-4 mr-2 inline"}),"Cancel"]})]})]})})})},Kn=()=>n.jsx("div",{className:"p-8",children:n.jsxs("div",{className:"max-w-2xl mx-auto",children:[n.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Add New Expense"}),n.jsx(Qn,{})]})}),Gn=()=>n.jsx("div",{className:"p-8",children:n.jsxs("div",{className:"max-w-4xl mx-auto",children:[n.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Record Settlement"}),n.jsx("div",{className:"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4",children:n.jsxs("p",{className:"text-blue-800 text-sm",children:[n.jsx("strong",{children:"💡 Tip:"})," Use this form to record when one person pays back money to settle shared expenses."]})}),n.jsx(Vn,{})]})}),Xn=()=>{const[e,t]=o.useState({sortBy:"date",sortOrder:"desc"}),[r,a]=o.useState("all"),{data:l=[],isLoading:s}=Ke(e),{data:i=[],isLoading:c}=et(),u=o.useMemo((()=>[...l.map((e=>({id:`expense-${e.id}`,type:"expense",date:e.date,amount:e.amount,description:e.description,paidBy:e.paidById,category:e.category,createdAt:e.createdAt}))),...i.map((e=>({id:`settlement-${e.id}`,type:"settlement",date:e.date,amount:e.amount,description:e.description||"Settlement payment",paidBy:e.paidBy,paidTo:e.paidTo,createdAt:e.createdAt})))]),[l,i]),d=o.useMemo((()=>{let t=u;return"all"!==r&&(t=t.filter((e=>e.type===r))),t.sort(((t,n)=>{let r=0;switch(e.sortBy||"date"){case"date":default:r=new Date(t.date).getTime()-new Date(n.date).getTime();break;case"amount":r=t.amount-n.amount;break;case"description":r=t.description.localeCompare(n.description);break;case"category":r=(t.category||"").localeCompare(n.category||"");break;case"paidBy":r=(t.paidBy||"").localeCompare(n.paidBy||"")}return"asc"===(e.sortOrder||"desc")?r:-r})),t}),[u,r,e.sortBy,e.sortOrder]),f=o.useMemo((()=>{const e=l.reduce(((e,t)=>e+t.amount),0),t=i.reduce(((e,t)=>e+t.amount),0),n=l.length,r=i.length;return{totalExpenses:e,totalSettlements:t,expenseCount:n,settlementCount:r,totalTransactions:n+r}}),[l,i]),m=s||c;return n.jsx("div",{className:"p-8",children:n.jsxs("div",{className:"max-w-6xl mx-auto",children:[n.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:[n.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4 sm:mb-0",children:"Transaction History"}),n.jsxs("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors",children:[n.jsx($,{className:"h-4 w-4 mr-2"}),"Export Data"]})]}),n.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-1",children:"Total Expenses"}),n.jsx("p",{className:"text-2xl font-bold text-red-600",children:m?"...":it(f.totalExpenses)}),n.jsxs("p",{className:"text-xs text-gray-400 mt-1",children:[f.expenseCount," transaction",1!==f.expenseCount?"s":""]})]}),n.jsx("div",{className:"h-10 w-10 bg-red-100 rounded-lg flex items-center justify-center",children:n.jsx(W,{className:"h-5 w-5 text-red-600"})})]})}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-1",children:"Total Settlements"}),n.jsx("p",{className:"text-2xl font-bold text-green-600",children:m?"...":it(f.totalSettlements)}),n.jsxs("p",{className:"text-xs text-gray-400 mt-1",children:[f.settlementCount," payment",1!==f.settlementCount?"s":""]})]}),n.jsx("div",{className:"h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center",children:n.jsx(H,{className:"h-5 w-5 text-green-600"})})]})}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-1",children:"Net Activity"}),n.jsx("p",{className:"text-2xl font-bold text-purple-600",children:m?"...":it(f.totalExpenses-f.totalSettlements)}),n.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Outstanding balance"})]}),n.jsx("div",{className:"h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center",children:n.jsx(I,{className:"h-5 w-5 text-purple-600"})})]})}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-1",children:"Total Transactions"}),n.jsx("p",{className:"text-2xl font-bold text-blue-600",children:m?"...":f.totalTransactions}),n.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"All time"})]}),n.jsx("div",{className:"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center",children:n.jsx(Y,{className:"h-5 w-5 text-blue-600"})})]})})]}),n.jsx(dt,{filters:e,onFiltersChange:t,onClearFilters:()=>t({sortBy:"date",sortOrder:"desc"}),showAdvanced:!0,className:"mb-6"}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6",children:n.jsxs("div",{className:"flex items-center gap-4",children:[n.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Show:"}),n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx(R,{className:"h-4 w-4 text-gray-500"}),n.jsxs("select",{value:r,onChange:e=>a(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",children:[n.jsx("option",{value:"all",children:"All Transactions"}),n.jsx("option",{value:"expense",children:"Expenses Only"}),n.jsx("option",{value:"settlement",children:"Settlements Only"})]})]})]})}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:m?n.jsxs("div",{className:"p-8 text-center",children:[n.jsx("div",{className:"animate-spin h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"}),n.jsx("p",{className:"text-gray-500",children:"Loading transactions..."})]}):0===d.length?n.jsxs("div",{className:"p-8 text-center",children:[n.jsx("div",{className:"h-12 w-12 bg-gray-100 rounded-lg mx-auto mb-4 flex items-center justify-center",children:n.jsx(_,{className:"h-6 w-6 text-gray-400"})}),n.jsx("p",{className:"text-gray-500 mb-2",children:"No transactions found"}),n.jsx("p",{className:"text-gray-400 text-sm",children:e.search?"Try adjusting your search or filters":"Start by adding some expenses or settlements"})]}):n.jsx("div",{className:"overflow-x-auto",children:n.jsxs("table",{className:"w-full",children:[n.jsx("thead",{className:"bg-gray-50 border-b border-gray-200",children:n.jsxs("tr",{children:[n.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),n.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),n.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),n.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Participants"}),n.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"})]})}),n.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map((e=>n.jsxs("tr",{className:"hover:bg-gray-50",children:[n.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:n.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("expense"===e.type?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:"expense"===e.type?n.jsxs(n.Fragment,{children:[n.jsx(W,{className:"h-3 w-3 mr-1"}),"Expense"]}):n.jsxs(n.Fragment,{children:[n.jsx(H,{className:"h-3 w-3 mr-1"}),"Settlement"]})})}),n.jsxs("td",{className:"px-6 py-4",children:[n.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.description}),e.category&&n.jsx("div",{className:"text-sm text-gray-500",children:e.category})]}),n.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:n.jsx("div",{className:"text-sm font-semibold "+("expense"===e.type?"text-red-600":"text-green-600"),children:it(e.amount)})}),n.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:n.jsx("div",{className:"text-sm text-gray-900",children:"expense"===e.type?n.jsx("span",{className:"px-2 py-1 text-xs rounded-full "+("taha"===e.paidBy?"bg-blue-100 text-blue-700":"bg-green-100 text-green-700"),children:"taha"===e.paidBy?"Taha":"Burak"}):n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("span",{className:"px-2 py-1 text-xs rounded-full "+("taha"===e.paidBy?"bg-blue-100 text-blue-700":"bg-green-100 text-green-700"),children:"taha"===e.paidBy?"Taha":"Burak"}),n.jsx("span",{className:"text-gray-400",children:"→"}),n.jsx("span",{className:"px-2 py-1 text-xs rounded-full "+("taha"===e.paidTo?"bg-blue-100 text-blue-700":"bg-green-100 text-green-700"),children:"taha"===e.paidTo?"Taha":"Burak"})]})})}),n.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.date).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})})]},e.id)))})]})})}),!m&&d.length>0&&n.jsxs("div",{className:"mt-4 text-center text-sm text-gray-500",children:["Showing ",d.length," of ",u.length," transactions"]})]})})},Jn=()=>{const[e,t]=o.useState({period:"all",granularity:"month"}),{data:a=[],isLoading:l,error:s}=$n(e),{data:i=[],isLoading:c,error:u}=Un(e),{data:d=[],isLoading:f,error:m}=Wn(e),{data:h=[],isLoading:p,error:g}=function(e){return r({queryKey:["analytics","balance-history",e],queryFn:async()=>{const t=new URLSearchParams;e?.startDate&&t.append("startDate",e.startDate),e?.endDate&&t.append("endDate",e.endDate);const n=await fetch(`${In}/api/analytics/balance-history?${t}`);if(!n.ok)throw new Error("Failed to fetch balance history analytics");const r=await n.json();if(!r.success)throw new Error(r.error||"Failed to fetch balance history analytics");return r.data},staleTime:3e5,refetchOnWindowFocus:!1})}(e),{data:x=[],isLoading:b,error:y}=function(e){return r({queryKey:["analytics","time-patterns",e],queryFn:async()=>{const t=new URLSearchParams;e?.period&&t.append("period",e.period);const n=await fetch(`${In}/api/analytics/time-patterns?${t}`);if(!n.ok)throw new Error("Failed to fetch time pattern analytics");const r=await n.json();if(!r.success)throw new Error(r.error||"Failed to fetch time pattern analytics");return r.data},staleTime:3e5,refetchOnWindowFocus:!1})}(e),v=l||c||f||p||b,w=s||u||m||g||y;return w?n.jsx("div",{className:"min-h-screen bg-gray-50 p-6",children:n.jsx("div",{className:"max-w-7xl mx-auto",children:n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"text-center",children:[n.jsx("div",{className:"h-12 w-12 bg-red-100 rounded-lg mx-auto mb-4 flex items-center justify-center",children:n.jsx(H,{className:"h-6 w-6 text-red-600"})}),n.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Failed to load reports"}),n.jsx("p",{className:"text-sm text-gray-500 mb-4",children:w instanceof Error?w.message:"An error occurred while loading report data"}),n.jsx("button",{onClick:()=>window.location.reload(),className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:"Try again"})]})})})}):n.jsxs("div",{className:"min-h-screen bg-gray-50",children:[n.jsx("div",{className:"bg-white shadow-sm border-b border-gray-200",children:n.jsx("div",{className:"max-w-7xl mx-auto px-6 py-4",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Analytics Reports"}),n.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Comprehensive insights into your expense patterns and trends"})]}),n.jsxs("div",{className:"flex items-center gap-4",children:[n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx(_,{className:"h-4 w-4 text-gray-500"}),n.jsx("select",{value:e.period,onChange:e=>{return n=e.target.value,void t((e=>({...e,period:n})));var n},className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm",children:[{value:"1month",label:"1 Month"},{value:"3months",label:"3 Months"},{value:"6months",label:"6 Months"},{value:"1year",label:"1 Year"},{value:"all",label:"All Time"}].map((e=>n.jsx("option",{value:e.value,children:e.label},e.value)))})]}),n.jsxs("button",{onClick:()=>{},className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[n.jsx($,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})})}),n.jsxs("div",{className:"max-w-7xl mx-auto px-6 py-6",children:[n.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center",children:[n.jsx("div",{className:"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center",children:n.jsx(H,{className:"h-6 w-6 text-blue-600"})}),n.jsxs("div",{className:"ml-4",children:[n.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Total Months"}),n.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:v?"...":a.length})]})]})}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center",children:[n.jsx("div",{className:"h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center",children:n.jsx(q,{className:"h-6 w-6 text-green-600"})}),n.jsxs("div",{className:"ml-4",children:[n.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Categories"}),n.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:v?"...":i.length})]})]})}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center",children:[n.jsx("div",{className:"h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center",children:n.jsx(C,{className:"h-6 w-6 text-purple-600"})}),n.jsxs("div",{className:"ml-4",children:[n.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Top Category"}),n.jsx("p",{className:"text-lg font-semibold text-gray-900",children:v?"...":i[0]?.category||"None"})]})]})}),n.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:n.jsxs("div",{className:"flex items-center",children:[n.jsx("div",{className:"h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center",children:n.jsx(I,{className:"h-6 w-6 text-orange-600"})}),n.jsxs("div",{className:"ml-4",children:[n.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Balance Status"}),n.jsx("p",{className:"text-lg font-semibold text-gray-900",children:v?"...":h.length>0?h[h.length-1].runningBalance>0?"Taha Owes":h[h.length-1].runningBalance<0?"Burak Owes":"Balanced":"Balanced"})]})]})})]}),n.jsxs("div",{className:"space-y-8",children:[n.jsx("div",{children:v?n.jsx(Zn,{title:"Monthly Expense Trends"}):n.jsx(Mn,{data:a,height:400,showComparison:!0,onDataPointClick:e=>{}})}),n.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[n.jsx("div",{children:v?n.jsx(Zn,{title:"Category Breakdown"}):n.jsx(An,{data:i,height:450,showPercentages:!0,onSliceClick:e=>{}})}),n.jsx("div",{children:v?n.jsx(Zn,{title:"User Comparison"}):n.jsx(On,{data:d,height:450,showDifference:!0,onBarClick:e=>{}})})]}),n.jsx("div",{children:v?n.jsx(Zn,{title:"Balance History"}):n.jsx(Bn,{data:h,height:400,showSettlements:!0,onPointClick:e=>{}})}),n.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[n.jsx("div",{children:v?n.jsx(Zn,{title:"Top Categories"}):n.jsx(Rn,{data:i,height:450,maxCategories:5,showTrends:!0,onCategoryClick:e=>{}})}),n.jsx("div",{children:v?n.jsx(Zn,{title:"Time Patterns"}):n.jsx(_n,{data:x,height:450,onCellClick:e=>{}})})]})]})]})]})};function Zn({title:e}){return n.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[n.jsx("div",{className:"flex items-center gap-2 mb-4",children:n.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:e})}),n.jsxs("div",{className:"animate-pulse",children:[n.jsx("div",{className:"h-64 bg-gray-200 rounded-lg"}),n.jsxs("div",{className:"mt-4 grid grid-cols-4 gap-4",children:[n.jsx("div",{className:"h-16 bg-gray-100 rounded-lg"}),n.jsx("div",{className:"h-16 bg-gray-100 rounded-lg"}),n.jsx("div",{className:"h-16 bg-gray-100 rounded-lg"}),n.jsx("div",{className:"h-16 bg-gray-100 rounded-lg"})]})]})]})}const er=new s({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>!(t?.status>=400&&t?.status<500)&&e<3,refetchOnWindowFocus:!1},mutations:{retry:1}}}),tr=({children:e})=>n.jsx(i,{client:er,children:e}),nr=o.createContext(void 0),rr="nawras-admin-settings",ar=({children:e})=>{const[t,r]=o.useState((()=>{try{const e=localStorage.getItem(rr);if(e){const t=JSON.parse(e);return{...lt,...t}}}catch(e){}return lt}));o.useEffect((()=>{try{localStorage.setItem(rr,JSON.stringify(t))}catch(e){}}),[t]);const a={settings:t,updateSettings:e=>{r((t=>{const n={...t};return e.notifications&&(n.notifications={...t.notifications,...e.notifications}),e.profile&&(n.profile={...t.profile,...e.profile}),Object.keys(e).forEach((t=>{"notifications"!==t&&"profile"!==t&&(n[t]=e[t])})),n}))},resetSettings:()=>{r(lt)},exportSettings:()=>JSON.stringify(t,null,2),importSettings:e=>{try{const t=JSON.parse(e);if("object"==typeof t&&null!==t){const e={...lt,...t};return r(e),!0}return!1}catch(t){return!1}}};return n.jsx(nr.Provider,{value:a,children:e})},lr=()=>{const{settings:e,updateSettings:t,resetSettings:r,exportSettings:a,importSettings:l}=(()=>{const e=o.useContext(nr);if(void 0===e)throw new Error("useSettings must be used within a SettingsProvider");return e})(),[s,i]=o.useState("profile"),[c,u]=o.useState("idle"),[d,f]=o.useState(null),m=async e=>{u("saving");try{t(e),u("saved"),setTimeout((()=>u("idle")),2e3)}catch(n){u("error"),setTimeout((()=>u("idle")),3e3)}},h=[{id:"profile",label:"Profile",icon:x},{id:"display",label:"Display",icon:X},{id:"notifications",label:"Notifications",icon:J},{id:"privacy",label:"Privacy",icon:Z},{id:"data",label:"Data",icon:$}];return n.jsx("div",{className:"p-8",children:n.jsxs("div",{className:"max-w-6xl mx-auto",children:[n.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:[n.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4 sm:mb-0",children:"Settings"}),n.jsxs("div",{className:"flex items-center gap-2",children:["saving"===c&&n.jsxs("div",{className:"flex items-center text-blue-600 text-sm",children:[n.jsx("div",{className:"animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full mr-2"}),"Saving..."]}),"saved"===c&&n.jsxs("div",{className:"flex items-center text-green-600 text-sm",children:[n.jsx(G,{className:"h-4 w-4 mr-2"}),"Saved"]}),"error"===c&&n.jsxs("div",{className:"flex items-center text-red-600 text-sm",children:[n.jsx(F,{className:"h-4 w-4 mr-2"}),"Error saving"]})]})]}),n.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[n.jsx("div",{className:"lg:col-span-1",children:n.jsx("nav",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:n.jsx("ul",{className:"space-y-2",children:h.map((({id:e,label:t,icon:r})=>n.jsx("li",{children:n.jsxs("button",{onClick:()=>i(e),className:"w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors "+(s===e?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"),children:[n.jsx(r,{className:"h-4 w-4 mr-3"}),t]})},e)))})})}),n.jsx("div",{className:"lg:col-span-3",children:n.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:["profile"===s&&n.jsx("div",{className:"space-y-6",children:n.jsxs("div",{children:[n.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Profile Information"}),n.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),n.jsx("input",{type:"text",value:e.profile.name,onChange:t=>m({profile:{...e.profile,name:t.target.value}}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Your name"})]}),n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),n.jsx("input",{type:"email",value:e.profile.email,onChange:t=>m({profile:{...e.profile,email:t.target.value}}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>"})]}),n.jsxs("div",{className:"md:col-span-2",children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Timezone"}),n.jsxs("select",{value:e.profile.timezone,onChange:t=>m({profile:{...e.profile,timezone:t.target.value}}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[n.jsx("option",{value:"America/New_York",children:"Eastern Time (ET)"}),n.jsx("option",{value:"America/Chicago",children:"Central Time (CT)"}),n.jsx("option",{value:"America/Denver",children:"Mountain Time (MT)"}),n.jsx("option",{value:"America/Los_Angeles",children:"Pacific Time (PT)"}),n.jsx("option",{value:"Europe/London",children:"London (GMT)"}),n.jsx("option",{value:"Europe/Istanbul",children:"Istanbul (TRT)"}),n.jsx("option",{value:"Asia/Tokyo",children:"Tokyo (JST)"})]})]})]})]})}),"display"===s&&n.jsx("div",{className:"space-y-6",children:n.jsxs("div",{children:[n.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Display Preferences"}),n.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Currency"}),n.jsxs("select",{value:e.currency,onChange:e=>{const t=e.target.value,n=[{value:"USD",symbol:"$"},{value:"EUR",symbol:"€"},{value:"GBP",symbol:"£"},{value:"TRY",symbol:"₺"}].find((e=>e.value===t));m({currency:t,currencySymbol:n?.symbol||"$"})},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[n.jsx("option",{value:"USD",children:"US Dollar ($)"}),n.jsx("option",{value:"EUR",children:"Euro (€)"}),n.jsx("option",{value:"GBP",children:"British Pound (£)"}),n.jsx("option",{value:"TRY",children:"Turkish Lira (₺)"})]})]}),n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date Format"}),n.jsxs("select",{value:e.dateFormat,onChange:e=>m({dateFormat:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[n.jsx("option",{value:"MM/DD/YYYY",children:"MM/DD/YYYY (US)"}),n.jsx("option",{value:"DD/MM/YYYY",children:"DD/MM/YYYY (EU)"}),n.jsx("option",{value:"YYYY-MM-DD",children:"YYYY-MM-DD (ISO)"})]})]}),n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time Format"}),n.jsxs("select",{value:e.timeFormat,onChange:e=>m({timeFormat:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[n.jsx("option",{value:"12h",children:"12 Hour (AM/PM)"}),n.jsx("option",{value:"24h",children:"24 Hour"})]})]}),n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Theme"}),n.jsxs("select",{value:e.theme,onChange:e=>m({theme:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[n.jsx("option",{value:"light",children:"Light"}),n.jsx("option",{value:"dark",children:"Dark"}),n.jsx("option",{value:"system",children:"System"})]})]})]}),n.jsxs("div",{className:"mt-6 space-y-4",children:[n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Show Decimal Places"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Display amounts with decimal precision"})]}),n.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[n.jsx("input",{type:"checkbox",checked:e.showDecimalPlaces,onChange:e=>m({showDecimalPlaces:e.target.checked}),className:"sr-only peer"}),n.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Auto-save Expenses"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Automatically save expense drafts"})]}),n.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[n.jsx("input",{type:"checkbox",checked:e.autoSaveExpenses,onChange:e=>m({autoSaveExpenses:e.target.checked}),className:"sr-only peer"}),n.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]})]})}),"notifications"===s&&n.jsx("div",{className:"space-y-6",children:n.jsxs("div",{children:[n.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Notification Preferences"}),n.jsxs("div",{className:"space-y-4",children:[n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Email Notifications"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Receive notifications via email"})]}),n.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[n.jsx("input",{type:"checkbox",checked:e.notifications.emailNotifications,onChange:t=>m({notifications:{...e.notifications,emailNotifications:t.target.checked}}),className:"sr-only peer"}),n.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Push Notifications"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Receive browser push notifications"})]}),n.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[n.jsx("input",{type:"checkbox",checked:e.notifications.pushNotifications,onChange:t=>m({notifications:{...e.notifications,pushNotifications:t.target.checked}}),className:"sr-only peer"}),n.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Expense Reminders"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Remind me to log expenses"})]}),n.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[n.jsx("input",{type:"checkbox",checked:e.notifications.expenseReminders,onChange:t=>m({notifications:{...e.notifications,expenseReminders:t.target.checked}}),className:"sr-only peer"}),n.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Settlement Reminders"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Remind me about pending settlements"})]}),n.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[n.jsx("input",{type:"checkbox",checked:e.notifications.settlementReminders,onChange:t=>m({notifications:{...e.notifications,settlementReminders:t.target.checked}}),className:"sr-only peer"}),n.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Weekly Reports"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Receive weekly spending summaries"})]}),n.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[n.jsx("input",{type:"checkbox",checked:e.notifications.weeklyReports,onChange:t=>m({notifications:{...e.notifications,weeklyReports:t.target.checked}}),className:"sr-only peer"}),n.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Monthly Reports"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Receive monthly financial reports"})]}),n.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[n.jsx("input",{type:"checkbox",checked:e.notifications.monthlyReports,onChange:t=>m({notifications:{...e.notifications,monthlyReports:t.target.checked}}),className:"sr-only peer"}),n.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]})]})}),"privacy"===s&&n.jsx("div",{className:"space-y-6",children:n.jsxs("div",{children:[n.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Privacy & Security"}),n.jsxs("div",{className:"space-y-6",children:[n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Data Retention"}),n.jsxs("select",{value:e.dataRetention,onChange:e=>m({dataRetention:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[n.jsx("option",{value:"1year",children:"1 Year"}),n.jsx("option",{value:"2years",children:"2 Years"}),n.jsx("option",{value:"5years",children:"5 Years"}),n.jsx("option",{value:"forever",children:"Forever"})]}),n.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"How long to keep your expense and settlement data"})]}),n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{children:[n.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Share Analytics"}),n.jsx("p",{className:"text-sm text-gray-500",children:"Help improve the app by sharing anonymous usage data"})]}),n.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[n.jsx("input",{type:"checkbox",checked:e.shareAnalytics,onChange:e=>m({shareAnalytics:e.target.checked}),className:"sr-only peer"}),n.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),n.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:n.jsxs("div",{className:"flex",children:[n.jsx("div",{className:"flex-shrink-0",children:n.jsx(Z,{className:"h-5 w-5 text-yellow-400"})}),n.jsxs("div",{className:"ml-3",children:[n.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"Privacy Notice"}),n.jsx("div",{className:"mt-2 text-sm text-yellow-700",children:n.jsx("p",{children:"Your financial data is stored locally in your browser and never sent to external servers without your explicit consent. We take your privacy seriously."})})]})]})})]})]})}),"data"===s&&n.jsx("div",{className:"space-y-6",children:n.jsxs("div",{children:[n.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Data Management"}),n.jsxs("div",{className:"space-y-6",children:[n.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[n.jsxs("div",{className:"flex items-center mb-4",children:[n.jsx($,{className:"h-5 w-5 text-blue-600 mr-2"}),n.jsx("h3",{className:"text-lg font-medium text-blue-900",children:"Export Settings"})]}),n.jsx("p",{className:"text-sm text-blue-700 mb-4",children:"Download your settings as a JSON file for backup or transfer to another device."}),n.jsxs("button",{onClick:()=>{const e=a(),t=new Blob([e],{type:"application/json"}),n=URL.createObjectURL(t),r=document.createElement("a");r.href=n,r.download=`nawras-settings-${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(n)},className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors",children:[n.jsx($,{className:"h-4 w-4 mr-2"}),"Export Settings"]})]}),n.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[n.jsxs("div",{className:"flex items-center mb-4",children:[n.jsx(ee,{className:"h-5 w-5 text-green-600 mr-2"}),n.jsx("h3",{className:"text-lg font-medium text-green-900",children:"Import Settings"})]}),n.jsx("p",{className:"text-sm text-green-700 mb-4",children:"Upload a settings file to restore your preferences from a backup."}),n.jsxs("div",{className:"flex items-center gap-4",children:[n.jsx("input",{type:"file",accept:".json",onChange:e=>f(e.target.files?.[0]||null),className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-green-50 file:text-green-700 hover:file:bg-green-100"}),n.jsxs("button",{onClick:async()=>{if(d)try{const e=await d.text();l(e)?(u("saved"),f(null)):u("error"),setTimeout((()=>u("idle")),3e3)}catch(e){u("error"),setTimeout((()=>u("idle")),3e3)}},disabled:!d,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[n.jsx(ee,{className:"h-4 w-4 mr-2"}),"Import"]})]})]}),n.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[n.jsxs("div",{className:"flex items-center mb-4",children:[n.jsx(te,{className:"h-5 w-5 text-red-600 mr-2"}),n.jsx("h3",{className:"text-lg font-medium text-red-900",children:"Reset Settings"})]}),n.jsx("p",{className:"text-sm text-red-700 mb-4",children:"Reset all settings to their default values. This action cannot be undone."}),n.jsxs("button",{onClick:()=>{window.confirm("Are you sure you want to reset all settings to defaults? This action cannot be undone.")&&(r(),u("saved"),setTimeout((()=>u("idle")),2e3))},className:"flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors",children:[n.jsx(te,{className:"h-4 w-4 mr-2"}),"Reset to Defaults"]})]}),n.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6",children:[n.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Current Settings Summary"}),n.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[n.jsxs("div",{children:[n.jsx("span",{className:"font-medium text-gray-700",children:"Currency:"}),n.jsxs("span",{className:"ml-2 text-gray-600",children:[e.currency," (",e.currencySymbol,")"]})]}),n.jsxs("div",{children:[n.jsx("span",{className:"font-medium text-gray-700",children:"Date Format:"}),n.jsx("span",{className:"ml-2 text-gray-600",children:e.dateFormat})]}),n.jsxs("div",{children:[n.jsx("span",{className:"font-medium text-gray-700",children:"Theme:"}),n.jsx("span",{className:"ml-2 text-gray-600 capitalize",children:e.theme})]}),n.jsxs("div",{children:[n.jsx("span",{className:"font-medium text-gray-700",children:"Email Notifications:"}),n.jsx("span",{className:"ml-2 text-gray-600",children:e.notifications.emailNotifications?"Enabled":"Disabled"})]}),n.jsxs("div",{children:[n.jsx("span",{className:"font-medium text-gray-700",children:"Data Retention:"}),n.jsx("span",{className:"ml-2 text-gray-600 capitalize",children:e.dataRetention.replace("years"," years")})]}),n.jsxs("div",{children:[n.jsx("span",{className:"font-medium text-gray-700",children:"Profile Name:"}),n.jsx("span",{className:"ml-2 text-gray-600",children:e.profile.name||"Not set"})]})]})]})]})]})})]})})]})]})})},sr=new s({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,refetchOnWindowFocus:!1,retry:2,retryDelay:e=>Math.min(1e3*2**e,3e4)},mutations:{retry:1,retryDelay:1e3}}}),ir=()=>n.jsxs("div",{className:"flex h-screen bg-gray-100",children:[n.jsx(Re,{}),n.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[n.jsx($e,{}),n.jsx("main",{className:"flex-1 overflow-x-hidden overflow-y-auto pb-16 md:pb-0",children:n.jsxs(f,{children:[n.jsx(m,{path:"/",component:Yn}),n.jsx(m,{path:"/add-expense",component:Kn}),n.jsx(m,{path:"/settlement",component:Gn}),n.jsx(m,{path:"/history",component:Xn}),n.jsx(m,{path:"/reports",component:Jn}),n.jsx(m,{path:"/settings",component:lr}),n.jsx(m,{children:n.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:n.jsxs("div",{className:"text-center",children:[n.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-4",children:"404"}),n.jsx("p",{className:"text-gray-600",children:"Page not found"})]})})})]})})]}),n.jsx(Ie,{})]});function or(){return n.jsxs(i,{client:sr,children:[n.jsx(ze,{children:n.jsx(Ae,{children:n.jsx(ir,{})})}),n.jsx(Pe,{initialIsOpen:!1})]})}Te.createRoot(document.getElementById("root")).render(n.jsx(o.StrictMode,{children:n.jsx(ot,{children:n.jsx(ar,{children:n.jsx(tr,{children:n.jsx(or,{})})})})}));
