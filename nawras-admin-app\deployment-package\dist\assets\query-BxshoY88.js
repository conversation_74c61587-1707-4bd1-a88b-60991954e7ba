var t,e,s,i,n,r,a,o,h,u,c,l,d,f,p,y,v,m,b,g,w,O,M,k,C,S,R,P,E,q,F,x,Q,W,A,D,U,T,j,K,I,L,H,_,G,B,N,$,z,J,V,X,Y,Z,tt,et,st,it,nt,rt,at,ot,ht,ut,ct,lt,dt,ft,pt,yt,vt,mt,bt,gt,wt,Ot=t=>{throw TypeError(t)},Mt=(t,e,s)=>e.has(t)||Ot("Cannot "+s),kt=(t,e,s)=>(Mt(t,e,"read from private field"),s?s.call(t):e.get(t)),Ct=(t,e,s)=>e.has(t)?Ot("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,s),St=(t,e,s,i)=>(Mt(t,e,"write to private field"),i?i.call(t,s):e.set(t,s),s),Rt=(t,e,s)=>(Mt(t,e,"access private method"),s),Pt=(t,e,s,i)=>({set _(i){St(t,e,i,s)},get _(){return kt(t,e,i)}});import{r as Et}from"./router-DRdqKDkt.js";var qt,Ft,xt={exports:{}},Qt={};var Wt=(Ft||(Ft=1,xt.exports=function(){if(qt)return Qt;qt=1;var t=Symbol.for("react.transitional.element"),e=Symbol.for("react.fragment");function s(e,s,i){var n=null;if(void 0!==i&&(n=""+i),void 0!==s.key&&(n=""+s.key),"key"in s)for(var r in i={},s)"key"!==r&&(i[r]=s[r]);else i=s;return s=i.ref,{$$typeof:t,type:e,key:n,ref:void 0!==s?s:null,props:i}}return Qt.Fragment=e,Qt.jsx=s,Qt.jsxs=s,Qt}()),xt.exports),At=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Dt="undefined"==typeof window||"Deno"in globalThis;function Ut(){}function Tt(t){return"number"==typeof t&&t>=0&&t!==1/0}function jt(t,e){return Math.max(t+(e||0)-Date.now(),0)}function Kt(t,e){return"function"==typeof t?t(e):t}function It(t,e){return"function"==typeof t?t(e):t}function Lt(t,e){const{type:s="all",exact:i,fetchStatus:n,predicate:r,queryKey:a,stale:o}=t;if(a)if(i){if(e.queryHash!==_t(a,e.options))return!1}else if(!Bt(e.queryKey,a))return!1;if("all"!==s){const t=e.isActive();if("active"===s&&!t)return!1;if("inactive"===s&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&((!n||n===e.state.fetchStatus)&&!(r&&!r(e)))}function Ht(t,e){const{exact:s,status:i,predicate:n,mutationKey:r}=t;if(r){if(!e.options.mutationKey)return!1;if(s){if(Gt(e.options.mutationKey)!==Gt(r))return!1}else if(!Bt(e.options.mutationKey,r))return!1}return(!i||e.state.status===i)&&!(n&&!n(e))}function _t(t,e){return(e?.queryKeyHashFn||Gt)(t)}function Gt(t){return JSON.stringify(t,((t,e)=>Jt(e)?Object.keys(e).sort().reduce(((t,s)=>(t[s]=e[s],t)),{}):e))}function Bt(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&Object.keys(e).every((s=>Bt(t[s],e[s]))))}function Nt(t,e){if(t===e)return t;const s=zt(t)&&zt(e);if(s||Jt(t)&&Jt(e)){const i=s?t:Object.keys(t),n=i.length,r=s?e:Object.keys(e),a=r.length,o=s?[]:{};let h=0;for(let u=0;u<a;u++){const n=s?u:r[u];(!s&&i.includes(n)||s)&&void 0===t[n]&&void 0===e[n]?(o[n]=void 0,h++):(o[n]=Nt(t[n],e[n]),o[n]===t[n]&&void 0!==t[n]&&h++)}return n===a&&h===n?t:o}return e}function $t(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function zt(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function Jt(t){if(!Vt(t))return!1;const e=t.constructor;if(void 0===e)return!0;const s=e.prototype;return!!Vt(s)&&(!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype)}function Vt(t){return"[object Object]"===Object.prototype.toString.call(t)}function Xt(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?Nt(t,e):e}function Yt(t,e,s=0){const i=[...t,e];return s&&i.length>s?i.slice(1):i}function Zt(t,e,s=0){const i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var te=Symbol();function ee(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==te?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}function se(t,e){return"function"==typeof t?t(...e):!!t}var ie=new(i=class extends At{constructor(){super(),Ct(this,t),Ct(this,e),Ct(this,s),St(this,s,(t=>{if(!Dt&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}))}onSubscribe(){kt(this,e)||this.setEventListener(kt(this,s))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=kt(this,e))||t.call(this),St(this,e,void 0))}setEventListener(t){var i;St(this,s,t),null==(i=kt(this,e))||i.call(this),St(this,e,t((t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})))}setFocused(e){kt(this,t)!==e&&(St(this,t,e),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach((e=>{e(t)}))}isFocused(){return"boolean"==typeof kt(this,t)?kt(this,t):"hidden"!==globalThis.document?.visibilityState}},t=new WeakMap,e=new WeakMap,s=new WeakMap,i),ne=new(o=class extends At{constructor(){super(),Ct(this,n,!0),Ct(this,r),Ct(this,a),St(this,a,(t=>{if(!Dt&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}))}onSubscribe(){kt(this,r)||this.setEventListener(kt(this,a))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=kt(this,r))||t.call(this),St(this,r,void 0))}setEventListener(t){var e;St(this,a,t),null==(e=kt(this,r))||e.call(this),St(this,r,t(this.setOnline.bind(this)))}setOnline(t){kt(this,n)!==t&&(St(this,n,t),this.listeners.forEach((e=>{e(t)})))}isOnline(){return kt(this,n)}},n=new WeakMap,r=new WeakMap,a=new WeakMap,o);function re(){let t,e;const s=new Promise(((s,i)=>{t=s,e=i}));function i(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch((()=>{})),s.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},s.reject=t=>{i({status:"rejected",reason:t}),e(t)},s}function ae(t){return Math.min(1e3*2**t,3e4)}function oe(t){return"online"!==(t??"online")||ne.isOnline()}var he=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function ue(t){return t instanceof he}function ce(t){let e,s=!1,i=0,n=!1;const r=re(),a=()=>ie.isFocused()&&("always"===t.networkMode||ne.isOnline())&&t.canRun(),o=()=>oe(t.networkMode)&&t.canRun(),h=s=>{n||(n=!0,t.onSuccess?.(s),e?.(),r.resolve(s))},u=s=>{n||(n=!0,t.onError?.(s),e?.(),r.reject(s))},c=()=>new Promise((s=>{e=t=>{(n||a())&&s(t)},t.onPause?.()})).then((()=>{e=void 0,n||t.onContinue?.()})),l=()=>{if(n)return;let e;const r=0===i?t.initialPromise:void 0;try{e=r??t.fn()}catch(o){e=Promise.reject(o)}Promise.resolve(e).then(h).catch((e=>{if(n)return;const r=t.retry??(Dt?0:3),o=t.retryDelay??ae,h="function"==typeof o?o(i,e):o,d=!0===r||"number"==typeof r&&i<r||"function"==typeof r&&r(i,e);var f;!s&&d?(i++,t.onFail?.(i,e),(f=h,new Promise((t=>{setTimeout(t,f)}))).then((()=>a()?void 0:c())).then((()=>{s?u(e):l()}))):u(e)}))};return{promise:r,cancel:e=>{n||(u(new he(e)),t.abort?.())},continue:()=>(e?.(),r),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:o,start:()=>(o()?l():c().then(l),r)}}var le=t=>setTimeout(t,0);var de=function(){let t=[],e=0,s=t=>{t()},i=t=>{t()},n=le;const r=i=>{e?t.push(i):n((()=>{s(i)}))};return{batch:r=>{let a;e++;try{a=r()}finally{e--,e||(()=>{const e=t;t=[],e.length&&n((()=>{i((()=>{e.forEach((t=>{s(t)}))}))}))})()}return a},batchCalls:t=>(...e)=>{r((()=>{t(...e)}))},schedule:r,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{n=t}}}(),fe=(u=class{constructor(){Ct(this,h)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Tt(this.gcTime)&&St(this,h,setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(Dt?1/0:3e5))}clearGcTimeout(){kt(this,h)&&(clearTimeout(kt(this,h)),St(this,h,void 0))}},h=new WeakMap,u),pe=(g=class extends fe{constructor(t){super(),Ct(this,m),Ct(this,c),Ct(this,l),Ct(this,d),Ct(this,f),Ct(this,p),Ct(this,y),Ct(this,v),St(this,v,!1),St(this,y,t.defaultOptions),this.setOptions(t.options),this.observers=[],St(this,f,t.client),St(this,d,kt(this,f).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,St(this,c,function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=t.state??kt(this,c),this.scheduleGc()}get meta(){return this.options.meta}get promise(){return kt(this,p)?.promise}setOptions(t){this.options={...kt(this,y),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||kt(this,d).remove(this)}setData(t,e){const s=Xt(this.state.data,t,this.options);return Rt(this,m,b).call(this,{data:s,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),s}setState(t,e){Rt(this,m,b).call(this,{type:"setState",state:t,setStateOptions:e})}cancel(t){const e=kt(this,p)?.promise;return kt(this,p)?.cancel(t),e?e.then(Ut).catch(Ut):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(kt(this,c))}isActive(){return this.observers.some((t=>!1!==It(t.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===te||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some((t=>"static"===Kt(t.options.staleTime,this)))}isStale(){return this.getObserversCount()>0?this.observers.some((t=>t.getCurrentResult().isStale)):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!jt(this.state.dataUpdatedAt,t))}onFocus(){const t=this.observers.find((t=>t.shouldFetchOnWindowFocus()));t?.refetch({cancelRefetch:!1}),kt(this,p)?.continue()}onOnline(){const t=this.observers.find((t=>t.shouldFetchOnReconnect()));t?.refetch({cancelRefetch:!1}),kt(this,p)?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),kt(this,d).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter((e=>e!==t)),this.observers.length||(kt(this,p)&&(kt(this,v)?kt(this,p).cancel({revert:!0}):kt(this,p).cancelRetry()),this.scheduleGc()),kt(this,d).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Rt(this,m,b).call(this,{type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(kt(this,p))return kt(this,p).continueRetry(),kt(this,p).promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find((t=>t.options.queryFn));t&&this.setOptions(t.options)}const s=new AbortController,i=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(St(this,v,!0),s.signal)})},n={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:kt(this,f),state:this.state,fetchFn:()=>{const t=ee(this.options,e),s={client:kt(this,f),queryKey:this.queryKey,meta:this.meta};return i(s),St(this,v,!1),this.options.persister?this.options.persister(t,s,this):t(s)}};i(n),this.options.behavior?.onFetch(n,this),St(this,l,this.state),"idle"!==this.state.fetchStatus&&this.state.fetchMeta===n.fetchOptions?.meta||Rt(this,m,b).call(this,{type:"fetch",meta:n.fetchOptions?.meta});const r=t=>{ue(t)&&t.silent||Rt(this,m,b).call(this,{type:"error",error:t}),ue(t)||(kt(this,d).config.onError?.(t,this),kt(this,d).config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return St(this,p,ce({initialPromise:e?.initialPromise,fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{if(void 0!==t){try{this.setData(t)}catch(e){return void r(e)}kt(this,d).config.onSuccess?.(t,this),kt(this,d).config.onSettled?.(t,this.state.error,this),this.scheduleGc()}else r(new Error(`${this.queryHash} data is undefined`))},onError:r,onFail:(t,e)=>{Rt(this,m,b).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{Rt(this,m,b).call(this,{type:"pause"})},onContinue:()=>{Rt(this,m,b).call(this,{type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0})),kt(this,p).start()}},c=new WeakMap,l=new WeakMap,d=new WeakMap,f=new WeakMap,p=new WeakMap,y=new WeakMap,v=new WeakMap,m=new WeakSet,b=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...ye(e.data,this.options),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return ue(s)&&s.revert&&kt(this,l)?{...kt(this,l),fetchStatus:"idle"}:{...e,error:s,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),de.batch((()=>{this.observers.forEach((t=>{t.onQueryUpdate()})),kt(this,d).notify({query:this,type:"updated",action:t})}))},g);function ye(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:oe(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}var ve=(O=class extends At{constructor(t={}){super(),Ct(this,w),this.config=t,St(this,w,new Map)}build(t,e,s){const i=e.queryKey,n=e.queryHash??_t(i,e);let r=this.get(n);return r||(r=new pe({client:t,queryKey:i,queryHash:n,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(i)}),this.add(r)),r}add(t){kt(this,w).has(t.queryHash)||(kt(this,w).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=kt(this,w).get(t.queryHash);e&&(t.destroy(),e===t&&kt(this,w).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){de.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}get(t){return kt(this,w).get(t)}getAll(){return[...kt(this,w).values()]}find(t){const e={exact:!0,...t};return this.getAll().find((t=>Lt(e,t)))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter((e=>Lt(t,e))):e}notify(t){de.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}onFocus(){de.batch((()=>{this.getAll().forEach((t=>{t.onFocus()}))}))}onOnline(){de.batch((()=>{this.getAll().forEach((t=>{t.onOnline()}))}))}},w=new WeakMap,O),me=(P=class extends fe{constructor(t){super(),Ct(this,S),Ct(this,M),Ct(this,k),Ct(this,C),this.mutationId=t.mutationId,St(this,k,t.mutationCache),St(this,M,[]),this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){kt(this,M).includes(t)||(kt(this,M).push(t),this.clearGcTimeout(),kt(this,k).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){St(this,M,kt(this,M).filter((e=>e!==t))),this.scheduleGc(),kt(this,k).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){kt(this,M).length||("pending"===this.state.status?this.scheduleGc():kt(this,k).remove(this))}continue(){return kt(this,C)?.continue()??this.execute(this.state.variables)}async execute(t){const e=()=>{Rt(this,S,R).call(this,{type:"continue"})};St(this,C,ce({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{Rt(this,S,R).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{Rt(this,S,R).call(this,{type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>kt(this,k).canRun(this)}));const s="pending"===this.state.status,i=!kt(this,C).canStart();try{if(s)e();else{Rt(this,S,R).call(this,{type:"pending",variables:t,isPaused:i}),await(kt(this,k).config.onMutate?.(t,this));const e=await(this.options.onMutate?.(t));e!==this.state.context&&Rt(this,S,R).call(this,{type:"pending",context:e,variables:t,isPaused:i})}const n=await kt(this,C).start();return await(kt(this,k).config.onSuccess?.(n,t,this.state.context,this)),await(this.options.onSuccess?.(n,t,this.state.context)),await(kt(this,k).config.onSettled?.(n,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(n,null,t,this.state.context)),Rt(this,S,R).call(this,{type:"success",data:n}),n}catch(n){try{throw await(kt(this,k).config.onError?.(n,t,this.state.context,this)),await(this.options.onError?.(n,t,this.state.context)),await(kt(this,k).config.onSettled?.(void 0,n,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,n,t,this.state.context)),n}finally{Rt(this,S,R).call(this,{type:"error",error:n})}}finally{kt(this,k).runNext(this)}}},M=new WeakMap,k=new WeakMap,C=new WeakMap,S=new WeakSet,R=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),de.batch((()=>{kt(this,M).forEach((e=>{e.onMutationUpdate(t)})),kt(this,k).notify({mutation:this,type:"updated",action:t})}))},P);var be=(x=class extends At{constructor(t={}){super(),Ct(this,E),Ct(this,q),Ct(this,F),this.config=t,St(this,E,new Set),St(this,q,new Map),St(this,F,0)}build(t,e,s){const i=new me({mutationCache:this,mutationId:++Pt(this,F)._,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){kt(this,E).add(t);const e=ge(t);if("string"==typeof e){const s=kt(this,q).get(e);s?s.push(t):kt(this,q).set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(kt(this,E).delete(t)){const e=ge(t);if("string"==typeof e){const s=kt(this,q).get(e);if(s)if(s.length>1){const e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&kt(this,q).delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){const e=ge(t);if("string"==typeof e){const s=kt(this,q).get(e),i=s?.find((t=>"pending"===t.state.status));return!i||i===t}return!0}runNext(t){const e=ge(t);if("string"==typeof e){const s=kt(this,q).get(e)?.find((e=>e!==t&&e.state.isPaused));return s?.continue()??Promise.resolve()}return Promise.resolve()}clear(){de.batch((()=>{kt(this,E).forEach((t=>{this.notify({type:"removed",mutation:t})})),kt(this,E).clear(),kt(this,q).clear()}))}getAll(){return Array.from(kt(this,E))}find(t){const e={exact:!0,...t};return this.getAll().find((t=>Ht(e,t)))}findAll(t={}){return this.getAll().filter((e=>Ht(t,e)))}notify(t){de.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}resumePausedMutations(){const t=this.getAll().filter((t=>t.state.isPaused));return de.batch((()=>Promise.all(t.map((t=>t.continue().catch(Ut))))))}},E=new WeakMap,q=new WeakMap,F=new WeakMap,x);function ge(t){return t.options.scope?.id}function we(t){return{onFetch:(e,s)=>{const i=e.options,n=e.fetchOptions?.meta?.fetchMore?.direction,r=e.state.data?.pages||[],a=e.state.data?.pageParams||[];let o={pages:[],pageParams:[]},h=0;const u=async()=>{let s=!1;const u=ee(e.options,e.fetchOptions),c=async(t,i,n)=>{if(s)return Promise.reject();if(null==i&&t.pages.length)return Promise.resolve(t);const r={client:e.client,queryKey:e.queryKey,pageParam:i,direction:n?"backward":"forward",meta:e.options.meta};var a;a=r,Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",(()=>{s=!0})),e.signal)});const o=await u(r),{maxPages:h}=e.options,c=n?Zt:Yt;return{pages:c(t.pages,o,h),pageParams:c(t.pageParams,i,h)}};if(n&&r.length){const t="backward"===n,e={pages:r,pageParams:a},s=(t?Me:Oe)(i,e);o=await c(e,s,t)}else{const e=t??r.length;do{const t=0===h?a[0]??i.initialPageParam:Oe(i,o);if(h>0&&null==t)break;o=await c(o,t),h++}while(h<e)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(u,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=u}}}function Oe(t,{pages:e,pageParams:s}){const i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}function Me(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}var ke=(I=class{constructor(t={}){Ct(this,Q),Ct(this,W),Ct(this,A),Ct(this,D),Ct(this,U),Ct(this,T),Ct(this,j),Ct(this,K),St(this,Q,t.queryCache||new ve),St(this,W,t.mutationCache||new be),St(this,A,t.defaultOptions||{}),St(this,D,new Map),St(this,U,new Map),St(this,T,0)}mount(){Pt(this,T)._++,1===kt(this,T)&&(St(this,j,ie.subscribe((async t=>{t&&(await this.resumePausedMutations(),kt(this,Q).onFocus())}))),St(this,K,ne.subscribe((async t=>{t&&(await this.resumePausedMutations(),kt(this,Q).onOnline())}))))}unmount(){var t,e;Pt(this,T)._--,0===kt(this,T)&&(null==(t=kt(this,j))||t.call(this),St(this,j,void 0),null==(e=kt(this,K))||e.call(this),St(this,K,void 0))}isFetching(t){return kt(this,Q).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return kt(this,W).findAll({...t,status:"pending"}).length}getQueryData(t){const e=this.defaultQueryOptions({queryKey:t});return kt(this,Q).get(e.queryHash)?.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),s=kt(this,Q).build(this,e),i=s.state.data;return void 0===i?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(Kt(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(i))}getQueriesData(t){return kt(this,Q).findAll(t).map((({queryKey:t,state:e})=>[t,e.data]))}setQueryData(t,e,s){const i=this.defaultQueryOptions({queryKey:t}),n=kt(this,Q).get(i.queryHash),r=n?.state.data,a=function(t,e){return"function"==typeof t?t(e):t}(e,r);if(void 0!==a)return kt(this,Q).build(this,i).setData(a,{...s,manual:!0})}setQueriesData(t,e,s){return de.batch((()=>kt(this,Q).findAll(t).map((({queryKey:t})=>[t,this.setQueryData(t,e,s)]))))}getQueryState(t){const e=this.defaultQueryOptions({queryKey:t});return kt(this,Q).get(e.queryHash)?.state}removeQueries(t){const e=kt(this,Q);de.batch((()=>{e.findAll(t).forEach((t=>{e.remove(t)}))}))}resetQueries(t,e){const s=kt(this,Q);return de.batch((()=>(s.findAll(t).forEach((t=>{t.reset()})),this.refetchQueries({type:"active",...t},e))))}cancelQueries(t,e={}){const s={revert:!0,...e},i=de.batch((()=>kt(this,Q).findAll(t).map((t=>t.cancel(s)))));return Promise.all(i).then(Ut).catch(Ut)}invalidateQueries(t,e={}){return de.batch((()=>(kt(this,Q).findAll(t).forEach((t=>{t.invalidate()})),"none"===t?.refetchType?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e))))}refetchQueries(t,e={}){const s={...e,cancelRefetch:e.cancelRefetch??!0},i=de.batch((()=>kt(this,Q).findAll(t).filter((t=>!t.isDisabled()&&!t.isStatic())).map((t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(Ut)),"paused"===t.state.fetchStatus?Promise.resolve():e}))));return Promise.all(i).then(Ut)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const s=kt(this,Q).build(this,e);return s.isStaleByTime(Kt(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(Ut).catch(Ut)}fetchInfiniteQuery(t){return t.behavior=we(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(Ut).catch(Ut)}ensureInfiniteQueryData(t){return t.behavior=we(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return ne.isOnline()?kt(this,W).resumePausedMutations():Promise.resolve()}getQueryCache(){return kt(this,Q)}getMutationCache(){return kt(this,W)}getDefaultOptions(){return kt(this,A)}setDefaultOptions(t){St(this,A,t)}setQueryDefaults(t,e){kt(this,D).set(Gt(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...kt(this,D).values()],s={};return e.forEach((e=>{Bt(t,e.queryKey)&&Object.assign(s,e.defaultOptions)})),s}setMutationDefaults(t,e){kt(this,U).set(Gt(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...kt(this,U).values()],s={};return e.forEach((e=>{Bt(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)})),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...kt(this,A).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=_t(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===te&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...kt(this,A).mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){kt(this,Q).clear(),kt(this,W).clear()}},Q=new WeakMap,W=new WeakMap,A=new WeakMap,D=new WeakMap,U=new WeakMap,T=new WeakMap,j=new WeakMap,K=new WeakMap,I),Ce=(dt=class extends At{constructor(t,e){super(),Ct(this,st),Ct(this,L),Ct(this,H),Ct(this,_),Ct(this,G),Ct(this,B),Ct(this,N),Ct(this,$),Ct(this,z),Ct(this,J),Ct(this,V),Ct(this,X),Ct(this,Y),Ct(this,Z),Ct(this,tt),Ct(this,et,new Set),this.options=e,St(this,L,t),St(this,z,null),St(this,$,re()),this.options.experimental_prefetchInRender||kt(this,$).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(kt(this,H).addObserver(this),Se(kt(this,H),this.options)?Rt(this,st,it).call(this):this.updateResult(),Rt(this,st,ot).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Re(kt(this,H),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Re(kt(this,H),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,Rt(this,st,ht).call(this),Rt(this,st,ut).call(this),kt(this,H).removeObserver(this)}setOptions(t){const e=this.options,s=kt(this,H);if(this.options=kt(this,L).defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof It(this.options.enabled,kt(this,H)))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");Rt(this,st,ct).call(this),kt(this,H).setOptions(this.options),e._defaulted&&!$t(this.options,e)&&kt(this,L).getQueryCache().notify({type:"observerOptionsUpdated",query:kt(this,H),observer:this});const i=this.hasListeners();i&&Pe(kt(this,H),s,this.options,e)&&Rt(this,st,it).call(this),this.updateResult(),!i||kt(this,H)===s&&It(this.options.enabled,kt(this,H))===It(e.enabled,kt(this,H))&&Kt(this.options.staleTime,kt(this,H))===Kt(e.staleTime,kt(this,H))||Rt(this,st,nt).call(this);const n=Rt(this,st,rt).call(this);!i||kt(this,H)===s&&It(this.options.enabled,kt(this,H))===It(e.enabled,kt(this,H))&&n===kt(this,tt)||Rt(this,st,at).call(this,n)}getOptimisticResult(t){const e=kt(this,L).getQueryCache().build(kt(this,L),t),s=this.createResult(e,t);return function(t,e){if(!$t(t.getCurrentResult(),e))return!0;return!1}(this,s)&&(St(this,G,s),St(this,N,this.options),St(this,B,kt(this,H).state)),s}getCurrentResult(){return kt(this,G)}trackResult(t,e){return new Proxy(t,{get:(t,s)=>(this.trackProp(s),e?.(s),Reflect.get(t,s))})}trackProp(t){kt(this,et).add(t)}getCurrentQuery(){return kt(this,H)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const e=kt(this,L).defaultQueryOptions(t),s=kt(this,L).getQueryCache().build(kt(this,L),e);return s.fetch().then((()=>this.createResult(s,e)))}fetch(t){return Rt(this,st,it).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then((()=>(this.updateResult(),kt(this,G))))}createResult(t,e){const s=kt(this,H),i=this.options,n=kt(this,G),r=kt(this,B),a=kt(this,N),o=t!==s?t.state:kt(this,_),{state:h}=t;let u,c={...h},l=!1;if(e._optimisticResults){const n=this.hasListeners(),r=!n&&Se(t,e),a=n&&Pe(t,s,e,i);(r||a)&&(c={...c,...ye(h.data,t.options)}),"isRestoring"===e._optimisticResults&&(c.fetchStatus="idle")}let{error:d,errorUpdatedAt:f,status:p}=c;u=c.data;let y=!1;if(void 0!==e.placeholderData&&void 0===u&&"pending"===p){let t;n?.isPlaceholderData&&e.placeholderData===a?.placeholderData?(t=n.data,y=!0):t="function"==typeof e.placeholderData?e.placeholderData(kt(this,X)?.state.data,kt(this,X)):e.placeholderData,void 0!==t&&(p="success",u=Xt(n?.data,t,e),l=!0)}if(e.select&&void 0!==u&&!y)if(n&&u===r?.data&&e.select===kt(this,J))u=kt(this,V);else try{St(this,J,e.select),u=e.select(u),u=Xt(n?.data,u,e),St(this,V,u),St(this,z,null)}catch(M){St(this,z,M)}kt(this,z)&&(d=kt(this,z),u=kt(this,V),f=Date.now(),p="error");const v="fetching"===c.fetchStatus,m="pending"===p,b="error"===p,g=m&&v,w=void 0!==u,O={status:p,fetchStatus:c.fetchStatus,isPending:m,isSuccess:"success"===p,isError:b,isInitialLoading:g,isLoading:g,data:u,dataUpdatedAt:c.dataUpdatedAt,error:d,errorUpdatedAt:f,failureCount:c.fetchFailureCount,failureReason:c.fetchFailureReason,errorUpdateCount:c.errorUpdateCount,isFetched:c.dataUpdateCount>0||c.errorUpdateCount>0,isFetchedAfterMount:c.dataUpdateCount>o.dataUpdateCount||c.errorUpdateCount>o.errorUpdateCount,isFetching:v,isRefetching:v&&!m,isLoadingError:b&&!w,isPaused:"paused"===c.fetchStatus,isPlaceholderData:l,isRefetchError:b&&w,isStale:Ee(t,e),refetch:this.refetch,promise:kt(this,$)};if(this.options.experimental_prefetchInRender){const e=t=>{"error"===O.status?t.reject(O.error):void 0!==O.data&&t.resolve(O.data)},i=()=>{const t=St(this,$,O.promise=re());e(t)},n=kt(this,$);switch(n.status){case"pending":t.queryHash===s.queryHash&&e(n);break;case"fulfilled":"error"!==O.status&&O.data===n.value||i();break;case"rejected":"error"===O.status&&O.error===n.reason||i()}}return O}updateResult(){const t=kt(this,G),e=this.createResult(kt(this,H),this.options);if(St(this,B,kt(this,H).state),St(this,N,this.options),void 0!==kt(this,B).data&&St(this,X,kt(this,H)),$t(e,t))return;St(this,G,e);Rt(this,st,lt).call(this,{listeners:(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,s="function"==typeof e?e():e;if("all"===s||!s&&!kt(this,et).size)return!0;const i=new Set(s??kt(this,et));return this.options.throwOnError&&i.add("error"),Object.keys(kt(this,G)).some((e=>{const s=e;return kt(this,G)[s]!==t[s]&&i.has(s)}))})()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&Rt(this,st,ot).call(this)}},L=new WeakMap,H=new WeakMap,_=new WeakMap,G=new WeakMap,B=new WeakMap,N=new WeakMap,$=new WeakMap,z=new WeakMap,J=new WeakMap,V=new WeakMap,X=new WeakMap,Y=new WeakMap,Z=new WeakMap,tt=new WeakMap,et=new WeakMap,st=new WeakSet,it=function(t){Rt(this,st,ct).call(this);let e=kt(this,H).fetch(this.options,t);return t?.throwOnError||(e=e.catch(Ut)),e},nt=function(){Rt(this,st,ht).call(this);const t=Kt(this.options.staleTime,kt(this,H));if(Dt||kt(this,G).isStale||!Tt(t))return;const e=jt(kt(this,G).dataUpdatedAt,t);St(this,Y,setTimeout((()=>{kt(this,G).isStale||this.updateResult()}),e+1))},rt=function(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(kt(this,H)):this.options.refetchInterval)??!1},at=function(t){Rt(this,st,ut).call(this),St(this,tt,t),!Dt&&!1!==It(this.options.enabled,kt(this,H))&&Tt(kt(this,tt))&&0!==kt(this,tt)&&St(this,Z,setInterval((()=>{(this.options.refetchIntervalInBackground||ie.isFocused())&&Rt(this,st,it).call(this)}),kt(this,tt)))},ot=function(){Rt(this,st,nt).call(this),Rt(this,st,at).call(this,Rt(this,st,rt).call(this))},ht=function(){kt(this,Y)&&(clearTimeout(kt(this,Y)),St(this,Y,void 0))},ut=function(){kt(this,Z)&&(clearInterval(kt(this,Z)),St(this,Z,void 0))},ct=function(){const t=kt(this,L).getQueryCache().build(kt(this,L),this.options);if(t===kt(this,H))return;const e=kt(this,H);St(this,H,t),St(this,_,t.state),this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))},lt=function(t){de.batch((()=>{t.listeners&&this.listeners.forEach((t=>{t(kt(this,G))})),kt(this,L).getQueryCache().notify({query:kt(this,H),type:"observerResultsUpdated"})}))},dt);function Se(t,e){return function(t,e){return!1!==It(e.enabled,t)&&void 0===t.state.data&&!("error"===t.state.status&&!1===e.retryOnMount)}(t,e)||void 0!==t.state.data&&Re(t,e,e.refetchOnMount)}function Re(t,e,s){if(!1!==It(e.enabled,t)&&"static"!==Kt(e.staleTime,t)){const i="function"==typeof s?s(t):s;return"always"===i||!1!==i&&Ee(t,e)}return!1}function Pe(t,e,s,i){return(t!==e||!1===It(i.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&Ee(t,s)}function Ee(t,e){return!1!==It(e.enabled,t)&&t.isStaleByTime(Kt(e.staleTime,t))}var qe=(wt=class extends At{constructor(t,e){super(),Ct(this,mt),Ct(this,ft),Ct(this,pt),Ct(this,yt),Ct(this,vt),St(this,ft,t),this.setOptions(e),this.bindMethods(),Rt(this,mt,bt).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){const e=this.options;this.options=kt(this,ft).defaultMutationOptions(t),$t(this.options,e)||kt(this,ft).getMutationCache().notify({type:"observerOptionsUpdated",mutation:kt(this,yt),observer:this}),e?.mutationKey&&this.options.mutationKey&&Gt(e.mutationKey)!==Gt(this.options.mutationKey)?this.reset():"pending"===kt(this,yt)?.state.status&&kt(this,yt).setOptions(this.options)}onUnsubscribe(){this.hasListeners()||kt(this,yt)?.removeObserver(this)}onMutationUpdate(t){Rt(this,mt,bt).call(this),Rt(this,mt,gt).call(this,t)}getCurrentResult(){return kt(this,pt)}reset(){kt(this,yt)?.removeObserver(this),St(this,yt,void 0),Rt(this,mt,bt).call(this),Rt(this,mt,gt).call(this)}mutate(t,e){return St(this,vt,e),kt(this,yt)?.removeObserver(this),St(this,yt,kt(this,ft).getMutationCache().build(kt(this,ft),this.options)),kt(this,yt).addObserver(this),kt(this,yt).execute(t)}},ft=new WeakMap,pt=new WeakMap,yt=new WeakMap,vt=new WeakMap,mt=new WeakSet,bt=function(){const t=kt(this,yt)?.state??{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0};St(this,pt,{...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset})},gt=function(t){de.batch((()=>{if(kt(this,vt)&&this.hasListeners()){const e=kt(this,pt).variables,s=kt(this,pt).context;"success"===t?.type?(kt(this,vt).onSuccess?.(t.data,e,s),kt(this,vt).onSettled?.(t.data,null,e,s)):"error"===t?.type&&(kt(this,vt).onError?.(t.error,e,s),kt(this,vt).onSettled?.(void 0,t.error,e,s))}this.listeners.forEach((t=>{t(kt(this,pt))}))}))},wt),Fe=Et.createContext(void 0),xe=t=>{const e=Et.useContext(Fe);if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},Qe=({client:t,children:e})=>(Et.useEffect((()=>(t.mount(),()=>{t.unmount()})),[t]),Wt.jsx(Fe.Provider,{value:t,children:e})),We=Et.createContext(!1);We.Provider;var Ae=Et.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),De=(t,e,s)=>e.fetchOptimistic(t).catch((()=>{s.clearReset()}));function Ue(t,e,s){const i=xe(),n=Et.useContext(We),r=Et.useContext(Ae),a=i.defaultQueryOptions(t);i.getDefaultOptions().queries?._experimental_beforeQuery?.(a),a._optimisticResults=n?"isRestoring":"optimistic",(t=>{if(t.suspense){const e=t=>"static"===t?t:Math.max(t??1e3,1e3),s=t.staleTime;t.staleTime="function"==typeof s?(...t)=>e(s(...t)):e(s),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3))}})(a),((t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(e.isReset()||(t.retryOnMount=!1))})(a,r),(t=>{Et.useEffect((()=>{t.clearReset()}),[t])})(r);const o=!i.getQueryCache().get(a.queryHash),[h]=Et.useState((()=>new e(i,a))),u=h.getOptimisticResult(a),c=!n&&!1!==t.subscribed;if(Et.useSyncExternalStore(Et.useCallback((t=>{const e=c?h.subscribe(de.batchCalls(t)):Ut;return h.updateResult(),e}),[h,c]),(()=>h.getCurrentResult()),(()=>h.getCurrentResult())),Et.useEffect((()=>{h.setOptions(a)}),[a,h]),((t,e)=>t?.suspense&&e.isPending)(a,u))throw De(a,h,r);if((({result:t,errorResetBoundary:e,throwOnError:s,query:i,suspense:n})=>t.isError&&!e.isReset()&&!t.isFetching&&i&&(n&&void 0===t.data||se(s,[t.error,i])))({result:u,errorResetBoundary:r,throwOnError:a.throwOnError,query:i.getQueryCache().get(a.queryHash),suspense:a.suspense}))throw u.error;if(i.getDefaultOptions().queries?._experimental_afterQuery?.(a,u),a.experimental_prefetchInRender&&!Dt&&((t,e)=>t.isLoading&&t.isFetching&&!e)(u,n)){const t=o?De(a,h,r):i.getQueryCache().get(a.queryHash)?.promise;t?.catch(Ut).finally((()=>{h.updateResult()}))}return a.notifyOnChangeProps?u:h.trackResult(u)}function Te(t,e){return Ue(t,Ce)}function je(t,e){const s=xe(),[i]=Et.useState((()=>new qe(s,t)));Et.useEffect((()=>{i.setOptions(t)}),[i,t]);const n=Et.useSyncExternalStore(Et.useCallback((t=>i.subscribe(de.batchCalls(t))),[i]),(()=>i.getCurrentResult()),(()=>i.getCurrentResult())),r=Et.useCallback(((t,e)=>{i.mutate(t,e).catch(Ut)}),[i]);if(n.error&&se(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:r,mutateAsync:n.mutate}}export{ke as Q,xe as a,je as b,Qe as c,Wt as j,Te as u};
