<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Expense Tracker</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 1200px;
            min-height: 600px;
            display: flex;
        }

        .sidebar {
            background: #2c3e50;
            color: white;
            width: 250px;
            padding: 2rem 0;
            display: flex;
            flex-direction: column;
        }

        .logo {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid #34495e;
            margin-bottom: 2rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .nav-item {
            padding: 1rem 2rem;
            cursor: pointer;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .nav-item:hover, .nav-item.active {
            background: #34495e;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            background: #f8f9fa;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .header h2 {
            color: #2c3e50;
            font-size: 2rem;
            font-weight: 600;
        }

        .header p {
            color: #6c757d;
            margin-top: 0.5rem;
        }

        .login-form {
            max-width: 400px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .btn-primary {
            background: #007bff;
            width: 100%;
            padding: 1rem;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-card h3 {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .stat-subtitle {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #c3e6cb;
        }

        .user-info {
            margin-top: auto;
            padding: 1rem 2rem;
            border-top: 1px solid #34495e;
        }

        .user-info p {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        .hidden {
            display: none;
        }

        .expenses-list {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .expense-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #eee;
        }

        .expense-item:last-child {
            border-bottom: none;
        }

        .expense-details h4 {
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .expense-details p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .expense-amount {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Login Screen -->
        <div id="loginScreen" class="login-form">
            <h2 style="text-align: center; margin-bottom: 2rem; color: #2c3e50;">Nawras Admin Login</h2>
            <div id="loginError" class="error-message hidden"></div>
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary">Sign In</button>
            </form>
            <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 5px; font-size: 0.9rem;">
                <strong>Demo Accounts:</strong><br>
                • <EMAIL> / taha2024<br>
                • <EMAIL> / burak2024
            </div>
        </div>

        <!-- Main Application -->
        <div id="mainApp" class="hidden" style="display: flex; width: 100%;">
            <div class="sidebar">
                <div class="logo">
                    <h1>Nawras Admin</h1>
                </div>
                <div class="nav-item active" data-page="dashboard">
                    📊 Dashboard
                </div>
                <div class="nav-item" data-page="expenses">
                    💰 Add Expense
                </div>
                <div class="nav-item" data-page="settlement">
                    🤝 Settlement
                </div>
                <div class="nav-item" data-page="history">
                    📋 History
                </div>
                <div class="nav-item" data-page="reports">
                    📈 Reports
                </div>
                <div class="nav-item" data-page="settings">
                    ⚙️ Settings
                </div>
                <div class="user-info">
                    <p><strong id="userName">User</strong></p>
                    <p id="userEmail"><EMAIL></p>
                    <button class="logout-btn" onclick="logout()">Sign Out</button>
                </div>
            </div>

            <div class="main-content">
                <div class="header">
                    <div>
                        <h2>Dashboard</h2>
                        <p>Welcome back! Here's your expense overview.</p>
                    </div>
                    <button class="btn">+ Add Expense</button>
                </div>

                <div id="errorMessage" class="error-message hidden"></div>
                <div id="successMessage" class="success-message hidden"></div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Taha's Expenses</h3>
                        <div class="stat-value" style="color: #007bff;" id="tahaExpenses">$0.00</div>
                        <div class="stat-subtitle" id="tahaCount">0 expenses</div>
                    </div>
                    <div class="stat-card">
                        <h3>Burak's Expenses</h3>
                        <div class="stat-value" style="color: #28a745;" id="burakExpenses">$0.00</div>
                        <div class="stat-subtitle" id="burakCount">0 expenses</div>
                    </div>
                    <div class="stat-card">
                        <h3>Combined Total</h3>
                        <div class="stat-value" style="color: #6f42c1;" id="totalExpenses">$0.00</div>
                        <div class="stat-subtitle" id="totalCount">0 total expenses</div>
                    </div>
                    <div class="stat-card">
                        <h3>Net Balance</h3>
                        <div class="stat-value" style="color: #fd7e14;" id="netBalance">$0.00</div>
                        <div class="stat-subtitle">All settled!</div>
                    </div>
                </div>

                <div class="expenses-list">
                    <h3 style="margin-bottom: 1rem;">Recent Expenses</h3>
                    <div id="expensesList">
                        <div style="text-align: center; padding: 2rem; color: #6c757d;">
                            Loading expenses...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;

        // Check if user is already logged in
        if (authToken) {
            checkSession();
        } else {
            showLogin();
        }

        function showLogin() {
            document.getElementById('loginScreen').classList.remove('hidden');
            document.getElementById('mainApp').classList.add('hidden');
        }

        function showApp() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            loadDashboardData();
        }

        function showError(message, elementId = 'loginError') {
            const errorEl = document.getElementById(elementId);
            errorEl.textContent = message;
            errorEl.classList.remove('hidden');
            setTimeout(() => errorEl.classList.add('hidden'), 5000);
        }

        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.classList.remove('hidden');
            setTimeout(() => successEl.classList.add('hidden'), 3000);
        }

        async function checkSession() {
            try {
                const response = await fetch('/api/auth/session', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.success && data.data) {
                    currentUser = data.data.user;
                    updateUserInfo();
                    showApp();
                } else {
                    localStorage.removeItem('authToken');
                    authToken = null;
                    showLogin();
                }
            } catch (error) {
                console.error('Session check failed:', error);
                localStorage.removeItem('authToken');
                authToken = null;
                showLogin();
            }
        }

        function updateUserInfo() {
            if (currentUser) {
                document.getElementById('userName').textContent = currentUser.name;
                document.getElementById('userEmail').textContent = currentUser.email;
            }
        }

        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('/api/auth/sign-in', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (data.success) {
                    authToken = data.data.session.token;
                    currentUser = data.data.user;
                    localStorage.setItem('authToken', authToken);
                    updateUserInfo();
                    showApp();
                    showSuccess('Successfully signed in!');
                } else {
                    showError(data.error || 'Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                showError('Network error. Please try again.');
            }
        });

        async function logout() {
            try {
                await fetch('/api/auth/sign-out', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
            } catch (error) {
                console.error('Logout error:', error);
            }

            localStorage.removeItem('authToken');
            authToken = null;
            currentUser = null;
            showLogin();
        }

        async function loadDashboardData() {
            try {
                // Load expenses
                const expensesResponse = await fetch('/api/expenses', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (expensesResponse.status === 401) {
                    logout();
                    return;
                }

                const expensesData = await expensesResponse.json();

                if (expensesData.success) {
                    updateExpensesDisplay(expensesData.data);
                } else {
                    showError('Failed to load expenses', 'errorMessage');
                }

                // Load settlements
                const settlementsResponse = await fetch('/api/settlements', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const settlementsData = await settlementsResponse.json();

                if (settlementsData.success) {
                    console.log('Settlements loaded:', settlementsData.data);
                }

            } catch (error) {
                console.error('Failed to load dashboard data:', error);
                showError('Failed to load data. Please refresh the page.', 'errorMessage');
            }
        }

        function updateExpensesDisplay(expenses) {
            // Calculate totals
            let tahaTotal = 0;
            let burakTotal = 0;
            let tahaCount = 0;
            let burakCount = 0;

            expenses.forEach(expense => {
                if (expense.paidById === 'taha') {
                    tahaTotal += expense.amount;
                    tahaCount++;
                } else if (expense.paidById === 'burak') {
                    burakTotal += expense.amount;
                    burakCount++;
                }
            });

            const total = tahaTotal + burakTotal;
            const balance = Math.abs(tahaTotal - burakTotal);

            // Update stats
            document.getElementById('tahaExpenses').textContent = `$${tahaTotal.toFixed(2)}`;
            document.getElementById('tahaCount').textContent = `${tahaCount} expenses`;
            document.getElementById('burakExpenses').textContent = `$${burakTotal.toFixed(2)}`;
            document.getElementById('burakCount').textContent = `${burakCount} expenses`;
            document.getElementById('totalExpenses').textContent = `$${total.toFixed(2)}`;
            document.getElementById('totalCount').textContent = `${expenses.length} total expenses`;
            document.getElementById('netBalance').textContent = `$${balance.toFixed(2)}`;

            // Update expenses list
            const expensesList = document.getElementById('expensesList');
            if (expenses.length === 0) {
                expensesList.innerHTML = '<div style="text-align: center; padding: 2rem; color: #6c757d;">No expenses found</div>';
            } else {
                expensesList.innerHTML = expenses.map(expense => `
                    <div class="expense-item">
                        <div class="expense-details">
                            <h4>${expense.description}</h4>
                            <p>${expense.category} • Paid by ${expense.paidById === 'taha' ? 'Taha' : 'Burak'} • ${expense.date}</p>
                        </div>
                        <div class="expense-amount">$${expense.amount.toFixed(2)}</div>
                    </div>
                `).join('');
            }
        }
    </script>
</body>
</html>
