<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Expense Tracker</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
        }
        .login-container { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
            padding: 3rem; 
            max-width: 400px; 
            width: 100%; 
        }
        .app-container { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
            overflow: hidden; 
            width: 100%; 
            max-width: 1200px; 
            min-height: 600px; 
            display: flex; 
        }
        .sidebar { 
            background: #2c3e50; 
            color: white; 
            width: 250px; 
            padding: 2rem 0; 
            display: flex; 
            flex-direction: column; 
        }
        .logo { 
            padding: 0 2rem 2rem; 
            border-bottom: 1px solid #34495e; 
            margin-bottom: 2rem; 
        }
        .logo h1 { 
            font-size: 1.5rem; 
            font-weight: 600; 
        }
        .nav-item { 
            padding: 1rem 2rem; 
            cursor: pointer; 
            transition: background 0.3s; 
            display: flex; 
            align-items: center; 
            gap: 0.75rem; 
        }
        .nav-item:hover, .nav-item.active { 
            background: #34495e; 
        }
        .main-content { 
            flex: 1; 
            padding: 2rem; 
            background: #f8f9fa; 
        }
        .header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 2rem; 
        }
        .header h2 { 
            color: #2c3e50; 
            font-size: 2rem; 
            font-weight: 600; 
        }
        .header p { 
            color: #6c757d; 
            margin-top: 0.5rem; 
        }
        .form-group { 
            margin-bottom: 1rem; 
        }
        .form-group label { 
            display: block; 
            margin-bottom: 0.5rem; 
            color: #2c3e50; 
            font-weight: 500; 
        }
        .form-group input, .form-group select, .form-group textarea { 
            width: 100%; 
            padding: 0.75rem; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            font-size: 1rem; 
        }
        .form-row { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 1rem; 
        }
        .btn { 
            background: #667eea; 
            color: white; 
            border: none; 
            padding: 0.75rem 1.5rem; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 1rem; 
            transition: background 0.3s; 
        }
        .btn:hover { 
            background: #5a6fd8; 
        }
        .btn-primary { 
            background: #007bff; 
            width: 100%; 
            padding: 1rem; 
        }
        .btn-primary:hover { 
            background: #0056b3; 
        }
        .btn-success { 
            background: #28a745; 
        }
        .btn-success:hover { 
            background: #218838; 
        }
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 1.5rem; 
            margin-bottom: 2rem; 
        }
        .stat-card { 
            background: white; 
            padding: 1.5rem; 
            border-radius: 10px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .stat-card h3 { 
            color: #6c757d; 
            font-size: 0.9rem; 
            margin-bottom: 0.5rem; 
            text-transform: uppercase; 
            letter-spacing: 0.5px; 
        }
        .stat-value { 
            font-size: 2rem; 
            font-weight: 600; 
            margin-bottom: 0.5rem; 
        }
        .stat-subtitle { 
            color: #6c757d; 
            font-size: 0.9rem; 
        }
        .error-message { 
            background: #f8d7da; 
            color: #721c24; 
            padding: 1rem; 
            border-radius: 5px; 
            margin-bottom: 1rem; 
            border: 1px solid #f5c6cb; 
        }
        .success-message { 
            background: #d4edda; 
            color: #155724; 
            padding: 1rem; 
            border-radius: 5px; 
            margin-bottom: 1rem; 
            border: 1px solid #c3e6cb; 
        }
        .user-info { 
            margin-top: auto; 
            padding: 1rem 2rem; 
            border-top: 1px solid #34495e; 
        }
        .user-info p { 
            margin-bottom: 0.5rem; 
            font-size: 0.9rem; 
        }
        .logout-btn { 
            background: #dc3545; 
            color: white; 
            border: none; 
            padding: 0.5rem 1rem; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 0.9rem; 
        }
        .logout-btn:hover { 
            background: #c82333; 
        }
        .expenses-list { 
            background: white; 
            border-radius: 10px; 
            padding: 1.5rem; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .expense-item { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            padding: 1rem 0; 
            border-bottom: 1px solid #eee; 
        }
        .expense-item:last-child { 
            border-bottom: none; 
        }
        .expense-details h4 { 
            color: #2c3e50; 
            margin-bottom: 0.25rem; 
        }
        .expense-details p { 
            color: #6c757d; 
            font-size: 0.9rem; 
        }
        .expense-amount { 
            font-size: 1.25rem; 
            font-weight: 600; 
            color: #2c3e50; 
        }
        .page { 
            display: none; 
        }
        .page.active { 
            display: block; 
        }
        .form-card { 
            background: white; 
            border-radius: 10px; 
            padding: 2rem; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
            max-width: 600px; 
        }
        .settlement-card { 
            background: white; 
            border-radius: 10px; 
            padding: 1.5rem; 
            margin-bottom: 1rem; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .settlement-amount { 
            font-size: 1.5rem; 
            font-weight: 600; 
            color: #28a745; 
        }
        .history-filters { 
            background: white; 
            border-radius: 10px; 
            padding: 1rem; 
            margin-bottom: 1rem; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .filter-row { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 1rem; 
            align-items: end; 
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <h2 style="text-align: center; margin-bottom: 2rem; color: #2c3e50;">Nawras Admin Login</h2>
        <div id="loginError" class="error-message" style="display: none;"></div>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn btn-primary">Sign In</button>
        </form>
        <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 5px; font-size: 0.9rem;">
            <strong>Demo Accounts:</strong><br>
            • <EMAIL> / taha2024<br>
            • <EMAIL> / burak2024
        </div>
    </div>    <!-- Main Application -->
    <div id="mainApp" class="app-container" style="display: none;">
        <div class="sidebar">
            <div class="logo"><h1>Nawras Admin</h1></div>
            <div class="nav-item active" data-page="dashboard">📊 Dashboard</div>
            <div class="nav-item" data-page="add-expense">💰 Add Expense</div>
            <div class="nav-item" data-page="settlement">🤝 Settlement</div>
            <div class="nav-item" data-page="history">📋 History</div>
            <div class="nav-item" data-page="reports">📈 Reports</div>
            <div class="nav-item" data-page="settings">⚙️ Settings</div>
            <div class="user-info">
                <p><strong id="userName">User</strong></p>
                <p id="userEmail"><EMAIL></p>
                <button class="logout-btn" onclick="logout()">Sign Out</button>
            </div>
        </div>
        <div class="main-content">
            <div id="errorMessage" class="error-message" style="display: none;"></div>
            <div id="successMessage" class="success-message" style="display: none;"></div>

            <!-- Dashboard Page -->
            <div id="dashboard" class="page active">
                <div class="header">
                    <div>
                        <h2>Dashboard</h2>
                        <p>Welcome back! Here's your expense overview.</p>
                    </div>
                    <button class="btn" onclick="showPage('add-expense')">+ Add Expense</button>
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Taha's Expenses</h3>
                        <div class="stat-value" style="color: #007bff;" id="tahaExpenses">$0.00</div>
                        <div class="stat-subtitle" id="tahaCount">0 expenses</div>
                    </div>
                    <div class="stat-card">
                        <h3>Burak's Expenses</h3>
                        <div class="stat-value" style="color: #28a745;" id="burakExpenses">$0.00</div>
                        <div class="stat-subtitle" id="burakCount">0 expenses</div>
                    </div>
                    <div class="stat-card">
                        <h3>Combined Total</h3>
                        <div class="stat-value" style="color: #6f42c1;" id="totalExpenses">$0.00</div>
                        <div class="stat-subtitle" id="totalCount">0 total expenses</div>
                    </div>
                    <div class="stat-card">
                        <h3>Net Balance</h3>
                        <div class="stat-value" style="color: #fd7e14;" id="netBalance">$0.00</div>
                        <div class="stat-subtitle">All settled!</div>
                    </div>
                </div>
                <div class="expenses-list">
                    <h3 style="margin-bottom: 1rem;">Recent Expenses</h3>
                    <div id="expensesList">
                        <div style="text-align: center; padding: 2rem; color: #6c757d;">Loading expenses...</div>
                    </div>
                </div>
            </div>

            <!-- Add Expense Page -->
            <div id="add-expense" class="page">
                <div class="header">
                    <div>
                        <h2>Add Expense</h2>
                        <p>Record a new expense for tracking.</p>
                    </div>
                </div>
                <div class="form-card">
                    <form id="expenseForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="expenseAmount">Amount ($)</label>
                                <input type="number" id="expenseAmount" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label for="expenseCategory">Category</label>
                                <select id="expenseCategory" required>
                                    <option value="">Select Category</option>
                                    <option value="Food">Food</option>
                                    <option value="Groceries">Groceries</option>
                                    <option value="Transportation">Transportation</option>
                                    <option value="Utilities">Utilities</option>
                                    <option value="Entertainment">Entertainment</option>
                                    <option value="Shopping">Shopping</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="expensePaidBy">Paid By</label>
                                <select id="expensePaidBy" required>
                                    <option value="">Select Person</option>
                                    <option value="taha">Taha</option>
                                    <option value="burak">Burak</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="expenseDate">Date</label>
                                <input type="date" id="expenseDate" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="expenseDescription">Description</label>
                            <textarea id="expenseDescription" rows="3" placeholder="Enter expense description..." required></textarea>
                        </div>
                        <button type="submit" class="btn btn-success" style="width: 100%;">Add Expense</button>
                    </form>
                </div>
            </div>            <!-- Settlement Page -->
            <div id="settlement" class="page">
                <div class="header">
                    <div>
                        <h2>Settlement</h2>
                        <p>Manage settlements between Taha and Burak.</p>
                    </div>
                    <button class="btn" onclick="calculateSettlement()">Calculate Settlement</button>
                </div>
                <div id="settlementsList">
                    <div style="text-align: center; padding: 2rem; color: #6c757d;">Loading settlements...</div>
                </div>
            </div>

            <!-- History Page -->
            <div id="history" class="page">
                <div class="header">
                    <div>
                        <h2>History</h2>
                        <p>View all expenses and settlements history.</p>
                    </div>
                </div>
                <div class="history-filters">
                    <div class="filter-row">
                        <div class="form-group">
                            <label for="filterCategory">Category</label>
                            <select id="filterCategory">
                                <option value="">All Categories</option>
                                <option value="Food">Food</option>
                                <option value="Groceries">Groceries</option>
                                <option value="Transportation">Transportation</option>
                                <option value="Utilities">Utilities</option>
                                <option value="Entertainment">Entertainment</option>
                                <option value="Shopping">Shopping</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="filterPaidBy">Paid By</label>
                            <select id="filterPaidBy">
                                <option value="">All People</option>
                                <option value="taha">Taha</option>
                                <option value="burak">Burak</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="filterDate">Date Range</label>
                            <input type="month" id="filterDate">
                        </div>
                        <div class="form-group">
                            <button class="btn" onclick="applyFilters()">Apply Filters</button>
                        </div>
                    </div>
                </div>
                <div id="historyList">
                    <div style="text-align: center; padding: 2rem; color: #6c757d;">Loading history...</div>
                </div>
            </div>

            <!-- Reports Page -->
            <div id="reports" class="page">
                <div class="header">
                    <div>
                        <h2>Reports</h2>
                        <p>Expense analytics and insights.</p>
                    </div>
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Monthly Total</h3>
                        <div class="stat-value" style="color: #007bff;" id="monthlyTotal">$0.00</div>
                        <div class="stat-subtitle">This month</div>
                    </div>
                    <div class="stat-card">
                        <h3>Average per Day</h3>
                        <div class="stat-value" style="color: #28a745;" id="dailyAverage">$0.00</div>
                        <div class="stat-subtitle">Daily spending</div>
                    </div>
                    <div class="stat-card">
                        <h3>Top Category</h3>
                        <div class="stat-value" style="color: #6f42c1;" id="topCategory">Food</div>
                        <div class="stat-subtitle">Most expenses</div>
                    </div>
                    <div class="stat-card">
                        <h3>Pending Settlement</h3>
                        <div class="stat-value" style="color: #fd7e14;" id="pendingSettlement">$0.00</div>
                        <div class="stat-subtitle">To be settled</div>
                    </div>
                </div>
                <div class="expenses-list">
                    <h3 style="margin-bottom: 1rem;">Category Breakdown</h3>
                    <div id="categoryBreakdown">
                        <div style="text-align: center; padding: 2rem; color: #6c757d;">Loading category data...</div>
                    </div>
                </div>
            </div>            <!-- Settings Page -->
            <div id="settings" class="page">
                <div class="header">
                    <div>
                        <h2>Settings</h2>
                        <p>Application preferences and account settings.</p>
                    </div>
                </div>
                <div class="form-card">
                    <h3 style="margin-bottom: 1rem;">Account Information</h3>
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" id="settingsName" readonly>
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" id="settingsEmail" readonly>
                    </div>
                    <div class="form-group">
                        <label>Member Since</label>
                        <input type="text" value="January 2024" readonly>
                    </div>
                    <hr style="margin: 2rem 0;">
                    <h3 style="margin-bottom: 1rem;">Preferences</h3>
                    <div class="form-group">
                        <label>Default Currency</label>
                        <select>
                            <option value="USD">USD ($)</option>
                            <option value="EUR">EUR (€)</option>
                            <option value="TRY">TRY (₺)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Date Format</label>
                        <select>
                            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                        </select>
                    </div>
                    <button class="btn btn-primary">Save Settings</button>
                </div>
            </div>
        </div>
    </div>    <script>
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;
        let allExpenses = [];
        let allSettlements = [];

        // Initialize app
        if (authToken) { 
            checkSession(); 
        } else { 
            showLogin(); 
        }

        function showLogin() {
            document.getElementById('loginScreen').style.display = 'block';
            document.getElementById('mainApp').style.display = 'none';
        }

        function showApp() {
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('mainApp').style.display = 'flex';
            loadDashboardData();
            setupNavigation();
            setDefaultDate();
        }

        function setupNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(function(item) {
                item.addEventListener('click', function() {
                    const page = this.getAttribute('data-page');
                    showPage(page);
                    
                    // Update active nav item
                    navItems.forEach(function(nav) { 
                        nav.classList.remove('active'); 
                    });
                    this.classList.add('active');
                });
            });
        }

        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page');
            pages.forEach(function(page) { 
                page.classList.remove('active'); 
            });
            
            // Show target page
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                
                // Load page-specific data
                if (pageId === 'settlement') { 
                    loadSettlements(); 
                } else if (pageId === 'history') { 
                    loadHistory(); 
                } else if (pageId === 'reports') { 
                    loadReports(); 
                } else if (pageId === 'settings') { 
                    loadSettings(); 
                }
            }
        }        function setDefaultDate() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('expenseDate').value = today;
        }

        function showError(message, elementId) {
            elementId = elementId || 'loginError';
            const errorEl = document.getElementById(elementId);
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            setTimeout(function() { 
                errorEl.style.display = 'none'; 
            }, 5000);
        }

        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.style.display = 'block';
            setTimeout(function() { 
                successEl.style.display = 'none'; 
            }, 3000);
        }

        function checkSession() {
            fetch('/api/auth/session', {
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .then(function(response) { 
                return response.json(); 
            })
            .then(function(data) {
                if (data.success && data.data) {
                    currentUser = data.data.user;
                    updateUserInfo();
                    showApp();
                } else {
                    localStorage.removeItem('authToken');
                    authToken = null;
                    showLogin();
                }
            })
            .catch(function(error) {
                console.error('Session check failed:', error);
                localStorage.removeItem('authToken');
                authToken = null;
                showLogin();
            });
        }

        function updateUserInfo() {
            if (currentUser) {
                document.getElementById('userName').textContent = currentUser.name;
                document.getElementById('userEmail').textContent = currentUser.email;
                document.getElementById('settingsName').value = currentUser.name;
                document.getElementById('settingsEmail').value = currentUser.email;
                document.getElementById('expensePaidBy').value = currentUser.id;
            }
        }        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            fetch('/api/auth/sign-in', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: email, password: password })
            })
            .then(function(response) { 
                return response.json(); 
            })
            .then(function(data) {
                if (data.success) {
                    authToken = data.data.session.token;
                    currentUser = data.data.user;
                    localStorage.setItem('authToken', authToken);
                    updateUserInfo();
                    showApp();
                    showSuccess('Successfully signed in!');
                } else {
                    showError(data.error || 'Login failed');
                }
            })
            .catch(function(error) {
                console.error('Login error:', error);
                showError('Network error. Please try again.');
            });
        });

        function logout() {
            fetch('/api/auth/sign-out', {
                method: 'POST',
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .catch(function(error) {
                console.error('Logout error:', error);
            });
            
            localStorage.removeItem('authToken');
            authToken = null;
            currentUser = null;
            showLogin();
        }

        function loadDashboardData() {
            fetch('/api/expenses', {
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .then(function(response) {
                if (response.status === 401) {
                    logout();
                    return;
                }
                return response.json();
            })
            .then(function(expensesData) {
                if (expensesData && expensesData.success) {
                    allExpenses = expensesData.data;
                    updateExpensesDisplay(expensesData.data);
                } else {
                    showError('Failed to load expenses', 'errorMessage');
                }
            })
            .catch(function(error) {
                console.error('Failed to load dashboard data:', error);
                showError('Failed to load data. Please refresh the page.', 'errorMessage');
            });
        }        function updateExpensesDisplay(expenses) {
            let tahaTotal = 0, burakTotal = 0, tahaCount = 0, burakCount = 0;
            
            for (let i = 0; i < expenses.length; i++) {
                const expense = expenses[i];
                if (expense.paidById === 'taha') {
                    tahaTotal += expense.amount;
                    tahaCount++;
                } else if (expense.paidById === 'burak') {
                    burakTotal += expense.amount;
                    burakCount++;
                }
            }
            
            const total = tahaTotal + burakTotal;
            const balance = Math.abs(tahaTotal - burakTotal);
            
            document.getElementById('tahaExpenses').textContent = '$' + tahaTotal.toFixed(2);
            document.getElementById('tahaCount').textContent = tahaCount + ' expenses';
            document.getElementById('burakExpenses').textContent = '$' + burakTotal.toFixed(2);
            document.getElementById('burakCount').textContent = burakCount + ' expenses';
            document.getElementById('totalExpenses').textContent = '$' + total.toFixed(2);
            document.getElementById('totalCount').textContent = expenses.length + ' total expenses';
            document.getElementById('netBalance').textContent = '$' + balance.toFixed(2);
            
            const expensesList = document.getElementById('expensesList');
            if (expenses.length === 0) {
                expensesList.innerHTML = '<div style="text-align: center; padding: 2rem; color: #6c757d;">No expenses found</div>';
            } else {
                let html = '';
                for (let i = 0; i < expenses.length; i++) {
                    const expense = expenses[i];
                    html += '<div class="expense-item">';
                    html += '<div class="expense-details">';
                    html += '<h4>' + expense.description + '</h4>';
                    html += '<p>' + expense.category + ' • Paid by ' + (expense.paidById === 'taha' ? 'Taha' : 'Burak') + ' • ' + expense.date + '</p>';
                    html += '</div>';
                    html += '<div class="expense-amount">$' + expense.amount.toFixed(2) + '</div>';
                    html += '</div>';
                }
                expensesList.innerHTML = html;
            }
        }

        function loadSettlements() {
            fetch('/api/settlements', {
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .then(function(response) { 
                return response.json(); 
            })
            .then(function(data) {
                if (data.success) {
                    allSettlements = data.data;
                    displaySettlements(data.data);
                }
            })
            .catch(function(error) {
                console.error('Failed to load settlements:', error);
            });
        }        function displaySettlements(settlements) {
            const container = document.getElementById('settlementsList');
            if (settlements.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 2rem; color: #6c757d;">No settlements found</div>';
            } else {
                let html = '';
                for (let i = 0; i < settlements.length; i++) {
                    const settlement = settlements[i];
                    html += '<div class="settlement-card">';
                    html += '<div style="display: flex; justify-content: space-between; align-items: center;">';
                    html += '<div>';
                    html += '<h4>' + settlement.description + '</h4>';
                    html += '<p>' + (settlement.paidBy === 'taha' ? 'Taha' : 'Burak') + ' paid ' + (settlement.paidTo === 'taha' ? 'Taha' : 'Burak') + ' • ' + settlement.date + '</p>';
                    html += '</div>';
                    html += '<div class="settlement-amount">$' + settlement.amount.toFixed(2) + '</div>';
                    html += '</div>';
                    html += '</div>';
                }
                container.innerHTML = html;
            }
        }

        function loadHistory() {
            const container = document.getElementById('historyList');
            container.innerHTML = '';
            
            for (let i = 0; i < allExpenses.length; i++) {
                const expense = allExpenses[i];
                const html = '<div class="expense-item">' +
                    '<div class="expense-details">' +
                    '<h4>' + expense.description + '</h4>' +
                    '<p>' + expense.category + ' • Paid by ' + (expense.paidById === 'taha' ? 'Taha' : 'Burak') + ' • ' + expense.date + '</p>' +
                    '</div>' +
                    '<div class="expense-amount">$' + expense.amount.toFixed(2) + '</div>' +
                    '</div>';
                container.innerHTML += html;
            }
        }

        function loadReports() {
            if (allExpenses.length === 0) return;
            
            const total = allExpenses.reduce(function(sum, expense) { return sum + expense.amount; }, 0);
            const avgPerDay = total / 30;
            
            const categories = {};
            for (let i = 0; i < allExpenses.length; i++) {
                const expense = allExpenses[i];
                categories[expense.category] = (categories[expense.category] || 0) + expense.amount;
            }
            
            const topCategory = Object.keys(categories).reduce(function(a, b) { 
                return categories[a] > categories[b] ? a : b; 
            });
            
            document.getElementById('monthlyTotal').textContent = '$' + total.toFixed(2);
            document.getElementById('dailyAverage').textContent = '$' + avgPerDay.toFixed(2);
            document.getElementById('topCategory').textContent = topCategory;
            document.getElementById('pendingSettlement').textContent = '$' + Math.abs(
                allExpenses.filter(function(e) { return e.paidById === 'taha'; }).reduce(function(sum, e) { return sum + e.amount; }, 0) -
                allExpenses.filter(function(e) { return e.paidById === 'burak'; }).reduce(function(sum, e) { return sum + e.amount; }, 0)
            ).toFixed(2);
            
            let categoryHtml = '';
            for (const category in categories) {
                categoryHtml += '<div class="expense-item">';
                categoryHtml += '<div class="expense-details"><h4>' + category + '</h4></div>';
                categoryHtml += '<div class="expense-amount">$' + categories[category].toFixed(2) + '</div>';
                categoryHtml += '</div>';
            }
            document.getElementById('categoryBreakdown').innerHTML = categoryHtml;
        }        function loadSettings() {
            // Settings are already loaded in updateUserInfo
        }

        function calculateSettlement() {
            const tahaTotal = allExpenses.filter(function(e) { return e.paidById === 'taha'; }).reduce(function(sum, e) { return sum + e.amount; }, 0);
            const burakTotal = allExpenses.filter(function(e) { return e.paidById === 'burak'; }).reduce(function(sum, e) { return sum + e.amount; }, 0);
            const difference = tahaTotal - burakTotal;
            
            if (Math.abs(difference) < 0.01) {
                showSuccess('No settlement needed - expenses are balanced!');
            } else if (difference > 0) {
                showSuccess('Burak owes Taha $' + difference.toFixed(2));
            } else {
                showSuccess('Taha owes Burak $' + Math.abs(difference).toFixed(2));
            }
        }

        function applyFilters() {
            showSuccess('Filters applied successfully!');
            loadHistory();
        }

        // Add Expense Form Handler
        document.getElementById('expenseForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                amount: parseFloat(document.getElementById('expenseAmount').value),
                category: document.getElementById('expenseCategory').value,
                paidById: document.getElementById('expensePaidBy').value,
                date: document.getElementById('expenseDate').value,
                description: document.getElementById('expenseDescription').value
            };
            
            fetch('/api/expenses', {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + authToken 
                },
                body: JSON.stringify(formData)
            })
            .then(function(response) { 
                return response.json(); 
            })
            .then(function(data) {
                if (data.success) {
                    showSuccess('Expense added successfully!');
                    document.getElementById('expenseForm').reset();
                    setDefaultDate();
                    loadDashboardData();
                    showPage('dashboard');
                } else {
                    showError('Failed to add expense: ' + (data.error || 'Unknown error'), 'errorMessage');
                }
            })
            .catch(function(error) {
                console.error('Error adding expense:', error);
                showError('Network error. Please try again.', 'errorMessage');
            });
        });
    </script>
</body>
</html>