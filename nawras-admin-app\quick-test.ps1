# Quick deployment test
$DOMAIN = "partner.nawrasinchina.com"
$SERVER_IP = "*************"

Write-Host "🧪 QUICK DEPLOYMENT TEST" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green
Write-Host "Time: $(Get-Date -Format 'HH:mm:ss')" -ForegroundColor Yellow
Write-Host ""

# Test 1: Domain
try {
    $domainTest = Invoke-WebRequest -Uri "http://$DOMAIN" -TimeoutSec 5 -UseBasicParsing
    if ($domainTest.StatusCode -eq 200) {
        Write-Host "✅ Domain: HTTP 200 - WORKING!" -ForegroundColor Green
        if ($domainTest.Content -like "*SUCCESS!*") {
            Write-Host "   🎉 SUCCESS MESSAGE DETECTED!" -ForegroundColor Green
        }
    } else {
        Write-Host "⚠️ Domain: HTTP $($domainTest.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Domain: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: API Health
try {
    $healthTest = Invoke-WebRequest -Uri "http://$DOMAIN/api/health" -TimeoutSec 5 -UseBasicParsing
    if ($healthTest.StatusCode -eq 200) {
        Write-Host "✅ API Health: HTTP 200 - WORKING!" -ForegroundColor Green
        $healthData = $healthTest.Content | ConvertFrom-Json
        Write-Host "   Server: $($healthData.server)" -ForegroundColor White
        if ($healthData.server -eq "emergency-fix-deployed") {
            Write-Host "   🚀 NEW SERVER DEPLOYED!" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "❌ API Health: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Direct API (fallback)
try {
    $directTest = Invoke-WebRequest -Uri "http://$SERVER_IP`:3001/api/health" -TimeoutSec 5 -UseBasicParsing
    if ($directTest.StatusCode -eq 200) {
        Write-Host "✅ Direct API: HTTP 200 - WORKING!" -ForegroundColor Green
        $directData = $directTest.Content | ConvertFrom-Json
        Write-Host "   Server: $($directData.server)" -ForegroundColor White
    }
} catch {
    Write-Host "❌ Direct API: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "📋 INSTRUCTIONS:" -ForegroundColor Blue
Write-Host "If tests are failing, execute the deployment commands in DigitalOcean terminal:" -ForegroundColor White
Write-Host "See SIMPLE-STEPS.txt for step-by-step instructions" -ForegroundColor White
