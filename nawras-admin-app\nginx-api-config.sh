#!/bin/bash

# Configure nginx to serve API responses directly
# This is a temporary solution until we get Node.js working

echo "Configuring nginx to serve API responses..."

# Create API response files
mkdir -p /var/www/api/auth
mkdir -p /var/www/api

# Health endpoint
cat > /var/www/api/health << 'EOF'
{
  "status": "ok",
  "timestamp": "2025-06-02T16:00:00.000Z",
  "version": "1.0.0"
}
EOF

# Session endpoint (returns no session)
cat > /var/www/api/auth/session << 'EOF'
{
  "success": true,
  "data": null
}
EOF

# Expenses endpoint (requires auth - returns 401)
cat > /var/www/api/expenses << 'EOF'
{
  "success": false,
  "error": "Authentication required"
}
EOF

# Settlements endpoint (requires auth - returns 401)
cat > /var/www/api/settlements << 'EOF'
{
  "success": false,
  "error": "Authentication required"
}
EOF

# Update nginx configuration
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80;
    server_name partner.nawrasinchina.com;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Serve frontend
    location / {
        root /opt/nawras-admin/dist;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API endpoints - serve static responses for now
    location /api/health {
        add_header Content-Type application/json;
        alias /var/www/api/health;
    }
    
    location /api/auth/session {
        add_header Content-Type application/json;
        alias /var/www/api/auth/session;
    }
    
    location /api/expenses {
        add_header Content-Type application/json;
        return 401 '{"success":false,"error":"Authentication required"}';
    }
    
    location /api/settlements {
        add_header Content-Type application/json;
        return 401 '{"success":false,"error":"Authentication required"}';
    }
    
    # Default API response
    location /api/ {
        add_header Content-Type application/json;
        return 404 '{"success":false,"error":"API endpoint not found"}';
    }
}
EOF

# Test and reload nginx
nginx -t && systemctl reload nginx

echo "Nginx configured to serve API responses"
echo "Testing endpoints..."

# Test health endpoint
echo "Health endpoint:"
curl -s http://localhost/api/health

echo ""
echo "Session endpoint:"
curl -s http://localhost/api/auth/session

echo ""
echo "Expenses endpoint (should return 401):"
curl -s http://localhost/api/expenses

echo ""
echo "Configuration completed!"
