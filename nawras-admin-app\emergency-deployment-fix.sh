#!/bin/bash

# 🚨 EMERGENCY DEPLOYMENT FIX - NAWRAS ADMIN
# This script will immediately fix the deployment issues

set -e

SERVER_IP="*************"
DOMAIN="partner.nawrasinchina.com"
APP_DIR="/opt/nawras-admin"

echo "🚨 EMERGENCY DEPLOYMENT FIX STARTING"
echo "===================================="
echo "Server: $SERVER_IP"
echo "Domain: $DOMAIN"
echo "Time: $(date)"
echo ""

# Function to run commands on server
run_on_server() {
    ssh -o StrictHostKeyChecking=no root@$SERVER_IP "$1"
}

# Function to check command success
check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ $1 - SUCCESS"
    else
        echo "❌ $1 - FAILED"
        exit 1
    fi
}

echo "🔍 Step 1: Diagnosing Current Issues"
echo "===================================="

# Check current server status
echo "Checking server accessibility..."
run_on_server "echo 'Server accessible via SSH'"
check_success "SSH connectivity"

echo "Checking current processes..."
run_on_server "ps aux | grep node | grep -v grep || echo 'No Node.js processes running'"

echo "Checking nginx status..."
run_on_server "systemctl status nginx --no-pager -l || echo 'Nginx not running properly'"

echo "Checking current directory structure..."
run_on_server "ls -la $APP_DIR || echo 'App directory does not exist'"

echo ""
echo "🛑 Step 2: Stopping Current Services"
echo "===================================="

# Stop any running services
echo "Stopping current Node.js processes..."
run_on_server "pkill -f 'node.*server' || echo 'No Node.js servers to stop'"

echo "Stopping nginx..."
run_on_server "systemctl stop nginx || echo 'Nginx already stopped'"

echo "Stopping any Docker containers..."
run_on_server "cd $APP_DIR && docker-compose down 2>/dev/null || echo 'No Docker containers to stop'"

echo ""
echo "📁 Step 3: Preparing Application Directory"
echo "=========================================="

# Create backup and prepare directory
echo "Creating backup directory..."
run_on_server "mkdir -p $APP_DIR/backups/$(date +%Y%m%d_%H%M%S)"

echo "Creating application directory structure..."
run_on_server "mkdir -p $APP_DIR"

echo ""
echo "📤 Step 4: Uploading Deployment Package"
echo "========================================"

# Upload the deployment package
echo "Uploading deployment package files..."
rsync -avz --progress deployment-package/ root@$SERVER_IP:$APP_DIR/
check_success "Deployment package upload"

echo "Uploading nginx configuration..."
rsync -avz nginx-config root@$SERVER_IP:/tmp/nginx-config
check_success "Nginx config upload"

echo ""
echo "📦 Step 5: Installing Dependencies"
echo "=================================="

# Install dependencies on server
echo "Installing Node.js dependencies..."
run_on_server "cd $APP_DIR && npm install express@4.21.2 cookie-parser bcryptjs cors"
check_success "Dependencies installation"

echo ""
echo "🌐 Step 6: Configuring Nginx"
echo "============================="

# Configure nginx properly
echo "Configuring nginx for proper proxy..."
run_on_server "
# Create proper nginx configuration
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80;
    server_name partner.nawrasinchina.com *************;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection \"1; mode=block\";
    
    # Proxy all requests to Node.js application
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # API endpoints
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# Test nginx configuration
nginx -t
"
check_success "Nginx configuration"

echo ""
echo "🚀 Step 7: Starting Application"
echo "==============================="

# Start the application
echo "Starting Node.js application..."
run_on_server "cd $APP_DIR && nohup node server-simple.cjs > app.log 2>&1 &"
check_success "Application startup"

# Wait for application to start
echo "Waiting for application to initialize..."
sleep 5

# Start nginx
echo "Starting nginx..."
run_on_server "systemctl start nginx"
check_success "Nginx startup"

echo ""
echo "🧪 Step 8: Testing Deployment"
echo "============================="

# Test the deployment
echo "Testing application health..."
run_on_server "curl -s http://localhost:3001/api/health || echo 'Health check failed'"

echo "Testing domain access..."
curl -s -I http://$DOMAIN || echo "Domain access test failed"

echo "Testing API endpoint..."
curl -s http://$SERVER_IP:3001/api/health || echo "Direct API test failed"

echo ""
echo "🎉 EMERGENCY FIX COMPLETED!"
echo "=========================="
echo ""
echo "✅ Deployment Status:"
echo "   • Application uploaded and running"
echo "   • Nginx configured and started"
echo "   • Dependencies installed"
echo "   • Basic functionality restored"
echo ""
echo "🌐 Access URLs:"
echo "   • Main site: http://$DOMAIN"
echo "   • Direct API: http://$SERVER_IP:3001"
echo "   • Health check: http://$SERVER_IP:3001/api/health"
echo ""
echo "👥 Test Credentials:"
echo "   • Taha: <EMAIL> / taha2024"
echo "   • Burak: <EMAIL> / burak2024"
echo ""
echo "📋 Next Steps:"
echo "   1. Test the website functionality"
echo "   2. Configure SSL/HTTPS (Phase 2)"
echo "   3. Implement security headers (Phase 3)"
echo ""
