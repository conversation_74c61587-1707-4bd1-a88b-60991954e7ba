const express = require('express');
const cors = require('cors');
const path = require('path');
const cookieParser = require('cookie-parser');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:5173',
    'https://partner.nawrasinchina.com',
    'http://partner.nawrasinchina.com',
    'http://*************',
  ],
  credentials: true,
}));
app.use(express.json());
app.use(cookieParser());

// In-memory storage with sample data
let expenses = [
  {
    id: 1,
    amount: 25.50,
    description: "Lunch at restaurant",
    category: "Food",
    paidById: "taha",
    date: "2024-01-15",
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    amount: 60.99,
    description: "Grocery shopping",
    category: "Groceries", 
    paidById: "burak",
    date: "2024-01-16",
    createdAt: new Date().toISOString()
  },
  {
    id: 3,
    amount: 15.75,
    description: "Coffee and snacks",
    category: "Food",
    paidById: "taha",
    date: "2024-01-17",
    createdAt: new Date().toISOString()
  }
];

let settlements = [
  {
    id: 1,
    amount: 30.00,
    paidBy: "taha",
    paidTo: "burak", 
    description: "Settlement for shared expenses",
    date: "2024-01-17",
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    amount: 25.75,
    paidBy: "burak",
    paidTo: "taha", 
    description: "Reimbursement for utilities",
    date: "2024-01-18",
    createdAt: new Date().toISOString()
  }
];

// Authentication data
const mockUsers = [
  {
    id: "taha",
    email: "<EMAIL>",
    name: "Taha",
    password: "taha2024", // In production, this would be hashed
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: "burak",
    email: "<EMAIL>",
    name: "Burak",
    password: "burak2024", // In production, this would be hashed
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

let sessions = new Map();

// Auth middleware
const requireAuth = (req, res, next) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') ||
                      req.cookies?.session;

  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }

  const session = sessions.get(sessionToken);

  // Check if session is expired
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    res.clearCookie('session');
    return res.status(401).json({ success: false, error: 'Session expired' });
  }

  req.user = session.user;
  req.session = session;
  next();
};

// Authentication Routes
app.post('/api/auth/sign-in', (req, res) => {
  const { email, password } = req.body;

  const user = mockUsers.find(u => u.email === email && u.password === password);

  if (!user) {
    return res.status(401).json({
      success: false,
      error: 'Invalid email or password'
    });
  }

  // Create session
  const sessionToken = `session_${Date.now()}_${Math.random()}`;
  const session = {
    id: sessionToken,
    userId: user.id,
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    },
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    token: sessionToken,
  };

  sessions.set(sessionToken, session);

  // Set cookie
  res.cookie('session', sessionToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  });

  res.json({
    success: true,
    data: {
      session,
      user: session.user,
    }
  });
});

app.post('/api/auth/sign-out', (req, res) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') ||
                      req.cookies?.session;

  if (sessionToken) {
    sessions.delete(sessionToken);
  }

  res.clearCookie('session');
  res.json({ success: true });
});

app.get('/api/auth/session', (req, res) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') ||
                      req.cookies?.session;

  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.json({ success: true, data: null });
  }

  const session = sessions.get(sessionToken);

  // Check if session is expired
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    res.clearCookie('session');
    return res.json({ success: true, data: null });
  }

  res.json({
    success: true,
    data: session
  });
});

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

app.get('/api/expenses', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: expenses,
    total: expenses.length,
    timestamp: new Date().toISOString()
  });
});

app.post('/api/expenses', requireAuth, (req, res) => {
  const expense = {
    id: expenses.length + 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  expenses.push(expense);
  res.json({ success: true, data: expense });
});

app.get('/api/settlements', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: settlements,
    total: settlements.length,
    timestamp: new Date().toISOString()
  });
});

app.post('/api/settlements', requireAuth, (req, res) => {
  const settlement = {
    id: settlements.length + 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  settlements.push(settlement);
  res.json({ success: true, data: settlement });
});

// Serve static files (frontend)
app.use(express.static(path.join(__dirname, 'dist')));

// Handle client-side routing
app.get('*', (req, res) => {
  if (req.path.startsWith('/api')) {
    return res.status(404).json({ success: false, error: 'API endpoint not found' });
  }
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ success: false, error: 'Internal server error' });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Nawras Admin server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🌐 Frontend: http://localhost:${PORT}`);
  console.log(`🔐 Authentication enabled`);
  console.log(`👥 Users: <EMAIL>, <EMAIL>`);
});
