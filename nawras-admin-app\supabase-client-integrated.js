const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');

const app = express();
const PORT = 3001;

// NEW DEDICATED NAWRAS ADMIN SUPABASE PROJECT
const SUPABASE_URL = 'https://khsdtnhjvgucpgybadki.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtoc2R0bmhqdmd1Y3BneWJhZGtpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg5ODY3NTUsImV4cCI6MjA2NDU2Mjc1NX0.iAp-OtmHThl9v0y42Gt9y-FdKQKucocRx874_LmIwuU';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Middleware
app.use(cors());
app.use(express.json());

// Helper functions
function generateSessionToken() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

async function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ success: false, error: 'Access token required' });
    }

    try {
        // Check session in Supabase
        const { data: session, error } = await supabase
            .from('nawras_sessions')
            .select(`
                *,
                nawras_users (
                    user_id,
                    name,
                    email
                )
            `)
            .eq('token', token)
            .gt('expires_at', new Date().toISOString())
            .single();

        if (error || !session) {
            return res.status(401).json({ success: false, error: 'Invalid or expired token' });
        }

        req.user = {
            id: session.nawras_users.user_id,
            name: session.nawras_users.name,
            email: session.nawras_users.email
        };
        next();
    } catch (error) {
        console.error('Auth error:', error);
        return res.status(401).json({ success: false, error: 'Authentication failed' });
    }
}

// SERVE HTML FRONTEND ON ROOT PATH
app.get('/', (req, res) => {
    res.send(\`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Live Supabase Integration</title>
    <style>
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; padding: 50px; text-align: center; color: white; }
        .container { background: white; color: #333; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 800px; margin: 0 auto; }
        .success { background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .info { background: #e7f3ff; color: #0c5460; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: left; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px; }
        .btn:hover { background: #0056b3; }
        .test-results { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; text-align: left; }
        .live-indicator { background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8rem; display: inline-block; margin-left: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Supabase Client Integration Active!</h1>
        <span class="live-indicator">● LIVE DATABASE</span>
        
        <div class="success">
            <h3>✅ REAL-TIME SUPABASE INTEGRATION</h3>
            <p>JavaScript client connected to dedicated Nawras Admin database</p>
        </div>
        
        <div class="info">
            <h3>📊 Live Database Connection</h3>
            <p><strong>Project:</strong> nawras-admin-expense-tracker</p>
            <p><strong>Database:</strong> \${SUPABASE_URL}</p>
            <p><strong>Status:</strong> Real-time operations active</p>
            <p><strong>Features:</strong> Live queries, instant updates, persistent storage</p>
        </div>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
            <button class="btn" onclick="testConnection()">Test Connection</button>
            <button class="btn" onclick="loadUsers()">Load Users</button>
            <button class="btn" onclick="loadExpenses()">Load Expenses</button>
            <button class="btn" onclick="addTestExpense()">Add Test Expense</button>
            <button class="btn" onclick="showLogin()">Go to Login</button>
        </div>
        
        <div id="result" class="test-results" style="display: none;">
            <h4>Test Results:</h4>
            <pre id="resultContent"></pre>
        </div>
    </div>
    
    <script>
        function showResult(title, data) {
            document.getElementById('result').style.display = 'block';
            document.getElementById('resultContent').textContent = 
                title + '\\n' + '='.repeat(50) + '\\n' + JSON.stringify(data, null, 2);
        }
        
        function testConnection() {
            fetch('/api/supabase/test-connection')
                .then(response => response.json())
                .then(data => showResult('Supabase Connection Test', data));
        }
        
        function loadUsers() {
            fetch('/api/supabase/users')
                .then(response => response.json())
                .then(data => showResult('Live Users from Supabase', data));
        }
        
        function loadExpenses() {
            fetch('/api/supabase/expenses')
                .then(response => response.json())
                .then(data => showResult('Live Expenses from Supabase', data));
        }
        
        function addTestExpense() {
            fetch('/api/supabase/add-test-expense', { method: 'POST' })
                .then(response => response.json())
                .then(data => showResult('New Expense Added to Supabase', data));
        }
        
        function showLogin() {
            window.location.href = '/login';
        }
    </script>
</body>
</html>\`);
});

// API ENDPOINTS WITH LIVE SUPABASE INTEGRATION

// Health check with Supabase status
app.get('/api/health', async (req, res) => {
    try {
        // Test Supabase connection
        const { data, error } = await supabase
            .from('nawras_users')
            .select('count')
            .limit(1);

        res.json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            version: '5.0.0',
            server: 'supabase-client-integrated',
            project: 'nawras-admin-expense-tracker',
            project_id: 'khsdtnhjvgucpgybadki',
            database: SUPABASE_URL,
            supabase_status: error ? 'error' : 'connected',
            message: 'Live Supabase client integration active'
        });
    } catch (error) {
        res.json({
            status: 'ok',
            supabase_status: 'connection_error',
            error: error.message
        });
    }
});

// Test Supabase connection
app.get('/api/supabase/test-connection', async (req, res) => {
    try {
        const { data, error } = await supabase
            .from('nawras_users')
            .select('user_id, name, email')
            .limit(5);

        if (error) {
            return res.json({
                success: false,
                error: error.message,
                details: error
            });
        }

        res.json({
            success: true,
            connection: 'active',
            project: 'nawras-admin-expense-tracker',
            users_found: data.length,
            sample_data: data
        });
    } catch (error) {
        res.json({
            success: false,
            error: error.message
        });
    }
});

// Load users from Supabase
app.get('/api/supabase/users', async (req, res) => {
    try {
        const { data: users, error } = await supabase
            .from('nawras_users')
            .select('*');

        if (error) {
            return res.status(500).json({
                success: false,
                error: error.message
            });
        }

        res.json({
            success: true,
            source: 'live_supabase_database',
            project_id: 'khsdtnhjvgucpgybadki',
            data: users
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Load expenses from Supabase with user details
app.get('/api/supabase/expenses', async (req, res) => {
    try {
        const { data: expenses, error } = await supabase
            .from('nawras_expenses')
            .select(\`
                *,
                nawras_users (
                    name,
                    email
                )
            \`)
            .order('date', { ascending: false });

        if (error) {
            return res.status(500).json({
                success: false,
                error: error.message
            });
        }

        res.json({
            success: true,
            source: 'live_supabase_database',
            project_id: 'khsdtnhjvgucpgybadki',
            total_expenses: expenses.length,
            data: expenses
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Add test expense to Supabase
app.post('/api/supabase/add-test-expense', async (req, res) => {
    try {
        const testExpense = {
            amount: Math.floor(Math.random() * 100) + 10,
            category: 'Test',
            description: \`Live Supabase test expense - \${new Date().toLocaleTimeString()}\`,
            paid_by_id: Math.random() > 0.5 ? 'taha' : 'burak',
            date: new Date().toISOString().split('T')[0]
        };

        const { data: newExpense, error } = await supabase
            .from('nawras_expenses')
            .insert(testExpense)
            .select(\`
                *,
                nawras_users (
                    name,
                    email
                )
            \`)
            .single();

        if (error) {
            return res.status(500).json({
                success: false,
                error: error.message
            });
        }

        res.json({
            success: true,
            message: 'Test expense added to live Supabase database',
            project_id: 'khsdtnhjvgucpgybadki',
            data: newExpense
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Authentication with Supabase
app.post('/api/auth/sign-in', async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({ 
                success: false, 
                error: 'Email and password are required' 
            });
        }

        // Check user in Supabase
        const { data: user, error } = await supabase
            .from('nawras_users')
            .select('*')
            .eq('email', email)
            .eq('password_hash', password) // In production, use proper password hashing
            .single();

        if (error || !user) {
            return res.status(401).json({ 
                success: false, 
                error: 'Invalid credentials' 
            });
        }

        // Create session in Supabase
        const token = generateSessionToken();
        const expiresAt = new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)); // 7 days

        const { error: sessionError } = await supabase
            .from('nawras_sessions')
            .insert({
                token,
                user_id: user.user_id,
                expires_at: expiresAt.toISOString()
            });

        if (sessionError) {
            console.error('Session creation error:', sessionError);
            return res.status(500).json({ 
                success: false, 
                error: 'Failed to create session' 
            });
        }

        res.json({
            success: true,
            data: {
                user: { 
                    id: user.user_id, 
                    name: user.name, 
                    email: user.email 
                },
                session: { 
                    token, 
                    expiresAt: expiresAt.getTime() 
                }
            }
        });

    } catch (error) {
        console.error('Sign-in error:', error);
        res.status(500).json({ 
            success: false, 
            error: 'Internal server error' 
        });
    }
});

app.get('/api/auth/session', authenticateToken, (req, res) => {
    res.json({
        success: true,
        data: {
            user: req.user
        }
    });
});

app.post('/api/auth/sign-out', authenticateToken, async (req, res) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];
        
        if (token) {
            // Delete session from Supabase
            await supabase
                .from('nawras_sessions')
                .delete()
                .eq('token', token);
        }

        res.json({ 
            success: true, 
            message: 'Signed out successfully' 
        });
    } catch (error) {
        console.error('Sign-out error:', error);
        res.json({ 
            success: true, 
            message: 'Signed out successfully' 
        });
    }
});

// Protected endpoints with live Supabase
app.get('/api/expenses', authenticateToken, async (req, res) => {
    try {
        const { data: expenses, error } = await supabase
            .from('nawras_expenses')
            .select('*')
            .order('date', { ascending: false });

        if (error) {
            console.error('Expenses fetch error:', error);
            return res.status(500).json({ 
                success: false, 
                error: 'Failed to fetch expenses from Supabase' 
            });
        }

        res.json({ 
            success: true, 
            source: 'live_supabase_database',
            project_id: 'khsdtnhjvgucpgybadki',
            data: expenses || [] 
        });
    } catch (error) {
        console.error('Expenses error:', error);
        res.status(500).json({ 
            success: false, 
            error: 'Internal server error' 
        });
    }
});

// Cleanup expired sessions (run every hour)
setInterval(async () => {
    try {
        const { error } = await supabase
            .from('nawras_sessions')
            .delete()
            .lt('expires_at', new Date().toISOString());

        if (error) {
            console.error('Session cleanup error:', error);
        } else {
            console.log('✅ Expired sessions cleaned up at', new Date().toISOString());
        }
    } catch (error) {
        console.error('Session cleanup error:', error);
    }
}, 60 * 60 * 1000); // 1 hour

// Start server
app.listen(PORT, () => {
    console.log('🚀 NAWRAS ADMIN - LIVE SUPABASE CLIENT INTEGRATION');
    console.log(\`✅ Server running on port \${PORT}\`);
    console.log(\`✅ Project: nawras-admin-expense-tracker\`);
    console.log(\`✅ Project ID: khsdtnhjvgucpgybadki\`);
    console.log(\`✅ Supabase URL: \${SUPABASE_URL}\`);
    console.log(\`✅ Live database operations active\`);
    console.log(\`✅ Real-time queries enabled\`);
    console.log(\`✅ Session management in Supabase\`);
    console.log(\`🌐 Access: http://partner.nawrasinchina.com:\${PORT}\`);
});