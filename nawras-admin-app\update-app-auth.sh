#!/bin/bash

# Update Application for Better Auth Integration
# Modifies existing components to include authentication

echo "🔄 UPDATING APPLICATION FOR AUTHENTICATION"
echo "=========================================="
echo ""

# Step 1: Update Main App Component
echo "📱 Step 1: Updating Main App Component"
echo "======================================"

# Backup current App.tsx
cp src/App.tsx src/App.tsx.backup

# Create new App.tsx with authentication
cat > src/App.tsx << 'EOF'
import React from 'react';
import { Router, Route, Switch } from 'wouter';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Auth components
import { AuthProvider, ProtectedRoute } from './components/auth';

// Layout components
import { Sidebar } from './components/Sidebar';
import { ErrorBoundary } from './components/ErrorBoundary';

// Page components
import HomePage from './pages/index';
import AddExpensePage from './pages/add-expense';
import SettlementPage from './pages/settlement';
import HistoryPage from './pages/history';
import ReportsPage from './pages/reports';
import SettingsPage from './pages/settings';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      retry: 2,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
});

const AppContent: React.FC = () => {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-auto">
          <ErrorBoundary>
            <Router>
              <Switch>
                <Route path="/" component={HomePage} />
                <Route path="/add-expense" component={AddExpensePage} />
                <Route path="/settlement" component={SettlementPage} />
                <Route path="/history" component={HistoryPage} />
                <Route path="/reports" component={ReportsPage} />
                <Route path="/settings" component={SettingsPage} />
                
                {/* 404 Route */}
                <Route>
                  <div className="p-8">
                    <div className="max-w-md mx-auto text-center">
                      <h1 className="text-2xl font-bold text-gray-900 mb-4">
                        Page Not Found
                      </h1>
                      <p className="text-gray-600 mb-6">
                        The page you're looking for doesn't exist.
                      </p>
                      <a
                        href="/"
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        Go to Dashboard
                      </a>
                    </div>
                  </div>
                </Route>
              </Switch>
            </Router>
          </ErrorBoundary>
        </main>
      </div>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <ProtectedRoute>
          <AppContent />
        </ProtectedRoute>
      </AuthProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
};

export default App;
EOF

echo "✅ App.tsx updated with authentication"

# Step 2: Update Sidebar Component
echo "🔧 Step 2: Updating Sidebar Component"
echo "====================================="

# Backup current Sidebar
cp src/components/Sidebar.tsx src/components/Sidebar.tsx.backup

# Update Sidebar to include user info and logout
cat > src/components/Sidebar.tsx << 'EOF'
import React, { useState } from 'react';
import { Link, useLocation } from 'wouter';
import { 
  Home, 
  Plus, 
  History, 
  BarChart3, 
  Settings, 
  Menu, 
  X,
  DollarSign,
  User
} from 'lucide-react';
import { useAuth } from './auth/AuthProvider';
import { LogoutButton } from './auth/LogoutButton';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
}

const navigation: NavItem[] = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'Add Expense', href: '/add-expense', icon: Plus },
  { name: 'Settlement', href: '/settlement', icon: DollarSign },
  { name: 'History', href: '/history', icon: History },
  { name: 'Reports', href: '/reports', icon: BarChart3 },
  { name: 'Settings', href: '/settings', icon: Settings },
];

export const Sidebar: React.FC = () => {
  const [location] = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user } = useAuth();

  const isActive = (href: string) => {
    if (href === '/') {
      return location === '/';
    }
    return location.startsWith(href);
  };

  const closeMobileMenu = () => setIsMobileMenuOpen(false);

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 rounded-lg bg-white shadow-lg border border-gray-200"
        >
          {isMobileMenuOpen ? (
            <X className="h-6 w-6 text-gray-600" />
          ) : (
            <Menu className="h-6 w-6 text-gray-600" />
          )}
        </button>
      </div>

      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={closeMobileMenu}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed lg:static inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  Nawras Admin
                </h1>
                <p className="text-xs text-gray-500">Expense Tracker</p>
              </div>
            </div>
          </div>

          {/* User Info */}
          {user && (
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <User className="h-5 w-5 text-blue-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {user.name}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {user.email}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href);
              
              return (
                <Link key={item.name} href={item.href}>
                  <a
                    onClick={closeMobileMenu}
                    className={`
                      flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors
                      ${active
                        ? 'bg-blue-50 text-blue-700 border border-blue-200'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                      }
                    `}
                  >
                    <Icon className={`h-5 w-5 ${active ? 'text-blue-600' : 'text-gray-500'}`} />
                    <span>{item.name}</span>
                  </a>
                </Link>
              );
            })}
          </nav>

          {/* Footer with Logout */}
          <div className="p-4 border-t border-gray-200">
            <LogoutButton className="w-full justify-center" />
          </div>
        </div>
      </div>

      {/* Main content spacer for mobile */}
      <div className="lg:hidden h-16" />
    </>
  );
};
EOF

echo "✅ Sidebar updated with user info and logout"

# Step 3: Update Server Configuration
echo "🖥️ Step 3: Updating Server Configuration"
echo "========================================"

# Backup current server
cp server-simple.js server-simple.js.backup

# Update server to include auth routes
cat > server-simple.js << 'EOF'
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:5173',
    'https://partner.nawrasinchina.com',
    'http://partner.nawrasinchina.com',
    'http://*************',
  ],
  credentials: true,
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname, 'dist')));

// Mock data storage
let expenses = [
  {
    id: 1,
    amount: 45.67,
    description: "Grocery shopping",
    category: "Food",
    paidById: "taha",
    date: "2024-12-01",
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    amount: 120.00,
    description: "Electric bill",
    category: "Utilities",
    paidById: "burak",
    date: "2024-12-02",
    createdAt: new Date().toISOString()
  },
  {
    id: 3,
    amount: 67.89,
    description: "Dinner out",
    category: "Food",
    paidById: "taha",
    date: "2024-12-03",
    createdAt: new Date().toISOString()
  },
  {
    id: 4,
    amount: 25.50,
    description: "Coffee and snacks",
    category: "Food",
    paidById: "burak",
    date: "2024-12-04",
    createdAt: new Date().toISOString()
  },
  {
    id: 5,
    amount: 42.43,
    description: "Transportation",
    category: "Transportation",
    paidById: "taha",
    date: "2024-12-05",
    createdAt: new Date().toISOString()
  }
];

let settlements = [
  {
    id: 1,
    amount: 50.00,
    paidBy: "burak",
    paidTo: "taha",
    description: "Settling dinner expenses",
    date: "2024-12-01",
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    amount: 25.75,
    paidBy: "taha",
    paidTo: "burak",
    description: "Coffee reimbursement",
    date: "2024-12-03",
    createdAt: new Date().toISOString()
  }
];

// Better Auth setup (simplified for mock)
const mockUsers = [
  {
    id: "taha",
    email: "<EMAIL>",
    name: "Taha",
    password: "taha2024", // In production, this would be hashed
  },
  {
    id: "burak",
    email: "<EMAIL>",
    name: "Burak",
    password: "burak2024", // In production, this would be hashed
  }
];

let sessions = new Map();

// Auth middleware
const requireAuth = (req, res, next) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') || 
                      req.cookies?.session;
  
  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
  
  req.user = sessions.get(sessionToken);
  next();
};

// Auth routes
app.post('/api/auth/sign-in', (req, res) => {
  const { email, password } = req.body;
  
  const user = mockUsers.find(u => u.email === email && u.password === password);
  
  if (!user) {
    return res.status(401).json({ 
      success: false, 
      error: 'Invalid email or password' 
    });
  }
  
  // Create session
  const sessionToken = `session_${Date.now()}_${Math.random()}`;
  const session = {
    id: sessionToken,
    userId: user.id,
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      emailVerified: true,
    },
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
  };
  
  sessions.set(sessionToken, session);
  
  // Set cookie
  res.cookie('session', sessionToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  });
  
  res.json({
    success: true,
    data: {
      session,
      user: session.user,
    }
  });
});

app.post('/api/auth/sign-out', (req, res) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') || 
                      req.cookies?.session;
  
  if (sessionToken) {
    sessions.delete(sessionToken);
  }
  
  res.clearCookie('session');
  res.json({ success: true });
});

app.get('/api/auth/session', (req, res) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') || 
                      req.cookies?.session;
  
  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.json({ success: true, data: null });
  }
  
  const session = sessions.get(sessionToken);
  
  // Check if session is expired
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    res.clearCookie('session');
    return res.json({ success: true, data: null });
  }
  
  res.json({
    success: true,
    data: session
  });
});

// Protected API routes
app.get('/api/expenses', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: expenses
  });
});

app.post('/api/expenses', requireAuth, (req, res) => {
  const newExpense = {
    id: expenses.length + 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  
  expenses.push(newExpense);
  
  res.status(201).json({
    success: true,
    data: newExpense
  });
});

app.get('/api/settlements', requireAuth, (req, res) => {
  res.json({
    success: true,
    data: settlements
  });
});

app.post('/api/settlements', requireAuth, (req, res) => {
  const newSettlement = {
    id: settlements.length + 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  
  settlements.push(newSettlement);
  
  res.status(201).json({
    success: true,
    data: newSettlement
  });
});

// Health check
app.get('/health', (req, res) => {
  res.status(200).send('healthy');
});

// Serve React app for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

app.listen(PORT, () => {
  console.log(`✅ Server running on port ${PORT}`);
  console.log(`🔐 Authentication enabled`);
  console.log(`👥 Users: <EMAIL>, <EMAIL>`);
});
EOF

echo "✅ Server updated with authentication"

# Step 4: Update Package.json Dependencies
echo "📦 Step 4: Updating Dependencies"
echo "==============================="

# Add cookie-parser for server
npm install cookie-parser
npm install --save-dev @types/cookie-parser

echo "✅ Dependencies updated"

echo ""

echo "🎉 APPLICATION UPDATE COMPLETE!"
echo "==============================="
echo ""
echo "✅ Updated Components:"
echo "   • App.tsx - Added AuthProvider and ProtectedRoute"
echo "   • Sidebar.tsx - Added user info and logout button"
echo "   • server-simple.js - Added authentication routes"
echo "   • Package dependencies updated"
echo ""
echo "✅ Authentication Features:"
echo "   • Route protection for all pages"
echo "   • User session management"
echo "   • Secure API endpoints"
echo "   • Login/logout functionality"
echo ""
echo "📋 Next Steps:"
echo "   1. Build the application: npm run build"
echo "   2. Test authentication locally"
echo "   3. Deploy to production server"
echo "   4. Test production authentication"
echo ""
