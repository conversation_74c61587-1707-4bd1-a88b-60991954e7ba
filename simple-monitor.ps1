# Simple deployment monitoring script
$DOMAIN = "partner.nawrasinchina.com"
$SERVER_IP = "*************"

Write-Host "🔍 DEPLOYMENT MONITORING" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green
Write-Host "Domain: $DOMAIN" -ForegroundColor Yellow
Write-Host "Server: $SERVER_IP" -ForegroundColor Yellow
Write-Host "Started: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

Write-Host "📋 DEPLOYMENT COMMANDS TO EXECUTE IN DIGITALOCEAN TERMINAL:" -ForegroundColor Blue
Write-Host "==========================================================" -ForegroundColor Blue
Write-Host "1. pkill -f `"node.*server`"" -ForegroundColor Green
Write-Host "2. systemctl stop nginx" -ForegroundColor Green
Write-Host "3. mkdir -p /opt/nawras-admin && cd /opt/nawras-admin" -ForegroundColor Green
Write-Host "4. npm init -y && npm install express cors" -ForegroundColor Green
Write-Host "5. Create server-fix.js file (see SIMPLE-STEPS.txt)" -ForegroundColor Green
Write-Host "6. nohup node server-fix.js > app.log 2>&1 &" -ForegroundColor Green
Write-Host "7. Configure nginx (see SIMPLE-STEPS.txt)" -ForegroundColor Green
Write-Host "8. nginx -t && systemctl start nginx" -ForegroundColor Green
Write-Host ""

for ($i = 1; $i -le 40; $i++) {
    $timestamp = Get-Date -Format 'HH:mm:ss'
    Write-Host "📊 Check #$i - $timestamp" -ForegroundColor Blue
    
    # Test domain
    try {
        $domainTest = Invoke-WebRequest -Uri "http://$DOMAIN" -TimeoutSec 5 -UseBasicParsing
        if ($domainTest.StatusCode -eq 200) {
            Write-Host "✅ BREAKTHROUGH! Domain is now working!" -ForegroundColor Green
            if ($domainTest.Content -like "*SUCCESS!*") {
                Write-Host "🎉 SUCCESS MESSAGE DETECTED!" -ForegroundColor Green
            }
            if ($domainTest.Content -like "*Nawras Admin*") {
                Write-Host "🏢 NAWRAS ADMIN DETECTED!" -ForegroundColor Green
            }
        } else {
            Write-Host "⚠️ Domain: HTTP $($domainTest.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ Domain: 403 Forbidden (deployment not executed yet)" -ForegroundColor Red
    }
    
    # Test API Health
    try {
        $healthTest = Invoke-WebRequest -Uri "http://$DOMAIN/api/health" -TimeoutSec 5 -UseBasicParsing
        if ($healthTest.StatusCode -eq 200) {
            Write-Host "✅ API Health: Working!" -ForegroundColor Green
            $healthData = $healthTest.Content | ConvertFrom-Json
            Write-Host "   Server: $($healthData.server)" -ForegroundColor White
            if ($healthData.server -eq "emergency-fix-deployed") {
                Write-Host "🚀 NEW SERVER DEPLOYED!" -ForegroundColor Green
                Write-Host ""
                Write-Host "🎉 DEPLOYMENT SUCCESSFUL!" -ForegroundColor Green
                Write-Host "=========================" -ForegroundColor Green
                Write-Host "✅ Website: http://$DOMAIN" -ForegroundColor Green
                Write-Host "✅ API Health: http://$DOMAIN/api/health" -ForegroundColor Green
                Write-Host "✅ API Expenses: http://$DOMAIN/api/expenses" -ForegroundColor Green
                Write-Host "✅ API Settlements: http://$DOMAIN/api/settlements" -ForegroundColor Green
                break
            }
        }
    } catch {
        Write-Host "❌ API Health: Not accessible" -ForegroundColor Red
    }
    
    # Test direct API (fallback)
    try {
        $directTest = Invoke-WebRequest -Uri "http://$SERVER_IP`:3001/api/health" -TimeoutSec 5 -UseBasicParsing
        if ($directTest.StatusCode -eq 200) {
            $directData = $directTest.Content | ConvertFrom-Json
            Write-Host "📡 Direct API: $($directData.server)" -ForegroundColor Cyan
        }
    } catch {
        Write-Host "❌ Direct API: Not accessible" -ForegroundColor Red
    }
    
    Write-Host ""
    
    if ($i -lt 40) {
        Start-Sleep -Seconds 30
    }
}

Write-Host "📋 MONITORING COMPLETED" -ForegroundColor Blue
Write-Host "If deployment was not detected, please execute the commands manually." -ForegroundColor Yellow
