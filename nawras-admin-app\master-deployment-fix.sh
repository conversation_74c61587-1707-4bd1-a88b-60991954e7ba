#!/bin/bash

# Master Deployment Fix Script
# Orchestrates the complete deployment fix process

set -e

SERVER_IP="*************"
DOMAIN="partner.nawrasinchina.com"

echo "🚀 MASTER DEPLOYMENT FIX ORCHESTRATOR"
echo "====================================="
echo "Server: $SERVER_IP"
echo "Domain: $DOMAIN"
echo ""
echo "This script will execute the complete deployment fix process:"
echo "1. Emergency Security Fix (1-2 hours)"
echo "2. Frontend Connectivity Fix"
echo "3. Authentication System Deployment"
echo "4. Comprehensive Validation"
echo "5. Production Hardening (optional)"
echo ""

# Function to prompt for continuation
prompt_continue() {
    local message="$1"
    echo ""
    echo "⏸️  $message"
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Deployment stopped by user"
        exit 1
    fi
}

# Function to check if script exists and is executable
check_script() {
    local script="$1"
    if [ ! -f "$script" ]; then
        echo "❌ Script not found: $script"
        exit 1
    fi
    if [ ! -x "$script" ]; then
        chmod +x "$script"
        echo "✅ Made $script executable"
    fi
}

echo "🔍 Pre-flight Checks"
echo "==================="

# Check if all required scripts exist
scripts=(
    "emergency-security-fix.sh"
    "fix-frontend-connectivity.sh"
    "deploy-authentication.sh"
    "comprehensive-validation.sh"
    "production-hardening.sh"
)

for script in "${scripts[@]}"; do
    check_script "$script"
done

# Check SSH connectivity
echo "Testing SSH connectivity..."
if ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no root@$SERVER_IP "echo 'SSH connection successful'" >/dev/null 2>&1; then
    echo "✅ SSH connectivity confirmed"
else
    echo "❌ SSH connectivity failed"
    echo "Please ensure:"
    echo "1. SSH key is properly configured"
    echo "2. Server is accessible"
    echo "3. Firewall allows SSH connections"
    exit 1
fi

# Check if server is responding
echo "Testing server accessibility..."
if curl -s --connect-timeout 10 http://$SERVER_IP >/dev/null 2>&1; then
    echo "✅ Server is accessible"
else
    echo "❌ Server is not accessible"
    echo "Please check server status and network connectivity"
    exit 1
fi

echo ""
echo "✅ All pre-flight checks passed!"

prompt_continue "Ready to begin Phase 1: Emergency Security Fix"

echo ""
echo "🚨 PHASE 1: EMERGENCY SECURITY FIX"
echo "=================================="
echo "Deploying authenticated server and SSL configuration..."

if ./emergency-security-fix.sh; then
    echo "✅ Phase 1 completed successfully"
else
    echo "❌ Phase 1 failed"
    echo "Please review the error messages above and fix any issues before continuing"
    exit 1
fi

prompt_continue "Phase 1 complete. Ready for Phase 2: Frontend Connectivity Fix"

echo ""
echo "🔧 PHASE 2: FRONTEND CONNECTIVITY FIX"
echo "===================================="
echo "Rebuilding and deploying frontend with correct API configuration..."

if ./fix-frontend-connectivity.sh; then
    echo "✅ Phase 2 completed successfully"
else
    echo "❌ Phase 2 failed"
    echo "Please review the error messages above and fix any issues before continuing"
    exit 1
fi

prompt_continue "Phase 2 complete. Ready for Phase 3: Authentication System Deployment"

echo ""
echo "🔐 PHASE 3: AUTHENTICATION SYSTEM DEPLOYMENT"
echo "==========================================="
echo "Verifying and testing authentication system..."

if ./deploy-authentication.sh; then
    echo "✅ Phase 3 completed successfully"
else
    echo "❌ Phase 3 failed"
    echo "Please review the error messages above and fix any issues before continuing"
    exit 1
fi

prompt_continue "Phase 3 complete. Ready for Phase 4: Comprehensive Validation"

echo ""
echo "🧪 PHASE 4: COMPREHENSIVE VALIDATION"
echo "==================================="
echo "Running complete application validation..."

if ./comprehensive-validation.sh; then
    echo "✅ Phase 4 completed successfully"
    
    # Extract success rate from validation output
    validation_output=$(./comprehensive-validation.sh 2>&1 | tail -20)
    success_rate=$(echo "$validation_output" | grep "Success Rate:" | grep -o '[0-9]\+%' | head -1)
    
    if [ -n "$success_rate" ]; then
        echo "📊 Validation Success Rate: $success_rate"
        
        # Extract numeric value
        numeric_rate=$(echo "$success_rate" | sed 's/%//')
        
        if [ "$numeric_rate" -ge 90 ]; then
            echo "🎉 Excellent! Application meets production requirements"
            
            echo ""
            read -p "Proceed with Production Hardening? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                echo ""
                echo "🚀 PHASE 5: PRODUCTION HARDENING"
                echo "==============================="
                echo "Applying performance optimizations and security hardening..."
                
                if ./production-hardening.sh; then
                    echo "✅ Phase 5 completed successfully"
                    echo ""
                    echo "🎉 COMPLETE DEPLOYMENT SUCCESS!"
                    echo "==============================="
                    echo "All phases completed successfully!"
                else
                    echo "⚠️ Phase 5 had issues but core application is functional"
                fi
            else
                echo "⏭️ Skipping production hardening"
                echo "You can run it later with: ./production-hardening.sh"
            fi
        elif [ "$numeric_rate" -ge 80 ]; then
            echo "✅ Good! Application is functional with minor issues"
            echo "⚠️ Consider fixing failed tests before production hardening"
        else
            echo "❌ Application has significant issues"
            echo "🚨 Do not proceed with production hardening until issues are resolved"
        fi
    else
        echo "⚠️ Could not determine validation success rate"
    fi
else
    echo "❌ Phase 4 failed"
    echo "Please review the validation results and fix any critical issues"
fi

echo ""
echo "📊 DEPLOYMENT SUMMARY"
echo "===================="
echo ""
echo "🔗 Application URL: https://$DOMAIN"
echo "👥 User Credentials:"
echo "   • Taha: <EMAIL> / taha2024"
echo "   • Burak: <EMAIL> / burak2024"
echo ""
echo "📋 Completed Phases:"
echo "✅ Phase 1: Emergency Security Fix"
echo "✅ Phase 2: Frontend Connectivity Fix"
echo "✅ Phase 3: Authentication System Deployment"
echo "✅ Phase 4: Comprehensive Validation"

if [ -f "/tmp/hardening_completed" ]; then
    echo "✅ Phase 5: Production Hardening"
    rm -f "/tmp/hardening_completed"
else
    echo "⏭️ Phase 5: Production Hardening (skipped or pending)"
fi

echo ""
echo "📞 Next Steps:"
echo "1. 🌐 Test the application in your browser"
echo "2. 🔐 Verify authentication with both users"
echo "3. 📊 Check that data loads correctly in dashboard"
echo "4. 📱 Test mobile responsiveness"
echo "5. ⚡ Monitor performance and response times"
echo ""
echo "🔧 Troubleshooting:"
echo "• If issues persist, check individual script logs"
echo "• Clear browser cache if frontend doesn't update"
echo "• Check server logs: ssh root@$SERVER_IP 'tail -f /opt/nawras-admin/server.log'"
echo "• Monitor health: curl https://$DOMAIN/api/health"
echo ""
echo "📚 Available Scripts:"
echo "• ./emergency-security-fix.sh - Re-run security fixes"
echo "• ./fix-frontend-connectivity.sh - Re-build and deploy frontend"
echo "• ./deploy-authentication.sh - Re-test authentication"
echo "• ./comprehensive-validation.sh - Re-run validation tests"
echo "• ./production-hardening.sh - Apply production optimizations"
echo ""

if [ "$numeric_rate" -ge 90 ] 2>/dev/null; then
    echo "🎉 DEPLOYMENT SUCCESSFUL!"
    echo "========================"
    echo "Your Nawras Admin application is now production-ready!"
    echo "All security, functionality, and performance requirements have been met."
elif [ "$numeric_rate" -ge 80 ] 2>/dev/null; then
    echo "✅ DEPLOYMENT MOSTLY SUCCESSFUL!"
    echo "==============================="
    echo "Your application is functional with minor issues to address."
    echo "Review the validation results and fix any remaining problems."
else
    echo "⚠️ DEPLOYMENT NEEDS ATTENTION"
    echo "============================="
    echo "Some critical issues were found during validation."
    echo "Please review and fix the issues before considering the deployment complete."
fi

echo ""
echo "Thank you for using the Nawras Admin Deployment Fix System! 🚀"
