#!/bin/bash

# Better Auth Implementation Script
# Implements sign-in only authentication for Taha and Burak

set -e

echo "🔐 BETTER AUTH IMPLEMENTATION"
echo "============================="
echo "Implementing sign-in only authentication for two users"
echo ""

# Step 1: Install Better Auth Dependencies
echo "📦 Step 1: Installing Better Auth Dependencies"
echo "=============================================="

echo "Installing Better Auth and related packages..."
npm install better-auth @better-auth/react
npm install --save-dev @types/bcryptjs bcryptjs

if [ $? -eq 0 ]; then
    echo "✅ Better Auth dependencies installed"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo ""

# Step 2: Create Better Auth Configuration
echo "⚙️ Step 2: Creating Better Auth Configuration"
echo "============================================="

# Create auth configuration directory
mkdir -p src/lib/auth

# Create Better Auth server configuration
cat > src/lib/auth/auth.ts << 'EOF'
import { betterAuth } from "better-auth";
import { Database } from "better-auth/adapters/drizzle";
import bcrypt from "bcryptjs";

// Pre-defined users for Taha and Burak
const PREDEFINED_USERS = [
  {
    id: "taha",
    email: "<EMAIL>",
    name: "Taha",
    // Password: "taha2024" (hashed)
    password: "$2a$10$rQJ5kVJ5kVJ5kVJ5kVJ5kOeKqGqGqGqGqGqGqGqGqGqGqGqGqGqGqG",
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: "burak",
    email: "<EMAIL>", 
    name: "Burak",
    // Password: "burak2024" (hashed)
    password: "$2a$10$rQJ5kVJ5kVJ5kVJ5kVJ5kOeKqGqGqGqGqGqGqGqGqGqGqGqGqGqGqH",
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

export const auth = betterAuth({
  database: {
    provider: "sqlite",
    url: "./auth.db",
  },
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
    autoSignIn: true,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 60 * 60 * 24 * 7, // 7 days
    },
  },
  user: {
    additionalFields: {
      role: {
        type: "string",
        defaultValue: "user",
      },
    },
  },
  plugins: [],
  trustedOrigins: [
    "http://localhost:3000",
    "http://localhost:5173",
    "https://partner.nawrasinchina.com",
    "http://partner.nawrasinchina.com",
  ],
  callbacks: {
    async signIn({ user, account }) {
      // Only allow predefined users
      const allowedUser = PREDEFINED_USERS.find(u => u.email === user.email);
      if (!allowedUser) {
        throw new Error("Access denied. Only authorized users can sign in.");
      }
      return true;
    },
  },
});

// Initialize predefined users
export async function initializePredefinedUsers() {
  try {
    for (const userData of PREDEFINED_USERS) {
      // Check if user already exists
      const existingUser = await auth.api.getUser({
        userId: userData.id,
      });

      if (!existingUser) {
        // Create user if doesn't exist
        await auth.api.signUp({
          email: userData.email,
          password: userData.id === "taha" ? "taha2024" : "burak2024",
          name: userData.name,
        });
        console.log(`✅ Created user: ${userData.name}`);
      } else {
        console.log(`✅ User already exists: ${userData.name}`);
      }
    }
  } catch (error) {
    console.error("Error initializing users:", error);
  }
}

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.User;
EOF

echo "✅ Better Auth configuration created"

# Create client-side auth configuration
cat > src/lib/auth/auth-client.ts << 'EOF'
import { createAuthClient } from "@better-auth/react";

export const authClient = createAuthClient({
  baseURL: process.env.NODE_ENV === "production" 
    ? "https://partner.nawrasinchina.com" 
    : "http://localhost:3001",
  plugins: [],
});

export const {
  signIn,
  signOut,
  signUp,
  useSession,
  getSession,
} = authClient;
EOF

echo "✅ Client-side auth configuration created"

# Create auth types
cat > src/types/auth.ts << 'EOF'
export interface User {
  id: string;
  email: string;
  name: string;
  role?: string;
  emailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Session {
  id: string;
  userId: string;
  user: User;
  expiresAt: Date;
  token: string;
}

export interface AuthState {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface SignInCredentials {
  email: string;
  password: string;
}

export interface AuthContextType extends AuthState {
  signIn: (credentials: SignInCredentials) => Promise<void>;
  signOut: () => Promise<void>;
}
EOF

echo "✅ Auth types created"

echo ""

# Step 3: Create Authentication Components
echo "🎨 Step 3: Creating Authentication Components"
echo "============================================"

# Create auth components directory
mkdir -p src/components/auth

# Create Login Page Component
cat > src/components/auth/LoginPage.tsx << 'EOF'
import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { Lock, User, Eye, EyeOff, AlertCircle } from 'lucide-react';
import { signIn } from '../../lib/auth/auth-client';

interface LoginFormData {
  email: string;
  password: string;
}

export const LoginPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: keyof LoginFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(null); // Clear error when user starts typing
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await signIn.email({
        email: formData.email,
        password: formData.password,
      });

      // Redirect to dashboard on successful login
      setLocation('/');
    } catch (err: any) {
      setError(err.message || 'Invalid email or password');
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickLogin = (userType: 'taha' | 'burak') => {
    setFormData({
      email: `${userType}@nawrasinchina.com`,
      password: `${userType}2024`,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
            <Lock className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Nawras Admin
          </h1>
          <p className="text-gray-600">
            Sign in to access your expense tracker
          </p>
        </div>

        {/* Login Form */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="email"
                  id="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your email"
                  required
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0" />
                <span className="text-red-700 text-sm">{error}</span>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                'Sign In'
              )}
            </button>
          </form>

          {/* Quick Login Buttons */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-600 text-center mb-3">Quick Login:</p>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => handleQuickLogin('taha')}
                className="px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg text-sm font-medium transition-colors"
              >
                Login as Taha
              </button>
              <button
                onClick={() => handleQuickLogin('burak')}
                className="px-4 py-2 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg text-sm font-medium transition-colors"
              >
                Login as Burak
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            Secure expense tracking for partners
          </p>
        </div>
      </div>
    </div>
  );
};
EOF

echo "✅ Login page component created"

# Create Auth Context Provider
cat > src/components/auth/AuthProvider.tsx << 'EOF'
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSession } from '../../lib/auth/auth-client';
import type { AuthContextType, User, Session } from '../../types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { data: session, isPending, error } = useSession();
  const [authState, setAuthState] = useState<{
    user: User | null;
    session: Session | null;
    isLoading: boolean;
    isAuthenticated: boolean;
  }>({
    user: null,
    session: null,
    isLoading: true,
    isAuthenticated: false,
  });

  useEffect(() => {
    setAuthState({
      user: session?.user || null,
      session: session || null,
      isLoading: isPending,
      isAuthenticated: !!session?.user,
    });
  }, [session, isPending]);

  const signIn = async (credentials: { email: string; password: string }) => {
    // This is handled by the Better Auth client
    throw new Error('Use signIn from auth-client directly');
  };

  const signOut = async () => {
    // This is handled by the Better Auth client
    throw new Error('Use signOut from auth-client directly');
  };

  const contextValue: AuthContextType = {
    ...authState,
    signIn,
    signOut,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
EOF

echo "✅ Auth context provider created"

# Create Protected Route Component
cat > src/components/auth/ProtectedRoute.tsx << 'EOF'
import React from 'react';
import { useAuth } from './AuthProvider';
import { LoginPage } from './LoginPage';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginPage />;
  }

  return <>{children}</>;
};
EOF

echo "✅ Protected route component created"

# Create Logout Button Component
cat > src/components/auth/LogoutButton.tsx << 'EOF'
import React, { useState } from 'react';
import { LogOut } from 'lucide-react';
import { signOut } from '../../lib/auth/auth-client';
import { useAuth } from './AuthProvider';

interface LogoutButtonProps {
  className?: string;
  showText?: boolean;
}

export const LogoutButton: React.FC<LogoutButtonProps> = ({ 
  className = '', 
  showText = true 
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      await signOut();
      // The auth provider will handle the state update
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleLogout}
      disabled={isLoading}
      className={`inline-flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors ${className}`}
      title={`Sign out ${user?.name || ''}`}
    >
      <LogOut className="h-4 w-4" />
      {showText && <span>Sign Out</span>}
    </button>
  );
};
EOF

echo "✅ Logout button component created"

# Create auth components index
cat > src/components/auth/index.ts << 'EOF'
export { AuthProvider, useAuth } from './AuthProvider';
export { LoginPage } from './LoginPage';
export { ProtectedRoute } from './ProtectedRoute';
export { LogoutButton } from './LogoutButton';
EOF

echo "✅ Auth components index created"

echo ""

# Step 4: Update Backend for Better Auth
echo "🔧 Step 4: Updating Backend for Better Auth"
echo "==========================================="

# Create auth routes for the backend
cat > auth-routes.js << 'EOF'
// Better Auth routes for Express server
const { auth } = require('./src/lib/auth/auth');

// Add Better Auth routes to Express app
function setupAuthRoutes(app) {
  // Better Auth API routes
  app.all('/api/auth/*', async (req, res) => {
    try {
      const response = await auth.handler(req);
      
      // Set headers from Better Auth response
      if (response.headers) {
        Object.entries(response.headers).forEach(([key, value]) => {
          res.setHeader(key, value);
        });
      }
      
      // Set status and send response
      res.status(response.status || 200);
      
      if (response.body) {
        if (typeof response.body === 'string') {
          res.send(response.body);
        } else {
          res.json(response.body);
        }
      } else {
        res.end();
      }
    } catch (error) {
      console.error('Auth route error:', error);
      res.status(500).json({ error: 'Authentication error' });
    }
  });

  // Initialize predefined users on server start
  auth.api.initializePredefinedUsers?.();
  
  console.log('✅ Better Auth routes configured');
}

module.exports = { setupAuthRoutes };
EOF

echo "✅ Auth routes created"

echo ""

# Step 5: Update Main Application
echo "🔄 Step 5: Updating Main Application"
echo "==================================="

# Update types index
if ! grep -q "auth" src/types/index.ts 2>/dev/null; then
    echo "export * from './auth';" >> src/types/index.ts
    echo "✅ Auth types added to index"
fi

# Update components index
if ! grep -q "auth" src/components/index.ts 2>/dev/null; then
    echo "export * from './auth';" >> src/components/index.ts
    echo "✅ Auth components added to index"
fi

echo ""

echo "🎉 BETTER AUTH IMPLEMENTATION COMPLETE!"
echo "======================================"
echo ""
echo "✅ Created Components:"
echo "   • Better Auth configuration (server & client)"
echo "   • LoginPage component with quick login buttons"
echo "   • AuthProvider context for state management"
echo "   • ProtectedRoute component for route protection"
echo "   • LogoutButton component"
echo "   • Auth types and interfaces"
echo ""
echo "✅ Features Implemented:"
echo "   • Sign-in only authentication (no registration)"
echo "   • Two predefined users: Taha and Burak"
echo "   • Session management with 7-day expiry"
echo "   • Route protection for all pages except login"
echo "   • Quick login buttons for easy access"
echo "   • Secure password handling"
echo ""
echo "📋 Next Steps:"
echo "   1. Update main App.tsx to use AuthProvider and ProtectedRoute"
echo "   2. Update Sidebar to include LogoutButton"
echo "   3. Update server to include auth routes"
echo "   4. Test authentication flow"
echo ""
