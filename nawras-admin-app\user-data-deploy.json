{"name": "nawras-admin-deployed", "region": "nyc3", "size": "s-1vcpu-1gb", "image": "ubuntu-22-04-x64", "ssh_keys": [47923799], "backups": false, "ipv6": true, "user_data": "#!/bin/bash\n\n# Nawras Admin Auto-Deployment via User Data\nset -e\nexport DEBIAN_FRONTEND=noninteractive\n\necho '🚀 Starting Nawras Admin Auto-Deployment...' > /var/log/deployment.log\necho '📍 Server deployment started' >> /var/log/deployment.log\necho '⏰ Started at: '$(date) >> /var/log/deployment.log\n\n# Update system\necho '📦 Updating system packages...' >> /var/log/deployment.log\napt-get update -y >> /var/log/deployment.log 2>&1\napt-get upgrade -y >> /var/log/deployment.log 2>&1\n\n# Install Node.js 18\necho '📦 Installing Node.js 18...' >> /var/log/deployment.log\ncurl -fsSL https://deb.nodesource.com/setup_18.x | bash - >> /var/log/deployment.log 2>&1\napt-get install -y nodejs >> /var/log/deployment.log 2>&1\n\n# Install additional packages\necho '📦 Installing additional packages...' >> /var/log/deployment.log\napt-get install -y nginx git curl wget unzip >> /var/log/deployment.log 2>&1\n\n# Install PM2 globally\necho '📦 Installing PM2...' >> /var/log/deployment.log\nnpm install -g pm2 >> /var/log/deployment.log 2>&1\n\n# Create application directory\necho '📁 Creating application directory...' >> /var/log/deployment.log\nmkdir -p /opt/nawras-admin\ncd /opt/nawras-admin\n\n# Create backend package.json\necho '📝 Creating backend package.json...' >> /var/log/deployment.log\ncat > package.json << 'PACKAGE_EOF'\n{\n  \"name\": \"nawras-admin-backend\",\n  \"version\": \"1.0.0\",\n  \"description\": \"Nawras Admin Backend API\",\n  \"main\": \"server.js\",\n  \"dependencies\": {\n    \"express\": \"^4.18.2\",\n    \"cors\": \"^2.8.5\"\n  },\n  \"scripts\": {\n    \"start\": \"node server.js\",\n    \"dev\": \"node server.js\"\n  }\n}\nPACKAGE_EOF\n\n# Install backend dependencies\necho '📦 Installing backend dependencies...' >> /var/log/deployment.log\nnpm install >> /var/log/deployment.log 2>&1\n\n# Create backend server\necho '🔧 Creating backend server...' >> /var/log/deployment.log\ncat > server.js << 'SERVER_EOF'\nconst express = require('express');\nconst cors = require('cors');\nconst app = express();\nconst PORT = process.env.PORT || 3001;\n\n// Middleware\napp.use(cors());\napp.use(express.json());\n\n// Sample data\nlet expenses = [\n  {id: 1, amount: 25.50, description: \"Lunch at restaurant\", category: \"Food\", paidById: \"taha\", date: \"2024-01-15\", createdAt: new Date().toISOString()},\n  {id: 2, amount: 60.99, description: \"Grocery shopping\", category: \"Groceries\", paidById: \"burak\", date: \"2024-01-16\", createdAt: new Date().toISOString()},\n  {id: 3, amount: 15.75, description: \"Coffee and snacks\", category: \"Food\", paidById: \"taha\", date: \"2024-01-17\", createdAt: new Date().toISOString()}\n];\n\nlet settlements = [\n  {id: 1, amount: 30.00, paidBy: \"taha\", paidTo: \"burak\", description: \"Settlement\", date: \"2024-01-17\", createdAt: new Date().toISOString()},\n  {id: 2, amount: 25.75, paidBy: \"burak\", paidTo: \"taha\", description: \"Utilities\", date: \"2024-01-18\", createdAt: new Date().toISOString()}\n];\n\n// API Routes\napp.get('/api/health', (req, res) => res.json({status: 'ok', timestamp: new Date().toISOString(), version: '1.0.0'}));\napp.get('/api/expenses', (req, res) => res.json({success: true, data: expenses, total: expenses.length}));\napp.post('/api/expenses', (req, res) => {\n  const expense = {id: expenses.length + 1, ...req.body, createdAt: new Date().toISOString()};\n  expenses.push(expense);\n  res.json({success: true, data: expense});\n});\napp.get('/api/settlements', (req, res) => res.json({success: true, data: settlements, total: settlements.length}));\napp.post('/api/settlements', (req, res) => {\n  const settlement = {id: settlements.length + 1, ...req.body, createdAt: new Date().toISOString()};\n  settlements.push(settlement);\n  res.json({success: true, data: settlement});\n});\n\napp.listen(PORT, '0.0.0.0', () => {\n  console.log(`🚀 Nawras Admin API server running on port ${PORT}`);\n});\nSERVER_EOF\n\necho '✅ Backend server created successfully!' >> /var/log/deployment.log\n\n# Start backend with PM2\necho '🔥 Starting backend with PM2...' >> /var/log/deployment.log\npm2 start server.js --name \"nawras-backend\" >> /var/log/deployment.log 2>&1\npm2 startup >> /var/log/deployment.log 2>&1\npm2 save >> /var/log/deployment.log 2>&1\n\necho '✅ Backend deployed and running!' >> /var/log/deployment.log\n\n# Configure firewall\necho '🔒 Configuring firewall...' >> /var/log/deployment.log\nufw allow 22 >> /var/log/deployment.log 2>&1\nufw allow 80 >> /var/log/deployment.log 2>&1\nufw allow 3001 >> /var/log/deployment.log 2>&1\nufw --force enable >> /var/log/deployment.log 2>&1\n\n# Configure Nginx\necho '🌐 Configuring Nginx...' >> /var/log/deployment.log\ncat > /etc/nginx/sites-available/default << 'NGINX_EOF'\nserver {\n    listen 80;\n    server_name _;\n    root /var/www/html;\n    index index.html;\n\n    location /api/ {\n        proxy_pass http://localhost:3001;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n    }\n\n    location / {\n        try_files $uri $uri/ /index.html;\n    }\n}\nNGINX_EOF\n\n# Create simple frontend\ncat > /var/www/html/index.html << 'HTML_EOF'\n<!DOCTYPE html>\n<html><head><title>Nawras Admin</title></head>\n<body style=\"font-family:Arial;text-align:center;padding:50px;\">\n<h1>🚀 Nawras Admin</h1>\n<h2>Deployment Successful!</h2>\n<p><a href=\"/api/health\">API Health</a> | <a href=\"/api/expenses\">Expenses</a> | <a href=\"/api/settlements\">Settlements</a></p>\n</body></html>\nHTML_EOF\n\n# Restart nginx\nsystemctl restart nginx >> /var/log/deployment.log 2>&1\nsystemctl enable nginx >> /var/log/deployment.log 2>&1\n\necho '🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!' >> /var/log/deployment.log\necho '🌐 Frontend: http://$(curl -s ifconfig.me)' >> /var/log/deployment.log\necho '🔗 Backend API: http://$(curl -s ifconfig.me):3001' >> /var/log/deployment.log\necho '⏰ Completed at: '$(date) >> /var/log/deployment.log\n", "monitoring": true, "tags": ["nawras", "admin", "production", "auto-deployed"]}