// Minimal authenticated server for Nawras Admin
const http = require('http');
const url = require('url');
const querystring = require('querystring');

const PORT = 3001;
const sessions = new Map();

// Mock users
const users = [
  { id: "taha", email: "<EMAIL>", password: "taha2024", name: "Ta<PERSON>" },
  { id: "burak", email: "<EMAIL>", password: "burak2024", name: "Bura<PERSON>" }
];

// Mock data
const expenses = [
  {id: 1, amount: 25.5, description: "Lunch at downtown cafe", category: "Food", paidById: "taha", date: "2024-01-15"},
  {id: 2, amount: 120, description: "Grocery shopping", category: "Groceries", paidById: "burak", date: "2024-01-14"},
  {id: 3, amount: 45.75, description: "Gas station fill-up", category: "Transportation", paidById: "taha", date: "2024-01-13"},
  {id: 4, amount: 89.99, description: "Monthly internet bill", category: "Utilities", paidById: "burak", date: "2024-01-12"},
  {id: 5, amount: 15.25, description: "Coffee and pastry", category: "Food", paidById: "taha", date: "2024-01-11"}
];

const settlements = [
  {id: 1, amount: 30.00, paidBy: "taha", paidTo: "burak", description: "Settlement", date: "2024-01-17"},
  {id: 2, amount: 25.75, paidBy: "burak", paidTo: "taha", description: "Utilities", date: "2024-01-18"}
];

// Helper functions
function parseBody(req) {
  return new Promise((resolve) => {
    let body = '';
    req.on('data', chunk => body += chunk.toString());
    req.on('end', () => {
      try {
        resolve(JSON.parse(body));
      } catch {
        resolve({});
      }
    });
  });
}

function sendJSON(res, data, status = 200) {
  res.writeHead(status, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Credentials': 'true'
  });
  res.end(JSON.stringify(data));
}

function requireAuth(req) {
  const authHeader = req.headers.authorization;
  const sessionToken = authHeader ? authHeader.replace('Bearer ', '') : null;
  
  if (!sessionToken || !sessions.has(sessionToken)) {
    return null;
  }
  
  const session = sessions.get(sessionToken);
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    return null;
  }
  
  return session;
}

// Server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Credentials': 'true'
    });
    res.end();
    return;
  }

  // Health check
  if (path === '/api/health' && method === 'GET') {
    sendJSON(res, {
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
    return;
  }

  // Sign in
  if (path === '/api/auth/sign-in' && method === 'POST') {
    const body = await parseBody(req);
    const { email, password } = body;
    
    const user = users.find(u => u.email === email && u.password === password);
    if (!user) {
      sendJSON(res, { success: false, error: 'Invalid credentials' }, 401);
      return;
    }
    
    const sessionToken = `session_${Date.now()}_${Math.random()}`;
    const session = {
      id: sessionToken,
      userId: user.id,
      user: { id: user.id, email: user.email, name: user.name },
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      token: sessionToken
    };
    
    sessions.set(sessionToken, session);
    sendJSON(res, { success: true, data: { session, user: session.user } });
    return;
  }

  // Session check
  if (path === '/api/auth/session' && method === 'GET') {
    const session = requireAuth(req);
    sendJSON(res, { success: true, data: session });
    return;
  }

  // Protected routes
  const session = requireAuth(req);
  if (!session) {
    sendJSON(res, { success: false, error: 'Authentication required' }, 401);
    return;
  }

  // Expenses
  if (path === '/api/expenses' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: expenses,
      total: expenses.length,
      timestamp: new Date().toISOString()
    });
    return;
  }

  // Settlements
  if (path === '/api/settlements' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: settlements,
      total: settlements.length,
      timestamp: new Date().toISOString()
    });
    return;
  }

  // 404
  sendJSON(res, { success: false, error: 'Not found' }, 404);
});

server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Nawras Admin server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔐 Authentication enabled`);
  console.log(`👥 Users: <EMAIL>, <EMAIL>`);
});
