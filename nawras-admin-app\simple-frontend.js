const express = require('express');
const app = express();
const PORT = 8080;

app.get('/', (req, res) => {
    res.send(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Working Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; padding: 20px; min-height: 100vh; }
        .container { background: white; border-radius: 20px; padding: 40px; max-width: 500px; margin: 50px auto; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; box-sizing: border-box; }
        .btn { background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 5px; cursor: pointer; width: 100%; font-size: 16px; }
        .btn:hover { background: #0056b3; }
        .demo-accounts { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; font-size: 14px; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin-bottom: 20px; display: none; }
        .dashboard { display: none; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center; }
        .stat-value { font-size: 2rem; font-weight: bold; margin-bottom: 5px; }
        .expenses-list { background: #f8f9fa; padding: 20px; border-radius: 10px; margin-top: 20px; }
        .expense-item { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #ddd; }
        .expense-item:last-child { border-bottom: none; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Login Screen -->
        <div id="loginScreen">
            <h1 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">🏢 Nawras Admin</h1>
            <div class="success">
                <strong>✅ Custom Frontend Working!</strong><br>
                Connected to API with Supabase integration
            </div>
            <div id="error" class="error"></div>
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" required>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" required>
                </div>
                <button type="submit" class="btn" id="loginBtn">Sign In</button>
            </form>
            <div class="demo-accounts">
                <strong>Demo Accounts:</strong><br>
                • <EMAIL> / taha2024<br>
                • <EMAIL> / burak2024
            </div>
        </div>

        <!-- Dashboard -->
        <div id="dashboard" class="dashboard">
            <h1 style="text-align: center; color: #2c3e50;">📊 Dashboard</h1>
            <div class="success">
                <strong>✅ Successfully Connected!</strong><br>
                Live data from Supabase database
            </div>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value" style="color: #007bff;" id="tahaTotal">$0.00</div>
                    <div>Taha's Expenses</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #28a745;" id="burakTotal">$0.00</div>
                    <div>Burak's Expenses</div>
                </div>
            </div>
            
            <div class="expenses-list">
                <h3>Recent Expenses</h3>
                <div id="expensesList">Loading...</div>
            </div>
            
            <button class="btn" onclick="logout()" style="background: #dc3545; margin-top: 20px;">Sign Out</button>
        </div>
    </div>

    <script>
        const API_URL = 'http://partner.nawrasinchina.com:3001';
        let authToken = localStorage.getItem('authToken');

        if (authToken) {
            checkSession();
        }

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            document.getElementById('loginBtn').textContent = 'Signing in...';
            
            fetch(API_URL + '/api/auth/sign-in', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    authToken = data.data.session.token;
                    localStorage.setItem('authToken', authToken);
                    showDashboard();
                    loadExpenses();
                } else {
                    showError(data.error || 'Login failed');
                }
            })
            .catch(error => {
                showError('Connection error: ' + error.message);
            })
            .finally(() => {
                document.getElementById('loginBtn').textContent = 'Sign In';
            });
        });

        function checkSession() {
            fetch(API_URL + '/api/auth/session', {
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showDashboard();
                    loadExpenses();
                } else {
                    localStorage.removeItem('authToken');
                    authToken = null;
                }
            })
            .catch(error => {
                localStorage.removeItem('authToken');
                authToken = null;
            });
        }

        function showDashboard() {
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('dashboard').style.display = 'block';
        }

        function showError(message) {
            const errorEl = document.getElementById('error');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            setTimeout(() => errorEl.style.display = 'none', 5000);
        }

        function loadExpenses() {
            fetch(API_URL + '/api/expenses', {
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayExpenses(data.data);
                } else {
                    document.getElementById('expensesList').innerHTML = 'Failed to load expenses';
                }
            })
            .catch(error => {
                document.getElementById('expensesList').innerHTML = 'Error loading expenses: ' + error.message;
            });
        }

        function displayExpenses(expenses) {
            let tahaTotal = 0, burakTotal = 0;
            let html = '';
            
            expenses.forEach(expense => {
                if (expense.paid_by_id === 'taha') {
                    tahaTotal += parseFloat(expense.amount);
                } else if (expense.paid_by_id === 'burak') {
                    burakTotal += parseFloat(expense.amount);
                }
                
                html += \`<div class="expense-item">
                    <div>
                        <strong>\${expense.description}</strong><br>
                        <small>\${expense.category} • \${expense.paid_by_id === 'taha' ? 'Taha' : 'Burak'} • \${expense.date}</small>
                    </div>
                    <div><strong>$\${parseFloat(expense.amount).toFixed(2)}</strong></div>
                </div>\`;
            });
            
            document.getElementById('tahaTotal').textContent = '$' + tahaTotal.toFixed(2);
            document.getElementById('burakTotal').textContent = '$' + burakTotal.toFixed(2);
            document.getElementById('expensesList').innerHTML = html || 'No expenses found';
        }

        function logout() {
            fetch(API_URL + '/api/auth/sign-out', {
                method: 'POST',
                headers: { 'Authorization': 'Bearer ' + authToken }
            }).catch(() => {});
            
            localStorage.removeItem('authToken');
            authToken = null;
            document.getElementById('loginScreen').style.display = 'block';
            document.getElementById('dashboard').style.display = 'none';
        }
    </script>
</body>
</html>`);
});

app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        server: 'simple-frontend',
        port: PORT,
        api: 'http://partner.nawrasinchina.com:3001',
        message: 'Simple frontend server working'
    });
});

app.listen(PORT, () => {
    console.log(\`🎉 SIMPLE FRONTEND SERVER STARTED\`);
    console.log(\`✅ Server running on port \${PORT}\`);
    console.log(\`✅ Connected to API on port 3001\`);
    console.log(\`🌐 Access: http://partner.nawrasinchina.com:\${PORT}\`);
});
