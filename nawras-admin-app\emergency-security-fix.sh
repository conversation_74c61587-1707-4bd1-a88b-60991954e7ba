#!/bin/bash

# Emergency Security Fix Script
# Deploys correct server version with authentication and SSL

set -e

SERVER_IP="*************"
DOMAIN="partner.nawrasinchina.com"
BACKUP_DIR="/opt/nawras-admin/backups/emergency-$(date +%Y%m%d_%H%M%S)"

echo "🚨 EMERGENCY SECURITY FIX DEPLOYMENT"
echo "===================================="
echo "Server: $SERVER_IP"
echo "Domain: $DOMAIN"
echo "Backup: $BACKUP_DIR"
echo ""

# Function to run commands on server
run_on_server() {
    ssh -o StrictHostKeyChecking=no root@$SERVER_IP "$1"
}

# Function to upload files to server
upload_file() {
    local source="$1"
    local destination="$2"
    scp -o StrictHostKeyChecking=no "$source" root@$SERVER_IP:"$destination"
}

echo "🔍 Step 1: Pre-deployment Backup"
echo "================================"

# Create backup directory and backup current state
run_on_server "
mkdir -p $BACKUP_DIR
cd /opt/nawras-admin

# Backup current server file
if [ -f server-simple.cjs ]; then
    cp server-simple.cjs $BACKUP_DIR/server-simple.cjs.backup
fi

# Backup current dist folder
if [ -d dist ]; then
    cp -r dist $BACKUP_DIR/dist.backup
fi

# Backup nginx config
if [ -f /etc/nginx/sites-available/default ]; then
    cp /etc/nginx/sites-available/default $BACKUP_DIR/nginx.conf.backup
fi

echo '✅ Backup completed at $BACKUP_DIR'
"

echo "📦 Step 2: Deploy Correct Server Version"
echo "========================================"

# Upload the correct server file
echo "Uploading authenticated server..."
upload_file "server-simple.cjs" "/opt/nawras-admin/server-simple.cjs"

# Upload deployment package if it exists
if [ -d "deployment-package" ]; then
    echo "Uploading deployment package..."
    rsync -avz --exclude 'node_modules' deployment-package/ root@$SERVER_IP:/opt/nawras-admin/
fi

echo "🔄 Step 3: Stop Current Services"
echo "==============================="

run_on_server "
cd /opt/nawras-admin

# Stop any running services
pkill -f 'node.*server' || echo 'No node processes found'
pm2 stop all || echo 'No PM2 processes found'
docker-compose down || echo 'No docker-compose found'

# Stop nginx temporarily
systemctl stop nginx || echo 'Nginx not running'
"

echo "🚀 Step 4: Start Authenticated Server"
echo "===================================="

run_on_server "
cd /opt/nawras-admin

# Ensure we have the correct server file
if [ ! -f server-simple.cjs ]; then
    echo '❌ server-simple.cjs not found!'
    exit 1
fi

# Install dependencies if needed
if [ ! -d node_modules ]; then
    npm install --production
fi

# Start server with PM2
if command -v pm2 >/dev/null 2>&1; then
    pm2 start server-simple.cjs --name nawras-admin-auth
    pm2 save
    pm2 startup
    echo '✅ Server started with PM2'
else
    # Fallback to nohup
    nohup node server-simple.cjs > server.log 2>&1 &
    echo '✅ Server started with nohup'
fi

# Wait for server to start
sleep 5

# Test server
if curl -s http://localhost:3001/api/health | grep -q 'ok'; then
    echo '✅ Authenticated server is running'
else
    echo '❌ Server health check failed'
    exit 1
fi
"

echo "🔒 Step 5: Configure SSL/HTTPS"
echo "============================="

run_on_server "
# Install certbot if not present
if ! command -v certbot >/dev/null 2>&1; then
    apt-get update
    apt-get install -y certbot python3-certbot-nginx
fi

# Configure nginx for SSL
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80;
    server_name $DOMAIN;
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN;
    
    # SSL configuration will be added by certbot
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection \"1; mode=block\";
    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;
    
    # Serve static files
    location / {
        root /opt/nawras-admin/dist;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control \"public, immutable\";
        }
    }
    
    # Proxy API requests
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # CORS headers
        add_header Access-Control-Allow-Origin \"https://$DOMAIN\" always;
        add_header Access-Control-Allow-Methods \"GET, POST, PUT, DELETE, OPTIONS\" always;
        add_header Access-Control-Allow-Headers \"Authorization, Content-Type\" always;
        
        if (\$request_method = 'OPTIONS') {
            return 204;
        }
    }
}
EOF

# Test nginx configuration
nginx -t

# Start nginx
systemctl start nginx
systemctl enable nginx

echo '✅ Nginx configured for SSL'
"

echo "🔐 Step 6: Obtain SSL Certificate"
echo "================================="

run_on_server "
# Get SSL certificate
certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email <EMAIL> --redirect

# Test certificate renewal
certbot renew --dry-run

echo '✅ SSL certificate obtained and configured'
"

echo "🧪 Step 7: Validate Security Fix"
echo "==============================="

# Test the deployment
echo "Testing HTTPS access..."
if curl -s -I https://$DOMAIN | grep -q "200"; then
    echo "✅ HTTPS access working"
else
    echo "❌ HTTPS access failed"
fi

echo "Testing authentication requirement..."
auth_test=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses -o /dev/null)
if [ "$auth_test" = "401" ]; then
    echo "✅ API properly requires authentication"
else
    echo "❌ API authentication not working (HTTP $auth_test)"
fi

echo "Testing health endpoint..."
if curl -s https://$DOMAIN/api/health | grep -q "ok"; then
    echo "✅ Health endpoint working"
else
    echo "❌ Health endpoint failed"
fi

echo ""
echo "🎉 EMERGENCY SECURITY FIX COMPLETED!"
echo "===================================="
echo "✅ Authenticated server deployed"
echo "✅ HTTPS/SSL configured"
echo "✅ API endpoints protected"
echo ""
echo "🔗 Application URL: https://$DOMAIN"
echo "🔐 Authentication: Required for all access"
echo "📊 API: Protected with 401 responses"
echo ""
echo "📞 Next Steps:"
echo "1. Test authentication with both users"
echo "2. Verify frontend data connectivity"
echo "3. Run comprehensive validation tests"
