const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');

const app = express();
const PORT = 3001;

// Supabase configuration
const SUPABASE_URL = 'https://ajpcbugydftdyhlbddpl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.dHPGCdWDJV-BEeQTnNhSDcck8PnvRMYKCIo2xn49Yqg';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Middleware
app.use(cors());
app.use(express.json());

// Helper functions
function generateSessionToken() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

async function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ success: false, error: 'Access token required' });
    }

    try {
        // Check session in Supabase
        const { data: session, error } = await supabase
            .from('nawras_sessions')
            .select(`
                *,
                nawras_users (
                    user_id,
                    name,
                    email
                )
            `)
            .eq('token', token)
            .gt('expires_at', new Date().toISOString())
            .single();

        if (error || !session) {
            return res.status(401).json({ success: false, error: 'Invalid or expired token' });
        }

        req.user = {
            id: session.nawras_users.user_id,
            name: session.nawras_users.name,
            email: session.nawras_users.email
        };
        next();
    } catch (error) {
        console.error('Auth error:', error);
        return res.status(401).json({ success: false, error: 'Authentication failed' });
    }
}