#!/bin/bash

# Simple server startup script

echo "Starting Nawras Admin Server..."
echo "==============================="

# Change to app directory
cd /opt/nawras-admin

# Check if server file exists
if [ ! -f "server-simple.cjs" ]; then
    echo "ERROR: server-simple.cjs not found!"
    exit 1
fi

echo "Found server-simple.cjs"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install --production
fi

# Start the server
echo "Starting server..."
export NODE_ENV=production
export PORT=3001

# Kill any existing node processes
pkill -f node || echo "No existing node processes"

# Start server in background
nohup node server-simple.cjs > /var/log/nawras-admin.log 2>&1 &

# Wait for server to start
sleep 5

# Test if server is running
if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "SUCCESS: Server is running!"
    echo "Health check response:"
    curl -s http://localhost:3001/api/health
else
    echo "ERROR: Server failed to start"
    echo "Log output:"
    tail -20 /var/log/nawras-admin.log
fi

echo "Startup script completed."
