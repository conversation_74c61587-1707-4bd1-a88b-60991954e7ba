#cloud-config

# Cloud-init script for Nawras Admin deployment
# This will automatically set up the entire system on droplet creation

package_update: true
package_upgrade: true

packages:
  - curl
  - wget
  - git
  - unzip
  - nginx
  - ufw
  - software-properties-common
  - build-essential

# Create application user
users:
  - name: nawras
    groups: sudo
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']

write_files:
  # Create package.json
  - path: /opt/nawras-admin/package.json
    content: |
      {
        "name": "nawras-admin",
        "version": "1.0.0",
        "description": "Nawras Admin Expense Tracker",
        "main": "server.js",
        "scripts": {
          "start": "node server.js",
          "dev": "node server.js"
        },
        "dependencies": {
          "express": "^4.18.2",
          "cors": "^2.8.5",
          "cookie-parser": "^1.4.6"
        },
        "engines": {
          "node": ">=18.0.0"
        }
      }
    owner: root:root
    permissions: '0644'

  # Create authenticated server
  - path: /opt/nawras-admin/server.js
    content: |
      const express = require('express');
      const cors = require('cors');
      const cookieParser = require('cookie-parser');
      const path = require('path');

      const app = express();
      const PORT = process.env.PORT || 3001;

      // Middleware
      app.use(cors({
        origin: [
          'http://localhost:5173', 
          'http://localhost:3000', 
          'https://partner.nawrasinchina.com', 
          'http://partner.nawrasinchina.com'
        ],
        credentials: true
      }));
      app.use(express.json());
      app.use(cookieParser());

      // Session storage
      const sessions = new Map();

      // Mock users
      const mockUsers = [
        {
          id: "taha",
          email: "<EMAIL>",
          name: "Taha",
          password: "taha2024",
          emailVerified: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "burak",
          email: "<EMAIL>",
          name: "Burak",
          password: "burak2024",
          emailVerified: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      ];

      // Authentication middleware
      const requireAuth = (req, res, next) => {
        const sessionToken = req.headers.authorization?.replace('Bearer ', '') ||
                            req.cookies?.session;

        if (!sessionToken || !sessions.has(sessionToken)) {
          return res.status(401).json({
            success: false,
            error: 'Authentication required'
          });
        }

        const session = sessions.get(sessionToken);
        if (new Date() > new Date(session.expiresAt)) {
          sessions.delete(sessionToken);
          res.clearCookie('session');
          return res.status(401).json({
            success: false,
            error: 'Session expired'
          });
        }

        req.user = session.user;
        req.session = session;
        next();
      };

      // Health check
      app.get('/api/health', (req, res) => {
        res.json({ 
          status: 'ok', 
          timestamp: new Date().toISOString(),
          version: '1.0.0',
          server: 'authenticated'
        });
      });

      // Authentication routes
      app.post('/api/auth/sign-in', (req, res) => {
        const { email, password } = req.body;
        const user = mockUsers.find(u => u.email === email && u.password === password);

        if (!user) {
          return res.status(401).json({
            success: false,
            error: 'Invalid email or password'
          });
        }

        const sessionToken = `session_${Date.now()}_${Math.random()}`;
        const session = {
          id: sessionToken,
          userId: user.id,
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            emailVerified: user.emailVerified,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          token: sessionToken,
        };

        sessions.set(sessionToken, session);

        res.cookie('session', sessionToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 7 * 24 * 60 * 60 * 1000,
        });

        res.json({
          success: true,
          data: { session, user: session.user }
        });
      });

      app.post('/api/auth/sign-out', (req, res) => {
        const sessionToken = req.headers.authorization?.replace('Bearer ', '') ||
                            req.cookies?.session;
        if (sessionToken) {
          sessions.delete(sessionToken);
        }
        res.clearCookie('session');
        res.json({ success: true });
      });

      app.get('/api/auth/session', (req, res) => {
        const sessionToken = req.headers.authorization?.replace('Bearer ', '') ||
                            req.cookies?.session;

        if (!sessionToken || !sessions.has(sessionToken)) {
          return res.json({ success: true, data: null });
        }

        const session = sessions.get(sessionToken);
        if (new Date() > new Date(session.expiresAt)) {
          sessions.delete(sessionToken);
          res.clearCookie('session');
          return res.json({ success: true, data: null });
        }

        res.json({ success: true, data: session });
      });

      // Protected routes
      app.get('/api/expenses', requireAuth, (req, res) => {
        const expenses = [
          {id: 1, amount: 25.5, description: "Lunch at downtown cafe", category: "Food", paidById: "taha", date: "2024-01-15", createdAt: "2024-01-15T12:30:00Z"},
          {id: 2, amount: 120, description: "Grocery shopping", category: "Groceries", paidById: "burak", date: "2024-01-14", createdAt: "2024-01-14T18:45:00Z"},
          {id: 3, amount: 45.75, description: "Gas station fill-up", category: "Transportation", paidById: "taha", date: "2024-01-13", createdAt: "2024-01-13T09:15:00Z"},
          {id: 4, amount: 89.99, description: "Monthly internet bill", category: "Utilities", paidById: "burak", date: "2024-01-12", createdAt: "2024-01-12T14:20:00Z"},
          {id: 5, amount: 15.25, description: "Coffee and pastry", category: "Food", paidById: "taha", date: "2024-01-11", createdAt: "2024-01-11T08:30:00Z"}
        ];
        
        res.json({
          success: true,
          data: expenses,
          total: expenses.length,
          timestamp: new Date().toISOString()
        });
      });

      app.get('/api/settlements', requireAuth, (req, res) => {
        const settlements = [
          {id: 1, amount: 30.00, paidBy: "taha", paidTo: "burak", description: "Settlement", date: "2024-01-17"},
          {id: 2, amount: 25.75, paidBy: "burak", paidTo: "taha", description: "Utilities", date: "2024-01-18"}
        ];
        
        res.json({
          success: true,
          data: settlements,
          total: settlements.length,
          timestamp: new Date().toISOString()
        });
      });

      app.use('/api/*', (req, res) => {
        res.status(404).json({ success: false, error: 'API endpoint not found' });
      });

      app.listen(PORT, '0.0.0.0', () => {
        console.log(`🚀 Nawras Admin server running on port ${PORT}`);
        console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
        console.log(`🔐 Authentication enabled`);
        console.log(`👥 Users: <EMAIL>, <EMAIL>`);
      });
    owner: root:root
    permissions: '0644'

  # Create nginx configuration
  - path: /etc/nginx/sites-available/default
    content: |
      server {
          listen 80;
          server_name partner.nawrasinchina.com;
          
          # Security headers
          add_header X-Frame-Options DENY;
          add_header X-Content-Type-Options nosniff;
          add_header X-XSS-Protection "1; mode=block";
          
          # Serve frontend
          location / {
              root /opt/nawras-admin/dist;
              try_files $uri $uri/ /index.html;
              
              location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                  expires 1y;
                  add_header Cache-Control "public, immutable";
              }
          }
          
          # Proxy API requests
          location /api/ {
              proxy_pass http://localhost:3001;
              proxy_http_version 1.1;
              proxy_set_header Upgrade $http_upgrade;
              proxy_set_header Connection 'upgrade';
              proxy_set_header Host $host;
              proxy_set_header X-Real-IP $remote_addr;
              proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
              proxy_set_header X-Forwarded-Proto $scheme;
              proxy_cache_bypass $http_upgrade;
              
              # CORS headers
              add_header Access-Control-Allow-Origin "http://partner.nawrasinchina.com" always;
              add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
              add_header Access-Control-Allow-Headers "Authorization, Content-Type" always;
              add_header Access-Control-Allow-Credentials "true" always;
              
              if ($request_method = 'OPTIONS') {
                  return 204;
              }
          }
      }
    owner: root:root
    permissions: '0644'

  # Create PM2 ecosystem file
  - path: /opt/nawras-admin/ecosystem.config.js
    content: |
      module.exports = {
        apps: [{
          name: 'nawras-admin',
          script: 'server.js',
          instances: 1,
          autorestart: true,
          watch: false,
          max_memory_restart: '1G',
          env: {
            NODE_ENV: 'production',
            PORT: 3001
          }
        }]
      };
    owner: root:root
    permissions: '0644'

runcmd:
  # Install Node.js 18 LTS
  - curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
  - apt-get install -y nodejs
  
  # Install PM2 globally
  - npm install -g pm2
  
  # Create application directory
  - mkdir -p /opt/nawras-admin/dist
  - chown -R root:root /opt/nawras-admin
  
  # Install Node.js dependencies
  - cd /opt/nawras-admin && npm install --production
  
  # Configure firewall
  - ufw --force reset
  - ufw default deny incoming
  - ufw default allow outgoing
  - ufw allow ssh
  - ufw allow 80/tcp
  - ufw allow 443/tcp
  - ufw allow 3001/tcp
  - ufw --force enable
  
  # Start and enable nginx
  - systemctl start nginx
  - systemctl enable nginx
  
  # Start application with PM2
  - cd /opt/nawras-admin && pm2 start ecosystem.config.js
  - pm2 startup systemd -u root --hp /root
  - pm2 save
  
  # Test the setup
  - sleep 10
  - curl -f http://localhost:3001/api/health || echo "Health check failed"
  
  # Create a status file
  - echo "Nawras Admin deployment completed at $(date)" > /opt/nawras-admin/deployment-status.txt
  - echo "Node.js version: $(node --version)" >> /opt/nawras-admin/deployment-status.txt
  - echo "PM2 status:" >> /opt/nawras-admin/deployment-status.txt
  - pm2 status >> /opt/nawras-admin/deployment-status.txt

final_message: |
  Nawras Admin droplet setup completed!
  
  Services installed:
  - Node.js 18 LTS
  - PM2 Process Manager
  - Nginx Web Server
  - UFW Firewall
  
  Application:
  - Backend API running on port 3001
  - Nginx proxy configured
  - Authentication system enabled
  
  Users:
  - <EMAIL> / taha2024
  - <EMAIL> / burak2024
  
  Next steps:
  1. Upload frontend files to /opt/nawras-admin/dist
  2. Update DNS to point to new droplet IP
  3. Configure SSL certificate
  4. Test authentication system
