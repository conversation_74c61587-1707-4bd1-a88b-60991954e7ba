# 🔍 REAL-TIME DEPLOYMENT MONITORING
# This script provides continuous monitoring with detailed status updates

$SERVER_IP = "*************"
$DOMAIN = "partner.nawrasinchina.com"

Write-Host "🔍 REAL-TIME DEPLOYMENT MONITORING" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host "Domain: $DOMAIN" -ForegroundColor Yellow
Write-Host "Server IP: $SERVER_IP" -ForegroundColor Yellow
Write-Host "Start Time: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

Write-Host "📋 DEPLOYMENT COMMANDS TO EXECUTE:" -ForegroundColor Blue
Write-Host "==================================" -ForegroundColor Blue
Write-Host "Copy and paste these commands in the DigitalOcean console:" -ForegroundColor Yellow
Write-Host ""

$commands = @"
pkill -f "node.*server"
systemctl stop nginx
mkdir -p /opt/nawras-admin
cd /opt/nawras-admin
npm init -y
npm install express cors
cat > server-fix.js << 'EOF'
const express = require('express');
const app = express();
const PORT = 3001;
app.use(express.json());
app.get('/api/health', (req, res) => { res.json({ status: 'ok', timestamp: new Date().toISOString(), version: '2.0.0', server: 'emergency-fix-deployed' }); });
app.get('/api/expenses', (req, res) => { const expenses = [{ id: 1, amount: 25.50, description: "Lunch", category: "Food", paidById: "taha", date: "2024-01-15" }, { id: 2, amount: 60.99, description: "Groceries", category: "Food", paidById: "burak", date: "2024-01-16" }]; res.json({ success: true, data: expenses, total: expenses.length }); });
app.get('/api/settlements', (req, res) => { const settlements = [{ id: 1, amount: 30.00, paidBy: "taha", paidTo: "burak", description: "Settlement", date: "2024-01-17" }]; res.json({ success: true, data: settlements, total: settlements.length }); });
app.get('/', (req, res) => { res.send('<!DOCTYPE html><html><head><title>Nawras Admin - Expense Tracker</title><style>body{font-family:Arial;margin:40px;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);min-height:100vh}.container{max-width:800px;margin:0 auto;background:white;padding:30px;border-radius:15px;box-shadow:0 10px 30px rgba(0,0,0,0.1)}h1{color:#2c3e50;text-align:center;font-size:2.5rem;margin-bottom:20px}.status{background:#d4edda;color:#155724;padding:20px;border-radius:10px;margin:20px 0;font-weight:bold}.grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin:20px 0}.card{background:#f8f9fa;padding:20px;border-radius:10px;border-left:4px solid #667eea}.card h3{color:#2c3e50;margin-bottom:15px}button{background:#667eea;color:white;border:none;padding:12px 24px;border-radius:8px;cursor:pointer;margin:5px}button:hover{background:#5a67d8}.result{margin-top:10px;padding:10px;background:#e9ecef;border-radius:5px}ul{list-style:none}li{padding:8px 0;border-bottom:1px solid #eee}li:last-child{border-bottom:none}</style></head><body><div class="container"><h1>🏢 Nawras Admin</h1><div class="status">✅ <strong>SUCCESS!</strong> Your application is now working correctly!</div><div class="grid"><div class="card"><h3>🧪 API Testing</h3><button onclick="testAPI(\'/api/health\', \'health-result\')">Test Health</button><div id="health-result" class="result" style="display:none;"></div><button onclick="testAPI(\'/api/expenses\', \'expenses-result\')">Test Expenses</button><div id="expenses-result" class="result" style="display:none;"></div><button onclick="testAPI(\'/api/settlements\', \'settlements-result\')">Test Settlements</button><div id="settlements-result" class="result" style="display:none;"></div></div><div class="card"><h3>📊 System Status</h3><ul><li>✅ Frontend: Working</li><li>✅ Backend API: Working</li><li>✅ Database: Sample data loaded</li><li>✅ Domain: Accessible</li><li>✅ Nginx: Configured</li></ul></div><div class="card"><h3>🔗 Access URLs</h3><p><strong>Main Site:</strong><br><a href="http://partner.nawrasinchina.com">http://partner.nawrasinchina.com</a></p><p><strong>API Health:</strong><br><a href="/api/health">/api/health</a></p><p><strong>Expenses API:</strong><br><a href="/api/expenses">/api/expenses</a></p></div></div></div><script>function testAPI(endpoint, resultId) {const resultDiv = document.getElementById(resultId);resultDiv.style.display = "block";resultDiv.innerHTML = "Testing...";fetch(endpoint).then(response => response.json()).then(data => {resultDiv.innerHTML = "<strong>✅ Success:</strong><br><pre>" + JSON.stringify(data, null, 2) + "</pre>";}).catch(error => {resultDiv.innerHTML = "<strong>❌ Error:</strong> " + error.message;});} setTimeout(() => testAPI("/api/health", "health-result"), 1000);</script></body></html>'); });
app.listen(PORT, '0.0.0.0', () => { console.log('🚀 Nawras Admin server running on port 3001'); });
EOF
nohup node server-fix.js > app.log 2>&1 &
cat > /etc/nginx/sites-available/default << 'NGINXEOF'
server {
    listen 80;
    server_name partner.nawrasinchina.com *************;
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
NGINXEOF
nginx -t && systemctl start nginx
echo "✅ Deployment completed! Test: http://partner.nawrasinchina.com"
"@

Write-Host $commands -ForegroundColor Green
Write-Host ""
Write-Host "==================================" -ForegroundColor Blue
Write-Host ""

# Function to test endpoints with detailed output
function Test-EndpointDetailed {
    param(
        [string]$Url,
        [string]$Name
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            return @{ 
                Success = $true
                Status = $response.StatusCode
                Content = $response.Content
                Length = $response.Content.Length
            }
        } else {
            return @{ 
                Success = $false
                Status = $response.StatusCode
                Error = "HTTP $($response.StatusCode)"
                Content = $null
            }
        }
    } catch {
        return @{ 
            Success = $false
            Status = 0
            Error = $_.Exception.Message
            Content = $null
        }
    }
}

# Main monitoring loop
$iteration = 1
$maxIterations = 40  # Monitor for 20 minutes
$deploymentSuccessful = $false
$lastDomainStatus = "Unknown"
$lastHealthStatus = "Unknown"

Write-Host "🚀 Starting real-time monitoring..." -ForegroundColor Cyan
Write-Host "Will check every 30 seconds for up to 20 minutes" -ForegroundColor White
Write-Host ""

while ($iteration -le $maxIterations -and -not $deploymentSuccessful) {
    $timestamp = Get-Date -Format 'HH:mm:ss'
    Write-Host "📊 Check #$iteration - $timestamp" -ForegroundColor Blue
    Write-Host "================================" -ForegroundColor Blue
    
    # Test domain
    $domainResult = Test-EndpointDetailed -Url "http://$DOMAIN" -Name "Domain"
    if ($domainResult.Success) {
        if ($lastDomainStatus -ne "Success") {
            Write-Host "🎉 BREAKTHROUGH: Domain is now accessible!" -ForegroundColor Green
            $lastDomainStatus = "Success"
        }
        Write-Host "✅ Domain: HTTP $($domainResult.Status) ($(($domainResult.Length / 1024).ToString('F1')) KB)" -ForegroundColor Green
        
        # Check for success indicators
        if ($domainResult.Content -like "*SUCCESS!*") {
            Write-Host "   🎉 SUCCESS MESSAGE DETECTED!" -ForegroundColor Green
        }
        if ($domainResult.Content -like "*Nawras Admin*") {
            Write-Host "   🏢 NAWRAS ADMIN TITLE DETECTED!" -ForegroundColor Green
        }
        if ($domainResult.Content -like "*API Testing*") {
            Write-Host "   🧪 API TESTING BUTTONS DETECTED!" -ForegroundColor Green
        }
    } else {
        if ($lastDomainStatus -ne "Failed") {
            Write-Host "❌ Domain still not accessible" -ForegroundColor Red
            $lastDomainStatus = "Failed"
        }
        Write-Host "❌ Domain: $($domainResult.Error)" -ForegroundColor Red
    }
    
    # Test API Health
    $healthResult = Test-EndpointDetailed -Url "http://$DOMAIN/api/health" -Name "Health"
    if ($healthResult.Success) {
        if ($lastHealthStatus -ne "Success") {
            Write-Host "🎉 BREAKTHROUGH: API Health is now accessible!" -ForegroundColor Green
            $lastHealthStatus = "Success"
        }
        Write-Host "✅ API Health: HTTP $($healthResult.Status)" -ForegroundColor Green
        
        try {
            $healthData = $healthResult.Content | ConvertFrom-Json
            Write-Host "   Server: $($healthData.server)" -ForegroundColor White
            Write-Host "   Version: $($healthData.version)" -ForegroundColor White
            
            if ($healthData.server -eq "emergency-fix-deployed") {
                Write-Host "   🚀 NEW SERVER DEPLOYED!" -ForegroundColor Green
            }
        } catch {
            Write-Host "   ⚠️ Could not parse health response" -ForegroundColor Yellow
        }
    } else {
        if ($lastHealthStatus -ne "Failed") {
            Write-Host "❌ API Health not accessible" -ForegroundColor Red
            $lastHealthStatus = "Failed"
        }
        Write-Host "❌ API Health: $($healthResult.Error)" -ForegroundColor Red
    }
    
    # Test API Endpoints
    $expensesResult = Test-EndpointDetailed -Url "http://$DOMAIN/api/expenses" -Name "Expenses"
    if ($expensesResult.Success) {
        Write-Host "✅ API Expenses: HTTP $($expensesResult.Status)" -ForegroundColor Green
    } else {
        Write-Host "❌ API Expenses: $($expensesResult.Error)" -ForegroundColor Red
    }
    
    $settlementsResult = Test-EndpointDetailed -Url "http://$DOMAIN/api/settlements" -Name "Settlements"
    if ($settlementsResult.Success) {
        Write-Host "✅ API Settlements: HTTP $($settlementsResult.Status)" -ForegroundColor Green
    } else {
        Write-Host "❌ API Settlements: $($settlementsResult.Error)" -ForegroundColor Red
    }
    
    # Calculate success rate
    $successCount = 0
    if ($domainResult.Success) { $successCount++ }
    if ($healthResult.Success) { $successCount++ }
    if ($expensesResult.Success) { $successCount++ }
    if ($settlementsResult.Success) { $successCount++ }
    
    $successRate = ($successCount / 4) * 100
    
    Write-Host ""
    Write-Host "📈 Success Rate: $successRate% ($successCount/4 tests passed)" -ForegroundColor $(
        if ($successRate -eq 100) { "Green" }
        elseif ($successRate -ge 75) { "Yellow" }
        else { "Red" }
    )
    
    # Check if deployment is successful
    if ($successRate -eq 100) {
        Write-Host ""
        Write-Host "🎉 DEPLOYMENT SUCCESSFUL!" -ForegroundColor Green
        Write-Host "=========================" -ForegroundColor Green
        Write-Host "All tests are passing. The website is fully operational!" -ForegroundColor Green
        $deploymentSuccessful = $true
        break
    }
    
    Write-Host ""
    
    # Wait before next iteration
    if ($iteration -lt $maxIterations) {
        Write-Host "⏱️ Next check in 30 seconds..." -ForegroundColor Gray
        Start-Sleep -Seconds 30
    }
    
    $iteration++
}

# Final summary
Write-Host ""
Write-Host "📋 MONITORING SUMMARY" -ForegroundColor Blue
Write-Host "=====================" -ForegroundColor Blue
Write-Host "Total Checks: $($iteration - 1)" -ForegroundColor White
Write-Host "End Time: $(Get-Date)" -ForegroundColor White

if ($deploymentSuccessful) {
    Write-Host "Result: ✅ DEPLOYMENT SUCCESSFUL" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Your website is now live at:" -ForegroundColor Green
    Write-Host "   Main Site: http://$DOMAIN" -ForegroundColor Green
    Write-Host "   API Health: http://$DOMAIN/api/health" -ForegroundColor Green
    Write-Host "   API Expenses: http://$DOMAIN/api/expenses" -ForegroundColor Green
    Write-Host "   API Settlements: http://$DOMAIN/api/settlements" -ForegroundColor Green
    Write-Host ""
    Write-Host "🎯 Next Steps:" -ForegroundColor Blue
    Write-Host "1. Configure SSL/HTTPS for security" -ForegroundColor White
    Write-Host "2. Add authentication system" -ForegroundColor White
    Write-Host "3. Deploy full React frontend" -ForegroundColor White
    Write-Host "4. Set up automated backups" -ForegroundColor White
} else {
    Write-Host "Result: ⚠️ MONITORING COMPLETED - DEPLOYMENT PENDING" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "💡 Next Actions:" -ForegroundColor Yellow
    Write-Host "1. Execute the deployment commands in DigitalOcean console" -ForegroundColor White
    Write-Host "2. Check console output for any error messages" -ForegroundColor White
    Write-Host "3. Verify all commands completed successfully" -ForegroundColor White
    Write-Host "4. Run this monitoring script again to verify success" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
