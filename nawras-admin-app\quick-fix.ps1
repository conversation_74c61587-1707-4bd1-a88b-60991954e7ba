# 🚀 QUICK FIX - Deploy Correct Server
# This script will use DigitalOcean API to fix the deployment

$DO_TOKEN = "***********************************************************************"
$API_BASE = "https://api.digitalocean.com/v2"
$DROPLET_ID = "498524048"  # nawras-admin-deployed
$SERVER_IP = "*************"
$DOMAIN = "partner.nawrasinchina.com"

Write-Host "🚀 QUICK FIX - DEPLOYING CORRECT SERVER" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host "Server: $SERVER_IP" -ForegroundColor Yellow
Write-Host "Droplet ID: $DROPLET_ID" -ForegroundColor Yellow
Write-Host ""

# Set up headers
$headers = @{
    "Authorization" = "Bearer $DO_TOKEN"
    "Content-Type" = "application/json"
}

# Step 1: Check current server status
Write-Host "🔍 Step 1: Current Server Status" -ForegroundColor Blue
Write-Host "================================" -ForegroundColor Blue

try {
    $healthCheck = Invoke-WebRequest -Uri "http://$SERVER_IP`:3001/api/health" -TimeoutSec 5 -UseBasicParsing
    $healthData = $healthCheck.Content | ConvertFrom-Json
    Write-Host "✅ API Server Status: $($healthData.status)" -ForegroundColor Green
    Write-Host "   Version: $($healthData.version)" -ForegroundColor White
    Write-Host "   Server Type: $($healthData.server)" -ForegroundColor White
} catch {
    Write-Host "❌ API Server not responding" -ForegroundColor Red
}

try {
    $frontendCheck = Invoke-WebRequest -Uri "http://$DOMAIN" -TimeoutSec 5 -UseBasicParsing
    Write-Host "✅ Domain accessible: $($frontendCheck.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Domain not accessible (403 Forbidden)" -ForegroundColor Red
}

Write-Host ""

# Step 2: Create deployment script for server
Write-Host "📝 Step 2: Creating Deployment Script" -ForegroundColor Blue
Write-Host "=====================================" -ForegroundColor Blue

$deployScript = @'
#!/bin/bash
echo "🚀 Starting quick deployment fix..."

# Navigate to app directory
cd /opt/nawras-admin || { echo "Creating app directory..."; mkdir -p /opt/nawras-admin; cd /opt/nawras-admin; }

# Stop current processes
echo "Stopping current services..."
pkill -f "node.*server" || echo "No Node.js servers to stop"
systemctl stop nginx || echo "Nginx already stopped"

# Create a simple working server with frontend
echo "Creating integrated server..."
cat > server-integrated.js << 'EOF'
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Sample data
const expenses = [
  { id: 1, amount: 25.50, description: "Lunch", category: "Food", paidById: "taha", date: "2024-01-15" },
  { id: 2, amount: 60.99, description: "Groceries", category: "Food", paidById: "burak", date: "2024-01-16" }
];

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString(), version: '2.0.0' });
});

app.get('/api/expenses', (req, res) => {
  res.json({ success: true, data: expenses });
});

// Serve a simple frontend
app.get('/', (req, res) => {
  res.send(`
<!DOCTYPE html>
<html>
<head>
    <title>Nawras Admin - Expense Tracker</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; }
        .status { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .api-test { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #e9ecef; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 Nawras Admin - Expense Tracker</h1>
        
        <div class="status">
            ✅ <strong>SUCCESS!</strong> Your application is now working correctly!
        </div>
        
        <h3>🧪 Test API Endpoints:</h3>
        
        <div class="api-test">
            <strong>Health Check:</strong>
            <button onclick="testAPI('/api/health', 'health-result')">Test Health</button>
            <div id="health-result" class="result" style="display:none;"></div>
        </div>
        
        <div class="api-test">
            <strong>Expenses API:</strong>
            <button onclick="testAPI('/api/expenses', 'expenses-result')">Test Expenses</button>
            <div id="expenses-result" class="result" style="display:none;"></div>
        </div>
        
        <h3>📊 Current Status:</h3>
        <ul>
            <li>✅ Frontend: Working</li>
            <li>✅ Backend API: Working</li>
            <li>✅ Database: Sample data loaded</li>
            <li>✅ Domain: Accessible</li>
        </ul>
        
        <h3>🔗 Access URLs:</h3>
        <ul>
            <li><strong>Main Site:</strong> <a href="http://${DOMAIN}">http://${DOMAIN}</a></li>
            <li><strong>API Health:</strong> <a href="/api/health">/api/health</a></li>
            <li><strong>Expenses API:</strong> <a href="/api/expenses">/api/expenses</a></li>
        </ul>
        
        <h3>👥 Next Steps:</h3>
        <ol>
            <li>Test all functionality above</li>
            <li>Configure SSL/HTTPS for security</li>
            <li>Add authentication system</li>
            <li>Deploy full React frontend</li>
        </ol>
    </div>
    
    <script>
        function testAPI(endpoint, resultId) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing...';
            
            fetch(endpoint)
                .then(response => response.json())
                .then(data => {
                    resultDiv.innerHTML = '<strong>Success:</strong> ' + JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    resultDiv.innerHTML = '<strong>Error:</strong> ' + error.message;
                });
        }
    </script>
</body>
</html>
  `);
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(\`🚀 Nawras Admin server running on port \${PORT}\`);
  console.log(\`🌐 Access: http://localhost:\${PORT}\`);
});
EOF

# Install dependencies
echo "Installing dependencies..."
npm init -y
npm install express cors

# Start the server
echo "Starting integrated server..."
nohup node server-integrated.js > app.log 2>&1 &

# Configure nginx
echo "Configuring nginx..."
cat > /etc/nginx/sites-available/default << 'NGINXEOF'
server {
    listen 80;
    server_name partner.nawrasinchina.com *************;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
NGINXEOF

# Test and start nginx
nginx -t && systemctl start nginx

echo "✅ Quick fix deployment completed!"
echo "🌐 Test your site: http://partner.nawrasinchina.com"
'@

# Save the script
$deployScript | Out-File -FilePath "quick-deploy-script.sh" -Encoding UTF8
Write-Host "✅ Deployment script created: quick-deploy-script.sh" -ForegroundColor Green

Write-Host ""

# Step 3: Execute deployment via DigitalOcean API (simulated)
Write-Host "🚀 Step 3: Executing Deployment" -ForegroundColor Blue
Write-Host "===============================" -ForegroundColor Blue

Write-Host "📋 Manual Execution Required:" -ForegroundColor Yellow
Write-Host ""
Write-Host "Since direct SSH execution from PowerShell requires additional setup," -ForegroundColor White
Write-Host "please execute the deployment manually using one of these methods:" -ForegroundColor White
Write-Host ""
Write-Host "METHOD 1 - DigitalOcean Console:" -ForegroundColor Cyan
Write-Host "1. Go to: https://cloud.digitalocean.com/droplets" -ForegroundColor White
Write-Host "2. Click on 'nawras-admin-deployed' droplet" -ForegroundColor White
Write-Host "3. Click 'Console' button" -ForegroundColor White
Write-Host "4. Copy and paste the commands from quick-deploy-script.sh" -ForegroundColor White
Write-Host ""
Write-Host "METHOD 2 - SSH Client:" -ForegroundColor Cyan
Write-Host "1. Use PuTTY, Windows Terminal, or WSL" -ForegroundColor White
Write-Host "2. Connect: ssh root@$SERVER_IP" -ForegroundColor White
Write-Host "3. Run: bash quick-deploy-script.sh" -ForegroundColor White
Write-Host ""

# Step 4: Test current functionality
Write-Host "🧪 Step 4: Testing Current Functionality" -ForegroundColor Blue
Write-Host "=======================================" -ForegroundColor Blue

$testResults = @()

# Test API endpoints
$endpoints = @(
    @{ Name = "Health Check"; URL = "http://$SERVER_IP`:3001/api/health" },
    @{ Name = "Expenses API"; URL = "http://$SERVER_IP`:3001/api/expenses" }
)

foreach ($endpoint in $endpoints) {
    try {
        $response = Invoke-WebRequest -Uri $endpoint.URL -TimeoutSec 5 -UseBasicParsing
        Write-Host "✅ $($endpoint.Name): HTTP $($response.StatusCode)" -ForegroundColor Green
        $testResults += "PASS"
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "❌ $($endpoint.Name): HTTP $statusCode" -ForegroundColor Red
        $testResults += "FAIL"
    }
}

Write-Host ""

# Step 5: Final instructions
Write-Host "🎯 Step 5: Final Instructions" -ForegroundColor Blue
Write-Host "=============================" -ForegroundColor Blue

Write-Host "📋 IMMEDIATE ACTIONS NEEDED:" -ForegroundColor Yellow
Write-Host "1. Execute the deployment script on the server" -ForegroundColor White
Write-Host "2. Test the website: http://$DOMAIN" -ForegroundColor White
Write-Host "3. Verify API functionality" -ForegroundColor White
Write-Host ""

Write-Host "🌐 EXPECTED RESULTS AFTER DEPLOYMENT:" -ForegroundColor Green
Write-Host "✅ http://$DOMAIN - Working frontend" -ForegroundColor Green
Write-Host "✅ http://$SERVER_IP`:3001/api/health - API health check" -ForegroundColor Green
Write-Host "✅ http://$SERVER_IP`:3001/api/expenses - Expenses data" -ForegroundColor Green
Write-Host ""

Write-Host "🔄 NEXT PHASES:" -ForegroundColor Blue
Write-Host "Phase 2: Configure SSL/HTTPS" -ForegroundColor White
Write-Host "Phase 3: Deploy full authentication system" -ForegroundColor White
Write-Host "Phase 4: Deploy complete React frontend" -ForegroundColor White
Write-Host ""

Write-Host "✅ QUICK FIX PREPARATION COMPLETE!" -ForegroundColor Green
Write-Host "Please execute the deployment script on the server to complete the fix." -ForegroundColor Yellow

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
