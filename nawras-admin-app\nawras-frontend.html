<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Expense Tracker</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
        }
        
        /* Login Screen Styles */
        .login-container { 
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .login-box { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
            padding: 3rem; 
            max-width: 450px; 
            width: 100%; 
        }
        
        .logo { 
            text-align: center; 
            margin-bottom: 2rem; 
            color: #2c3e50; 
        }
        
        .logo h1 { 
            font-size: 2rem; 
            margin-bottom: 0.5rem; 
        }
        
        .logo p { 
            color: #6c757d; 
            font-size: 0.9rem; 
        }
        
        /* App Container Styles */
        .app-container { 
            background: white; 
            min-height: 100vh;
            display: flex; 
        }
        
        .sidebar { 
            background: #2c3e50; 
            color: white; 
            width: 280px; 
            padding: 2rem 0; 
            display: flex; 
            flex-direction: column; 
        }
        
        .sidebar-logo { 
            padding: 0 2rem 2rem; 
            border-bottom: 1px solid #34495e; 
            margin-bottom: 2rem; 
        }
        
        .sidebar-logo h1 { 
            font-size: 1.5rem; 
            font-weight: 600; 
        }
        
        .sidebar-logo p { 
            font-size: 0.8rem; 
            color: #bdc3c7; 
            margin-top: 0.5rem; 
        }
        
        .nav-item { 
            padding: 1rem 2rem; 
            cursor: pointer; 
            transition: background 0.3s; 
            display: flex; 
            align-items: center; 
            gap: 0.75rem; 
            border: none;
            background: none;
            color: white;
            text-align: left;
            width: 100%;
            font-size: 1rem;
        }
        
        .nav-item:hover, .nav-item.active { 
            background: #34495e; 
        }
        
        .main-content { 
            flex: 1; 
            padding: 2rem; 
            background: #f8f9fa; 
            overflow-y: auto;
        }
        
        .header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 2rem; 
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .header h2 { 
            color: #2c3e50; 
            font-size: 2rem; 
            font-weight: 600; 
        }
        
        .header p { 
            color: #6c757d; 
            margin-top: 0.5rem; 
        }
        
        /* Form Styles */
        .form-group { 
            margin-bottom: 1.5rem; 
        }
        
        .form-group label { 
            display: block; 
            margin-bottom: 0.5rem; 
            color: #2c3e50; 
            font-weight: 500; 
        }
        
        .form-group input { 
            width: 100%; 
            padding: 0.75rem; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            font-size: 1rem; 
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        /* Button Styles */
        .btn { 
            background: #667eea; 
            color: white; 
            border: none; 
            padding: 0.75rem 1.5rem; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 1rem; 
            transition: all 0.3s; 
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover { 
            background: #5a6fd8; 
            transform: translateY(-1px);
        }
        
        .btn-primary { 
            background: #007bff; 
            width: 100%; 
            padding: 1rem; 
        }
        
        .btn-primary:hover { 
            background: #0056b3; 
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        /* Stats Grid */
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 1.5rem; 
            margin-bottom: 2rem; 
        }
        
        .stat-card { 
            background: white; 
            padding: 1.5rem; 
            border-radius: 12px; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.1); 
            transition: transform 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-card h3 { 
            color: #6c757d; 
            font-size: 0.9rem; 
            margin-bottom: 0.5rem; 
            text-transform: uppercase; 
            letter-spacing: 0.5px; 
        }
        
        .stat-value { 
            font-size: 2rem; 
            font-weight: 600; 
            margin-bottom: 0.5rem; 
        }
        
        .stat-subtitle { 
            color: #6c757d; 
            font-size: 0.9rem; 
        }
        
        /* Message Styles */
        .error-message { 
            background: #f8d7da; 
            color: #721c24; 
            padding: 1rem; 
            border-radius: 8px; 
            margin-bottom: 1rem; 
            border: 1px solid #f5c6cb; 
            display: none; 
        }
        
        .success-message { 
            background: #d4edda; 
            color: #155724; 
            padding: 1rem; 
            border-radius: 8px; 
            margin-bottom: 1rem; 
            border: 1px solid #c3e6cb; 
            display: none; 
        }
        
        .info-message {
            background: #e7f3ff;
            color: #0c5460;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #b8daff;
        }
        
        /* User Info */
        .user-info { 
            margin-top: auto; 
            padding: 1rem 2rem; 
            border-top: 1px solid #34495e; 
        }
        
        .user-info p { 
            margin-bottom: 0.5rem; 
            font-size: 0.9rem; 
        }
        
        .logout-btn { 
            background: #dc3545; 
            color: white; 
            border: none; 
            padding: 0.5rem 1rem; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 0.9rem; 
            width: 100%;
        }
        
        .logout-btn:hover { 
            background: #c82333; 
        }
        
        /* Expenses List */
        .expenses-list { 
            background: white; 
            border-radius: 12px; 
            padding: 1.5rem; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.1); 
        }
        
        .expense-item { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            padding: 1rem 0; 
            border-bottom: 1px solid #eee; 
        }
        
        .expense-item:last-child { 
            border-bottom: none; 
        }
        
        .expense-details h4 { 
            color: #2c3e50; 
            margin-bottom: 0.25rem; 
        }
        
        .expense-details p { 
            color: #6c757d; 
            font-size: 0.9rem; 
        }
        
        .expense-amount { 
            font-size: 1.25rem; 
            font-weight: 600; 
            color: #2c3e50; 
        }
        
        /* Demo Accounts */
        .demo-accounts { 
            background: #f8f9fa; 
            padding: 1rem; 
            border-radius: 8px; 
            margin-top: 1.5rem; 
            font-size: 0.9rem; 
        }
        
        /* Loading Spinner */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                padding: 1rem 0;
            }
            
            .nav-item {
                padding: 0.75rem 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* Hidden class */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-box">
            <div class="logo">
                <h1>🏢 Nawras Admin</h1>
                <p>Expense Tracker with Supabase</p>
            </div>
            
            <div class="info-message">
                <strong>✅ Custom Frontend Ready!</strong><br>
                Connected to working API with Supabase integration
            </div>
            
            <div id="loginError" class="error-message"></div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required placeholder="Enter your email">
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required placeholder="Enter your password">
                </div>
                <button type="submit" class="btn-primary" id="loginBtn">
                    <span id="loginBtnText">Sign In</span>
                    <span id="loginSpinner" class="loading hidden"></span>
                </button>
            </form>
            
            <div class="demo-accounts">
                <strong>Demo Accounts:</strong><br>
                • <EMAIL> / taha2024<br>
                • <EMAIL> / burak2024
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="app-container hidden">
        <div class="sidebar">
            <div class="sidebar-logo">
                <h1>🏢 Nawras Admin</h1>
                <p>Powered by Supabase</p>
            </div>
            
            <button class="nav-item active" data-view="dashboard">
                📊 Dashboard
            </button>
            <button class="nav-item" data-view="expenses">
                💰 Expenses
            </button>
            <button class="nav-item" data-view="settlements">
                🤝 Settlements
            </button>
            <button class="nav-item" data-view="reports">
                📈 Reports
            </button>
            
            <div class="user-info">
                <p><strong id="userName">User</strong></p>
                <p id="userEmail"><EMAIL></p>
                <button class="logout-btn" onclick="logout()">Sign Out</button>
            </div>
        </div>
        
        <div class="main-content">
            <!-- Dashboard View -->
            <div id="dashboardView" class="view">
                <div class="header">
                    <div>
                        <h2>📊 Dashboard</h2>
                        <p>Real-time expense tracking with live Supabase data</p>
                    </div>
                    <button class="btn btn-success" onclick="showAddExpenseForm()">
                        + Add Expense
                    </button>
                </div>
                
                <div id="errorMessage" class="error-message"></div>
                <div id="successMessage" class="success-message"></div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Taha's Expenses</h3>
                        <div class="stat-value" style="color: #007bff;" id="tahaExpenses">$0.00</div>
                        <div class="stat-subtitle" id="tahaCount">0 expenses</div>
                    </div>
                    <div class="stat-card">
                        <h3>Burak's Expenses</h3>
                        <div class="stat-value" style="color: #28a745;" id="burakExpenses">$0.00</div>
                        <div class="stat-subtitle" id="burakCount">0 expenses</div>
                    </div>
                    <div class="stat-card">
                        <h3>Combined Total</h3>
                        <div class="stat-value" style="color: #6f42c1;" id="totalExpenses">$0.00</div>
                        <div class="stat-subtitle" id="totalCount">0 total expenses</div>
                    </div>
                    <div class="stat-card">
                        <h3>Net Balance</h3>
                        <div class="stat-value" style="color: #fd7e14;" id="netBalance">$0.00</div>
                        <div class="stat-subtitle">Settlement needed</div>
                    </div>
                </div>
                
                <div class="expenses-list">
                    <h3 style="margin-bottom: 1rem;">Recent Expenses</h3>
                    <div id="expensesList">
                        <div style="text-align: center; padding: 2rem; color: #6c757d;">
                            <div class="loading"></div>
                            Loading expenses from API...
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Other views can be added here -->
            <div id="expensesView" class="view hidden">
                <div class="header">
                    <h2>💰 Expenses</h2>
                </div>
                <p>Expenses management view - Coming soon!</p>
            </div>
            
            <div id="settlementsView" class="view hidden">
                <div class="header">
                    <h2>🤝 Settlements</h2>
                </div>
                <p>Settlements management view - Coming soon!</p>
            </div>
            
            <div id="reportsView" class="view hidden">
                <div class="header">
                    <h2>📈 Reports</h2>
                </div>
                <p>Reports and analytics view - Coming soon!</p>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = 'http://partner.nawrasinchina.com:3001';
        
        // Global state
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;
        let currentView = 'dashboard';

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            if (authToken) { 
                checkSession(); 
            } else { 
                showLogin(); 
            }
            
            // Setup navigation
            setupNavigation();
        });

        // Navigation setup
        function setupNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    const view = this.getAttribute('data-view');
                    if (view) {
                        switchView(view);
                    }
                });
            });
        }

        // View switching
        function switchView(viewName) {
            // Hide all views
            document.querySelectorAll('.view').forEach(view => {
                view.classList.add('hidden');
            });
            
            // Show selected view
            const targetView = document.getElementById(viewName + 'View');
            if (targetView) {
                targetView.classList.remove('hidden');
            }
            
            // Update navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            const activeNavItem = document.querySelector(`[data-view="${viewName}"]`);
            if (activeNavItem) {
                activeNavItem.classList.add('active');
            }
            
            currentView = viewName;
            
            // Load view-specific data
            if (viewName === 'dashboard') {
                loadDashboardData();
            }
        }

        // Screen management
        function showLogin() {
            document.getElementById('loginScreen').classList.remove('hidden');
            document.getElementById('mainApp').classList.add('hidden');
        }

        function showApp() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            loadDashboardData();
        }

        // Message functions
        function showError(message, elementId = 'loginError') {
            const errorEl = document.getElementById(elementId);
            if (errorEl) {
                errorEl.textContent = message;
                errorEl.style.display = 'block';
                setTimeout(() => errorEl.style.display = 'none', 5000);
            }
        }

        function showSuccess(message, elementId = 'successMessage') {
            const successEl = document.getElementById(elementId);
            if (successEl) {
                successEl.textContent = message;
                successEl.style.display = 'block';
                setTimeout(() => successEl.style.display = 'none', 3000);
            }
        }

        // Authentication functions
        function checkSession() {
            fetch(`${API_BASE_URL}/api/auth/session`, {
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    currentUser = data.data.user;
                    updateUserInfo();
                    showApp();
                } else {
                    localStorage.removeItem('authToken');
                    authToken = null;
                    showLogin();
                }
            })
            .catch(error => {
                console.error('Session check failed:', error);
                localStorage.removeItem('authToken');
                authToken = null;
                showLogin();
            });
        }

        function updateUserInfo() {
            if (currentUser) {
                document.getElementById('userName').textContent = currentUser.name;
                document.getElementById('userEmail').textContent = currentUser.email;
            }
        }

        // Login form handling
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const submitBtn = document.getElementById('loginBtn');
            const btnText = document.getElementById('loginBtnText');
            const spinner = document.getElementById('loginSpinner');
            
            // Show loading state
            btnText.classList.add('hidden');
            spinner.classList.remove('hidden');
            submitBtn.disabled = true;
            
            fetch(`${API_BASE_URL}/api/auth/sign-in`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: email, password: password })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    authToken = data.data.session.token;
                    currentUser = data.data.user;
                    localStorage.setItem('authToken', authToken);
                    updateUserInfo();
                    showApp();
                    showSuccess('Welcome back, ' + currentUser.name + '! Connected to live API.');
                } else {
                    showError(data.error || 'Login failed');
                }
            })
            .catch(error => {
                console.error('Login error:', error);
                showError('Network error. Please check your connection.');
            })
            .finally(() => {
                // Reset loading state
                btnText.classList.remove('hidden');
                spinner.classList.add('hidden');
                submitBtn.disabled = false;
            });
        });

        function logout() {
            fetch(`${API_BASE_URL}/api/auth/sign-out`, {
                method: 'POST',
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .catch(error => console.error('Logout error:', error));
            
            localStorage.removeItem('authToken');
            authToken = null;
            currentUser = null;
            showLogin();
        }

        // Data loading functions
        function loadDashboardData() {
            fetch(`${API_BASE_URL}/api/expenses`, {
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .then(response => {
                if (response.status === 401) {
                    logout();
                    return;
                }
                return response.json();
            })
            .then(expensesData => {
                if (expensesData && expensesData.success) {
                    updateExpensesDisplay(expensesData.data);
                } else {
                    showError('Failed to load expenses from API', 'errorMessage');
                }
            })
            .catch(error => {
                console.error('Failed to load dashboard data:', error);
                showError('Failed to load data from API. Please refresh the page.', 'errorMessage');
            });
        }

        function updateExpensesDisplay(expenses) {
            let tahaTotal = 0, burakTotal = 0, tahaCount = 0, burakCount = 0;
            
            expenses.forEach(expense => {
                if (expense.paid_by_id === 'taha') {
                    tahaTotal += parseFloat(expense.amount);
                    tahaCount++;
                } else if (expense.paid_by_id === 'burak') {
                    burakTotal += parseFloat(expense.amount);
                    burakCount++;
                }
            });
            
            const total = tahaTotal + burakTotal;
            const balance = Math.abs(tahaTotal - burakTotal);
            
            // Update stats
            document.getElementById('tahaExpenses').textContent = '$' + tahaTotal.toFixed(2);
            document.getElementById('tahaCount').textContent = tahaCount + ' expenses';
            document.getElementById('burakExpenses').textContent = '$' + burakTotal.toFixed(2);
            document.getElementById('burakCount').textContent = burakCount + ' expenses';
            document.getElementById('totalExpenses').textContent = '$' + total.toFixed(2);
            document.getElementById('totalCount').textContent = expenses.length + ' total expenses';
            document.getElementById('netBalance').textContent = '$' + balance.toFixed(2);
            
            // Update expenses list
            const expensesList = document.getElementById('expensesList');
            if (expenses.length === 0) {
                expensesList.innerHTML = '<div style="text-align: center; padding: 2rem; color: #6c757d;">No expenses found</div>';
            } else {
                let html = '';
                expenses.forEach(expense => {
                    html += `<div class="expense-item">
                        <div class="expense-details">
                            <h4>${expense.description}</h4>
                            <p>${expense.category} • Paid by ${expense.paid_by_id === 'taha' ? 'Taha' : 'Burak'} • ${expense.date}</p>
                        </div>
                        <div class="expense-amount">$${parseFloat(expense.amount).toFixed(2)}</div>
                    </div>`;
                });
                expensesList.innerHTML = html;
            }
        }

        // Placeholder functions for future features
        function showAddExpenseForm() {
            alert('Add Expense feature coming soon! The API is ready for this functionality.');
        }
    </script>
</body>
</html>