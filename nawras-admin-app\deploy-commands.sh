#!/bin/bash

# Commands to run on the DigitalOcean droplet for deployment

# Update system
apt-get update

# Install required packages
apt-get install -y git curl

# Create app directory
mkdir -p /opt/nawras-admin
cd /opt/nawras-admin

# Clone or download the application (we'll upload files via API)
# For now, let's create a simple setup

# Create a simple backend server
cat > server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage
let expenses = [
  {
    id: 1,
    amount: 25.50,
    description: "Lunch at restaurant",
    category: "Food",
    paidById: "taha",
    date: "2024-01-15",
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    amount: 60.99,
    description: "Grocery shopping",
    category: "Groceries", 
    paidById: "burak",
    date: "2024-01-16",
    createdAt: new Date().toISOString()
  }
];

let settlements = [
  {
    id: 1,
    amount: 30.00,
    paidBy: "taha",
    paidTo: "burak", 
    description: "Settlement for shared expenses",
    date: "2024-01-17",
    createdAt: new Date().toISOString()
  }
];

// Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.get('/api/expenses', (req, res) => {
  res.json({ success: true, data: expenses, total: expenses.length });
});

app.post('/api/expenses', (req, res) => {
  const expense = {
    id: expenses.length + 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  expenses.push(expense);
  res.json({ success: true, data: expense });
});

app.get('/api/settlements', (req, res) => {
  res.json({ success: true, data: settlements, total: settlements.length });
});

app.post('/api/settlements', (req, res) => {
  const settlement = {
    id: settlements.length + 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  settlements.push(settlement);
  res.json({ success: true, data: settlement });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on port ${PORT}`);
});
EOF

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Create package.json
cat > package.json << 'EOF'
{
  "name": "nawras-admin-backend",
  "version": "1.0.0",
  "main": "server.js",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5"
  },
  "scripts": {
    "start": "node server.js"
  }
}
EOF

# Install dependencies
npm install

# Start the server with PM2 for process management
npm install -g pm2
pm2 start server.js --name "nawras-backend"
pm2 startup
pm2 save

echo "✅ Backend deployed and running on port 3001"
echo "🌐 API available at: http://***************:3001"
