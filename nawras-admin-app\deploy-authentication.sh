#!/bin/bash

# Authentication System Deployment Script
# Ensures proper authentication is configured and working

set -e

SERVER_IP="*************"
DOMAIN="partner.nawrasinchina.com"

echo "🔐 AUTHENTICATION SYSTEM DEPLOYMENT"
echo "==================================="
echo "Server: $SERVER_IP"
echo "Domain: $DOMAIN"
echo ""

# Function to run commands on server
run_on_server() {
    ssh -o StrictHostKeyChecking=no root@$SERVER_IP "$1"
}

echo "📋 Step 1: Verify Server Authentication"
echo "======================================"

run_on_server "
cd /opt/nawras-admin

# Check if authenticated server is running
if pgrep -f 'server-simple.cjs' > /dev/null; then
    echo '✅ Authenticated server is running'
    echo 'Process info:'
    ps aux | grep server-simple.cjs | grep -v grep
else
    echo '❌ Authenticated server not running'
    echo 'Starting authenticated server...'
    
    # Start the authenticated server
    if command -v pm2 >/dev/null 2>&1; then
        pm2 start server-simple.cjs --name nawras-admin-auth
        pm2 save
    else
        nohup node server-simple.cjs > server.log 2>&1 &
    fi
    
    sleep 3
    
    if pgrep -f 'server-simple.cjs' > /dev/null; then
        echo '✅ Authenticated server started'
    else
        echo '❌ Failed to start authenticated server'
        exit 1
    fi
fi
"

echo ""
echo "🧪 Step 2: Test Authentication Endpoints"
echo "======================================="

echo "Testing session endpoint..."
session_test=$(curl -s -w "%{http_code}" https://$DOMAIN/api/auth/session -o /tmp/session_test.json)
if [ "$session_test" = "200" ]; then
    echo "✅ Session endpoint accessible"
    echo "Response:"
    cat /tmp/session_test.json | jq '.' 2>/dev/null || cat /tmp/session_test.json
else
    echo "❌ Session endpoint failed (HTTP $session_test)"
fi

echo ""
echo "Testing sign-in endpoint..."
signin_test=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"wrong"}' \
    -o /tmp/signin_test.json)

if [ "$signin_test" = "401" ]; then
    echo "✅ Sign-in endpoint properly rejects invalid credentials"
    echo "Response:"
    cat /tmp/signin_test.json | jq '.' 2>/dev/null || cat /tmp/signin_test.json
else
    echo "❌ Sign-in endpoint not working properly (HTTP $signin_test)"
fi

echo ""
echo "🛡️ Step 3: Test API Protection"
echo "============================="

echo "Testing expenses endpoint without auth..."
expenses_test=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses -o /tmp/expenses_test.json)
if [ "$expenses_test" = "401" ]; then
    echo "✅ Expenses endpoint properly protected"
    echo "Response:"
    cat /tmp/expenses_test.json | jq '.' 2>/dev/null || cat /tmp/expenses_test.json
else
    echo "❌ Expenses endpoint not protected (HTTP $expenses_test)"
    echo "Response:"
    cat /tmp/expenses_test.json
fi

echo ""
echo "Testing settlements endpoint without auth..."
settlements_test=$(curl -s -w "%{http_code}" https://$DOMAIN/api/settlements -o /tmp/settlements_test.json)
if [ "$settlements_test" = "401" ]; then
    echo "✅ Settlements endpoint properly protected"
else
    echo "❌ Settlements endpoint not protected (HTTP $settlements_test)"
fi

echo ""
echo "🔑 Step 4: Test Valid User Authentication"
echo "========================================"

echo "Testing Taha's credentials..."
taha_test=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"taha2024"}' \
    -c /tmp/taha_cookies.txt \
    -o /tmp/taha_auth.json)

if [ "$taha_test" = "200" ]; then
    echo "✅ Taha can authenticate successfully"
    echo "Response:"
    cat /tmp/taha_auth.json | jq '.' 2>/dev/null || cat /tmp/taha_auth.json
    
    # Test authenticated API access
    echo ""
    echo "Testing authenticated API access for Taha..."
    auth_api_test=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses \
        -b /tmp/taha_cookies.txt \
        -o /tmp/auth_expenses.json)
    
    if [ "$auth_api_test" = "200" ]; then
        echo "✅ Authenticated API access working"
        echo "Expenses count: $(cat /tmp/auth_expenses.json | jq '.total' 2>/dev/null || echo 'Unknown')"
    else
        echo "❌ Authenticated API access failed (HTTP $auth_api_test)"
    fi
else
    echo "❌ Taha authentication failed (HTTP $taha_test)"
    echo "Response:"
    cat /tmp/taha_auth.json
fi

echo ""
echo "Testing Burak's credentials..."
burak_test=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"burak2024"}' \
    -c /tmp/burak_cookies.txt \
    -o /tmp/burak_auth.json)

if [ "$burak_test" = "200" ]; then
    echo "✅ Burak can authenticate successfully"
    echo "Response:"
    cat /tmp/burak_auth.json | jq '.' 2>/dev/null || cat /tmp/burak_auth.json
else
    echo "❌ Burak authentication failed (HTTP $burak_test)"
    echo "Response:"
    cat /tmp/burak_auth.json
fi

echo ""
echo "🌐 Step 5: Test Frontend Authentication"
echo "======================================"

echo "Testing main application access..."
main_app_test=$(curl -s -w "%{http_code}" https://$DOMAIN -o /tmp/main_app.html)
if [ "$main_app_test" = "200" ]; then
    if grep -q "login" /tmp/main_app.html || grep -q "sign-in" /tmp/main_app.html; then
        echo "✅ Frontend shows authentication interface"
    else
        echo "⚠️ Frontend loads but may not have authentication UI"
        echo "Checking for authentication components..."
        grep -i "auth\|login\|sign" /tmp/main_app.html | head -3 || echo "No auth components found"
    fi
else
    echo "❌ Frontend not accessible (HTTP $main_app_test)"
fi

echo ""
echo "🔧 Step 6: Authentication Configuration Summary"
echo "=============================================="

run_on_server "
cd /opt/nawras-admin

echo 'Server Status:'
if pgrep -f 'server-simple.cjs' > /dev/null; then
    echo '✅ Authenticated server running'
else
    echo '❌ Authenticated server not running'
fi

echo ''
echo 'Server Logs (last 10 lines):'
if [ -f server.log ]; then
    tail -10 server.log
elif command -v pm2 >/dev/null 2>&1; then
    pm2 logs nawras-admin-auth --lines 10 --nostream
else
    echo 'No logs available'
fi

echo ''
echo 'Listening Ports:'
netstat -tlnp | grep -E ':(80|443|3001) ' || echo 'No relevant ports found'
"

echo ""
echo "🎉 AUTHENTICATION DEPLOYMENT COMPLETED!"
echo "======================================="

# Calculate success rate
total_tests=6
passed_tests=0

[ "$session_test" = "200" ] && ((passed_tests++))
[ "$signin_test" = "401" ] && ((passed_tests++))
[ "$expenses_test" = "401" ] && ((passed_tests++))
[ "$settlements_test" = "401" ] && ((passed_tests++))
[ "$taha_test" = "200" ] && ((passed_tests++))
[ "$burak_test" = "200" ] && ((passed_tests++))

success_rate=$((passed_tests * 100 / total_tests))

echo "📊 Authentication Test Results:"
echo "Total Tests: $total_tests"
echo "Passed: $passed_tests"
echo "Success Rate: $success_rate%"
echo ""

if [ $success_rate -ge 80 ]; then
    echo "✅ Authentication system is working correctly!"
    echo ""
    echo "👥 User Credentials:"
    echo "   • Taha: <EMAIL> / taha2024"
    echo "   • Burak: <EMAIL> / burak2024"
    echo ""
    echo "🔗 Application: https://$DOMAIN"
    echo "🔐 All API endpoints are now protected"
    echo "🛡️ Authentication required for access"
else
    echo "❌ Authentication system needs attention!"
    echo "Please review the test results above and fix any issues."
fi

# Cleanup temp files
rm -f /tmp/*_test.json /tmp/*_auth.json /tmp/*_cookies.txt /tmp/main_app.html

echo ""
echo "📞 Next Steps:"
echo "1. Test login flow in browser"
echo "2. Verify data loading after authentication"
echo "3. Test all application features"
echo "4. Run comprehensive validation tests"
