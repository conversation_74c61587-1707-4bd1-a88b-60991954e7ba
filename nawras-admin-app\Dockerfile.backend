# Backend Dockerfile for Express API server

FROM node:18-alpine

WORKDIR /app

# Create data directory for SQLite
RUN mkdir -p /app/data

# Copy server files
COPY server/ ./

# Install dependencies if package.json exists
RUN if [ -f package.json ]; then npm ci --only=production; fi

# Expose port
EXPOSE 3001

# Set environment
ENV NODE_ENV=production
ENV PORT=3001

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001
RUN chown -R nodejs:nodejs /app
USER nodejs

# Start the server
CMD ["node", "index.js"]
