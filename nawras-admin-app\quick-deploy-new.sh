#!/bin/bash

# Quick deployment script for new droplet
# This will set up the authenticated Nawras Admin system

echo "🚀 QUICK DEPLOYMENT - NEW DROPLET"
echo "================================="
echo "Setting up Nawras Admin with authentication..."

# Update system
export DEBIAN_FRONTEND=noninteractive
apt-get update -y
apt-get upgrade -y

# Install basic packages
apt-get install -y curl wget git unzip nginx ufw

# Install Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Install PM2
npm install -g pm2

# Create application directory
mkdir -p /opt/nawras-admin
cd /opt/nawras-admin

# Create package.json
cat > package.json << 'EOF'
{
  "name": "nawras-admin",
  "version": "1.0.0",
  "description": "Nawras Admin Expense Tracker",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "cookie-parser": "^1.4.6"
  }
}
EOF

# Install dependencies
npm install --production

# Create authenticated server
cat > server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');

const app = express();
const PORT = 3001;

app.use(cors({
  origin: ['http://partner.nawrasinchina.com', 'https://partner.nawrasinchina.com'],
  credentials: true
}));
app.use(express.json());
app.use(cookieParser());

const sessions = new Map();
const mockUsers = [
  { id: "taha", email: "<EMAIL>", name: "Taha", password: "taha2024" },
  { id: "burak", email: "<EMAIL>", name: "Burak", password: "burak2024" }
];

const requireAuth = (req, res, next) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') || req.cookies?.session;
  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.status(401).json({ success: false, error: 'Authentication required' });
  }
  const session = sessions.get(sessionToken);
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    res.clearCookie('session');
    return res.status(401).json({ success: false, error: 'Session expired' });
  }
  req.user = session.user;
  next();
};

app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString(), version: '1.0.0', server: 'authenticated' });
});

app.post('/api/auth/sign-in', (req, res) => {
  const { email, password } = req.body;
  const user = mockUsers.find(u => u.email === email && u.password === password);
  if (!user) {
    return res.status(401).json({ success: false, error: 'Invalid credentials' });
  }
  const sessionToken = `session_${Date.now()}_${Math.random()}`;
  const session = {
    id: sessionToken,
    userId: user.id,
    user: { id: user.id, email: user.email, name: user.name },
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    token: sessionToken
  };
  sessions.set(sessionToken, session);
  res.cookie('session', sessionToken, {
    httpOnly: true,
    secure: false,
    sameSite: 'lax',
    maxAge: 7 * 24 * 60 * 60 * 1000
  });
  res.json({ success: true, data: { session, user: session.user } });
});

app.get('/api/auth/session', (req, res) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') || req.cookies?.session;
  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.json({ success: true, data: null });
  }
  const session = sessions.get(sessionToken);
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    res.clearCookie('session');
    return res.json({ success: true, data: null });
  }
  res.json({ success: true, data: session });
});

app.post('/api/auth/sign-out', (req, res) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') || req.cookies?.session;
  if (sessionToken) sessions.delete(sessionToken);
  res.clearCookie('session');
  res.json({ success: true });
});

app.get('/api/expenses', requireAuth, (req, res) => {
  const expenses = [
    {id: 1, amount: 25.5, description: "Lunch at downtown cafe", category: "Food", paidById: "taha", date: "2024-01-15"},
    {id: 2, amount: 120, description: "Grocery shopping", category: "Groceries", paidById: "burak", date: "2024-01-14"},
    {id: 3, amount: 45.75, description: "Gas station fill-up", category: "Transportation", paidById: "taha", date: "2024-01-13"},
    {id: 4, amount: 89.99, description: "Monthly internet bill", category: "Utilities", paidById: "burak", date: "2024-01-12"},
    {id: 5, amount: 15.25, description: "Coffee and pastry", category: "Food", paidById: "taha", date: "2024-01-11"}
  ];
  res.json({ success: true, data: expenses, total: expenses.length, timestamp: new Date().toISOString() });
});

app.get('/api/settlements', requireAuth, (req, res) => {
  const settlements = [
    {id: 1, amount: 30.00, paidBy: "taha", paidTo: "burak", description: "Settlement", date: "2024-01-17"},
    {id: 2, amount: 25.75, paidBy: "burak", paidTo: "taha", description: "Utilities", date: "2024-01-18"}
  ];
  res.json({ success: true, data: settlements, total: settlements.length, timestamp: new Date().toISOString() });
});

app.use('/api/*', (req, res) => {
  res.status(404).json({ success: false, error: 'API endpoint not found' });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Nawras Admin server running on port ${PORT}`);
  console.log(`🔐 Authentication enabled`);
  console.log(`👥 Users: <EMAIL>, <EMAIL>`);
});
EOF

# Configure firewall
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3001/tcp
ufw --force enable

# Configure nginx
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80;
    server_name partner.nawrasinchina.com;
    
    location / {
        root /opt/nawras-admin/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        add_header Access-Control-Allow-Origin "http://partner.nawrasinchina.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Authorization, Content-Type" always;
        add_header Access-Control-Allow-Credentials "true" always;
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
}
EOF

# Start services
systemctl start nginx
systemctl enable nginx

# Start application with PM2
pm2 start server.js --name nawras-admin
pm2 startup systemd -u root --hp /root
pm2 save

# Wait and test
sleep 5

echo ""
echo "🧪 TESTING DEPLOYMENT"
echo "===================="

# Test health endpoint
echo "Testing health endpoint..."
health_response=$(curl -s http://localhost:3001/api/health)
if echo "$health_response" | grep -q '"status":"ok"'; then
    echo "✅ Health check PASSED"
    echo "Response: $health_response"
else
    echo "❌ Health check FAILED"
    echo "Response: $health_response"
fi

# Test authentication requirement
echo ""
echo "Testing authentication requirement..."
auth_response=$(curl -s -w "%{http_code}" http://localhost:3001/api/expenses -o /tmp/auth_test.json)
if [ "$auth_response" = "401" ]; then
    echo "✅ Authentication requirement PASSED"
    echo "Response: $(cat /tmp/auth_test.json)"
else
    echo "❌ Authentication requirement FAILED (HTTP $auth_response)"
    echo "Response: $(cat /tmp/auth_test.json)"
fi

# Test nginx proxy
echo ""
echo "Testing nginx proxy..."
proxy_response=$(curl -s http://localhost/api/health)
if echo "$proxy_response" | grep -q '"status":"ok"'; then
    echo "✅ Nginx proxy PASSED"
    echo "Response: $proxy_response"
else
    echo "❌ Nginx proxy FAILED"
    echo "Response: $proxy_response"
fi

echo ""
echo "🎉 DEPLOYMENT COMPLETED!"
echo "======================="
echo ""
echo "📊 System Status:"
echo "   • Node.js: $(node --version)"
echo "   • Backend API: Running on port 3001"
echo "   • Nginx: Running and configured"
echo "   • Firewall: Configured"
echo "   • PM2: Managing application"
echo ""
echo "🌐 Access URLs:"
echo "   • Health Check: http://***************:3001/api/health"
echo "   • Nginx Proxy: http://***************/api/health"
echo ""
echo "👥 User Credentials:"
echo "   • Taha: <EMAIL> / taha2024"
echo "   • Burak: <EMAIL> / burak2024"
echo ""
echo "📋 Next Steps:"
echo "   1. Upload frontend files to /opt/nawras-admin/dist"
echo "   2. Update DNS to point to ***************"
echo "   3. Test authentication in browser"
echo "   4. Configure SSL certificate"

echo ""
echo "Deployment script completed! 🚀"
