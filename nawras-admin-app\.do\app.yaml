name: nawras-admin-partner
services:
- name: web
  source_dir: /
  github:
    repo: TahaOsa/nawras-admin-partner
    branch: main
    deploy_on_push: true
  run_command: npm start
  build_command: npm install --only=production
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  http_port: 3001
  routes:
  - path: /
  envs:
  - key: NODE_ENV
    value: production
  - key: PORT
    value: "3001"
domains:
- domain: partner.nawrasinchina.com
  type: PRIMARY
  zone: nawrasinchina.com
alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
features:
- buildpack-stack=heroku-20
