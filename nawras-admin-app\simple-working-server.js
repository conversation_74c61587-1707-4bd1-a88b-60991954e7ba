const express = require('express');
const app = express();
const PORT = 3005;

app.use(express.json());

// Sample users
const users = [
    { id: 'taha', name: 'Ta<PERSON>', email: '<EMAIL>', password: 'taha2024' },
    { id: 'burak', name: '<PERSON><PERSON><PERSON>', email: '<EMAIL>', password: 'burak2024' }
];

const sessions = new Map();

function generateToken() {
    return 'token_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// SERVE HTML FRONTEND ON ROOT
app.get('/', (req, res) => {
    res.send(`<!DOCTYPE html>
<html>
<head>
    <title>Nawras Admin - Working Solution</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 400px; margin: 50px auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        button { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .demo-info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <h2>🎉 Nawras Admin - WORKING!</h2>
        <div id="message"></div>
        <form id="loginForm">
            <div class="form-group">
                <label>Email:</label>
                <input type="email" id="email" required>
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" id="password" required>
            </div>
            <button type="submit">Sign In</button>
        </form>
        <div class="demo-info">
            <strong>✅ SOLUTION WORKING!</strong><br>
            Demo Accounts:<br>
            • <EMAIL> / taha2024<br>
            • <EMAIL> / burak2024<br><br>
            <strong>Access:</strong> http://partner.nawrasinchina.com:3005
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            fetch('/api/auth/sign-in', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password })
            })
            .then(response => response.json())
            .then(data => {
                const messageDiv = document.getElementById('message');
                if (data.success) {
                    messageDiv.innerHTML = '<div class="success">✅ Login Successful! Authentication system working perfectly.</div>';
                    localStorage.setItem('authToken', data.data.session.token);
                } else {
                    messageDiv.innerHTML = '<div class="error">❌ ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('message').innerHTML = '<div class="error">❌ Network error</div>';
            });
        });
    </script>
</body>
</html>`);
});

// API endpoints
app.post('/api/auth/sign-in', (req, res) => {
    const { email, password } = req.body;
    const user = users.find(u => u.email === email && u.password === password);
    
    if (!user) {
        return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    const token = generateToken();
    sessions.set(token, { user, expiresAt: Date.now() + 86400000 });

    res.json({
        success: true,
        data: {
            user: { id: user.id, name: user.name, email: user.email },
            session: { token }
        }
    });
});

app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        message: 'Working solution deployed successfully!',
        timestamp: new Date().toISOString(),
        port: PORT
    });
});

app.listen(PORT, () => {
    console.log(`✅ WORKING SOLUTION RUNNING ON PORT ${PORT}`);
    console.log(`🌐 Access: http://partner.nawrasinchina.com:${PORT}`);
});