#!/bin/bash

# Complete Production Deployment Script
# Execute this script on the production server: *************

set -e

DOMAIN="partner.nawrasinchina.com"
APP_DIR="/opt/nawras-admin"
BACKUP_DIR="/opt/nawras-admin/backups/$(date +%Y%m%d_%H%M%S)"

echo "🚀 PRODUCTION DEPLOYMENT - NAWRAS ADMIN"
echo "======================================="
echo "Domain: $DOMAIN"
echo "App Directory: $APP_DIR"
echo "Backup Directory: $BACKUP_DIR"
echo ""

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check command success
check_success() {
    if [ $? -eq 0 ]; then
        log "✅ $1 - SUCCESS"
    else
        log "❌ $1 - FAILED"
        exit 1
    fi
}

# Step 1: Create Backup
echo "📋 STEP 1: CREATING BACKUP"
echo "=========================="

log "Creating backup directory..."
mkdir -p $BACKUP_DIR
check_success "Backup directory creation"

log "Backing up current application..."
if [ -d "$APP_DIR/dist" ]; then
    cp -r $APP_DIR/dist $BACKUP_DIR/dist.backup
    check_success "Application backup"
fi

if [ -f "$APP_DIR/server-simple.js" ]; then
    cp $APP_DIR/server-simple.js $BACKUP_DIR/server-simple.js.backup
    check_success "Server backup"
fi

if [ -f "$APP_DIR/package.json" ]; then
    cp $APP_DIR/package.json $BACKUP_DIR/package.json.backup
    check_success "Package.json backup"
fi

log "Backup completed successfully"
echo ""

# Step 2: Stop Current Application
echo "🛑 STEP 2: STOPPING CURRENT APPLICATION"
echo "======================================="

log "Stopping current application..."
# Try different methods to stop the application
pkill -f "node.*server" || true
pm2 stop all || true
systemctl stop nawras-admin || true

log "Current application stopped"
echo ""

# Step 3: Install Dependencies
echo "📦 STEP 3: INSTALLING DEPENDENCIES"
echo "=================================="

log "Navigating to application directory..."
cd $APP_DIR
check_success "Directory navigation"

log "Installing required dependencies..."
npm install express@4.21.2 cookie-parser bcryptjs
check_success "Dependency installation"

log "Installing dev dependencies..."
npm install --save-dev @types/cookie-parser @types/bcryptjs
check_success "Dev dependency installation"

log "Dependencies installed successfully"
echo ""

# Step 4: Update Application Files
echo "📁 STEP 4: UPDATING APPLICATION FILES"
echo "====================================="

log "Note: Application files should be uploaded manually before running this script"
log "Required files:"
log "  - dist/ folder (built React application)"
log "  - server-simple.cjs (authentication server)"
log "  - package.json (updated dependencies)"

# Verify files exist
if [ ! -d "$APP_DIR/dist" ]; then
    log "❌ dist/ folder not found. Please upload the deployment package first."
    exit 1
fi

if [ ! -f "$APP_DIR/server-simple.cjs" ]; then
    log "❌ server-simple.cjs not found. Please upload the deployment package first."
    exit 1
fi

log "✅ Application files verified"
echo ""

# Step 5: Configure Security (SSL/HTTPS)
echo "🔒 STEP 5: CONFIGURING SECURITY"
echo "==============================="

log "Installing Certbot for SSL..."
apt update
apt install -y certbot python3-certbot-nginx
check_success "Certbot installation"

log "Backing up nginx configuration..."
cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)
check_success "Nginx backup"

log "Creating new nginx configuration with security headers..."
cat > /etc/nginx/nginx.conf << 'EOF'
user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
    worker_connections 768;
}

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # HTTP Server (Redirect to HTTPS)
    server {
        listen 80;
        server_name partner.nawrasinchina.com;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS Server
    server {
        listen 443 ssl http2;
        server_name partner.nawrasinchina.com;

        # SSL Certificate (will be configured by Certbot)
        ssl_certificate /etc/letsencrypt/live/partner.nawrasinchina.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/partner.nawrasinchina.com/privkey.pem;

        # Security Headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        # Proxy to Node.js application
        location / {
            proxy_pass http://localhost:3001;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_set_header Cookie $http_cookie;
        }

        location /api/ {
            proxy_pass http://localhost:3001;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Cookie $http_cookie;
        }
    }
}
EOF

log "Testing nginx configuration..."
nginx -t
check_success "Nginx configuration test"

log "Stopping nginx for SSL certificate generation..."
systemctl stop nginx

log "Obtaining SSL certificate..."
certbot certonly --standalone -d partner.nawrasinchina.com --non-interactive --agree-tos --email <EMAIL>
check_success "SSL certificate generation"

log "Starting nginx..."
systemctl start nginx
systemctl enable nginx
check_success "Nginx startup"

log "Configuring firewall..."
ufw allow 'Nginx Full'
ufw allow ssh
ufw --force enable
check_success "Firewall configuration"

log "Security configuration completed"
echo ""

# Step 6: Start Application
echo "🚀 STEP 6: STARTING APPLICATION"
echo "==============================="

log "Starting Node.js application..."
cd $APP_DIR

# Test the application first
log "Testing application startup..."
timeout 10s node server-simple.cjs &
APP_PID=$!
sleep 5

if kill -0 $APP_PID 2>/dev/null; then
    log "✅ Application test successful"
    kill $APP_PID
else
    log "❌ Application test failed"
    exit 1
fi

# Start with PM2 if available, otherwise use systemd
if command -v pm2 >/dev/null 2>&1; then
    log "Starting with PM2..."
    pm2 start server-simple.cjs --name nawras-admin
    pm2 save
    pm2 startup
    check_success "PM2 startup"
else
    log "Creating systemd service..."
    cat > /etc/systemd/system/nawras-admin.service << EOF
[Unit]
Description=Nawras Admin Application
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/node server-simple.cjs
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable nawras-admin
    systemctl start nawras-admin
    check_success "Systemd service startup"
fi

log "Application started successfully"
echo ""

# Step 7: Validate Deployment
echo "🧪 STEP 7: VALIDATING DEPLOYMENT"
echo "==============================="

log "Waiting for application to stabilize..."
sleep 15

log "Testing health endpoint..."
if curl -s http://localhost:3001/api/health | grep -q "ok"; then
    log "✅ Health check passed"
else
    log "❌ Health check failed"
    exit 1
fi

log "Testing HTTPS access..."
if curl -s -I https://$DOMAIN | grep -q "200"; then
    log "✅ HTTPS access working"
else
    log "❌ HTTPS access failed"
fi

log "Testing authentication endpoint..."
if curl -s https://$DOMAIN/api/auth/session | grep -q "success"; then
    log "✅ Authentication endpoint working"
else
    log "❌ Authentication endpoint failed"
fi

log "Deployment validation completed"
echo ""

echo "🎉 DEPLOYMENT COMPLETE!"
echo "======================"
echo ""
echo "✅ Deployment Summary:"
echo "   • Application files updated"
echo "   • Dependencies installed"
echo "   • SSL certificate configured"
echo "   • Security headers enabled"
echo "   • Application started successfully"
echo ""
echo "🌐 Your application is now live:"
echo "   • HTTPS: https://$DOMAIN"
echo "   • HTTP redirects to HTTPS automatically"
echo ""
echo "👥 User Credentials:"
echo "   • Taha: <EMAIL> / taha2024"
echo "   • Burak: <EMAIL> / burak2024"
echo ""
echo "🆘 Rollback (if needed):"
echo "   cd $APP_DIR"
echo "   systemctl stop nawras-admin"
echo "   cp -r $BACKUP_DIR/dist.backup/* dist/"
echo "   cp $BACKUP_DIR/server-simple.js.backup server-simple.js"
echo "   systemctl start nawras-admin"
echo ""
echo "🎯 Deployment Status: SUCCESS"
