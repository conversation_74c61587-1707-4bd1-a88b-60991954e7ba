<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Test Login</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            padding: 1rem;
        }
        .container { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
            padding: 3rem; 
            max-width: 500px; 
            width: 100%; 
        }
        .form-group { margin-bottom: 1rem; }
        .form-group label { display: block; margin-bottom: 0.5rem; color: #2c3e50; font-weight: 500; }
        .form-group input { width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 5px; font-size: 1rem; }
        .btn { background: #007bff; color: white; border: none; width: 100%; padding: 1rem; border-radius: 5px; cursor: pointer; font-size: 1rem; margin-bottom: 1rem; }
        .btn:hover { background: #0056b3; }
        .btn-test { background: #28a745; }
        .btn-test:hover { background: #218838; }
        .result { padding: 1rem; border-radius: 5px; margin-top: 1rem; font-family: monospace; font-size: 0.9rem; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #e7f3ff; color: #0c5460; border: 1px solid #b8daff; }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 2rem; color: #2c3e50;">Nawras Admin - Authentication Test</h1>
        
        <div class="info result">
            <strong>API Server:</strong> http://partner.nawrasinchina.com:3001<br>
            <strong>Status:</strong> ✅ Working<br>
            <strong>Issue:</strong> Nginx blocking port 80
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" value="taha2024" required>
            </div>
            <button type="submit" class="btn">Test Login</button>
        </form>

        <button class="btn btn-test" onclick="testAPI()">Test API Health</button>
        <button class="btn btn-test" onclick="testExpenses()">Test Expenses (Requires Login)</button>

        <div id="result"></div>

        <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 5px; font-size: 0.9rem;">
            <strong>Demo Accounts:</strong><br>
            • <EMAIL> / taha2024<br>
            • <EMAIL> / burak2024
        </div>
    </div>

    <script>
        let authToken = null;

        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + type;
            resultDiv.innerHTML = message;
        }

        function testAPI() {
            showResult('Testing API health...', 'info');
            
            fetch('http://partner.nawrasinchina.com:3001/api/health')
                .then(response => response.json())
                .then(data => {
                    showResult(`✅ API Health Check Successful<br>
                        Status: ${data.status}<br>
                        Server: ${data.server}<br>
                        Timestamp: ${data.timestamp}`, 'success');
                })
                .catch(error => {
                    showResult(`❌ API Health Check Failed<br>Error: ${error.message}`, 'error');
                });
        }

        function testExpenses() {
            if (!authToken) {
                showResult('❌ Please login first to test expenses', 'error');
                return;
            }

            showResult('Testing expenses endpoint...', 'info');
            
            fetch('http://partner.nawrasinchina.com:3001/api/expenses', {
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showResult(`✅ Expenses Loaded Successfully<br>
                            Count: ${data.data.length} expenses<br>
                            Total: $${data.data.reduce((sum, exp) => sum + exp.amount, 0).toFixed(2)}`, 'success');
                    } else {
                        showResult(`❌ Failed to load expenses<br>Error: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    showResult(`❌ Expenses Request Failed<br>Error: ${error.message}`, 'error');
                });
        }

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            showResult('Testing login...', 'info');
            
            fetch('http://partner.nawrasinchina.com:3001/api/auth/sign-in', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: email, password: password })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        authToken = data.data.session.token;
                        showResult(`✅ Login Successful!<br>
                            User: ${data.data.user.name}<br>
                            Email: ${data.data.user.email}<br>
                            Token: ${authToken.substring(0, 20)}...`, 'success');
                    } else {
                        showResult(`❌ Login Failed<br>Error: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    showResult(`❌ Login Request Failed<br>Error: ${error.message}`, 'error');
                });
        });

        // Test API on page load
        window.onload = function() {
            testAPI();
        };
    </script>
</body>
</html>