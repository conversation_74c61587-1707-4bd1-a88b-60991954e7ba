import{c as t}from"./ui-_TY0ykpl.js";import{r as e,R as r}from"./router-DRdqKDkt.js";import{c as n,g as o}from"./vendor-CFHJfABC.js";var i,a,u,c,l,s,f,p,h,y,d,v,m,b,g,w,x,O,j,S,P,A,E,k,M,T,_,C,D,I,N,B,R,L,z,F,U,$,W,q,V,X,H,G,K,Y,Z,J,Q,tt,et,rt,nt,ot,it,at,ut,ct,lt,st,ft,pt,ht,yt,dt,vt,mt,bt,gt,wt,xt,Ot,jt,St,Pt,At,Et,kt,Mt,Tt,_t,Ct,Dt,It,Nt,Bt,Rt,Lt,zt,Ft,Ut,$t,Wt,qt,Vt,Xt,Ht,Gt,Kt,Yt,Zt,Jt,Qt,te;function ee(){if(a)return i;a=1;var t=Array.isArray;return i=t}function re(){if(c)return u;c=1;var t="object"==typeof n&&n&&n.Object===Object&&n;return u=t}function ne(){if(s)return l;s=1;var t=re(),e="object"==typeof self&&self&&self.Object===Object&&self,r=t||e||Function("return this")();return l=r}function oe(){if(p)return f;p=1;var t=ne().Symbol;return f=t}function ie(){if(b)return m;b=1;var t=oe(),e=function(){if(y)return h;y=1;var t=oe(),e=Object.prototype,r=e.hasOwnProperty,n=e.toString,o=t?t.toStringTag:void 0;return h=function(t){var e=r.call(t,o),i=t[o];try{t[o]=void 0;var a=!0}catch(c){}var u=n.call(t);return a&&(e?t[o]=i:delete t[o]),u}}(),r=function(){if(v)return d;v=1;var t=Object.prototype.toString;return d=function(e){return t.call(e)}}(),n=t?t.toStringTag:void 0;return m=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":n&&n in Object(t)?e(t):r(t)}}function ae(){if(w)return g;return w=1,g=function(t){return null!=t&&"object"==typeof t}}function ue(){if(O)return x;O=1;var t=ie(),e=ae();return x=function(r){return"symbol"==typeof r||e(r)&&"[object Symbol]"==t(r)}}function ce(){if(S)return j;S=1;var t=ee(),e=ue(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;return j=function(o,i){if(t(o))return!1;var a=typeof o;return!("number"!=a&&"symbol"!=a&&"boolean"!=a&&null!=o&&!e(o))||(n.test(o)||!r.test(o)||null!=i&&o in Object(i))}}function le(){if(A)return P;return A=1,P=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}}function se(){if(k)return E;k=1;var t=ie(),e=le();return E=function(r){if(!e(r))return!1;var n=t(r);return"[object Function]"==n||"[object GeneratorFunction]"==n||"[object AsyncFunction]"==n||"[object Proxy]"==n}}function fe(){if(C)return _;C=1;var t,e=function(){if(T)return M;T=1;var t=ne()["__core-js_shared__"];return M=t}(),r=(t=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||""))?"Symbol(src)_1."+t:"";return _=function(t){return!!r&&r in t}}function pe(){if(I)return D;I=1;var t=Function.prototype.toString;return D=function(e){if(null!=e){try{return t.call(e)}catch(r){}try{return e+""}catch(r){}}return""}}function he(){if(F)return z;F=1;var t=function(){if(B)return N;B=1;var t=se(),e=fe(),r=le(),n=pe(),o=/^\[object .+?Constructor\]$/,i=Function.prototype,a=Object.prototype,u=i.toString,c=a.hasOwnProperty,l=RegExp("^"+u.call(c).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");return N=function(i){return!(!r(i)||e(i))&&(t(i)?l:o).test(n(i))}}(),e=L?R:(L=1,R=function(t,e){return null==t?void 0:t[e]});return z=function(r,n){var o=e(r,n);return t(o)?o:void 0}}function ye(){if($)return U;$=1;var t=he()(Object,"create");return U=t}function de(){if(tt)return Q;tt=1;var t=function(){if(q)return W;q=1;var t=ye();return W=function(){this.__data__=t?t(null):{},this.size=0}}(),e=X?V:(X=1,V=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}),r=function(){if(G)return H;G=1;var t=ye(),e=Object.prototype.hasOwnProperty;return H=function(r){var n=this.__data__;if(t){var o=n[r];return"__lodash_hash_undefined__"===o?void 0:o}return e.call(n,r)?n[r]:void 0}}(),n=function(){if(Y)return K;Y=1;var t=ye(),e=Object.prototype.hasOwnProperty;return K=function(r){var n=this.__data__;return t?void 0!==n[r]:e.call(n,r)}}(),o=function(){if(J)return Z;J=1;var t=ye();return Z=function(e,r){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=t&&void 0===r?"__lodash_hash_undefined__":r,this}}();function i(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}return i.prototype.clear=t,i.prototype.delete=e,i.prototype.get=r,i.prototype.has=n,i.prototype.set=o,Q=i}function ve(){if(ot)return nt;return ot=1,nt=function(t,e){return t===e||t!=t&&e!=e}}function me(){if(at)return it;at=1;var t=ve();return it=function(e,r){for(var n=e.length;n--;)if(t(e[n][0],r))return n;return-1}}function be(){if(vt)return dt;vt=1;var t=rt?et:(rt=1,et=function(){this.__data__=[],this.size=0}),e=function(){if(ct)return ut;ct=1;var t=me(),e=Array.prototype.splice;return ut=function(r){var n=this.__data__,o=t(n,r);return!(o<0||(o==n.length-1?n.pop():e.call(n,o,1),--this.size,0))}}(),r=function(){if(st)return lt;st=1;var t=me();return lt=function(e){var r=this.__data__,n=t(r,e);return n<0?void 0:r[n][1]}}(),n=function(){if(pt)return ft;pt=1;var t=me();return ft=function(e){return t(this.__data__,e)>-1}}(),o=function(){if(yt)return ht;yt=1;var t=me();return ht=function(e,r){var n=this.__data__,o=t(n,e);return o<0?(++this.size,n.push([e,r])):n[o][1]=r,this}}();function i(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}return i.prototype.clear=t,i.prototype.delete=e,i.prototype.get=r,i.prototype.has=n,i.prototype.set=o,dt=i}function ge(){if(bt)return mt;bt=1;var t=he()(ne(),"Map");return mt=t}function we(){if(St)return jt;St=1;var t=Ot?xt:(Ot=1,xt=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t});return jt=function(e,r){var n=e.__data__;return t(r)?n["string"==typeof r?"string":"hash"]:n.map}}function xe(){if(It)return Dt;It=1;var t=function(){if(wt)return gt;wt=1;var t=de(),e=be(),r=ge();return gt=function(){this.size=0,this.__data__={hash:new t,map:new(r||e),string:new t}}}(),e=function(){if(At)return Pt;At=1;var t=we();return Pt=function(e){var r=t(this,e).delete(e);return this.size-=r?1:0,r}}(),r=function(){if(kt)return Et;kt=1;var t=we();return Et=function(e){return t(this,e).get(e)}}(),n=function(){if(Tt)return Mt;Tt=1;var t=we();return Mt=function(e){return t(this,e).has(e)}}(),o=function(){if(Ct)return _t;Ct=1;var t=we();return _t=function(e,r){var n=t(this,e),o=n.size;return n.set(e,r),this.size+=n.size==o?0:1,this}}();function i(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}return i.prototype.clear=t,i.prototype.delete=e,i.prototype.get=r,i.prototype.has=n,i.prototype.set=o,Dt=i}function Oe(){if(Bt)return Nt;Bt=1;var t=xe();function e(r,n){if("function"!=typeof r||null!=n&&"function"!=typeof n)throw new TypeError("Expected a function");var o=function(){var t=arguments,e=n?n.apply(this,t):t[0],i=o.cache;if(i.has(e))return i.get(e);var a=r.apply(this,t);return o.cache=i.set(e,a)||i,a};return o.cache=new(e.Cache||t),o}return e.Cache=t,Nt=e}function je(){if(Ft)return zt;Ft=1;var t=function(){if(Lt)return Rt;Lt=1;var t=Oe();return Rt=function(e){var r=t(e,(function(t){return 500===n.size&&n.clear(),t})),n=r.cache;return r}}(),e=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,n=t((function(t){var n=[];return 46===t.charCodeAt(0)&&n.push(""),t.replace(e,(function(t,e,o,i){n.push(o?i.replace(r,"$1"):e||t)})),n}));return zt=n}function Se(){if($t)return Ut;return $t=1,Ut=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}}function Pe(){if(Xt)return Vt;Xt=1;var t=function(){if(qt)return Wt;qt=1;var t=oe(),e=Se(),r=ee(),n=ue(),o=t?t.prototype:void 0,i=o?o.toString:void 0;return Wt=function t(o){if("string"==typeof o)return o;if(r(o))return e(o,t)+"";if(n(o))return i?i.call(o):"";var a=o+"";return"0"==a&&1/o==-1/0?"-0":a},Wt}();return Vt=function(e){return null==e?"":t(e)}}function Ae(){if(Gt)return Ht;Gt=1;var t=ee(),e=ce(),r=je(),n=Pe();return Ht=function(o,i){return t(o)?o:e(o,i)?[o]:r(n(o))}}function Ee(){if(Yt)return Kt;Yt=1;var t=ue();return Kt=function(e){if("string"==typeof e||t(e))return e;var r=e+"";return"0"==r&&1/e==-1/0?"-0":r}}function ke(){if(Jt)return Zt;Jt=1;var t=Ae(),e=Ee();return Zt=function(r,n){for(var o=0,i=(n=t(n,r)).length;null!=r&&o<i;)r=r[e(n[o++])];return o&&o==i?r:void 0}}function Me(){if(te)return Qt;te=1;var t=ke();return Qt=function(e,r,n){var o=null==e?void 0:t(e,r);return void 0===o?n:o}}const Te=o(Me());var _e,Ce;const De=o(Ce?_e:(Ce=1,_e=function(t){return null==t}));var Ie,Ne;const Be=o(function(){if(Ne)return Ie;Ne=1;var t=ie(),e=ee(),r=ae();return Ie=function(n){return"string"==typeof n||!e(n)&&r(n)&&"[object String]"==t(n)}}());const Re=o(se());const Le=o(le());var ze,Fe,Ue={exports:{}},$e={};var We,qe,Ve,Xe,He=(Fe||(Fe=1,Ue.exports=function(){if(ze)return $e;ze=1;var t,e=Symbol.for("react.element"),r=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),u=Symbol.for("react.context"),c=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),s=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),y=Symbol.for("react.offscreen");function d(t){if("object"==typeof t&&null!==t){var y=t.$$typeof;switch(y){case e:switch(t=t.type){case n:case i:case o:case s:case f:return t;default:switch(t=t&&t.$$typeof){case c:case u:case l:case h:case p:case a:return t;default:return y}}case r:return y}}}return t=Symbol.for("react.module.reference"),$e.ContextConsumer=u,$e.ContextProvider=a,$e.Element=e,$e.ForwardRef=l,$e.Fragment=n,$e.Lazy=h,$e.Memo=p,$e.Portal=r,$e.Profiler=i,$e.StrictMode=o,$e.Suspense=s,$e.SuspenseList=f,$e.isAsyncMode=function(){return!1},$e.isConcurrentMode=function(){return!1},$e.isContextConsumer=function(t){return d(t)===u},$e.isContextProvider=function(t){return d(t)===a},$e.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===e},$e.isForwardRef=function(t){return d(t)===l},$e.isFragment=function(t){return d(t)===n},$e.isLazy=function(t){return d(t)===h},$e.isMemo=function(t){return d(t)===p},$e.isPortal=function(t){return d(t)===r},$e.isProfiler=function(t){return d(t)===i},$e.isStrictMode=function(t){return d(t)===o},$e.isSuspense=function(t){return d(t)===s},$e.isSuspenseList=function(t){return d(t)===f},$e.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===n||e===i||e===o||e===s||e===f||e===y||"object"==typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===p||e.$$typeof===a||e.$$typeof===u||e.$$typeof===l||e.$$typeof===t||void 0!==e.getModuleId)},$e.typeOf=d,$e}()),Ue.exports);function Ge(){if(qe)return We;qe=1;var t=ie(),e=ae();return We=function(r){return"number"==typeof r||e(r)&&"[object Number]"==t(r)}}const Ke=o(function(){if(Xe)return Ve;Xe=1;var t=Ge();return Ve=function(e){return t(e)&&e!=+e}}());const Ye=o(Ge());var Ze=function(t){return 0===t?0:t>0?1:-1},Je=function(t){return Be(t)&&t.indexOf("%")===t.length-1},Qe=function(t){return Ye(t)&&!Ke(t)},tr=function(t){return Qe(t)||Be(t)},er=0,rr=function(t){var e=++er;return"".concat(t||"").concat(e)},nr=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!Qe(t)&&!Be(t))return n;if(Je(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return Ke(r)&&(r=n),o&&r>e&&(r=e),r},or=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},ir=function(t,e){return Qe(t)&&Qe(e)?function(r){return t+r*(e-t)}:function(){return e}};function ar(t,e,r){return t&&t.length?t.find((function(t){return t&&("function"==typeof e?e(t):Te(t,e))===r})):null}var ur=function(t,e){return Qe(t)&&Qe(e)?t-e:Be(t)&&Be(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))};function cr(t,e){for(var r in t)if({}.hasOwnProperty.call(t,r)&&(!{}.hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if({}.hasOwnProperty.call(e,n)&&!{}.hasOwnProperty.call(t,n))return!1;return!0}function lr(t){return(lr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sr=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],fr=["points","pathLength"],pr={svg:["viewBox","children"],polygon:fr,polyline:fr},hr=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],yr=function(t,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if(e.isValidElement(t)&&(n=t.props),!Le(n))return null;var o={};return Object.keys(n).forEach((function(t){hr.includes(t)&&(o[t]=r||function(e){return n[t](n,e)})})),o},dr=function(t,e,r){if(!Le(t)||"object"!==lr(t))return null;var n=null;return Object.keys(t).forEach((function(o){var i=t[o];hr.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t,e,r){return function(n){return t(e,r,n),null}}(i,e,r))})),n},vr=["children"],mr=["children"];function br(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function gr(t){return(gr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var wr={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},xr=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},Or=null,jr=null,Sr=function t(r){if(r===Or&&Array.isArray(jr))return jr;var n=[];return e.Children.forEach(r,(function(e){De(e)||(He.isFragment(e)?n=n.concat(t(e.props.children)):n.push(e))})),jr=n,Or=r,n};function Pr(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map((function(t){return xr(t)})):[xr(e)],Sr(t).forEach((function(t){var e=Te(t,"type.displayName")||Te(t,"type.name");-1!==n.indexOf(e)&&r.push(t)})),r}function Ar(t,e){var r=Pr(t,e);return r&&r[0]}var Er=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!(!Qe(r)||r<=0||!Qe(n)||n<=0)},kr=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],Mr=function(t){return t&&"object"===gr(t)&&"clipDot"in t},Tr=function(t,r,n){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var o=t;if(e.isValidElement(t)&&(o=t.props),!Le(o))return null;var i={};return Object.keys(o).forEach((function(t){var e;(function(t,e,r,n){var o,i=null!==(o=null==pr?void 0:pr[n])&&void 0!==o?o:[];return e.startsWith("data-")||!Re(t)&&(n&&i.includes(e)||sr.includes(e))||r&&hr.includes(e)})(null===(e=o)||void 0===e?void 0:e[t],t,r,n)&&(i[t]=o[t])})),i},_r=function t(r,n){if(r===n)return!0;var o=e.Children.count(r);if(o!==e.Children.count(n))return!1;if(0===o)return!0;if(1===o)return Cr(Array.isArray(r)?r[0]:r,Array.isArray(n)?n[0]:n);for(var i=0;i<o;i++){var a=r[i],u=n[i];if(Array.isArray(a)||Array.isArray(u)){if(!t(a,u))return!1}else if(!Cr(a,u))return!1}return!0},Cr=function(t,e){if(De(t)&&De(e))return!0;if(!De(t)&&!De(e)){var r=t.props||{},n=r.children,o=br(r,vr),i=e.props||{},a=i.children,u=br(i,mr);return n&&a?cr(o,u)&&_r(n,a):!n&&!a&&cr(o,u)}return!1},Dr=function(t,e){var r=[],n={};return Sr(t).forEach((function(t,o){if(function(t){return t&&t.type&&Be(t.type)&&kr.indexOf(t.type)>=0}(t))r.push(t);else if(t){var i=xr(t.type),a=e[i]||{},u=a.handler,c=a.once;if(u&&(!c||!n[i])){var l=u(t,i,o);r.push(l),n[i]=!0}}})),r},Ir=["children","width","height","viewBox","className","style","title","desc"];function Nr(){return Nr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Nr.apply(this,arguments)}function Br(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Rr(e){var n=e.children,o=e.width,i=e.height,a=e.viewBox,u=e.className,c=e.style,l=e.title,s=e.desc,f=Br(e,Ir),p=a||{width:o,height:i,x:0,y:0},h=t("recharts-surface",u);return r.createElement("svg",Nr({},Tr(f,!0,"svg"),{className:h,width:o,height:i,style:c,viewBox:"".concat(p.x," ").concat(p.y," ").concat(p.width," ").concat(p.height)}),r.createElement("title",null,l),r.createElement("desc",null,s),n)}var Lr=["children","className"];function zr(){return zr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},zr.apply(this,arguments)}function Fr(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var Ur,$r,Wr,qr,Vr,Xr,Hr,Gr,Kr,Yr,Zr,Jr,Qr,tn,en,rn,nn=r.forwardRef((function(e,n){var o=e.children,i=e.className,a=Fr(e,Lr),u=t("recharts-layer",i);return r.createElement("g",zr({className:u},Tr(a,!0),{ref:n}),o)})),on=function(t,e){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]};function an(){if(qr)return Wr;qr=1;var t=$r?Ur:($r=1,Ur=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i});return Wr=function(e,r,n){var o=e.length;return n=void 0===n?o:n,!r&&n>=o?e:t(e,r,n)}}function un(){if(Xr)return Vr;Xr=1;var t=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");return Vr=function(e){return t.test(e)}}function cn(){if(Jr)return Zr;Jr=1;var t=Gr?Hr:(Gr=1,Hr=function(t){return t.split("")}),e=un(),r=function(){if(Yr)return Kr;Yr=1;var t="\\ud800-\\udfff",e="["+t+"]",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\\ud83c[\\udffb-\\udfff]",o="[^"+t+"]",i="(?:\\ud83c[\\udde6-\\uddff]){2}",a="[\\ud800-\\udbff][\\udc00-\\udfff]",u="(?:"+r+"|"+n+")?",c="[\\ufe0e\\ufe0f]?",l=c+u+"(?:\\u200d(?:"+[o,i,a].join("|")+")"+c+u+")*",s="(?:"+[o+r+"?",r,i,a,e].join("|")+")",f=RegExp(n+"(?="+n+")|"+s+l,"g");return Kr=function(t){return t.match(f)||[]}}();return Zr=function(n){return e(n)?r(n):t(n)}}const ln=o(function(){if(rn)return en;rn=1;var t=function(){if(tn)return Qr;tn=1;var t=an(),e=un(),r=cn(),n=Pe();return Qr=function(o){return function(i){i=n(i);var a=e(i)?r(i):void 0,u=a?a[0]:i.charAt(0),c=a?t(a,1).join(""):i.slice(1);return u[o]()+c}}}()("toUpperCase");return en=t}());function sn(t){return function(){return t}}const fn=Math.cos,pn=Math.sin,hn=Math.sqrt,yn=Math.PI,dn=2*yn,vn=Math.PI,mn=2*vn,bn=1e-6,gn=mn-bn;function wn(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class xn{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?wn:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return wn;const r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t=+t,e=+e,r=+r,n=+n,(o=+o)<0)throw new Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,u=r-t,c=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>bn)if(Math.abs(s*u-c*l)>bn&&o){let p=r-i,h=n-a,y=u*u+c*c,d=p*p+h*h,v=Math.sqrt(y),m=Math.sqrt(f),b=o*Math.tan((vn-Math.acos((y+f-d)/(2*v*m)))/2),g=b/m,w=b/v;Math.abs(g-1)>bn&&this._append`L${t+g*l},${e+g*s}`,this._append`A${o},${o},0,0,${+(s*p>l*h)},${this._x1=t+w*u},${this._y1=e+w*c}`}else this._append`L${this._x1=t},${this._y1=e}`;else;}arc(t,e,r,n,o,i){if(t=+t,e=+e,i=!!i,(r=+r)<0)throw new Error(`negative radius: ${r}`);let a=r*Math.cos(n),u=r*Math.sin(n),c=t+a,l=e+u,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${c},${l}`:(Math.abs(this._x1-c)>bn||Math.abs(this._y1-l)>bn)&&this._append`L${c},${l}`,r&&(f<0&&(f=f%mn+mn),f>gn?this._append`A${r},${r},0,1,${s},${t-a},${e-u}A${r},${r},0,1,${s},${this._x1=c},${this._y1=l}`:f>bn&&this._append`A${r},${r},0,${+(f>=vn)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function On(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{const t=Math.floor(r);if(!(t>=0))throw new RangeError(`invalid digits: ${r}`);e=t}return t},()=>new xn(e)}function jn(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function Sn(t){this._context=t}function Pn(t){return new Sn(t)}function An(t){return t[0]}function En(t){return t[1]}function kn(t,e){var r=sn(!0),n=null,o=Pn,i=null,a=On(u);function u(u){var c,l,s,f=(u=jn(u)).length,p=!1;for(null==n&&(i=o(s=a())),c=0;c<=f;++c)!(c<f&&r(l=u[c],c,u))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,c,u),+e(l,c,u));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?An:sn(t),e="function"==typeof e?e:void 0===e?En:sn(e),u.x=function(e){return arguments.length?(t="function"==typeof e?e:sn(+e),u):t},u.y=function(t){return arguments.length?(e="function"==typeof t?t:sn(+t),u):e},u.defined=function(t){return arguments.length?(r="function"==typeof t?t:sn(!!t),u):r},u.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),u):o},u.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),u):n},u}function Mn(t,e,r){var n=null,o=sn(!0),i=null,a=Pn,u=null,c=On(l);function l(l){var s,f,p,h,y,d=(l=jn(l)).length,v=!1,m=new Array(d),b=new Array(d);for(null==i&&(u=a(y=c())),s=0;s<=d;++s){if(!(s<d&&o(h=l[s],s,l))===v)if(v=!v)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=s-1;p>=f;--p)u.point(m[p],b[p]);u.lineEnd(),u.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),u.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(y)return u=null,y+""||null}function s(){return kn().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?An:sn(+t),e="function"==typeof e?e:sn(void 0===e?0:+e),r="function"==typeof r?r:void 0===r?En:sn(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:sn(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:sn(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:sn(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:sn(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:sn(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:sn(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:sn(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(u=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=u=null:u=a(i=t),l):i},l}Sn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}};class Tn{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}const _n={draw(t,e){const r=hn(e/yn);t.moveTo(r,0),t.arc(0,0,r,0,dn)}},Cn={draw(t,e){const r=hn(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},Dn=hn(1/3),In=2*Dn,Nn={draw(t,e){const r=hn(e/In),n=r*Dn;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},Bn={draw(t,e){const r=hn(e),n=-r/2;t.rect(n,n,r,r)}},Rn=pn(yn/10)/pn(7*yn/10),Ln=pn(dn/10)*Rn,zn=-fn(dn/10)*Rn,Fn={draw(t,e){const r=hn(.8908130915292852*e),n=Ln*r,o=zn*r;t.moveTo(0,-r),t.lineTo(n,o);for(let i=1;i<5;++i){const e=dn*i/5,a=fn(e),u=pn(e);t.lineTo(u*r,-a*r),t.lineTo(a*n-u*o,u*n+a*o)}t.closePath()}},Un=hn(3),$n={draw(t,e){const r=-hn(e/(3*Un));t.moveTo(0,2*r),t.lineTo(-Un*r,-r),t.lineTo(Un*r,-r),t.closePath()}},Wn=-.5,qn=hn(3)/2,Vn=1/hn(12),Xn=3*(Vn/2+1),Hn={draw(t,e){const r=hn(e/Xn),n=r/2,o=r*Vn,i=n,a=r*Vn+r,u=-i,c=a;t.moveTo(n,o),t.lineTo(i,a),t.lineTo(u,c),t.lineTo(Wn*n-qn*o,qn*n+Wn*o),t.lineTo(Wn*i-qn*a,qn*i+Wn*a),t.lineTo(Wn*u-qn*c,qn*u+Wn*c),t.lineTo(Wn*n+qn*o,Wn*o-qn*n),t.lineTo(Wn*i+qn*a,Wn*a-qn*i),t.lineTo(Wn*u+qn*c,Wn*c-qn*u),t.closePath()}};function Gn(){}function Kn(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function Yn(t){this._context=t}function Zn(t){this._context=t}function Jn(t){this._context=t}function Qn(t){this._context=t}function to(t){return t<0?-1:1}function eo(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0),u=(i*o+a*n)/(n+o);return(to(i)+to(a))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs(u))||0}function ro(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function no(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,u=(i-n)/3;t._context.bezierCurveTo(n+u,o+u*e,i-u,a-u*r,i,a)}function oo(t){this._context=t}function io(t){this._context=new ao(t)}function ao(t){this._context=t}function uo(t){this._context=t}function co(t){var e,r,n=t.length-1,o=new Array(n),i=new Array(n),a=new Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(i[n-1]=(t[n]+o[n-1])/2,e=0;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function lo(t,e){this._context=t,this._t=e}function so(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],u=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<u;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function fo(t){for(var e=t.length,r=new Array(e);--e>=0;)r[e]=e;return r}function po(t,e){return t[e]}function ho(t){const e=[];return e.key=t,e}function yo(t){return(yo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Yn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Kn(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Kn(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},Zn.prototype={areaStart:Gn,areaEnd:Gn,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:Kn(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},Jn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Kn(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},Qn.prototype={areaStart:Gn,areaEnd:Gn,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},oo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:no(this,this._t0,ro(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,no(this,ro(this,r=eo(this,t,e)),r);break;default:no(this,this._t0,r=eo(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(io.prototype=Object.create(oo.prototype)).point=function(t,e){oo.prototype.point.call(this,e,t)},ao.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},uo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=co(t),o=co(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},lo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var vo=["type","size","sizeType"];function mo(){return mo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},mo.apply(this,arguments)}function bo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function go(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?bo(Object(r),!0).forEach((function(e){wo(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):bo(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function wo(t,e,r){var n;return n=function(t,e){if("object"!=yo(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=yo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==yo(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function xo(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var Oo={symbolCircle:_n,symbolCross:Cn,symbolDiamond:Nn,symbolSquare:Bn,symbolStar:Fn,symbolTriangle:$n,symbolWye:Hn},jo=Math.PI/180,So=function(e){var n,o,i=e.type,a=void 0===i?"circle":i,u=e.size,c=void 0===u?64:u,l=e.sizeType,s=void 0===l?"area":l,f=go(go({},xo(e,vo)),{},{type:a,size:c,sizeType:s}),p=f.className,h=f.cx,y=f.cy,d=Tr(f,!0);return h===+h&&y===+y&&c===+c?r.createElement("path",mo({},d,{className:t("recharts-symbols",p),transform:"translate(".concat(h,", ").concat(y,")"),d:(n=function(t){var e="symbol".concat(ln(t));return Oo[e]||_n}(a),o=function(t,e){let r=null,n=On(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:sn(t||_n),e="function"==typeof e?e:sn(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:sn(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:sn(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o}().type(n).size(function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return.5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*jo;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}}(c,s,a)),o())})):null};function Po(t){return(Po="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ao(){return Ao=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ao.apply(this,arguments)}function Eo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ko(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Io(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Mo(t,e,r){return e=_o(e),function(t,e){if(e&&("object"===Po(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,To()?Reflect.construct(e,r||[],_o(t).constructor):e.apply(t,r))}function To(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(To=function(){return!!t})()}function _o(t){return(_o=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Co(t,e){return(Co=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Do(t,e,r){return(e=Io(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Io(t){var e=function(t,e){if("object"!=Po(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=Po(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==Po(e)?e:e+""}So.registerSymbol=function(t,e){Oo["symbol".concat(ln(t))]=e};var No,Bo,Ro,Lo,zo,Fo,Uo,$o,Wo,qo,Vo,Xo,Ho,Go,Ko,Yo,Zo,Jo,Qo,ti,ei,ri,ni,oi,ii,ai,ui,ci,li,si,fi,pi,hi,yi,di,vi,mi,bi,gi,wi,xi,Oi,ji,Si,Pi,Ai,Ei,ki,Mi=32,Ti=function(){function n(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),Mo(this,n,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Co(t,e)}(n,e.PureComponent),ko(n,[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,n=16,o=Mi/6,i=Mi/3,a=t.inactive?e:t.color;if("plainline"===t.type)return r.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:n,x2:Mi,y2:n,className:"recharts-legend-icon"});if("line"===t.type)return r.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(n,"h").concat(i,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(2*i,",").concat(n,"\n            H").concat(Mi,"M").concat(2*i,",").concat(n,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(i,",").concat(n),className:"recharts-legend-icon"});if("rect"===t.type)return r.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(Mi,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(r.isValidElement(t.legendIcon)){var u=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Eo(Object(r),!0).forEach((function(e){Do(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Eo(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},t);return delete u.legendIcon,r.cloneElement(t.legendIcon,u)}return r.createElement(So,{fill:a,cx:n,cy:n,size:Mi,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var e=this,n=this.props,o=n.payload,i=n.iconSize,a=n.layout,u=n.formatter,c=n.inactiveColor,l={x:0,y:0,width:Mi,height:Mi},s={display:"horizontal"===a?"inline-block":"block",marginRight:10},f={display:"inline-block",verticalAlign:"middle",marginRight:4};return o.map((function(n,o){var a=n.formatter||u,p=t(Do(Do({"recharts-legend-item":!0},"legend-item-".concat(o),!0),"inactive",n.inactive));if("none"===n.type)return null;var h=Re(n.value)?null:n.value;on(!Re(n.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var y=n.inactive?c:n.color;return r.createElement("li",Ao({className:p,style:s,key:"legend-item-".concat(o)},dr(e.props,n,o)),r.createElement(Rr,{width:i,height:i,viewBox:l,style:f},e.renderIcon(n)),r.createElement("span",{className:"recharts-legend-item-text",style:{color:y}},a?a(h,n,o):h))}))}},{key:"render",value:function(){var t=this.props,e=t.payload,n=t.layout,o=t.align;if(!e||!e.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===n?o:"left"};return r.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}])}();function _i(){if(Xo)return Vo;Xo=1;var t=be(),e=function(){if(Bo)return No;Bo=1;var t=be();return No=function(){this.__data__=new t,this.size=0}}(),r=Lo?Ro:(Lo=1,Ro=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}),n=Fo?zo:(Fo=1,zo=function(t){return this.__data__.get(t)}),o=$o?Uo:($o=1,Uo=function(t){return this.__data__.has(t)}),i=function(){if(qo)return Wo;qo=1;var t=be(),e=ge(),r=xe();return Wo=function(n,o){var i=this.__data__;if(i instanceof t){var a=i.__data__;if(!e||a.length<199)return a.push([n,o]),this.size=++i.size,this;i=this.__data__=new r(a)}return i.set(n,o),this.size=i.size,this}}();function a(e){var r=this.__data__=new t(e);this.size=r.size}return a.prototype.clear=e,a.prototype.delete=r,a.prototype.get=n,a.prototype.has=o,a.prototype.set=i,Vo=a}function Ci(){if(Jo)return Zo;Jo=1;var t=xe(),e=Go?Ho:(Go=1,Ho=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}),r=Yo?Ko:(Yo=1,Ko=function(t){return this.__data__.has(t)});function n(e){var r=-1,n=null==e?0:e.length;for(this.__data__=new t;++r<n;)this.add(e[r])}return n.prototype.add=n.prototype.push=e,n.prototype.has=r,Zo=n}function Di(){if(ti)return Qo;return ti=1,Qo=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}function Ii(){if(ri)return ei;return ri=1,ei=function(t,e){return t.has(e)}}function Ni(){if(oi)return ni;oi=1;var t=Ci(),e=Di(),r=Ii();return ni=function(n,o,i,a,u,c){var l=1&i,s=n.length,f=o.length;if(s!=f&&!(l&&f>s))return!1;var p=c.get(n),h=c.get(o);if(p&&h)return p==o&&h==n;var y=-1,d=!0,v=2&i?new t:void 0;for(c.set(n,o),c.set(o,n);++y<s;){var m=n[y],b=o[y];if(a)var g=l?a(b,m,y,o,n,c):a(m,b,y,n,o,c);if(void 0!==g){if(g)continue;d=!1;break}if(v){if(!e(o,(function(t,e){if(!r(v,e)&&(m===t||u(m,t,i,a,c)))return v.push(e)}))){d=!1;break}}else if(m!==b&&!u(m,b,i,a,c)){d=!1;break}}return c.delete(n),c.delete(o),d}}function Bi(){if(si)return li;return si=1,li=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r}}function Ri(){if(pi)return fi;pi=1;var t=oe(),e=function(){if(ai)return ii;ai=1;var t=ne().Uint8Array;return ii=t}(),r=ve(),n=Ni(),o=ci?ui:(ci=1,ui=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t,n){r[++e]=[n,t]})),r}),i=Bi(),a=t?t.prototype:void 0,u=a?a.valueOf:void 0;return fi=function(t,a,c,l,s,f,p){switch(c){case"[object DataView]":if(t.byteLength!=a.byteLength||t.byteOffset!=a.byteOffset)return!1;t=t.buffer,a=a.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=a.byteLength||!f(new e(t),new e(a)));case"[object Boolean]":case"[object Date]":case"[object Number]":return r(+t,+a);case"[object Error]":return t.name==a.name&&t.message==a.message;case"[object RegExp]":case"[object String]":return t==a+"";case"[object Map]":var h=o;case"[object Set]":var y=1&l;if(h||(h=i),t.size!=a.size&&!y)return!1;var d=p.get(t);if(d)return d==a;l|=2,p.set(t,a);var v=n(h(t),h(a),l,s,f,p);return p.delete(t),v;case"[object Symbol]":if(u)return u.call(t)==u.call(a)}return!1}}function Li(){if(yi)return hi;return yi=1,hi=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}}function zi(){if(Oi)return xi;Oi=1;var t=bi?mi:(bi=1,mi=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}),e=wi?gi:(wi=1,gi=function(){return[]}),r=Object.prototype.propertyIsEnumerable,n=Object.getOwnPropertySymbols;return xi=n?function(e){return null==e?[]:(e=Object(e),t(n(e),(function(t){return r.call(e,t)})))}:e}function Fi(){if(ki)return Ei;ki=1;var t=function(){if(Ai)return Pi;Ai=1;var t=ie(),e=ae();return Pi=function(r){return e(r)&&"[object Arguments]"==t(r)}}(),e=ae(),r=Object.prototype,n=r.hasOwnProperty,o=r.propertyIsEnumerable,i=t(function(){return arguments}())?t:function(t){return e(t)&&n.call(t,"callee")&&!o.call(t,"callee")};return Ei=i}Do(Ti,"displayName","Legend"),Do(Ti,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var Ui,$i,Wi,qi,Vi,Xi,Hi,Gi,Ki,Yi,Zi,Ji={exports:{}};function Qi(){return Wi||(Wi=1,t=Ji,e=Ji.exports,r=ne(),n=$i?Ui:($i=1,Ui=function(){return!1}),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o?r.Buffer:void 0,u=(a?a.isBuffer:void 0)||n,t.exports=u),Ji.exports;var t,e,r,n,o,i,a,u}function ta(){if(Vi)return qi;Vi=1;var t=/^(?:0|[1-9]\d*)$/;return qi=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}}function ea(){if(Hi)return Xi;Hi=1;return Xi=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}}function ra(){if(Zi)return Yi;return Zi=1,Yi=function(t){return function(e){return t(e)}}}var na,oa,ia,aa,ua,ca,la,sa,fa,pa,ha,ya,da,va,ma,ba,ga,wa,xa,Oa,ja,Sa,Pa,Aa,Ea,ka,Ma,Ta,_a,Ca,Da,Ia,Na,Ba,Ra,La,za,Fa,Ua,$a,Wa,qa,Va,Xa,Ha,Ga,Ka,Ya,Za,Ja,Qa,tu,eu,ru,nu,ou,iu,au,uu,cu,lu,su,fu,pu,hu,yu,du,vu,mu,bu,gu,wu,xu,Ou,ju,Su,Pu,Au,Eu,ku,Mu,Tu,_u,Cu={exports:{}};function Du(){if(ia)return oa;ia=1;var t=function(){if(Ki)return Gi;Ki=1;var t=ie(),e=ea(),r=ae(),n={};return n["[object Float32Array]"]=n["[object Float64Array]"]=n["[object Int8Array]"]=n["[object Int16Array]"]=n["[object Int32Array]"]=n["[object Uint8Array]"]=n["[object Uint8ClampedArray]"]=n["[object Uint16Array]"]=n["[object Uint32Array]"]=!0,n["[object Arguments]"]=n["[object Array]"]=n["[object ArrayBuffer]"]=n["[object Boolean]"]=n["[object DataView]"]=n["[object Date]"]=n["[object Error]"]=n["[object Function]"]=n["[object Map]"]=n["[object Number]"]=n["[object Object]"]=n["[object RegExp]"]=n["[object Set]"]=n["[object String]"]=n["[object WeakMap]"]=!1,Gi=function(o){return r(o)&&e(o.length)&&!!n[t(o)]}}(),e=ra(),r=function(){return na||(na=1,t=Cu,e=Cu.exports,r=re(),n=e&&!e.nodeType&&e,o=n&&t&&!t.nodeType&&t,i=o&&o.exports===n&&r.process,a=function(){try{return o&&o.require&&o.require("util").types||i&&i.binding&&i.binding("util")}catch(t){}}(),t.exports=a),Cu.exports;var t,e,r,n,o,i,a}(),n=r&&r.isTypedArray,o=n?e(n):t;return oa=o}function Iu(){if(ua)return aa;ua=1;var t=Si?ji:(Si=1,ji=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}),e=Fi(),r=ee(),n=Qi(),o=ta(),i=Du(),a=Object.prototype.hasOwnProperty;return aa=function(u,c){var l=r(u),s=!l&&e(u),f=!l&&!s&&n(u),p=!l&&!s&&!f&&i(u),h=l||s||f||p,y=h?t(u.length,String):[],d=y.length;for(var v in u)!c&&!a.call(u,v)||h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||o(v,d))||y.push(v);return y}}function Nu(){if(fa)return sa;return fa=1,sa=function(t,e){return function(r){return t(e(r))}}}function Bu(){if(da)return ya;da=1;var t=function(){if(la)return ca;la=1;var t=Object.prototype;return ca=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}}(),e=function(){if(ha)return pa;ha=1;var t=Nu()(Object.keys,Object);return pa=t}(),r=Object.prototype.hasOwnProperty;return ya=function(n){if(!t(n))return e(n);var o=[];for(var i in Object(n))r.call(n,i)&&"constructor"!=i&&o.push(i);return o}}function Ru(){if(ma)return va;ma=1;var t=se(),e=ea();return va=function(r){return null!=r&&e(r.length)&&!t(r)}}function Lu(){if(ga)return ba;ga=1;var t=Iu(),e=Bu(),r=Ru();return ba=function(n){return r(n)?t(n):e(n)}}function zu(){if(xa)return wa;xa=1;var t=function(){if(vi)return di;vi=1;var t=Li(),e=ee();return di=function(r,n,o){var i=n(r);return e(r)?i:t(i,o(r))}}(),e=zi(),r=Lu();return wa=function(n){return t(n,r,e)}}function Fu(){if(Ma)return ka;Ma=1;var t=he()(ne(),"Set");return ka=t}function Uu(){if(Da)return Ca;Da=1;var t=function(){if(Pa)return Sa;Pa=1;var t=he()(ne(),"DataView");return Sa=t}(),e=ge(),r=function(){if(Ea)return Aa;Ea=1;var t=he()(ne(),"Promise");return Aa=t}(),n=Fu(),o=function(){if(_a)return Ta;_a=1;var t=he()(ne(),"WeakMap");return Ta=t}(),i=ie(),a=pe(),u="[object Map]",c="[object Promise]",l="[object Set]",s="[object WeakMap]",f="[object DataView]",p=a(t),h=a(e),y=a(r),d=a(n),v=a(o),m=i;return(t&&m(new t(new ArrayBuffer(1)))!=f||e&&m(new e)!=u||r&&m(r.resolve())!=c||n&&m(new n)!=l||o&&m(new o)!=s)&&(m=function(t){var e=i(t),r="[object Object]"==e?t.constructor:void 0,n=r?a(r):"";if(n)switch(n){case p:return f;case h:return u;case y:return c;case d:return l;case v:return s}return e}),Ca=m}function $u(){if(Na)return Ia;Na=1;var t=_i(),e=Ni(),r=Ri(),n=function(){if(ja)return Oa;ja=1;var t=zu(),e=Object.prototype.hasOwnProperty;return Oa=function(r,n,o,i,a,u){var c=1&o,l=t(r),s=l.length;if(s!=t(n).length&&!c)return!1;for(var f=s;f--;){var p=l[f];if(!(c?p in n:e.call(n,p)))return!1}var h=u.get(r),y=u.get(n);if(h&&y)return h==n&&y==r;var d=!0;u.set(r,n),u.set(n,r);for(var v=c;++f<s;){var m=r[p=l[f]],b=n[p];if(i)var g=c?i(b,m,p,n,r,u):i(m,b,p,r,n,u);if(!(void 0===g?m===b||a(m,b,o,i,u):g)){d=!1;break}v||(v="constructor"==p)}if(d&&!v){var w=r.constructor,x=n.constructor;w==x||!("constructor"in r)||!("constructor"in n)||"function"==typeof w&&w instanceof w&&"function"==typeof x&&x instanceof x||(d=!1)}return u.delete(r),u.delete(n),d}}(),o=Uu(),i=ee(),a=Qi(),u=Du(),c="[object Arguments]",l="[object Array]",s="[object Object]",f=Object.prototype.hasOwnProperty;return Ia=function(p,h,y,d,v,m){var b=i(p),g=i(h),w=b?l:o(p),x=g?l:o(h),O=(w=w==c?s:w)==s,j=(x=x==c?s:x)==s,S=w==x;if(S&&a(p)){if(!a(h))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new t),b||u(p)?e(p,h,y,d,v,m):r(p,h,w,y,d,v,m);if(!(1&y)){var P=O&&f.call(p,"__wrapped__"),A=j&&f.call(h,"__wrapped__");if(P||A){var E=P?p.value():p,k=A?h.value():h;return m||(m=new t),v(E,k,y,d,m)}}return!!S&&(m||(m=new t),n(p,h,y,d,v,m))}}function Wu(){if(Ra)return Ba;Ra=1;var t=$u(),e=ae();return Ba=function r(n,o,i,a,u){return n===o||(null==n||null==o||!e(n)&&!e(o)?n!=n&&o!=o:t(n,o,i,a,r,u))},Ba}function qu(){if(Ua)return Fa;Ua=1;var t=le();return Fa=function(e){return e==e&&!t(e)}}function Vu(){if(Va)return qa;return Va=1,qa=function(t,e){return function(r){return null!=r&&(r[t]===e&&(void 0!==e||t in Object(r)))}}}function Xu(){if(Ha)return Xa;Ha=1;var t=function(){if(za)return La;za=1;var t=_i(),e=Wu();return La=function(r,n,o,i){var a=o.length,u=a,c=!i;if(null==r)return!u;for(r=Object(r);a--;){var l=o[a];if(c&&l[2]?l[1]!==r[l[0]]:!(l[0]in r))return!1}for(;++a<u;){var s=(l=o[a])[0],f=r[s],p=l[1];if(c&&l[2]){if(void 0===f&&!(s in r))return!1}else{var h=new t;if(i)var y=i(f,p,s,r,n,h);if(!(void 0===y?e(p,f,3,i,h):y))return!1}}return!0}}(),e=function(){if(Wa)return $a;Wa=1;var t=qu(),e=Lu();return $a=function(r){for(var n=e(r),o=n.length;o--;){var i=n[o],a=r[i];n[o]=[i,a,t(a)]}return n}}(),r=Vu();return Xa=function(n){var o=e(n);return 1==o.length&&o[0][2]?r(o[0][0],o[0][1]):function(e){return e===n||t(e,n,o)}}}function Hu(){if(Qa)return Ja;Qa=1;var t=Ka?Ga:(Ka=1,Ga=function(t,e){return null!=t&&e in Object(t)}),e=function(){if(Za)return Ya;Za=1;var t=Ae(),e=Fi(),r=ee(),n=ta(),o=ea(),i=Ee();return Ya=function(a,u,c){for(var l=-1,s=(u=t(u,a)).length,f=!1;++l<s;){var p=i(u[l]);if(!(f=null!=a&&c(a,p)))break;a=a[p]}return f||++l!=s?f:!!(s=null==a?0:a.length)&&o(s)&&n(p,s)&&(r(a)||e(a))}}();return Ja=function(r,n){return null!=r&&e(r,n,t)}}function Gu(){if(nu)return ru;return nu=1,ru=function(t){return t}}function Ku(){if(lu)return cu;lu=1;var t=iu?ou:(iu=1,ou=function(t){return function(e){return null==e?void 0:e[t]}}),e=function(){if(uu)return au;uu=1;var t=ke();return au=function(e){return function(r){return t(r,e)}}}(),r=ce(),n=Ee();return cu=function(o){return r(o)?t(n(o)):e(o)}}function Yu(){if(fu)return su;fu=1;var t=Xu(),e=function(){if(eu)return tu;eu=1;var t=Wu(),e=Me(),r=Hu(),n=ce(),o=qu(),i=Vu(),a=Ee();return tu=function(u,c){return n(u)&&o(c)?i(a(u),c):function(n){var o=e(n,u);return void 0===o&&o===c?r(n,u):t(c,o,3)}}}(),r=Gu(),n=ee(),o=Ku();return su=function(i){return"function"==typeof i?i:null==i?r:"object"==typeof i?n(i)?e(i[0],i[1]):t(i):o(i)}}function Zu(){if(hu)return pu;return hu=1,pu=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return-1}}function Ju(){if(gu)return bu;gu=1;var t=Zu(),e=du?yu:(du=1,yu=function(t){return t!=t}),r=mu?vu:(mu=1,vu=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return-1});return bu=function(n,o,i){return o==o?r(n,o,i):t(n,e,i)}}function Qu(){if(Eu)return Au;Eu=1;var t=Fu(),e=Pu?Su:(Pu=1,Su=function(){}),r=Bi(),n=t&&1/r(new t([,-0]))[1]==1/0?function(e){return new t(e)}:e;return Au=n}function tc(){if(Mu)return ku;Mu=1;var t=Ci(),e=function(){if(xu)return wu;xu=1;var t=Ju();return wu=function(e,r){return!(null==e||!e.length)&&t(e,r,0)>-1}}(),r=ju?Ou:(ju=1,Ou=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}),n=Ii(),o=Qu(),i=Bi();return ku=function(a,u,c){var l=-1,s=e,f=a.length,p=!0,h=[],y=h;if(c)p=!1,s=r;else if(f>=200){var d=u?null:o(a);if(d)return i(d);p=!1,s=n,y=new t}else y=u?[]:h;t:for(;++l<f;){var v=a[l],m=u?u(v):v;if(v=c||0!==v?v:0,p&&m==m){for(var b=y.length;b--;)if(y[b]===m)continue t;u&&y.push(m),h.push(v)}else s(y,m,c)||(y!==h&&y.push(m),h.push(v))}return h}}const ec=o(function(){if(_u)return Tu;_u=1;var t=Yu(),e=tc();return Tu=function(r,n){return r&&r.length?e(r,t(n,2)):[]}}());function rc(t,e,r){return!0===e?ec(t,r):Re(e)?ec(t,e):t}function nc(t){return(nc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var oc=["ref"];function ic(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ac(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ic(Object(r),!0).forEach((function(e){pc(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ic(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function uc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hc(n.key),n)}}function cc(t,e,r){return e=sc(e),function(t,e){if(e&&("object"===nc(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,lc()?Reflect.construct(e,r||[],sc(t).constructor):e.apply(t,r))}function lc(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(lc=function(){return!!t})()}function sc(t){return(sc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fc(t,e){return(fc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pc(t,e,r){return(e=hc(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hc(t){var e=function(t,e){if("object"!=nc(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=nc(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==nc(e)?e:e+""}function yc(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function dc(t){return t.value}var vc,mc,bc,gc,wc,xc,Oc,jc,Sc,Pc,Ac,Ec,kc,Mc,Tc,_c,Cc,Dc,Ic,Nc,Bc,Rc,Lc,zc,Fc,Uc,$c,Wc,qc,Vc,Xc,Hc,Gc,Kc,Yc,Zc,Jc,Qc,tl,el,rl,nl,ol,il,al=function(){function t(){var e;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return pc(e=cc(this,t,[].concat(n)),"lastBoundingBox",{width:-1,height:-1}),e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fc(t,e)}(t,e.PureComponent),n=t,i=[{key:"getWithHeight",value:function(t,e){var r=ac(ac({},this.defaultProps),t.props).layout;return"vertical"===r&&Qe(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],(o=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):-1===this.lastBoundingBox.width&&-1===this.lastBoundingBox.height||(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?ac({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,u=n.margin,c=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:u&&u.right||0}:{left:u&&u.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),ac(ac({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,n=e.content,o=e.width,i=e.height,a=e.wrapperStyle,u=e.payloadUniqBy,c=e.payload,l=ac(ac({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return r.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(r.isValidElement(t))return r.cloneElement(t,e);if("function"==typeof t)return r.createElement(t,e);e.ref;var n=yc(e,oc);return r.createElement(Ti,n)}(n,ac(ac({},this.props),{},{payload:rc(c,u,dc)})))}}])&&uc(n.prototype,o),i&&uc(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}();function ul(){if(gc)return bc;gc=1;var t=Li(),e=function(){if(mc)return vc;mc=1;var t=oe(),e=Fi(),r=ee(),n=t?t.isConcatSpreadable:void 0;return vc=function(t){return r(t)||e(t)||!!(n&&t&&t[n])}}();return bc=function r(n,o,i,a,u){var c=-1,l=n.length;for(i||(i=e),u||(u=[]);++c<l;){var s=n[c];o>0&&i(s)?o>1?r(s,o-1,i,a,u):t(u,s):a||(u[u.length]=s)}return u},bc}function cl(){if(jc)return Oc;jc=1;var t=(xc?wc:(xc=1,wc=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),u=a.length;u--;){var c=a[t?u:++o];if(!1===r(i[c],c,i))break}return e}}))();return Oc=t}function ll(){if(Pc)return Sc;Pc=1;var t=cl(),e=Lu();return Sc=function(r,n){return r&&t(r,n,e)}}function sl(){if(Mc)return kc;Mc=1;var t=ll(),e=function(){if(Ec)return Ac;Ec=1;var t=Ru();return Ac=function(e,r){return function(n,o){if(null==n)return n;if(!t(n))return e(n,o);for(var i=n.length,a=r?i:-1,u=Object(n);(r?a--:++a<i)&&!1!==o(u[a],a,u););return n}}}()(t);return kc=e}function fl(){if(_c)return Tc;_c=1;var t=sl(),e=Ru();return Tc=function(r,n){var o=-1,i=e(r)?Array(r.length):[];return t(r,(function(t,e,r){i[++o]=n(t,e,r)})),i}}function pl(){if(Rc)return Bc;Rc=1;var t=function(){if(Nc)return Ic;Nc=1;var t=ue();return Ic=function(e,r){if(e!==r){var n=void 0!==e,o=null===e,i=e==e,a=t(e),u=void 0!==r,c=null===r,l=r==r,s=t(r);if(!c&&!s&&!a&&e>r||a&&u&&l&&!c&&!s||o&&u&&l||!n&&l||!i)return 1;if(!o&&!a&&!s&&e<r||s&&n&&i&&!o&&!a||c&&n&&i||!u&&i||!l)return-1}return 0}}();return Bc=function(e,r,n){for(var o=-1,i=e.criteria,a=r.criteria,u=i.length,c=n.length;++o<u;){var l=t(i[o],a[o]);if(l)return o>=c?l:l*("desc"==n[o]?-1:1)}return e.index-r.index}}function hl(){if(zc)return Lc;zc=1;var t=Se(),e=ke(),r=Yu(),n=fl(),o=Dc?Cc:(Dc=1,Cc=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}),i=ra(),a=pl(),u=Gu(),c=ee();return Lc=function(l,s,f){s=s.length?t(s,(function(t){return c(t)?function(r){return e(r,1===t.length?t[0]:t)}:t})):[u];var p=-1;s=t(s,i(r));var h=n(l,(function(e,r,n){return{criteria:t(s,(function(t){return t(e)})),index:++p,value:e}}));return o(h,(function(t,e){return a(t,e,f)}))}}function yl(){if(Wc)return $c;Wc=1;var t=Uc?Fc:(Uc=1,Fc=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}),e=Math.max;return $c=function(r,n,o){return n=e(void 0===n?r.length-1:n,0),function(){for(var i=arguments,a=-1,u=e(i.length-n,0),c=Array(u);++a<u;)c[a]=i[n+a];a=-1;for(var l=Array(n+1);++a<n;)l[a]=i[a];return l[n]=o(c),t(r,this,l)}},$c}function dl(){if(Hc)return Xc;Hc=1;var t=he(),e=function(){try{var e=t(Object,"defineProperty");return e({},"",{}),e}catch(r){}}();return Xc=e}function vl(){if(Kc)return Gc;Kc=1;var t=Vc?qc:(Vc=1,qc=function(t){return function(){return t}}),e=dl(),r=Gu();return Gc=e?function(r,n){return e(r,"toString",{configurable:!0,enumerable:!1,value:t(n),writable:!0})}:r}function ml(){if(Qc)return Jc;Qc=1;var t=vl(),e=function(){if(Zc)return Yc;Zc=1;var t=Date.now;return Yc=function(e){var r=0,n=0;return function(){var o=t(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}},Yc}(),r=e(t);return Jc=r}function bl(){if(nl)return rl;nl=1;var t=ve(),e=Ru(),r=ta(),n=le();return rl=function(o,i,a){if(!n(a))return!1;var u=typeof i;return!!("number"==u?e(a)&&r(i,a.length):"string"==u&&i in a)&&t(a[i],o)}}pc(al,"displayName","Legend"),pc(al,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});const gl=o(function(){if(il)return ol;il=1;var t=ul(),e=hl(),r=function(){if(el)return tl;el=1;var t=Gu(),e=yl(),r=ml();return tl=function(n,o){return r(e(n,o,t),n+"")}}(),n=bl(),o=r((function(r,o){if(null==r)return[];var i=o.length;return i>1&&n(r,o[0],o[1])?o=[]:i>2&&n(o[0],o[1],o[2])&&(o=[o[0]]),e(r,t(o,1),[])}));return ol=o}());function wl(t){return(wl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function xl(){return xl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xl.apply(this,arguments)}function Ol(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return jl(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return jl(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function jl(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Sl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Pl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Sl(Object(r),!0).forEach((function(e){Al(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Sl(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Al(t,e,r){var n;return n=function(t,e){if("object"!=wl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=wl(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==wl(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function El(t){return Array.isArray(t)&&tr(t[0])&&tr(t[1])?t.join(" ~ "):t}var kl=function(e){var n=e.separator,o=void 0===n?" : ":n,i=e.contentStyle,a=void 0===i?{}:i,u=e.itemStyle,c=void 0===u?{}:u,l=e.labelStyle,s=void 0===l?{}:l,f=e.payload,p=e.formatter,h=e.itemSorter,y=e.wrapperClassName,d=e.labelClassName,v=e.label,m=e.labelFormatter,b=e.accessibilityLayer,g=void 0!==b&&b,w=Pl({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),x=Pl({margin:0},s),O=!De(v),j=O?v:"",S=t("recharts-default-tooltip",y),P=t("recharts-tooltip-label",d);O&&m&&null!=f&&(j=m(v,f));var A=g?{role:"status","aria-live":"assertive"}:{};return r.createElement("div",xl({className:S,style:w},A),r.createElement("p",{className:P,style:x},r.isValidElement(j)?j:"".concat(j)),function(){if(f&&f.length){var t=(h?gl(f,h):f).map((function(t,e){if("none"===t.type)return null;var n=Pl({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},c),i=t.formatter||p||El,a=t.value,u=t.name,l=a,s=u;if(i&&null!=l&&null!=s){var h=i(a,u,t,e,f);if(Array.isArray(h)){var y=Ol(h,2);l=y[0],s=y[1]}else l=h}return r.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},tr(s)?r.createElement("span",{className:"recharts-tooltip-item-name"},s):null,tr(s)?r.createElement("span",{className:"recharts-tooltip-item-separator"},o):null,r.createElement("span",{className:"recharts-tooltip-item-value"},l),r.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))}));return r.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function Ml(t){return(Ml="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Tl(t,e,r){var n;return n=function(t,e){if("object"!=Ml(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=Ml(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Ml(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var _l="recharts-tooltip-wrapper",Cl={visibility:"hidden"};function Dl(e){var r=e.coordinate,n=e.translateX,o=e.translateY;return t(_l,Tl(Tl(Tl(Tl({},"".concat(_l,"-right"),Qe(n)&&r&&Qe(r.x)&&n>=r.x),"".concat(_l,"-left"),Qe(n)&&r&&Qe(r.x)&&n<r.x),"".concat(_l,"-bottom"),Qe(o)&&r&&Qe(r.y)&&o>=r.y),"".concat(_l,"-top"),Qe(o)&&r&&Qe(r.y)&&o<r.y))}function Il(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,u=t.tooltipDimension,c=t.viewBox,l=t.viewBoxDimension;if(i&&Qe(i[n]))return i[n];var s=r[n]-u-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<c[n]?Math.max(f,c[n]):Math.max(s,c[n]):f+u>c[n]+l?Math.max(s,c[n]):Math.max(f,c[n])}function Nl(t){return(Nl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Bl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Rl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Bl(Object(r),!0).forEach((function(e){Wl(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Bl(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ll(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ql(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function zl(t,e,r){return e=Ul(e),function(t,e){if(e&&("object"===Nl(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Fl()?Reflect.construct(e,r||[],Ul(t).constructor):e.apply(t,r))}function Fl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Fl=function(){return!!t})()}function Ul(t){return(Ul=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function $l(t,e){return($l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Wl(t,e,r){return(e=ql(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ql(t){var e=function(t,e){if("object"!=Nl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=Nl(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==Nl(e)?e:e+""}var Vl=function(){function t(){var e;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return Wl(e=zl(this,t,[].concat(n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Wl(e,"handleKeyDown",(function(t){var r,n,o,i;"Escape"===t.key&&e.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=e.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=e.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})})),e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&$l(t,e)}(t,e.PureComponent),Ll(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else-1===this.state.lastBoundingBox.width&&-1===this.state.lastBoundingBox.height||this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)===this.state.dismissedAtCoordinate.x&&(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}},{key:"render",value:function(){var t=this,e=this.props,n=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,u=e.children,c=e.coordinate,l=e.hasPayload,s=e.isAnimationActive,f=e.offset,p=e.position,h=e.reverseDirection,y=e.useTranslate3d,d=e.viewBox,v=e.wrapperStyle,m=function(t){var e,r,n=t.allowEscapeViewBox,o=t.coordinate,i=t.offsetTopLeft,a=t.position,u=t.reverseDirection,c=t.tooltipBox,l=t.useTranslate3d,s=t.viewBox;return{cssProperties:c.height>0&&c.width>0&&o?function(t){var e=t.translateX,r=t.translateY;return{transform:t.useTranslate3d?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}({translateX:e=Il({allowEscapeViewBox:n,coordinate:o,key:"x",offsetTopLeft:i,position:a,reverseDirection:u,tooltipDimension:c.width,viewBox:s,viewBoxDimension:s.width}),translateY:r=Il({allowEscapeViewBox:n,coordinate:o,key:"y",offsetTopLeft:i,position:a,reverseDirection:u,tooltipDimension:c.height,viewBox:s,viewBoxDimension:s.height}),useTranslate3d:l}):Cl,cssClasses:Dl({translateX:e,translateY:r,coordinate:o})}}({allowEscapeViewBox:o,coordinate:c,offsetTopLeft:f,position:p,reverseDirection:h,tooltipBox:this.state.lastBoundingBox,useTranslate3d:y,viewBox:d}),b=m.cssClasses,g=m.cssProperties,w=Rl(Rl({transition:s&&n?"transform ".concat(i,"ms ").concat(a):void 0},g),{},{pointerEvents:"none",visibility:!this.state.dismissed&&n&&l?"visible":"hidden",position:"absolute",top:0,left:0},v);return r.createElement("div",{tabIndex:-1,className:b,style:w,ref:function(e){t.wrapperNode=e}},u)}}])}(),Xl=!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout);function Hl(t){return(Hl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Gl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Kl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Gl(Object(r),!0).forEach((function(e){es(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Gl(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Yl(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,rs(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Zl(t,e,r){return e=Ql(e),function(t,e){if(e&&("object"===Hl(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Jl()?Reflect.construct(e,r||[],Ql(t).constructor):e.apply(t,r))}function Jl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Jl=function(){return!!t})()}function Ql(t){return(Ql=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ts(t,e){return(ts=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function es(t,e,r){return(e=rs(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rs(t){var e=function(t,e){if("object"!=Hl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=Hl(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==Hl(e)?e:e+""}function ns(t){return t.dataKey}var os,is,as,us,cs,ls,ss,fs,ps,hs,ys,ds,vs=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Zl(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ts(t,e)}(t,e.PureComponent),Yl(t,[{key:"render",value:function(){var t=this,e=this.props,n=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,u=e.content,c=e.coordinate,l=e.filterNull,s=e.isAnimationActive,f=e.offset,p=e.payload,h=e.payloadUniqBy,y=e.position,d=e.reverseDirection,v=e.useTranslate3d,m=e.viewBox,b=e.wrapperStyle,g=null!=p?p:[];l&&g.length&&(g=rc(p.filter((function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)})),h,ns));var w=g.length>0;return r.createElement(Vl,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:s,active:n,coordinate:c,hasPayload:w,offset:f,position:y,reverseDirection:d,useTranslate3d:v,viewBox:m,wrapperStyle:b},function(t,e){return r.isValidElement(t)?r.cloneElement(t,e):"function"==typeof t?r.createElement(t,e):r.createElement(kl,e)}(u,Kl(Kl({},this.props),{},{payload:g})))}}])}();function ms(){if(ls)return cs;ls=1;var t=function(){if(us)return as;us=1;var t=/\s/;return as=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}}(),e=/^\s+/;return cs=function(r){return r?r.slice(0,t(r)+1).replace(e,""):r}}function bs(){if(fs)return ss;fs=1;var t=ms(),e=le(),r=ue(),n=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,i=/^0o[0-7]+$/i,a=parseInt;return ss=function(u){if("number"==typeof u)return u;if(r(u))return NaN;if(e(u)){var c="function"==typeof u.valueOf?u.valueOf():u;u=e(c)?c+"":c}if("string"!=typeof u)return 0===u?u:+u;u=t(u);var l=o.test(u);return l||i.test(u)?a(u.slice(2),l?2:8):n.test(u)?NaN:+u}}function gs(){if(hs)return ps;hs=1;var t=le(),e=function(){if(is)return os;is=1;var t=ne();return os=function(){return t.Date.now()}}(),r=bs(),n=Math.max,o=Math.min;return ps=function(i,a,u){var c,l,s,f,p,h,y=0,d=!1,v=!1,m=!0;if("function"!=typeof i)throw new TypeError("Expected a function");function b(t){var e=c,r=l;return c=l=void 0,y=t,f=i.apply(r,e)}function g(t){var e=t-h;return void 0===h||e>=a||e<0||v&&t-y>=s}function w(){var t=e();if(g(t))return x(t);p=setTimeout(w,function(t){var e=a-(t-h);return v?o(e,s-(t-y)):e}(t))}function x(t){return p=void 0,m&&c?b(t):(c=l=void 0,f)}function O(){var t=e(),r=g(t);if(c=arguments,l=this,h=t,r){if(void 0===p)return function(t){return y=t,p=setTimeout(w,a),d?b(t):f}(h);if(v)return clearTimeout(p),p=setTimeout(w,a),b(h)}return void 0===p&&(p=setTimeout(w,a)),f}return a=r(a)||0,t(u)&&(d=!!u.leading,s=(v="maxWait"in u)?n(r(u.maxWait)||0,a):s,m="trailing"in u?!!u.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),y=0,c=h=l=p=void 0},O.flush=function(){return void 0===p?f:x(e())},O},ps}es(vs,"displayName","Tooltip"),es(vs,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!Xl,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});const ws=o(function(){if(ds)return ys;ds=1;var t=gs(),e=le();return ys=function(r,n,o){var i=!0,a=!0;if("function"!=typeof r)throw new TypeError("Expected a function");return e(o)&&(i="leading"in o?!!o.leading:i,a="trailing"in o?!!o.trailing:a),t(r,n,{leading:i,maxWait:n,trailing:a})}}());function xs(t){return(xs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Os(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function js(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Os(Object(r),!0).forEach((function(e){Ss(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Os(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ss(t,e,r){var n;return n=function(t,e){if("object"!=xs(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=xs(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==xs(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ps(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return As(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return As(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function As(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Es=e.forwardRef((function(n,o){var i=n.aspect,a=n.initialDimension,u=void 0===a?{width:-1,height:-1}:a,c=n.width,l=void 0===c?"100%":c,s=n.height,f=void 0===s?"100%":s,p=n.minWidth,h=void 0===p?0:p,y=n.minHeight,d=n.maxHeight,v=n.children,m=n.debounce,b=void 0===m?0:m,g=n.id,w=n.className,x=n.onResize,O=n.style,j=void 0===O?{}:O,S=e.useRef(null),P=e.useRef();P.current=x,e.useImperativeHandle(o,(function(){return Object.defineProperty(S.current,"current",{get:function(){return S.current},configurable:!0})}));var A=Ps(e.useState({containerWidth:u.width,containerHeight:u.height}),2),E=A[0],k=A[1],M=e.useCallback((function(t,e){k((function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}}))}),[]);e.useEffect((function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;M(n,o),null===(e=P.current)||void 0===e||e.call(P,n,o)};b>0&&(t=ws(t,b,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=S.current.getBoundingClientRect(),n=r.width,o=r.height;return M(n,o),e.observe(S.current),function(){e.disconnect()}}),[M,b]);var T=e.useMemo((function(){var t=E.containerWidth,n=E.containerHeight;if(t<0||n<0)return null;on(Je(l)||Je(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",l,f),on(!i||i>0,"The aspect(%s) must be greater than zero.",i);var o=Je(l)?t:l,a=Je(f)?n:f;i&&i>0&&(o?a=o/i:a&&(o=a*i),d&&a>d&&(a=d)),on(o>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",o,a,l,f,h,y,i);var u=!Array.isArray(v)&&xr(v.type).endsWith("Chart");return r.Children.map(v,(function(t){return r.isValidElement(t)?e.cloneElement(t,js({width:o,height:a},u?{style:js({height:"100%",width:"100%",maxHeight:a,maxWidth:o},t.props.style)}:{})):t}))}),[i,v,f,d,y,h,E,l]);return r.createElement("div",{id:g?"".concat(g):void 0,className:t("recharts-responsive-container",w),style:js(js({},j),{},{width:l,height:f,minWidth:h,minHeight:y,maxHeight:d}),ref:S},T)})),ks=function(t){return null};function Ms(t){return(Ms="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ts(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function _s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ts(Object(r),!0).forEach((function(e){Cs(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ts(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Cs(t,e,r){var n;return n=function(t,e){if("object"!=Ms(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=Ms(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Ms(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}ks.displayName="Cell";var Ds={widthCache:{},cacheCount:0},Is={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Ns="recharts_measurement_span";var Bs=function(t){if(null==t||Xl)return{width:0,height:0};var e,r=(e=_s({},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),Object.keys(e).forEach((function(t){e[t]||delete e[t]})),e),n=JSON.stringify({text:t,copyStyle:r});if(Ds.widthCache[n])return Ds.widthCache[n];try{var o=document.getElementById(Ns);o||((o=document.createElement("span")).setAttribute("id",Ns),o.setAttribute("aria-hidden","true"),document.body.appendChild(o));var i=_s(_s({},Is),r);Object.assign(o.style,i),o.textContent="".concat(t);var a=o.getBoundingClientRect(),u={width:a.width,height:a.height};return Ds.widthCache[n]=u,++Ds.cacheCount>2e3&&(Ds.cacheCount=0,Ds.widthCache={}),u}catch(c){return{width:0,height:0}}};function Rs(t){return(Rs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ls(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return zs(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zs(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zs(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Fs(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Us(n.key),n)}}function Us(t){var e=function(t,e){if("object"!=Rs(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=Rs(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==Rs(e)?e:e+""}var $s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Ws=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,qs=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,Vs=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Xs={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},Hs=Object.keys(Xs),Gs="NaN";var Ks=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.num=e,this.unit=r,this.num=e,this.unit=r,Number.isNaN(e)&&(this.unit=""),""===r||qs.test(r)||(this.num=NaN,this.unit=""),Hs.includes(r)&&(this.num=function(t,e){return t*Xs[e]}(e,r),this.unit="px")}return e=t,n=[{key:"parse",value:function(e){var r,n=Ls(null!==(r=Vs.exec(e))&&void 0!==r?r:[],3),o=n[1],i=n[2];return new t(parseFloat(o),null!=i?i:"")}}],(r=[{key:"add",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}])&&Fs(e.prototype,r),n&&Fs(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();function Ys(t){if(t.includes(Gs))return Gs;for(var e=t;e.includes("*")||e.includes("/");){var r,n=Ls(null!==(r=$s.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],u=Ks.parse(null!=o?o:""),c=Ks.parse(null!=a?a:""),l="*"===i?u.multiply(c):u.divide(c);if(l.isNaN())return Gs;e=e.replace($s,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=Ls(null!==(s=Ws.exec(e))&&void 0!==s?s:[],4),p=f[1],h=f[2],y=f[3],d=Ks.parse(null!=p?p:""),v=Ks.parse(null!=y?y:""),m="+"===h?d.add(v):d.subtract(v);if(m.isNaN())return Gs;e=e.replace(Ws,m.toString())}return e}var Zs=/\(([^()]*)\)/;function Js(t){var e=t.replace(/\s+/g,"");return e=function(t){for(var e=t;e.includes("(");){var r=Ls(Zs.exec(e),2)[1];e=e.replace(Zs,Ys(r))}return e}(e),e=Ys(e)}function Qs(t){var e=function(t){try{return Js(t)}catch(e){return Gs}}(t.slice(5,-1));return e===Gs?"":e}var tf=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],ef=["dx","dy","angle","className","breakAll"];function rf(){return rf=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},rf.apply(this,arguments)}function nf(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function of(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return af(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return af(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function af(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var uf=/[ \f\n\r\t\v\u2028\u2029]+/,cf=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];return De(e)||(o=r?e.toString().split(""):e.toString().split(uf)),{wordsWithComputedWidth:o.map((function(t){return{word:t,width:Bs(t,n).width}})),spaceWidth:r?0:Bs(" ",n).width}}catch(i){return null}},lf=function(t){return[{words:De(t)?[]:t.toString().split(uf)}]},sf=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!Xl){var u=cf({breakAll:i,children:n,style:o});return u?function(t,e,r,n,o){var i=t.maxLines,a=t.children,u=t.style,c=t.breakAll,l=Qe(i),s=a,f=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce((function(t,e){var i=e.word,a=e.width,u=t[t.length-1];if(u&&(null==n||o||u.width+a+r<Number(n)))u.words.push(i),u.width+=a+r;else{var c={words:[i],width:a};t.push(c)}return t}),[])},p=f(e);if(!l)return p;for(var h,y=function(t){var e=s.slice(0,t),r=cf({breakAll:c,style:u,children:e+"…"}).wordsWithComputedWidth,o=f(r),a=o.length>i||function(t){return t.reduce((function(t,e){return t.width>e.width?t:e}))}(o).width>Number(n);return[a,o]},d=0,v=s.length-1,m=0;d<=v&&m<=s.length-1;){var b=Math.floor((d+v)/2),g=of(y(b-1),2),w=g[0],x=g[1],O=of(y(b),1)[0];if(w||O||(d=b+1),w&&O&&(v=b-1),!w&&O){h=x;break}m++}return h||p}({breakAll:i,children:n,maxLines:a,style:o},u.wordsWithComputedWidth,u.spaceWidth,e,r):lf(n)}return lf(n)},ff="#808080",pf=function(n){var o=n.x,i=void 0===o?0:o,a=n.y,u=void 0===a?0:a,c=n.lineHeight,l=void 0===c?"1em":c,s=n.capHeight,f=void 0===s?"0.71em":s,p=n.scaleToFit,h=void 0!==p&&p,y=n.textAnchor,d=void 0===y?"start":y,v=n.verticalAnchor,m=void 0===v?"end":v,b=n.fill,g=void 0===b?ff:b,w=nf(n,tf),x=e.useMemo((function(){return sf({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:h,style:w.style,width:w.width})}),[w.breakAll,w.children,w.maxLines,h,w.style,w.width]),O=w.dx,j=w.dy,S=w.angle,P=w.className,A=w.breakAll,E=nf(w,ef);if(!tr(i)||!tr(u))return null;var k,M=i+(Qe(O)?O:0),T=u+(Qe(j)?j:0);switch(m){case"start":k=Qs("calc(".concat(f,")"));break;case"middle":k=Qs("calc(".concat((x.length-1)/2," * -").concat(l," + (").concat(f," / 2))"));break;default:k=Qs("calc(".concat(x.length-1," * -").concat(l,")"))}var _=[];if(h){var C=x[0].width,D=w.width;_.push("scale(".concat((Qe(D)?D/C:1)/C,")"))}return S&&_.push("rotate(".concat(S,", ").concat(M,", ").concat(T,")")),_.length&&(E.transform=_.join(" ")),r.createElement("text",rf({},Tr(E,!0),{x:M,y:T,className:t("recharts-text",P),textAnchor:d,fill:g.includes("url")?ff:g}),x.map((function(t,e){var n=t.words.join(A?"":" ");return r.createElement("tspan",{x:M,dy:0===e?k:l,key:"".concat(n,"-").concat(e)},n)})))};function hf(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function yf(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function df(t){let e,r,n;function o(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<0?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=hf,r=(e,r)=>hf(t(e),r),n=(e,r)=>t(e)-r):(e=t===hf||t===yf?t:vf,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){const a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<=0?o=e+1:i=e}while(o<i)}return o}}}function vf(){return 0}function mf(t){return null===t?NaN:+t}const bf=df(hf).right;df(mf).center;class gf extends Map{constructor(t,e=xf){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[r,n]of t)this.set(r,n)}get(t){return super.get(wf(this,t))}has(t){return super.has(wf(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){const n=e(r);t.has(n)&&(r=t.get(n),t.delete(n));return r}(this,t))}}function wf({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):r}function xf(t){return null!==t&&"object"==typeof t?t.valueOf():t}function Of(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}const jf=Math.sqrt(50),Sf=Math.sqrt(10),Pf=Math.sqrt(2);function Af(t,e,r){const n=(e-t)/Math.max(0,r),o=Math.floor(Math.log10(n)),i=n/Math.pow(10,o),a=i>=jf?10:i>=Sf?5:i>=Pf?2:1;let u,c,l;return o<0?(l=Math.pow(10,-o)/a,u=Math.round(t*l),c=Math.round(e*l),u/l<t&&++u,c/l>e&&--c,l=-l):(l=Math.pow(10,o)*a,u=Math.round(t/l),c=Math.round(e/l),u*l<t&&++u,c*l>e&&--c),c<u&&.5<=r&&r<2?Af(t,e,2*r):[u,c,l]}function Ef(t,e,r){if(!((r=+r)>0))return[];if((t=+t)===(e=+e))return[t];const n=e<t,[o,i,a]=n?Af(e,t,r):Af(t,e,r);if(!(i>=o))return[];const u=i-o+1,c=new Array(u);if(n)if(a<0)for(let l=0;l<u;++l)c[l]=(i-l)/-a;else for(let l=0;l<u;++l)c[l]=(i-l)*a;else if(a<0)for(let l=0;l<u;++l)c[l]=(o+l)/-a;else for(let l=0;l<u;++l)c[l]=(o+l)*a;return c}function kf(t,e,r){return Af(t=+t,e=+e,r=+r)[2]}function Mf(t,e,r){r=+r;const n=(e=+e)<(t=+t),o=n?kf(e,t,r):kf(t,e,r);return(n?-1:1)*(o<0?1/-o:o)}function Tf(t,e){let r;for(const n of t)null!=n&&(r<n||void 0===r&&n>=n)&&(r=n);return r}function _f(t,e){let r;for(const n of t)null!=n&&(r>n||void 0===r&&n>=n)&&(r=n);return r}function Cf(t,e,r=0,n=1/0,o){if(e=Math.floor(e),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(t.length-1,n)),!(r<=e&&e<=n))return t;for(o=void 0===o?Of:function(t=hf){if(t===hf)return Of;if("function"!=typeof t)throw new TypeError("compare is not a function");return(e,r)=>{const n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);n>r;){if(n-r>600){const i=n-r+1,a=e-r+1,u=Math.log(i),c=.5*Math.exp(2*u/3),l=.5*Math.sqrt(u*c*(i-c)/i)*(a-i/2<0?-1:1);Cf(t,e,Math.max(r,Math.floor(e-a*c/i+l)),Math.min(n,Math.floor(e+(i-a)*c/i+l)),o)}const i=t[e];let a=r,u=n;for(Df(t,r,e),o(t[n],i)>0&&Df(t,r,n);a<u;){for(Df(t,a,u),++a,--u;o(t[a],i)<0;)++a;for(;o(t[u],i)>0;)--u}0===o(t[r],i)?Df(t,r,u):(++u,Df(t,u,n)),u<=e&&(r=u+1),e<=u&&(n=u-1)}return t}function Df(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function If(t,e,r=mf){if((n=t.length)&&!isNaN(e=+e)){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}function Nf(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function Bf(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}const Rf=Symbol("implicit");function Lf(){var t=new gf,e=[],r=[],n=Rf;function o(o){let i=t.get(o);if(void 0===i){if(n!==Rf)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();e=[],t=new gf;for(const n of r)t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return Lf(e,r).unknown(n)},Nf.apply(o,arguments),o}function zf(){var t,e,r=Lf().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,u=!1,c=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,r-c+2*l),u&&(t=Math.floor(t)),p+=(h-p-t*(r-c))*s,e=t*(1-c),u&&(p=Math.round(p),e=Math.round(e));var y=function(t,e,r){t=+t,e=+e,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=new Array(o);++n<o;)i[n]=t+n*r;return i}(r).map((function(e){return p+t*e}));return o(f?y.reverse():y)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i=+i,a=+a,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i=+i,a=+a,u=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(u=!!t,f()):u},r.padding=function(t){return arguments.length?(c=Math.min(1,l=+t),f()):c},r.paddingInner=function(t){return arguments.length?(c=Math.min(1,t),f()):c},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return zf(n(),[i,a]).round(u).paddingInner(c).paddingOuter(l).align(s)},Nf.apply(f(),arguments)}function Ff(t){var e=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return Ff(e())},t}function Uf(){return Ff(zf.apply(null,arguments).paddingInner(1))}function $f(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function Wf(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function qf(){}var Vf=.7,Xf=1/Vf,Hf="\\s*([+-]?\\d+)\\s*",Gf="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Kf="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Yf=/^#([0-9a-f]{3,8})$/,Zf=new RegExp(`^rgb\\(${Hf},${Hf},${Hf}\\)$`),Jf=new RegExp(`^rgb\\(${Kf},${Kf},${Kf}\\)$`),Qf=new RegExp(`^rgba\\(${Hf},${Hf},${Hf},${Gf}\\)$`),tp=new RegExp(`^rgba\\(${Kf},${Kf},${Kf},${Gf}\\)$`),ep=new RegExp(`^hsl\\(${Gf},${Kf},${Kf}\\)$`),rp=new RegExp(`^hsla\\(${Gf},${Kf},${Kf},${Gf}\\)$`),np={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function op(){return this.rgb().formatHex()}function ip(){return this.rgb().formatRgb()}function ap(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=Yf.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?up(e):3===r?new sp(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?cp(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?cp(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=Zf.exec(t))?new sp(e[1],e[2],e[3],1):(e=Jf.exec(t))?new sp(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=Qf.exec(t))?cp(e[1],e[2],e[3],e[4]):(e=tp.exec(t))?cp(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=ep.exec(t))?vp(e[1],e[2]/100,e[3]/100,1):(e=rp.exec(t))?vp(e[1],e[2]/100,e[3]/100,e[4]):np.hasOwnProperty(t)?up(np[t]):"transparent"===t?new sp(NaN,NaN,NaN,0):null}function up(t){return new sp(t>>16&255,t>>8&255,255&t,1)}function cp(t,e,r,n){return n<=0&&(t=e=r=NaN),new sp(t,e,r,n)}function lp(t,e,r,n){return 1===arguments.length?((o=t)instanceof qf||(o=ap(o)),o?new sp((o=o.rgb()).r,o.g,o.b,o.opacity):new sp):new sp(t,e,r,null==n?1:n);var o}function sp(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function fp(){return`#${dp(this.r)}${dp(this.g)}${dp(this.b)}`}function pp(){const t=hp(this.opacity);return`${1===t?"rgb(":"rgba("}${yp(this.r)}, ${yp(this.g)}, ${yp(this.b)}${1===t?")":`, ${t})`}`}function hp(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function yp(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function dp(t){return((t=yp(t))<16?"0":"")+t.toString(16)}function vp(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new bp(t,e,r,n)}function mp(t){if(t instanceof bp)return new bp(t.h,t.s,t.l,t.opacity);if(t instanceof qf||(t=ap(t)),!t)return new bp;if(t instanceof bp)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,u=i-o,c=(i+o)/2;return u?(a=e===i?(r-n)/u+6*(r<n):r===i?(n-e)/u+2:(e-r)/u+4,u/=c<.5?i+o:2-i-o,a*=60):u=c>0&&c<1?0:a,new bp(a,u,c,t.opacity)}function bp(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function gp(t){return(t=(t||0)%360)<0?t+360:t}function wp(t){return Math.max(0,Math.min(1,t||0))}function xp(t,e,r){return 255*(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)}$f(qf,ap,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:op,formatHex:op,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return mp(this).formatHsl()},formatRgb:ip,toString:ip}),$f(sp,lp,Wf(qf,{brighter(t){return t=null==t?Xf:Math.pow(Xf,t),new sp(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?Vf:Math.pow(Vf,t),new sp(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new sp(yp(this.r),yp(this.g),yp(this.b),hp(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:fp,formatHex:fp,formatHex8:function(){return`#${dp(this.r)}${dp(this.g)}${dp(this.b)}${dp(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:pp,toString:pp})),$f(bp,(function(t,e,r,n){return 1===arguments.length?mp(t):new bp(t,e,r,null==n?1:n)}),Wf(qf,{brighter(t){return t=null==t?Xf:Math.pow(Xf,t),new bp(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?Vf:Math.pow(Vf,t),new bp(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new sp(xp(t>=240?t-240:t+120,o,n),xp(t,o,n),xp(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new bp(gp(this.h),wp(this.s),wp(this.l),hp(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=hp(this.opacity);return`${1===t?"hsl(":"hsla("}${gp(this.h)}, ${100*wp(this.s)}%, ${100*wp(this.l)}%${1===t?")":`, ${t})`}`}}));const Op=t=>()=>t;function jp(t){return 1===(t=+t)?Sp:function(e,r){return r-e?function(t,e,r){return t=Math.pow(t,r),e=Math.pow(e,r)-t,r=1/r,function(n){return Math.pow(t+n*e,r)}}(e,r,t):Op(isNaN(e)?r:e)}}function Sp(t,e){var r=e-t;return r?function(t,e){return function(r){return t+r*e}}(t,r):Op(isNaN(t)?e:t)}const Pp=function t(e){var r=jp(e);function n(t,e){var n=r((t=lp(t)).r,(e=lp(e)).r),o=r(t.g,e.g),i=r(t.b,e.b),a=Sp(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return n.gamma=t,n}(1);function Ap(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}}function Ep(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=new Array(o),a=new Array(n);for(r=0;r<o;++r)i[r]=Ip(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}function kp(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}function Mp(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}function Tp(t,e){var r,n={},o={};for(r in null!==t&&"object"==typeof t||(t={}),null!==e&&"object"==typeof e||(e={}),e)r in t?n[r]=Ip(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}var _p=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Cp=new RegExp(_p.source,"g");function Dp(t,e){var r,n,o,i=_p.lastIndex=Cp.lastIndex=0,a=-1,u=[],c=[];for(t+="",e+="";(r=_p.exec(t))&&(n=Cp.exec(e));)(o=n.index)>i&&(o=e.slice(i,o),u[a]?u[a]+=o:u[++a]=o),(r=r[0])===(n=n[0])?u[a]?u[a]+=n:u[++a]=n:(u[++a]=null,c.push({i:a,x:Mp(r,n)})),i=Cp.lastIndex;return i<e.length&&(o=e.slice(i),u[a]?u[a]+=o:u[++a]=o),u.length<2?c[0]?function(t){return function(e){return t(e)+""}}(c[0].x):function(t){return function(){return t}}(e):(e=c.length,function(t){for(var r,n=0;n<e;++n)u[(r=c[n]).i]=r.x(t);return u.join("")})}function Ip(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?Op(e):("number"===o?Mp:"string"===o?(r=ap(e))?(e=r,Pp):Dp:e instanceof ap?Pp:e instanceof Date?kp:(n=e,!ArrayBuffer.isView(n)||n instanceof DataView?Array.isArray(e)?Ep:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?Tp:Mp:Ap))(t,e)}function Np(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function Bp(t){return+t}var Rp=[0,1];function Lp(t){return t}function zp(t,e){return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r});var r}function Fp(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=zp(o,n),i=r(a,i)):(n=zp(n,o),i=r(i,a)),function(t){return i(n(t))}}function Up(t,e,r){var n=Math.min(t.length,e.length)-1,o=new Array(n),i=new Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=zp(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=bf(t,e,1,n)-1;return i[r](o[r](e))}}function $p(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function Wp(){var t,e,r,n,o,i,a=Rp,u=Rp,c=Ip,l=Lp;function s(){var t,e,r,c=Math.min(a.length,u.length);return l!==Lp&&(t=a[0],e=a[c-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=c>2?Up:Fp,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?r:(o||(o=n(a.map(t),u,c)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(u,a.map(t),Mp)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,Bp),s()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),s()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),c=Np,s()},f.clamp=function(t){return arguments.length?(l=!!t||Lp,s()):l!==Lp},f.interpolate=function(t){return arguments.length?(c=t,s()):c},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function qp(){return Wp()(Lp,Lp)}function Vp(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function Xp(t){return(t=Vp(Math.abs(t)))?t[1]:NaN}var Hp,Gp=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Kp(t){if(!(e=Gp.exec(t)))throw new Error("invalid format: "+t);var e;return new Yp({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function Yp(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Zp(t,e){var r=Vp(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+new Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+new Array(o-n.length+2).join("0")}Kp.prototype=Yp.prototype,Yp.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const Jp={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>Zp(100*t,e),r:Zp,s:function(t,e){var r=Vp(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(Hp=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+new Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+new Array(1-i).join("0")+Vp(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Qp(t){return t}var th,eh,rh,nh=Array.prototype.map,oh=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function ih(t){var e,r,n=void 0===t.grouping||void 0===t.thousands?Qp:(e=nh.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,u=e[0],c=0;o>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),i.push(t.substring(o-=u,o+u)),!((c+=u+1)>n));)u=e[a=(a+1)%e.length];return i.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",i=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?Qp:function(t){return function(e){return e.replace(/[0-9]/g,(function(e){return t[+e]}))}}(nh.call(t.numerals,String)),c=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"−":t.minus+"",s=void 0===t.nan?"NaN":t.nan+"";function f(t){var e=(t=Kp(t)).fill,r=t.align,f=t.sign,p=t.symbol,h=t.zero,y=t.width,d=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(d=!0,b="g"):Jp[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===p?i:/[%p]/.test(b)?c:"",x=Jp[b],O=/[defgprs%]/.test(b);function j(t){var o,i,c,p=g,j=w;if("c"===b)j=x(t)+j,t="";else{var S=(t=+t)<0||1/t<0;if(t=isNaN(t)?s:x(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0===+t&&"+"!==f&&(S=!1),p=(S?"("===f?f:l:"-"===f||"("===f?"":f)+p,j=("s"===b?oh[8+Hp/3]:"")+j+(S&&"("===f?")":""),O)for(o=-1,i=t.length;++o<i;)if(48>(c=t.charCodeAt(o))||c>57){j=(46===c?a+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}d&&!h&&(t=n(t,1/0));var P=p.length+t.length+j.length,A=P<y?new Array(y-P+1).join(e):"";switch(d&&h&&(t=n(A+t,A.length?y-j.length:1/0),A=""),r){case"<":t=p+t+j+A;break;case"=":t=p+A+t+j;break;case"^":t=A.slice(0,P=A.length>>1)+p+t+j+A.slice(P);break;default:t=A+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:f,formatPrefix:function(t,e){var r=f(((t=Kp(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(Xp(e)/3))),o=Math.pow(10,-n),i=oh[8+n/3];return function(t){return r(o*t)+i}}}}function ah(t,e,r,n){var o,i=Mf(t,e,r);switch((n=Kp(null==n?",f":n)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(o=function(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Xp(e)/3)))-Xp(Math.abs(t)))}(i,a))||(n.precision=o),rh(n,a);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(o=function(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,Xp(e)-Xp(t))+1}(i,Math.max(Math.abs(t),Math.abs(e))))||(n.precision=o-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(o=function(t){return Math.max(0,-Xp(Math.abs(t)))}(i))||(n.precision=o-2*("%"===n.type))}return eh(n)}function uh(t){var e=t.domain;return t.ticks=function(t){var r=e();return Ef(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return ah(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,u=i.length-1,c=i[a],l=i[u],s=10;for(l<c&&(o=c,c=l,l=o,o=a,a=u,u=o);s-- >0;){if((o=kf(c,l,r))===n)return i[a]=c,i[u]=l,e(i);if(o>0)c=Math.floor(c/o)*o,l=Math.ceil(l/o)*o;else{if(!(o<0))break;c=Math.ceil(c*o)/o,l=Math.floor(l*o)/o}n=o}return t},t}function ch(){var t=qp();return t.copy=function(){return $p(t,ch())},Nf.apply(t,arguments),uh(t)}function lh(t,e){var r,n=0,o=(t=t.slice()).length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function sh(t){return Math.log(t)}function fh(t){return Math.exp(t)}function ph(t){return-Math.log(-t)}function hh(t){return-Math.exp(-t)}function yh(t){return isFinite(t)?+("1e"+t):t<0?0:t}function dh(t){return(e,r)=>-t(-e,r)}function vh(t){const e=t(sh,fh),r=e.domain;let n,o,i=10;function a(){return n=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),e=>Math.log(e)/t)}(i),o=function(t){return 10===t?yh:t===Math.E?Math.exp:e=>Math.pow(t,e)}(i),r()[0]<0?(n=dh(n),o=dh(o),t(ph,hh)):t(sh,fh),e}return e.base=function(t){return arguments.length?(i=+t,a()):i},e.domain=function(t){return arguments.length?(r(t),a()):r()},e.ticks=t=>{const e=r();let a=e[0],u=e[e.length-1];const c=u<a;c&&([a,u]=[u,a]);let l,s,f=n(a),p=n(u);const h=null==t?10:+t;let y=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),a>0){for(;f<=p;++f)for(l=1;l<i;++l)if(s=f<0?l/o(-f):l*o(f),!(s<a)){if(s>u)break;y.push(s)}}else for(;f<=p;++f)for(l=i-1;l>=1;--l)if(s=f>0?l/o(-f):l*o(f),!(s<a)){if(s>u)break;y.push(s)}2*y.length<h&&(y=Ef(a,u,h))}else y=Ef(f,p,Math.min(p-f,h)).map(o);return c?y.reverse():y},e.tickFormat=(t,r)=>{if(null==t&&(t=10),null==r&&(r=10===i?"s":","),"function"!=typeof r&&(i%1||null!=(r=Kp(r)).precision||(r.trim=!0),r=eh(r)),t===1/0)return r;const a=Math.max(1,i*t/e.ticks().length);return t=>{let e=t/o(Math.round(n(t)));return e*i<i-.5&&(e*=i),e<=a?r(t):""}},e.nice=()=>r(lh(r(),{floor:t=>o(Math.floor(n(t))),ceil:t=>o(Math.ceil(n(t)))})),e}function mh(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function bh(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function gh(t){var e=1,r=t(mh(e),bh(e));return r.constant=function(r){return arguments.length?t(mh(e=+r),bh(e)):e},uh(r)}function wh(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function xh(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function Oh(t){return t<0?-t*t:t*t}function jh(t){var e=t(Lp,Lp),r=1;return e.exponent=function(e){return arguments.length?1===(r=+e)?t(Lp,Lp):.5===r?t(xh,Oh):t(wh(r),wh(1/r)):r},uh(e)}function Sh(){var t=jh(Wp());return t.copy=function(){return $p(t,Sh()).exponent(t.exponent())},Nf.apply(t,arguments),t}function Ph(t){return Math.sign(t)*t*t}th=ih({thousands:",",grouping:[3],currency:["$",""]}),eh=th.format,rh=th.formatPrefix;const Ah=new Date,Eh=new Date;function kh(t,e,r,n){function o(e){return t(e=0===arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{const e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{const a=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n&&i>0))return a;let u;do{a.push(u=new Date(+r)),e(r,i),t(r)}while(u<r&&r<n);return a},o.filter=r=>kh((e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)}),((t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););})),r&&(o.count=(e,n)=>(Ah.setTime(+e),Eh.setTime(+n),t(Ah),t(Eh),Math.floor(r(Ah,Eh))),o.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?o.filter(n?e=>n(e)%t===0:e=>o.count(0,e)%t===0):o:null)),o}const Mh=kh((()=>{}),((t,e)=>{t.setTime(+t+e)}),((t,e)=>e-t));Mh.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?kh((e=>{e.setTime(Math.floor(e/t)*t)}),((e,r)=>{e.setTime(+e+r*t)}),((e,r)=>(r-e)/t)):Mh:null),Mh.range;const Th=1e3,_h=6e4,Ch=36e5,Dh=864e5,Ih=6048e5,Nh=2592e6,Bh=31536e6,Rh=kh((t=>{t.setTime(t-t.getMilliseconds())}),((t,e)=>{t.setTime(+t+e*Th)}),((t,e)=>(e-t)/Th),(t=>t.getUTCSeconds()));Rh.range;const Lh=kh((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Th)}),((t,e)=>{t.setTime(+t+e*_h)}),((t,e)=>(e-t)/_h),(t=>t.getMinutes()));Lh.range;const zh=kh((t=>{t.setUTCSeconds(0,0)}),((t,e)=>{t.setTime(+t+e*_h)}),((t,e)=>(e-t)/_h),(t=>t.getUTCMinutes()));zh.range;const Fh=kh((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Th-t.getMinutes()*_h)}),((t,e)=>{t.setTime(+t+e*Ch)}),((t,e)=>(e-t)/Ch),(t=>t.getHours()));Fh.range;const Uh=kh((t=>{t.setUTCMinutes(0,0,0)}),((t,e)=>{t.setTime(+t+e*Ch)}),((t,e)=>(e-t)/Ch),(t=>t.getUTCHours()));Uh.range;const $h=kh((t=>t.setHours(0,0,0,0)),((t,e)=>t.setDate(t.getDate()+e)),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*_h)/Dh),(t=>t.getDate()-1));$h.range;const Wh=kh((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/Dh),(t=>t.getUTCDate()-1));Wh.range;const qh=kh((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/Dh),(t=>Math.floor(t/Dh)));function Vh(t){return kh((e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)}),((t,e)=>{t.setDate(t.getDate()+7*e)}),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*_h)/Ih))}qh.range;const Xh=Vh(0),Hh=Vh(1),Gh=Vh(2),Kh=Vh(3),Yh=Vh(4),Zh=Vh(5),Jh=Vh(6);function Qh(t){return kh((e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)}),((t,e)=>(e-t)/Ih))}Xh.range,Hh.range,Gh.range,Kh.range,Yh.range,Zh.range,Jh.range;const ty=Qh(0),ey=Qh(1),ry=Qh(2),ny=Qh(3),oy=Qh(4),iy=Qh(5),ay=Qh(6);ty.range,ey.range,ry.range,ny.range,oy.range,iy.range,ay.range;const uy=kh((t=>{t.setDate(1),t.setHours(0,0,0,0)}),((t,e)=>{t.setMonth(t.getMonth()+e)}),((t,e)=>e.getMonth()-t.getMonth()+12*(e.getFullYear()-t.getFullYear())),(t=>t.getMonth()));uy.range;const cy=kh((t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)}),((t,e)=>e.getUTCMonth()-t.getUTCMonth()+12*(e.getUTCFullYear()-t.getUTCFullYear())),(t=>t.getUTCMonth()));cy.range;const ly=kh((t=>{t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,e)=>{t.setFullYear(t.getFullYear()+e)}),((t,e)=>e.getFullYear()-t.getFullYear()),(t=>t.getFullYear()));ly.every=t=>isFinite(t=Math.floor(t))&&t>0?kh((e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)}),((e,r)=>{e.setFullYear(e.getFullYear()+r*t)})):null,ly.range;const sy=kh((t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)}),((t,e)=>e.getUTCFullYear()-t.getUTCFullYear()),(t=>t.getUTCFullYear()));function fy(t,e,r,n,o,i){const a=[[Rh,1,Th],[Rh,5,5e3],[Rh,15,15e3],[Rh,30,3e4],[i,1,_h],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,Ch],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,Dh],[n,2,1728e5],[r,1,Ih],[e,1,Nh],[e,3,7776e6],[t,1,Bh]];function u(e,r,n){const o=Math.abs(r-e)/n,i=df((([,,t])=>t)).right(a,o);if(i===a.length)return t.every(Mf(e/Bh,r/Bh,n));if(0===i)return Mh.every(Math.max(Mf(e,r,n),1));const[u,c]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return u.every(c)}return[function(t,e,r){const n=e<t;n&&([t,e]=[e,t]);const o=r&&"function"==typeof r.range?r:u(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},u]}sy.every=t=>isFinite(t=Math.floor(t))&&t>0?kh((e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)}),((e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)})):null,sy.range;const[py,hy]=fy(sy,cy,ty,qh,Uh,zh),[yy,dy]=fy(ly,uy,Xh,$h,Fh,Lh);function vy(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function my(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function by(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var gy,wy,xy,Oy={"-":"",_:" ",0:"0"},jy=/^\s*\d+/,Sy=/^%/,Py=/[\\^$*+?|[\]().{}]/g;function Ay(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?new Array(r-i+1).join(e)+o:o)}function Ey(t){return t.replace(Py,"\\$&")}function ky(t){return new RegExp("^(?:"+t.map(Ey).join("|")+")","i")}function My(t){return new Map(t.map(((t,e)=>[t.toLowerCase(),e])))}function Ty(t,e,r){var n=jy.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function _y(t,e,r){var n=jy.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function Cy(t,e,r){var n=jy.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function Dy(t,e,r){var n=jy.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function Iy(t,e,r){var n=jy.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function Ny(t,e,r){var n=jy.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function By(t,e,r){var n=jy.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function Ry(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function Ly(t,e,r){var n=jy.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function zy(t,e,r){var n=jy.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function Fy(t,e,r){var n=jy.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function Uy(t,e,r){var n=jy.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function $y(t,e,r){var n=jy.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function Wy(t,e,r){var n=jy.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function qy(t,e,r){var n=jy.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function Vy(t,e,r){var n=jy.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function Xy(t,e,r){var n=jy.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function Hy(t,e,r){var n=Sy.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function Gy(t,e,r){var n=jy.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function Ky(t,e,r){var n=jy.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function Yy(t,e){return Ay(t.getDate(),e,2)}function Zy(t,e){return Ay(t.getHours(),e,2)}function Jy(t,e){return Ay(t.getHours()%12||12,e,2)}function Qy(t,e){return Ay(1+$h.count(ly(t),t),e,3)}function td(t,e){return Ay(t.getMilliseconds(),e,3)}function ed(t,e){return td(t,e)+"000"}function rd(t,e){return Ay(t.getMonth()+1,e,2)}function nd(t,e){return Ay(t.getMinutes(),e,2)}function od(t,e){return Ay(t.getSeconds(),e,2)}function id(t){var e=t.getDay();return 0===e?7:e}function ad(t,e){return Ay(Xh.count(ly(t)-1,t),e,2)}function ud(t){var e=t.getDay();return e>=4||0===e?Yh(t):Yh.ceil(t)}function cd(t,e){return t=ud(t),Ay(Yh.count(ly(t),t)+(4===ly(t).getDay()),e,2)}function ld(t){return t.getDay()}function sd(t,e){return Ay(Hh.count(ly(t)-1,t),e,2)}function fd(t,e){return Ay(t.getFullYear()%100,e,2)}function pd(t,e){return Ay((t=ud(t)).getFullYear()%100,e,2)}function hd(t,e){return Ay(t.getFullYear()%1e4,e,4)}function yd(t,e){var r=t.getDay();return Ay((t=r>=4||0===r?Yh(t):Yh.ceil(t)).getFullYear()%1e4,e,4)}function dd(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+Ay(e/60|0,"0",2)+Ay(e%60,"0",2)}function vd(t,e){return Ay(t.getUTCDate(),e,2)}function md(t,e){return Ay(t.getUTCHours(),e,2)}function bd(t,e){return Ay(t.getUTCHours()%12||12,e,2)}function gd(t,e){return Ay(1+Wh.count(sy(t),t),e,3)}function wd(t,e){return Ay(t.getUTCMilliseconds(),e,3)}function xd(t,e){return wd(t,e)+"000"}function Od(t,e){return Ay(t.getUTCMonth()+1,e,2)}function jd(t,e){return Ay(t.getUTCMinutes(),e,2)}function Sd(t,e){return Ay(t.getUTCSeconds(),e,2)}function Pd(t){var e=t.getUTCDay();return 0===e?7:e}function Ad(t,e){return Ay(ty.count(sy(t)-1,t),e,2)}function Ed(t){var e=t.getUTCDay();return e>=4||0===e?oy(t):oy.ceil(t)}function kd(t,e){return t=Ed(t),Ay(oy.count(sy(t),t)+(4===sy(t).getUTCDay()),e,2)}function Md(t){return t.getUTCDay()}function Td(t,e){return Ay(ey.count(sy(t)-1,t),e,2)}function _d(t,e){return Ay(t.getUTCFullYear()%100,e,2)}function Cd(t,e){return Ay((t=Ed(t)).getUTCFullYear()%100,e,2)}function Dd(t,e){return Ay(t.getUTCFullYear()%1e4,e,4)}function Id(t,e){var r=t.getUTCDay();return Ay((t=r>=4||0===r?oy(t):oy.ceil(t)).getUTCFullYear()%1e4,e,4)}function Nd(){return"+0000"}function Bd(){return"%"}function Rd(t){return+t}function Ld(t){return Math.floor(+t/1e3)}function zd(t){return new Date(t)}function Fd(t){return t instanceof Date?+t:+new Date(+t)}function Ud(t,e,r,n,o,i,a,u,c,l){var s=qp(),f=s.invert,p=s.domain,h=l(".%L"),y=l(":%S"),d=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),w=l("%Y");function x(t){return(c(t)<t?h:u(t)<t?y:a(t)<t?d:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:w)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,Fd)):p().map(zd)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?x:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(lh(r,t)):s},s.copy=function(){return $p(s,Ud(t,e,r,n,o,i,a,u,c,l))},s}function $d(){var t,e,r,n,o,i=0,a=1,u=Lp,c=!1;function l(e){return null==e||isNaN(e=+e)?o:u(0===r?.5:(e=(n(e)-t)*r,c?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,u=t(r,n),l):[u(0),u(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i=+i),e=n(a=+a),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(c=!!t,l):c},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=s(Ip),l.rangeRound=s(Np),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function Wd(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function qd(){var t=jh($d());return t.copy=function(){return Wd(t,qd()).exponent(t.exponent())},Bf.apply(t,arguments)}function Vd(){var t,e,r,n,o,i,a,u=0,c=.5,l=1,s=1,f=Lp,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function y(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=Ip);for(var r=0,n=e.length-1,o=e[0],i=new Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([u,c,l]=a,t=i(u=+u),e=i(c=+c),r=i(l=+l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[u,c,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=y(Ip),h.rangeRound=y(Np),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(u),e=a(c),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function Xd(){var t=jh(Vd());return t.copy=function(){return Wd(t,Xd()).exponent(t.exponent())},Bf.apply(t,arguments)}!function(t){gy=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=ky(o),s=My(o),f=ky(i),p=My(i),h=ky(a),y=My(a),d=ky(u),v=My(u),m=ky(c),b=My(c),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:Yy,e:Yy,f:ed,g:pd,G:yd,H:Zy,I:Jy,j:Qy,L:td,m:rd,M:nd,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:Rd,s:Ld,S:od,u:id,U:ad,V:cd,w:ld,W:sd,x:null,X:null,y:fd,Y:hd,Z:dd,"%":Bd},w={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:vd,e:vd,f:xd,g:Cd,G:Id,H:md,I:bd,j:gd,L:wd,m:Od,M:jd,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:Rd,s:Ld,S:Sd,u:Pd,U:Ad,V:kd,w:Md,W:Td,x:null,X:null,y:_d,Y:Dd,Z:Nd,"%":Bd},x={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=y.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=d.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:Fy,e:Fy,f:Xy,g:By,G:Ny,H:$y,I:$y,j:Uy,L:Vy,m:zy,M:Wy,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:Ly,Q:Gy,s:Ky,S:qy,u:_y,U:Cy,V:Dy,w:Ty,W:Iy,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:By,Y:Ny,Z:Ry,"%":Hy};function O(t,e){return function(r){var n,o,i,a=[],u=-1,c=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(o=Oy[n=t.charAt(++u)])?n=t.charAt(++u):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function j(t,e){return function(r){var n,o,i=by(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(e&&!("Z"in i)&&(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(o=(n=my(by(i.y,0,1))).getUTCDay(),n=o>4||0===o?ey.ceil(n):ey(n),n=Wh.offset(n,7*(i.V-1)),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(o=(n=vy(by(i.y,0,1))).getDay(),n=o>4||0===o?Hh.ceil(n):Hh(n),n=$h.offset(n,7*(i.V-1)),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?my(by(i.y,0,1)).getUTCDay():vy(by(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,my(i)):vy(i)}}function S(t,e,r,n){for(var o,i,a=0,u=e.length,c=r.length;a<u;){if(n>=c)return-1;if(37===(o=e.charCodeAt(a++))){if(o=e.charAt(a++),!(i=x[o in Oy?e.charAt(a++):o])||(n=i(t,r,n))<0)return-1}else if(o!=r.charCodeAt(n++))return-1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),w.x=O(r,w),w.X=O(n,w),w.c=O(e,w),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",w);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}(t),wy=gy.format,gy.parse,xy=gy.utcFormat,gy.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});const Hd=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:zf,scaleDiverging:function t(){var e=uh(Vd()(Lp));return e.copy=function(){return Wd(e,t())},Bf.apply(e,arguments)},scaleDivergingLog:function t(){var e=vh(Vd()).domain([.1,1,10]);return e.copy=function(){return Wd(e,t()).base(e.base())},Bf.apply(e,arguments)},scaleDivergingPow:Xd,scaleDivergingSqrt:function(){return Xd.apply(null,arguments).exponent(.5)},scaleDivergingSymlog:function t(){var e=gh(Vd());return e.copy=function(){return Wd(e,t()).constant(e.constant())},Bf.apply(e,arguments)},scaleIdentity:function t(e){var r;function n(t){return null==t||isNaN(t=+t)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,Bp),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,Bp):[0,1],uh(n)},scaleImplicit:Rf,scaleLinear:ch,scaleLog:function t(){const e=vh(Wp()).domain([1,10]);return e.copy=()=>$p(e,t()).base(e.base()),Nf.apply(e,arguments),e},scaleOrdinal:Lf,scalePoint:Uf,scalePow:Sh,scaleQuantile:function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=new Array(e-1);++t<e;)o[t-1]=If(r,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:n[bf(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();r=[];for(let e of t)null==e||isNaN(e=+e)||r.push(e);return r.sort(hf),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},Nf.apply(a,arguments)},scaleQuantize:function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function u(t){return null!=t&&t<=t?a[bf(i,t,0,o)]:e}function c(){var t=-1;for(i=new Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return u}return u.domain=function(t){return arguments.length?([r,n]=t,r=+r,n=+n,c()):[r,n]},u.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,c()):a.slice()},u.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},u.unknown=function(t){return arguments.length?(e=t,u):u},u.thresholds=function(){return i.slice()},u.copy=function(){return t().domain([r,n]).range(a).unknown(e)},Nf.apply(uh(u),arguments)},scaleRadial:function t(){var e,r=qp(),n=[0,1],o=!1;function i(t){var n=function(t){return Math.sign(t)*Math.sqrt(Math.abs(t))}(r(t));return isNaN(n)?e:o?Math.round(n):n}return i.invert=function(t){return r.invert(Ph(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,Bp)).map(Ph)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},Nf.apply(i,arguments),uh(i)},scaleSequential:function t(){var e=uh($d()(Lp));return e.copy=function(){return Wd(e,t())},Bf.apply(e,arguments)},scaleSequentialLog:function t(){var e=vh($d()).domain([1,10]);return e.copy=function(){return Wd(e,t()).base(e.base())},Bf.apply(e,arguments)},scaleSequentialPow:qd,scaleSequentialQuantile:function t(){var e=[],r=Lp;function n(t){if(null!=t&&!isNaN(t=+t))return r((bf(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();e=[];for(let r of t)null==r||isNaN(r=+r)||e.push(r);return e.sort(hf),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map(((t,n)=>r(n/(e.length-1))))},n.quantiles=function(t){return Array.from({length:t+1},((r,n)=>function(t,e){if((r=(t=Float64Array.from(function*(t){for(let e of t)null!=e&&(e=+e)>=e&&(yield e)}(t))).length)&&!isNaN(e=+e)){if(e<=0||r<2)return _f(t);if(e>=1)return Tf(t);var r,n=(r-1)*e,o=Math.floor(n),i=Tf(Cf(t,o).subarray(0,o+1));return i+(_f(t.subarray(o+1))-i)*(n-o)}}(e,n/t)))},n.copy=function(){return t(r).domain(e)},Bf.apply(n,arguments)},scaleSequentialSqrt:function(){return qd.apply(null,arguments).exponent(.5)},scaleSequentialSymlog:function t(){var e=gh($d());return e.copy=function(){return Wd(e,t()).constant(e.constant())},Bf.apply(e,arguments)},scaleSqrt:function(){return Sh.apply(null,arguments).exponent(.5)},scaleSymlog:function t(){var e=gh(Wp());return e.copy=function(){return $p(e,t()).constant(e.constant())},Nf.apply(e,arguments)},scaleThreshold:function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[bf(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(r=Array.from(t),o=Math.min(r.length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},Nf.apply(i,arguments)},scaleTime:function(){return Nf.apply(Ud(yy,dy,ly,uy,Xh,$h,Fh,Lh,Rh,wy).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)},scaleUtc:function(){return Nf.apply(Ud(py,hy,sy,cy,ty,Wh,Uh,zh,Rh,xy).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)},tickFormat:ah},Symbol.toStringTag,{value:"Module"}));var Gd,Kd,Yd,Zd,Jd,Qd;function tv(){if(Kd)return Gd;Kd=1;var t=ue();return Gd=function(e,r,n){for(var o=-1,i=e.length;++o<i;){var a=e[o],u=r(a);if(null!=u&&(void 0===c?u==u&&!t(u):n(u,c)))var c=u,l=a}return l}}function ev(){if(Zd)return Yd;return Zd=1,Yd=function(t,e){return t>e}}const rv=o(function(){if(Qd)return Jd;Qd=1;var t=tv(),e=ev(),r=Gu();return Jd=function(n){return n&&n.length?t(n,r,e):void 0}}());var nv,ov,iv,av;function uv(){if(ov)return nv;return ov=1,nv=function(t,e){return t<e}}const cv=o(function(){if(av)return iv;av=1;var t=tv(),e=uv(),r=Gu();return iv=function(n){return n&&n.length?t(n,r,e):void 0}}());var lv,sv,fv,pv;const hv=o(function(){if(pv)return fv;pv=1;var t=ul(),e=function(){if(sv)return lv;sv=1;var t=Se(),e=Yu(),r=fl(),n=ee();return lv=function(o,i){return(n(o)?t:r)(o,e(i,3))}}();return fv=function(r,n){return t(e(r,n),1)}}());var yv,dv;const vv=o(function(){if(dv)return yv;dv=1;var t=Wu();return yv=function(e,r){return t(e,r)}}());var mv,bv=1e9,gv=!0,wv="[DecimalError] ",xv=wv+"Invalid argument: ",Ov=wv+"Exponent out of range: ",jv=Math.floor,Sv=Math.pow,Pv=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Av=1e7,Ev=9007199254740991,kv=jv(1286742750677284.5),Mv={};function Tv(t,e){var r,n,o,i,a,u,c,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),gv?Fv(e,f):e;if(c=t.d,l=e.d,a=t.e,o=e.e,c=c.slice(),i=a-o){for(i<0?(n=c,i=-i,u=l.length):(n=l,o=a,u=c.length),i>(u=(a=Math.ceil(f/7))>u?a+1:u+1)&&(i=u,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((u=c.length)-(i=l.length)<0&&(i=u,n=l,l=c,c=n),r=0;i;)r=(c[--i]=c[i]+l[i]+r)/Av|0,c[i]%=Av;for(r&&(c.unshift(r),++o),u=c.length;0==c[--u];)c.pop();return e.d=c,e.e=o,gv?Fv(e,f):e}function _v(t,e,r){if(t!==~~t||t<e||t>r)throw Error(xv+t)}function Cv(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=Rv(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=Rv(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}Mv.absoluteValue=Mv.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},Mv.comparedTo=Mv.cmp=function(t){var e,r,n,o,i=this;if(t=new i.constructor(t),i.s!==t.s)return i.s||-t.s;if(i.e!==t.e)return i.e>t.e^i.s<0?1:-1;for(e=0,r=(n=i.d.length)<(o=t.d.length)?n:o;e<r;++e)if(i.d[e]!==t.d[e])return i.d[e]>t.d[e]^i.s<0?1:-1;return n===o?0:n>o^i.s<0?1:-1},Mv.decimalPlaces=Mv.dp=function(){var t=this,e=t.d.length-1,r=7*(e-t.e);if(e=t.d[e])for(;e%10==0;e/=10)r--;return r<0?0:r},Mv.dividedBy=Mv.div=function(t){return Dv(this,new this.constructor(t))},Mv.dividedToIntegerBy=Mv.idiv=function(t){var e=this.constructor;return Fv(Dv(this,new e(t),0,1),e.precision)},Mv.equals=Mv.eq=function(t){return!this.cmp(t)},Mv.exponent=function(){return Nv(this)},Mv.greaterThan=Mv.gt=function(t){return this.cmp(t)>0},Mv.greaterThanOrEqualTo=Mv.gte=function(t){return this.cmp(t)>=0},Mv.isInteger=Mv.isint=function(){return this.e>this.d.length-2},Mv.isNegative=Mv.isneg=function(){return this.s<0},Mv.isPositive=Mv.ispos=function(){return this.s>0},Mv.isZero=function(){return 0===this.s},Mv.lessThan=Mv.lt=function(t){return this.cmp(t)<0},Mv.lessThanOrEqualTo=Mv.lte=function(t){return this.cmp(t)<1},Mv.logarithm=Mv.log=function(t){var e,r=this,n=r.constructor,o=n.precision,i=o+5;if(void 0===t)t=new n(10);else if((t=new n(t)).s<1||t.eq(mv))throw Error(wv+"NaN");if(r.s<1)throw Error(wv+(r.s?"NaN":"-Infinity"));return r.eq(mv)?new n(0):(gv=!1,e=Dv(Lv(r,i),Lv(t,i),i),gv=!0,Fv(e,o))},Mv.minus=Mv.sub=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?Uv(e,t):Tv(e,(t.s=-t.s,t))},Mv.modulo=Mv.mod=function(t){var e,r=this,n=r.constructor,o=n.precision;if(!(t=new n(t)).s)throw Error(wv+"NaN");return r.s?(gv=!1,e=Dv(r,t,0,1).times(t),gv=!0,r.minus(e)):Fv(new n(r),o)},Mv.naturalExponential=Mv.exp=function(){return Iv(this)},Mv.naturalLogarithm=Mv.ln=function(){return Lv(this)},Mv.negated=Mv.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},Mv.plus=Mv.add=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?Tv(e,t):Uv(e,(t.s=-t.s,t))},Mv.precision=Mv.sd=function(t){var e,r,n,o=this;if(void 0!==t&&t!==!!t&&1!==t&&0!==t)throw Error(xv+t);if(e=Nv(o)+1,r=7*(n=o.d.length-1)+1,n=o.d[n]){for(;n%10==0;n/=10)r--;for(n=o.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},Mv.squareRoot=Mv.sqrt=function(){var t,e,r,n,o,i,a,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(wv+"NaN")}for(t=Nv(u),gv=!1,0==(o=Math.sqrt(+u))||o==1/0?(((e=Cv(u.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=jv((t+1)/2)-(t<0||t%2),n=new c(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(Dv(u,i,a+2)).times(.5),Cv(i.d).slice(0,a)===(e=Cv(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(Fv(i,r+1,0),i.times(i).eq(u)){n=i;break}}else if("9999"!=e)break;a+=4}return gv=!0,Fv(n,r)},Mv.times=Mv.mul=function(t){var e,r,n,o,i,a,u,c,l,s=this,f=s.constructor,p=s.d,h=(t=new f(t)).d;if(!s.s||!t.s)return new f(0);for(t.s*=s.s,r=s.e+t.e,(c=p.length)<(l=h.length)&&(i=p,p=h,h=i,a=c,c=l,l=a),i=[],n=a=c+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=c+n;o>n;)u=i[o]+h[n]*p[o-n-1]+e,i[o--]=u%Av|0,e=u/Av|0;i[o]=(i[o]+e)%Av|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,gv?Fv(t,f.precision):t},Mv.toDecimalPlaces=Mv.todp=function(t,e){var r=this,n=r.constructor;return r=new n(r),void 0===t?r:(_v(t,0,bv),void 0===e?e=n.rounding:_v(e,0,8),Fv(r,t+Nv(r)+1,e))},Mv.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=$v(n,!0):(_v(t,0,bv),void 0===e?e=o.rounding:_v(e,0,8),r=$v(n=Fv(new o(n),t+1,e),!0,t+1)),r},Mv.toFixed=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?$v(o):(_v(t,0,bv),void 0===e?e=i.rounding:_v(e,0,8),r=$v((n=Fv(new i(o),t+Nv(o)+1,e)).abs(),!1,t+Nv(n)+1),o.isneg()&&!o.isZero()?"-"+r:r)},Mv.toInteger=Mv.toint=function(){var t=this,e=t.constructor;return Fv(new e(t),Nv(t)+1,e.rounding)},Mv.toNumber=function(){return+this},Mv.toPower=Mv.pow=function(t){var e,r,n,o,i,a,u=this,c=u.constructor,l=+(t=new c(t));if(!t.s)return new c(mv);if(!(u=new c(u)).s){if(t.s<1)throw Error(wv+"Infinity");return u}if(u.eq(mv))return u;if(n=c.precision,t.eq(mv))return Fv(u,n);if(a=(e=t.e)>=(r=t.d.length-1),i=u.s,a){if((r=l<0?-l:l)<=Ev){for(o=new c(mv),e=Math.ceil(n/7+4),gv=!1;r%2&&Wv((o=o.times(u)).d,e),0!==(r=jv(r/2));)Wv((u=u.times(u)).d,e);return gv=!0,t.s<0?new c(mv).div(o):Fv(o,n)}}else if(i<0)throw Error(wv+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,u.s=1,gv=!1,o=t.times(Lv(u,n+12)),gv=!0,(o=Iv(o)).s=i,o},Mv.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?n=$v(o,(r=Nv(o))<=i.toExpNeg||r>=i.toExpPos):(_v(t,1,bv),void 0===e?e=i.rounding:_v(e,0,8),n=$v(o=Fv(new i(o),t,e),t<=(r=Nv(o))||r<=i.toExpNeg,t)),n},Mv.toSignificantDigits=Mv.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(_v(t,1,bv),void 0===e?e=r.rounding:_v(e,0,8)),Fv(new r(this),t,e)},Mv.toString=Mv.valueOf=Mv.val=Mv.toJSON=Mv[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=this,e=Nv(t),r=t.constructor;return $v(t,e<=r.toExpNeg||e>=r.toExpPos)};var Dv=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%Av|0,n=r/Av|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=n*Av+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var u,c,l,s,f,p,h,y,d,v,m,b,g,w,x,O,j,S,P=n.constructor,A=n.s==o.s?1:-1,E=n.d,k=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(wv+"Division by zero");for(c=n.e-o.e,j=k.length,x=E.length,y=(h=new P(A)).d=[],l=0;k[l]==(E[l]||0);)++l;if(k[l]>(E[l]||0)&&--c,(b=null==i?i=P.precision:a?i+(Nv(n)-Nv(o))+1:i)<0)return new P(0);if(b=b/7+2|0,l=0,1==j)for(s=0,k=k[0],b++;(l<x||s)&&b--;l++)g=s*Av+(E[l]||0),y[l]=g/k|0,s=g%k|0;else{for((s=Av/(k[0]+1)|0)>1&&(k=t(k,s),E=t(E,s),j=k.length,x=E.length),w=j,v=(d=E.slice(0,j)).length;v<j;)d[v++]=0;(S=k.slice()).unshift(0),O=k[0],k[1]>=Av/2&&++O;do{s=0,(u=e(k,d,j,v))<0?(m=d[0],j!=v&&(m=m*Av+(d[1]||0)),(s=m/O|0)>1?(s>=Av&&(s=Av-1),1==(u=e(f=t(k,s),d,p=f.length,v=d.length))&&(s--,r(f,j<p?S:k,p))):(0==s&&(u=s=1),f=k.slice()),(p=f.length)<v&&f.unshift(0),r(d,f,v),-1==u&&(u=e(k,d,j,v=d.length))<1&&(s++,r(d,j<v?S:k,v)),v=d.length):0===u&&(s++,d=[0]),y[l++]=s,u&&d[0]?d[v++]=E[w]||0:(d=[E[w]],v=1)}while((w++<x||void 0!==d[0])&&b--)}return y[0]||y.shift(),h.e=c,Fv(h,a?i+Nv(h)+1:i)}}();function Iv(t,e){var r,n,o,i,a,u=0,c=0,l=t.constructor,s=l.precision;if(Nv(t)>16)throw Error(Ov+Nv(t));if(!t.s)return new l(mv);for(gv=!1,a=s,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),c+=5;for(a+=Math.log(Sv(2,c))/Math.LN10*2+5|0,r=n=o=new l(mv),l.precision=a;;){if(n=Fv(n.times(t),a),r=r.times(++u),Cv((i=o.plus(Dv(n,r,a))).d).slice(0,a)===Cv(o.d).slice(0,a)){for(;c--;)o=Fv(o.times(o),a);return l.precision=s,null==e?(gv=!0,Fv(o,s)):o}o=i}}function Nv(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function Bv(t,e,r){if(e>t.LN10.sd())throw gv=!0,r&&(t.precision=r),Error(wv+"LN10 precision limit exceeded");return Fv(new t(t.LN10),e)}function Rv(t){for(var e="";t--;)e+="0";return e}function Lv(t,e){var r,n,o,i,a,u,c,l,s,f=1,p=t,h=p.d,y=p.constructor,d=y.precision;if(p.s<1)throw Error(wv+(p.s?"NaN":"-Infinity"));if(p.eq(mv))return new y(0);if(null==e?(gv=!1,l=d):l=e,p.eq(10))return null==e&&(gv=!0),Bv(y,l);if(l+=10,y.precision=l,n=(r=Cv(h)).charAt(0),i=Nv(p),!(Math.abs(i)<15e14))return c=Bv(y,l+2,d).times(i+""),p=Lv(new y(n+"."+r.slice(1)),l-10).plus(c),y.precision=d,null==e?(gv=!0,Fv(p,d)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=Cv((p=p.times(t)).d)).charAt(0),f++;for(i=Nv(p),n>1?(p=new y("0."+r),i++):p=new y(n+"."+r.slice(1)),u=a=p=Dv(p.minus(mv),p.plus(mv),l),s=Fv(p.times(p),l),o=3;;){if(a=Fv(a.times(s),l),Cv((c=u.plus(Dv(a,new y(o),l))).d).slice(0,l)===Cv(u.d).slice(0,l))return u=u.times(2),0!==i&&(u=u.plus(Bv(y,l+2,d).times(i+""))),u=Dv(u,new y(f),l),y.precision=d,null==e?(gv=!0,Fv(u,d)):u;u=c,o+=2}}function zv(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=jv(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),gv&&(t.e>kv||t.e<-kv))throw Error(Ov+r)}else t.s=0,t.e=0,t.d=[0];return t}function Fv(t,e,r){var n,o,i,a,u,c,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(l=i=f[s],a=1;i>=10;i/=10)a++;o=(n%=7)-7+a}if(void 0!==r&&(u=l/(i=Sv(10,a-o-1))%10|0,c=e<0||void 0!==f[s+1]||l%i,c=r<4?(u||c)&&(0==r||r==(t.s<0?3:2)):u>5||5==u&&(4==r||c||6==r&&(n>0?o>0?l/Sv(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return c?(i=Nv(t),f.length=1,e=e-i-1,f[0]=Sv(10,(7-e%7)%7),t.e=jv(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=Sv(10,7-n),f[s]=o>0?(l/Sv(10,a-o)%Sv(10,o)|0)*i:0),c)for(;;){if(0==s){(f[0]+=i)==Av&&(f[0]=1,++t.e);break}if(f[s]+=i,f[s]!=Av)break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(gv&&(t.e>kv||t.e<-kv))throw Error(Ov+Nv(t));return t}function Uv(t,e){var r,n,o,i,a,u,c,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),gv?Fv(e,h):e;if(c=t.d,f=e.d,n=e.e,l=t.e,c=c.slice(),a=l-n){for((s=a<0)?(r=c,a=-a,u=f.length):(r=f,n=l,u=c.length),a>(o=Math.max(Math.ceil(h/7),u)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=c.length)<(u=f.length))&&(u=o),o=0;o<u;o++)if(c[o]!=f[o]){s=c[o]<f[o];break}a=0}for(s&&(r=c,c=f,f=r,e.s=-e.s),u=c.length,o=f.length-u;o>0;--o)c[u++]=0;for(o=f.length;o>a;){if(c[--o]<f[o]){for(i=o;i&&0===c[--i];)c[i]=Av-1;--c[i],c[o]+=Av}c[o]-=f[o]}for(;0===c[--u];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(e.d=c,e.e=n,gv?Fv(e,h):e):new p(0)}function $v(t,e,r){var n,o=Nv(t),i=Cv(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+Rv(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+Rv(-o-1)+i,r&&(n=r-a)>0&&(i+=Rv(n))):o>=a?(i+=Rv(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+Rv(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=Rv(n))),t.s<0?"-"+i:i}function Wv(t,e){if(t.length>e)return t.length=e,!0}function qv(t){if(!t||"object"!=typeof t)throw Error(wv+"Object expected");var e,r,n,o=["precision",1,bv,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(!(jv(n)===n&&n>=o[e+1]&&n<=o[e+2]))throw Error(xv+r+": "+n);this[r]=n}if(void 0!==(n=t[r="LN10"])){if(n!=Math.LN10)throw Error(xv+r+": "+n);this[r]=new this(n)}return this}var Vv=function t(e){var r,n,o;function i(t){var e=this;if(!(e instanceof i))return new i(t);if(e.constructor=i,t instanceof i)return e.s=t.s,e.e=t.e,void(e.d=(t=t.d)?t.slice():t);if("number"==typeof t){if(0*t!=0)throw Error(xv+t);if(t>0)e.s=1;else{if(!(t<0))return e.s=0,e.e=0,void(e.d=[0]);t=-t,e.s=-1}return t===~~t&&t<1e7?(e.e=0,void(e.d=[t])):zv(e,t.toString())}if("string"!=typeof t)throw Error(xv+t);if(45===t.charCodeAt(0)?(t=t.slice(1),e.s=-1):e.s=1,!Pv.test(t))throw Error(xv+t);zv(e,t)}if(i.prototype=Mv,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=qv,void 0===e&&(e={}),e)for(o=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});mv=new Vv(1);const Xv=Vv;function Hv(t){return function(t){if(Array.isArray(t))return Gv(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Gv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gv(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Gv(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Kv=function(t){return t},Yv={},Zv=function(t){return t===Yv},Jv=function(t){return function e(){return 0===arguments.length||1===arguments.length&&Zv(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},Qv=function t(e,r){return 1===e?r:Jv((function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter((function(t){return t!==Yv})).length;return a>=e?r.apply(void 0,o):t(e-a,Jv((function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map((function(t){return Zv(t)?e.shift():t}));return r.apply(void 0,Hv(i).concat(e))})))}))},tm=function(t){return Qv(t.length,t)},em=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},rm=tm((function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map((function(t){return e[t]})).map(t)})),nm=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},om=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every((function(t,r){return t===e[r]}))?r:(e=o,r=t.apply(void 0,o))}};const im={rangeStep:function(t,e,r){for(var n=new Xv(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new Xv(t).abs().log(10).toNumber())+1},interpolateNumber:tm((function(t,e,r){var n=+t;return n+r*(+e-n)})),uninterpolateNumber:tm((function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)})),uninterpolateTruncation:tm((function(t,e,r){var n=e-+t;return n=n||1/0,Math.max(0,Math.min(1,(r-t)/n))}))};function am(t){return function(t){if(Array.isArray(t))return lm(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||cm(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function um(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var r=[],n=!0,o=!1,i=void 0;try{for(var a,u=t[Symbol.iterator]();!(n=(a=u.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(c){o=!0,i=c}finally{try{n||null==u.return||u.return()}finally{if(o)throw i}}return r}(t,e)||cm(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cm(t,e){if(t){if("string"==typeof t)return lm(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?lm(t,e):void 0}}function lm(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function sm(t){var e=um(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function fm(t,e,r){if(t.lte(0))return new Xv(0);var n=im.getDigitCount(t.toNumber()),o=new Xv(10).pow(n),i=t.div(o),a=1!==n?.05:.1,u=new Xv(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?u:new Xv(Math.ceil(u))}function pm(t,e,r){var n=1,o=new Xv(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new Xv(10).pow(im.getDigitCount(t)-1),o=new Xv(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new Xv(Math.floor(t)))}else 0===t?o=new Xv(Math.floor((e-1)/2)):r||(o=new Xv(Math.floor(t)));var a=Math.floor((e-1)/2),u=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return Kv;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce((function(t,e){return e(t)}),o.apply(void 0,arguments))}}(rm((function(t){return o.add(new Xv(t-a).mul(n)).toNumber()})),em);return u(0,e)}function hm(t,e,r,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new Xv(0),tickMin:new Xv(0),tickMax:new Xv(0)};var i,a=fm(new Xv(e).sub(t).div(r-1),n,o);i=t<=0&&e>=0?new Xv(0):(i=new Xv(t).add(e).div(2)).sub(new Xv(i).mod(a));var u=Math.ceil(i.sub(t).div(a).toNumber()),c=Math.ceil(new Xv(e).sub(i).div(a).toNumber()),l=u+c+1;return l>r?hm(t,e,r,n,o+1):(l<r&&(c=e>0?c+(r-l):c,u=e>0?u:u+(r-l)),{step:a,tickMin:i.sub(new Xv(u).mul(a)),tickMax:i.add(new Xv(c).mul(a))})}var ym=om((function(t){var e=um(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(o,2),u=um(sm([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0){var s=l===1/0?[c].concat(am(em(0,o-1).map((function(){return 1/0})))):[].concat(am(em(0,o-1).map((function(){return-1/0}))),[l]);return r>n?nm(s):s}if(c===l)return pm(c,o,i);var f=hm(c,l,a,i),p=f.step,h=f.tickMin,y=f.tickMax,d=im.rangeStep(h,y.add(new Xv(.1).mul(p)),p);return r>n?nm(d):d})),dm=om((function(t,e){var r=um(t,2),n=r[0],o=r[1],i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=um(sm([n,o]),2),u=a[0],c=a[1];if(u===-1/0||c===1/0)return[n,o];if(u===c)return[u];var l=Math.max(e,2),s=fm(new Xv(c).sub(u).div(l-1),i,0),f=[].concat(am(im.rangeStep(new Xv(u),new Xv(c).sub(new Xv(.99).mul(s)),s)),[c]);return n>o?nm(f):f}));function vm(t,e){throw new Error("Invariant failed")}var mm=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function bm(t){return(bm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function gm(){return gm=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},gm.apply(this,arguments)}function wm(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return xm(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xm(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xm(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Om(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function jm(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Mm(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Sm(t,e,r){return e=Am(e),function(t,e){if(e&&("object"===bm(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Pm()?Reflect.construct(e,r||[],Am(t).constructor):e.apply(t,r))}function Pm(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Pm=function(){return!!t})()}function Am(t){return(Am=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Em(t,e){return(Em=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function km(t,e,r){return(e=Mm(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Mm(t){var e=function(t,e){if("object"!=bm(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=bm(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==bm(e)?e:e+""}var Tm=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Sm(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Em(t,e)}(t,r.Component),jm(t,[{key:"render",value:function(){var t=this.props,e=t.offset,n=t.layout,o=t.width,i=t.dataKey,a=t.data,u=t.dataPointFormatter,c=t.xAxis,l=t.yAxis,s=Om(t,mm),f=Tr(s,!1);"x"===this.props.direction&&"number"!==c.type&&vm();var p=a.map((function(t){var a=u(t,i),s=a.x,p=a.y,h=a.value,y=a.errorVal;if(!y)return null;var d,v,m=[];if(Array.isArray(y)){var b=wm(y,2);d=b[0],v=b[1]}else d=v=y;if("vertical"===n){var g=c.scale,w=p+e,x=w+o,O=w-o,j=g(h-d),S=g(h+v);m.push({x1:S,y1:x,x2:S,y2:O}),m.push({x1:j,y1:w,x2:S,y2:w}),m.push({x1:j,y1:x,x2:j,y2:O})}else if("horizontal"===n){var P=l.scale,A=s+e,E=A-o,k=A+o,M=P(h-d),T=P(h+v);m.push({x1:E,y1:T,x2:k,y2:T}),m.push({x1:A,y1:M,x2:A,y2:T}),m.push({x1:E,y1:M,x2:k,y2:M})}return r.createElement(nn,gm({className:"recharts-errorBar",key:"bar-".concat(m.map((function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)})))},f),m.map((function(t){return r.createElement("line",gm({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))})))}));return r.createElement(nn,{className:"recharts-errorBars"},p)}}])}();function _m(t){return(_m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Cm(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Dm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Cm(Object(r),!0).forEach((function(e){Im(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Cm(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Im(t,e,r){var n;return n=function(t,e){if("object"!=_m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=_m(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==_m(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}km(Tm,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),km(Tm,"displayName","ErrorBar");var Nm=function(t){var e=t.children,r=t.formattedGraphicalItems,n=t.legendWidth,o=t.legendContent,i=Ar(e,al);if(!i)return null;var a,u=al.defaultProps,c=void 0!==u?Dm(Dm({},u),i.props):{};return a=i.props&&i.props.payload?i.props&&i.props.payload:"children"===o?(r||[]).reduce((function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map((function(t){return{type:i.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}})))}),[]):(r||[]).map((function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?Dm(Dm({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:c.iconType||a||"square",color:qm(e),value:i||o,payload:n}})),Dm(Dm(Dm({},c),al.getWithHeight(i,n)),{},{payload:a,item:i})};function Bm(t){return(Bm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Rm(t){return function(t){if(Array.isArray(t))return Lm(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Lm(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Lm(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Lm(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function zm(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Fm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?zm(Object(r),!0).forEach((function(e){Um(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):zm(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Um(t,e,r){var n;return n=function(t,e){if("object"!=Bm(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=Bm(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Bm(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function $m(t,e,r){return De(t)||De(e)?r:tr(e)?Te(t,e,r):Re(e)?e(t):r}function Wm(t,e,r,n){var o=hv(t,(function(t){return $m(t,e)}));if("number"===r){var i=o.filter((function(t){return Qe(t)||parseFloat(t)}));return i.length?[cv(i),rv(i)]:[1/0,-1/0]}return(n?o.filter((function(t){return!De(t)})):o).map((function(t){return tr(t)||t instanceof Date?t:""}))}var qm=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?Fm(Fm({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},Vm=function(t,e,r,n,o){var i=Pr(e.props.children,Tm).filter((function(t){return function(t,e,r){return!!De(e)||("horizontal"===t?"yAxis"===e:"vertical"===t||"x"===r?"xAxis"===e:"y"!==r||"yAxis"===e)}(n,o,t.props.direction)}));if(i&&i.length){var a=i.map((function(t){return t.props.dataKey}));return t.reduce((function(t,e){var n=$m(e,r);if(De(n))return t;var o=Array.isArray(n)?[cv(n),rv(n)]:[n,n],i=a.reduce((function(t,r){var n=$m(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]}),[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]}),[1/0,-1/0])}return null},Xm=function(t,e,r,n,o){var i=e.map((function(e){var i=e.props.dataKey;return"number"===r&&i&&Vm(t,e,i,n)||Wm(t,i,r,o)}));if("number"===r)return i.reduce((function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}),[1/0,-1/0]);var a={};return i.reduce((function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t}),[])},Hm=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},Gm=function(t,e,r,n){if(n)return t.map((function(t){return t.coordinate}));var o,i,a=t.map((function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate}));return o||a.push(e),i||a.push(r),a},Km=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,u="scaleBand"===t.realScaleType?n.bandwidth()/2:2,c=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/u:0;return c="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*Ze(a[0]-a[1])*c:c,e&&(t.ticks||t.niceTicks)?(t.ticks||t.niceTicks).map((function(t){var e=o?o.indexOf(t):t;return{coordinate:n(e)+c,value:t,offset:c}})).filter((function(t){return!Ke(t.coordinate)})):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map((function(t,e){return{coordinate:n(t)+c,value:t,index:e,offset:c}})):n.ticks&&!r?n.ticks(t.tickCount).map((function(t){return{coordinate:n(t)+c,value:t,offset:c}})):n.domain().map((function(t,e){return{coordinate:n(t)+c,value:o?o[t]:t,index:e,offset:c}}))},Ym=new WeakMap,Zm=function(t,e){if("function"!=typeof e)return t;Ym.has(t)||Ym.set(t,new WeakMap);var r=Ym.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},Jm=function(t,e,r){var n=t.scale,o=t.type,i=t.layout,a=t.axisType;if("auto"===n)return"radial"===i&&"radiusAxis"===a?{scale:zf(),realScaleType:"band"}:"radial"===i&&"angleAxis"===a?{scale:ch(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:Uf(),realScaleType:"point"}:"category"===o?{scale:zf(),realScaleType:"band"}:{scale:ch(),realScaleType:"linear"};if(Be(n)){var u="scale".concat(ln(n));return{scale:(Hd[u]||Uf)(),realScaleType:Hd[u]?u:"point"}}return Re(n)?{scale:n}:{scale:Uf(),realScaleType:"point"}},Qm=1e-4,tb=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-Qm,i=Math.max(n[0],n[1])+Qm,a=t(e[0]),u=t(e[r-1]);(a<o||a>i||u<o||u>i)&&t.domain([e[0],e[r-1]])}},eb={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var u=Ke(t[a][r][1])?t[a][r][0]:t[a][r][1];u>=0?(t[a][r][0]=o,t[a][r][1]=o+u,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+u,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}so(t,e)}},none:so,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,u=0;a<r;++a)u+=t[a][n][1]||0;o[n][1]+=o[n][0]=-u/2}so(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var u=0,c=0,l=0;u<o;++u){for(var s=t[e[u]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<u;++h){var y=t[e[h]];p+=(y[a][1]||0)-(y[a-1][1]||0)}c+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,c&&(i-=l/c)}r[a-1][1]+=r[a-1][0]=i,so(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=Ke(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},rb=function(t,e,r){var n=e.map((function(t){return t.props.dataKey})),o=eb[r],i=function(){var t=sn([]),e=fo,r=so,n=po;function o(o){var i,a,u=Array.from(t.apply(this,arguments),ho),c=u.length,l=-1;for(const t of o)for(i=0,++l;i<c;++i)(u[i][l]=[0,+n(t,u[i].key,l,o)]).data=t;for(i=0,a=jn(e(u));i<c;++i)u[a[i]].index=i;return r(u,a),u}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:sn(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:sn(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?fo:"function"==typeof t?t:sn(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?so:t,o):r},o}().keys(n).value((function(t,e){return+$m(t,e,0)})).order(fo).offset(o);return i(t)},nb=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,u=r||e.scale;if("auto"!==u&&"linear"!==u)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var c=t.domain();if(!c.length)return null;var l=ym(c,o,a);return t.domain([cv(l),rv(l)]),{niceTicks:l}}if(o&&"number"===n){var s=t.domain();return{niceTicks:dm(s,o,a)}}return null};function ob(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!De(o[e.dataKey])){var u=ar(r,"value",o[e.dataKey]);if(u)return u.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var c=$m(o,De(a)?e.dataKey:a);return De(c)?null:e.scale(c)}var ib=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var u=$m(i,e.dataKey,e.domain[a]);return De(u)?null:e.scale(u)-o/2+n},ab=function(t,e,r){return Object.keys(t).reduce((function(n,o){var i=t[o].stackedData.reduce((function(t,n){var o=n.slice(e,r+1).reduce((function(t,e){return[cv(e.concat([t[0]]).filter(Qe)),rv(e.concat([t[1]]).filter(Qe))]}),[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]}),[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]}),[1/0,-1/0]).map((function(t){return t===1/0||t===-1/0?0:t}))},ub=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,cb=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lb=function(t,e,r){if(Re(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(Qe(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(ub.test(t[0])){var o=+ub.exec(t[0])[1];n[0]=e[0]-o}else Re(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(Qe(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(cb.test(t[1])){var i=+cb.exec(t[1])[1];n[1]=e[1]+i}else Re(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},sb=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=gl(e,(function(t){return t.coordinate})),i=1/0,a=1,u=o.length;a<u;a++){var c=o[a],l=o[a-1];i=Math.min((c.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},fb=function(t,e,r){return t&&t.length?vv(t,Te(r,"type.defaultProps.domain"))?e:t:e},pb=function(t,e){var r=t.type.defaultProps?Fm(Fm({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,u=r.tooltipType,c=r.chartType,l=r.hide;return Fm(Fm({},Tr(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:qm(t),value:$m(e,n),type:u,payload:e,chartType:c,hide:l})};function hb(t){return(hb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function yb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function db(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?yb(Object(r),!0).forEach((function(e){vb(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yb(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function vb(t,e,r){var n;return n=function(t,e){if("object"!=hb(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=hb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==hb(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function mb(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return bb(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return bb(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function bb(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var gb=Math.PI/180,wb=function(t){return 180*t/Math.PI},xb=function(t,e,r,n){return{x:t+Math.cos(-gb*n)*r,y:e+Math.sin(-gb*n)*r}},Ob=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},jb=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return Math.sqrt(Math.pow(r-o,2)+Math.pow(n-i,2))}({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var u=(r-o)/a,c=Math.acos(u);return n>i&&(c=2*Math.PI-c),{radius:a,angle:wb(c),angleInRadian:c}},Sb=function(t,e){var r=e.startAngle,n=e.endAngle,o=Math.floor(r/360),i=Math.floor(n/360);return t+360*Math.min(o,i)},Pb=function(t,e){var r=t.x,n=t.y,o=jb({x:r,y:n},e),i=o.radius,a=o.angle,u=e.innerRadius,c=e.outerRadius;if(i<u||i>c)return!1;if(0===i)return!0;var l,s=function(t){var e=t.startAngle,r=t.endAngle,n=Math.floor(e/360),o=Math.floor(r/360),i=Math.min(n,o);return{startAngle:e-360*i,endAngle:r-360*i}}(e),f=s.startAngle,p=s.endAngle,h=a;if(f<=p){for(;h>p;)h-=360;for(;h<f;)h+=360;l=h>=f&&h<=p}else{for(;h>f;)h-=360;for(;h<p;)h+=360;l=h>=p&&h<=f}return l?db(db({},e),{},{radius:i,angle:Sb(h,e)}):null},Ab=function(t){return e.isValidElement(t)||Re(t)||"boolean"==typeof t?"":t.className};function Eb(t){return(Eb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var kb=["offset"];function Mb(t){return function(t){if(Array.isArray(t))return Tb(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Tb(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Tb(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tb(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _b(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Cb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Db(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Cb(Object(r),!0).forEach((function(e){Ib(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Cb(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ib(t,e,r){var n;return n=function(t,e){if("object"!=Eb(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=Eb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Eb(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Nb(){return Nb=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Nb.apply(this,arguments)}var Bb=function(e,n,o){var i,a,u=e.position,c=e.viewBox,l=e.offset,s=e.className,f=c,p=f.cx,h=f.cy,y=f.innerRadius,d=f.outerRadius,v=f.startAngle,m=f.endAngle,b=f.clockWise,g=(y+d)/2,w=function(t,e){return Ze(e-t)*Math.min(Math.abs(e-t),360)}(v,m),x=w>=0?1:-1;"insideStart"===u?(i=v+x*l,a=b):"insideEnd"===u?(i=m-x*l,a=!b):"end"===u&&(i=m+x*l,a=b),a=w<=0?a:!a;var O=xb(p,h,g,i),j=xb(p,h,g,i+359*(a?1:-1)),S="M".concat(O.x,",").concat(O.y,"\n    A").concat(g,",").concat(g,",0,1,").concat(a?0:1,",\n    ").concat(j.x,",").concat(j.y),P=De(e.id)?rr("recharts-radial-line-"):e.id;return r.createElement("text",Nb({},o,{dominantBaseline:"central",className:t("recharts-radial-bar-label",s)}),r.createElement("defs",null,r.createElement("path",{id:P,d:S})),r.createElement("textPath",{xlinkHref:"#".concat(P)},n))};function Rb(n){var o,i=n.offset,a=Db({offset:void 0===i?5:i},_b(n,kb)),u=a.viewBox,c=a.position,l=a.value,s=a.children,f=a.content,p=a.className,h=void 0===p?"":p,y=a.textBreakAll;if(!u||De(l)&&De(s)&&!e.isValidElement(f)&&!Re(f))return null;if(e.isValidElement(f))return e.cloneElement(f,a);if(Re(f)){if(o=e.createElement(f,a),e.isValidElement(o))return o}else o=function(t){var e=t.value,r=t.formatter,n=De(t.children)?e:t.children;return Re(r)?r(n):n}(a);var d=function(t){return"cx"in t&&Qe(t.cx)}(u),v=Tr(a,!0);if(d&&("insideStart"===c||"insideEnd"===c||"end"===c))return Bb(a,o,v);var m=d?function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e,i=o.cx,a=o.cy,u=o.innerRadius,c=o.outerRadius,l=(o.startAngle+o.endAngle)/2;if("outside"===n){var s=xb(i,a,c+r,l),f=s.x;return{x:f,y:s.y,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=xb(i,a,(u+c)/2,l);return{x:p.x,y:p.y,textAnchor:"middle",verticalAnchor:"middle"}}(a):function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e,a=i.x,u=i.y,c=i.width,l=i.height,s=l>=0?1:-1,f=s*n,p=s>0?"end":"start",h=s>0?"start":"end",y=c>=0?1:-1,d=y*n,v=y>0?"end":"start",m=y>0?"start":"end";if("top"===o)return Db(Db({},{x:a+c/2,y:u-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(u-r.y,0),width:c}:{});if("bottom"===o)return Db(Db({},{x:a+c/2,y:u+l+f,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(u+l),0),width:c}:{});if("left"===o){var b={x:a-d,y:u+l/2,textAnchor:v,verticalAnchor:"middle"};return Db(Db({},b),r?{width:Math.max(b.x-r.x,0),height:l}:{})}if("right"===o){var g={x:a+c+d,y:u+l/2,textAnchor:m,verticalAnchor:"middle"};return Db(Db({},g),r?{width:Math.max(r.x+r.width-g.x,0),height:l}:{})}var w=r?{width:c,height:l}:{};return"insideLeft"===o?Db({x:a+d,y:u+l/2,textAnchor:m,verticalAnchor:"middle"},w):"insideRight"===o?Db({x:a+c-d,y:u+l/2,textAnchor:v,verticalAnchor:"middle"},w):"insideTop"===o?Db({x:a+c/2,y:u+f,textAnchor:"middle",verticalAnchor:h},w):"insideBottom"===o?Db({x:a+c/2,y:u+l-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===o?Db({x:a+d,y:u+f,textAnchor:m,verticalAnchor:h},w):"insideTopRight"===o?Db({x:a+c-d,y:u+f,textAnchor:v,verticalAnchor:h},w):"insideBottomLeft"===o?Db({x:a+d,y:u+l-f,textAnchor:m,verticalAnchor:p},w):"insideBottomRight"===o?Db({x:a+c-d,y:u+l-f,textAnchor:v,verticalAnchor:p},w):Le(o)&&(Qe(o.x)||Je(o.x))&&(Qe(o.y)||Je(o.y))?Db({x:a+nr(o.x,c),y:u+nr(o.y,l),textAnchor:"end",verticalAnchor:"end"},w):Db({x:a+c/2,y:u+l/2,textAnchor:"middle",verticalAnchor:"middle"},w)}(a);return r.createElement(pf,Nb({className:t("recharts-label",h)},v,m,{breakAll:y}),o)}Rb.displayName="Label";var Lb,zb,Fb=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,u=t.radius,c=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,y=t.width,d=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(Qe(y)&&Qe(d)){if(Qe(s)&&Qe(f))return{x:s,y:f,width:y,height:d};if(Qe(p)&&Qe(h))return{x:p,y:h,width:y,height:d}}return Qe(s)&&Qe(f)?{x:s,y:f,width:0,height:0}:Qe(e)&&Qe(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:c||0,outerRadius:l||u||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};Rb.parseViewBox=Fb,Rb.renderCallByParent=function(t,n){var o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=t.children,a=Fb(t),u=Pr(i,Rb).map((function(t,r){return e.cloneElement(t,{viewBox:n||a,key:"label-".concat(r)})}));if(!o)return u;var c=function(t,n){return t?!0===t?r.createElement(Rb,{key:"label-implicit",viewBox:n}):tr(t)?r.createElement(Rb,{key:"label-implicit",viewBox:n,value:t}):e.isValidElement(t)?t.type===Rb?e.cloneElement(t,{key:"label-implicit",viewBox:n}):r.createElement(Rb,{key:"label-implicit",content:t,viewBox:n}):Re(t)?r.createElement(Rb,{key:"label-implicit",content:t,viewBox:n}):Le(t)?r.createElement(Rb,Nb({viewBox:n},t,{key:"label-implicit"})):null:null}(t.label,n||a);return[c].concat(Mb(u))};const Ub=o(zb?Lb:(zb=1,Lb=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}));function $b(t){return($b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Wb=["valueAccessor"],qb=["data","dataKey","clockWise","id","textBreakAll"];function Vb(t){return function(t){if(Array.isArray(t))return Xb(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Xb(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xb(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Xb(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Hb(){return Hb=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Hb.apply(this,arguments)}function Gb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Kb(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Gb(Object(r),!0).forEach((function(e){Yb(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Gb(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Yb(t,e,r){var n;return n=function(t,e){if("object"!=$b(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=$b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==$b(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Zb(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var Jb=function(t){return Array.isArray(t.value)?Ub(t.value):t.value};function Qb(t){var e=t.valueAccessor,n=void 0===e?Jb:e,o=Zb(t,Wb),i=o.data,a=o.dataKey,u=o.clockWise,c=o.id,l=o.textBreakAll,s=Zb(o,qb);return i&&i.length?r.createElement(nn,{className:"recharts-label-list"},i.map((function(t,e){var o=De(a)?n(t,e):$m(t&&t.payload,a),i=De(c)?{}:{id:"".concat(c,"-").concat(e)};return r.createElement(Rb,Hb({},Tr(t,!0),s,i,{parentViewBox:t.parentViewBox,value:o,textBreakAll:l,viewBox:Rb.parseViewBox(De(u)?t:Kb(Kb({},t),{},{clockWise:u})),key:"label-".concat(e),index:e}))}))):null}function tg(t){return(tg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eg(){return eg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},eg.apply(this,arguments)}function rg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ng(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rg(Object(r),!0).forEach((function(e){og(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rg(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function og(t,e,r){var n;return n=function(t,e){if("object"!=tg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=tg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tg(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}Qb.displayName="LabelList",Qb.renderCallByParent=function(t,n){var o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=Pr(t.children,Qb).map((function(t,r){return e.cloneElement(t,{data:n,key:"labelList-".concat(r)})}));return o?[function(t,e){return t?!0===t?r.createElement(Qb,{key:"labelList-implicit",data:e}):r.isValidElement(t)||Re(t)?r.createElement(Qb,{key:"labelList-implicit",data:e,content:t}):Le(t)?r.createElement(Qb,Hb({data:e},t,{key:"labelList-implicit"})):null:null}(t.label,n)].concat(Vb(i)):i};var ig=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,u=t.cornerRadius,c=t.cornerIsExternal,l=u*(a?1:-1)+n,s=Math.asin(u/l)/gb,f=c?o:o+i*s,p=c?o-i*s:o;return{center:xb(e,r,l,f),circleTangency:xb(e,r,n,f),lineTangency:xb(e,r,l*Math.cos(s*gb),p),theta:s}},ag=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,a=function(t,e){return Ze(e-t)*Math.min(Math.abs(e-t),359.999)}(i,t.endAngle),u=i+a,c=xb(e,r,o,i),l=xb(e,r,o,u),s="M ".concat(c.x,",").concat(c.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(a)>180),",").concat(+(i>u),",\n    ").concat(l.x,",").concat(l.y,"\n  ");if(n>0){var f=xb(e,r,n,i),p=xb(e,r,n,u);s+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(a)>180),",").concat(+(i<=u),",\n            ").concat(f.x,",").concat(f.y," Z")}else s+="L ".concat(e,",").concat(r," Z");return s},ug={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},cg=function(e){var n=ng(ng({},ug),e),o=n.cx,i=n.cy,a=n.innerRadius,u=n.outerRadius,c=n.cornerRadius,l=n.forceCornerRadius,s=n.cornerIsExternal,f=n.startAngle,p=n.endAngle,h=n.className;if(u<a||f===p)return null;var y,d=t("recharts-sector",h),v=u-a,m=nr(c,v,0,!0);return y=m>0&&Math.abs(f-p)<360?function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,u=t.cornerIsExternal,c=t.startAngle,l=t.endAngle,s=Ze(l-c),f=ig({cx:e,cy:r,radius:o,angle:c,sign:s,cornerRadius:i,cornerIsExternal:u}),p=f.circleTangency,h=f.lineTangency,y=f.theta,d=ig({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:u}),v=d.circleTangency,m=d.lineTangency,b=d.theta,g=u?Math.abs(c-l):Math.abs(c-l)-y-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*-i,",0\n      "):ag({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:c,endAngle:l});var w="M ".concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var x=ig({cx:e,cy:r,radius:n,angle:c,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),O=x.circleTangency,j=x.lineTangency,S=x.theta,P=ig({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),A=P.circleTangency,E=P.lineTangency,k=P.theta,M=u?Math.abs(c-l):Math.abs(c-l)-S-k;if(M<0&&0===i)return"".concat(w,"L").concat(e,",").concat(r,"Z");w+="L".concat(E.x,",").concat(E.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else w+="L".concat(e,",").concat(r,"Z");return w}({cx:o,cy:i,innerRadius:a,outerRadius:u,cornerRadius:Math.min(m,v/2),forceCornerRadius:l,cornerIsExternal:s,startAngle:f,endAngle:p}):ag({cx:o,cy:i,innerRadius:a,outerRadius:u,startAngle:f,endAngle:p}),r.createElement("path",eg({},Tr(n,!0),{className:d,d:y,role:"img"}))};function lg(t){return(lg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sg(){return sg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},sg.apply(this,arguments)}function fg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function pg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fg(Object(r),!0).forEach((function(e){hg(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fg(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function hg(t,e,r){var n;return n=function(t,e){if("object"!=lg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=lg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lg(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var yg,dg,vg,mg,bg,gg={curveBasisClosed:function(t){return new Zn(t)},curveBasisOpen:function(t){return new Jn(t)},curveBasis:function(t){return new Yn(t)},curveBumpX:function(t){return new Tn(t,!0)},curveBumpY:function(t){return new Tn(t,!1)},curveLinearClosed:function(t){return new Qn(t)},curveLinear:Pn,curveMonotoneX:function(t){return new oo(t)},curveMonotoneY:function(t){return new io(t)},curveNatural:function(t){return new uo(t)},curveStep:function(t){return new lo(t,.5)},curveStepAfter:function(t){return new lo(t,1)},curveStepBefore:function(t){return new lo(t,0)}},wg=function(t){return t.x===+t.x&&t.y===+t.y},xg=function(t){return t.x},Og=function(t){return t.y},jg=function(t){var e,r=t.type,n=void 0===r?"linear":r,o=t.points,i=void 0===o?[]:o,a=t.baseLine,u=t.layout,c=t.connectNulls,l=void 0!==c&&c,s=function(t,e){if(Re(t))return t;var r="curve".concat(ln(t));return"curveMonotone"!==r&&"curveBump"!==r||!e?gg[r]||Pn:gg["".concat(r).concat("vertical"===e?"Y":"X")]}(n,u),f=l?i.filter((function(t){return wg(t)})):i;if(Array.isArray(a)){var p=l?a.filter((function(t){return wg(t)})):a,h=f.map((function(t,e){return pg(pg({},t),{},{base:p[e]})}));return(e="vertical"===u?Mn().y(Og).x1(xg).x0((function(t){return t.base.x})):Mn().x(xg).y1(Og).y0((function(t){return t.base.y}))).defined(wg).curve(s),e(h)}return(e="vertical"===u&&Qe(a)?Mn().y(Og).x1(xg).x0(a):Qe(a)?Mn().x(xg).y1(Og).y0(a):kn().x(xg).y(Og)).defined(wg).curve(s),e(f)},Sg=function(e){var n=e.className,o=e.points,i=e.path,a=e.pathRef;if(!(o&&o.length||i))return null;var u=o&&o.length?jg(e):i;return r.createElement("path",sg({},Tr(e,!1),yr(e),{className:t("recharts-curve",n),d:u,ref:a}))},Pg={exports:{}};function Ag(){if(dg)return yg;dg=1;return yg="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}function Eg(){if(mg)return vg;mg=1;var t=Ag();function e(){}function r(){}return r.resetWarningCache=e,vg=function(){function n(e,r,n,o,i,a){if(a!==t){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function o(){return n}n.isRequired=n;var i={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:o,element:n,elementType:n,instanceOf:o,node:n,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:r,resetWarningCache:e};return i.PropTypes=i,i}}function kg(){return bg||(bg=1,Pg.exports=Eg()()),Pg.exports}const Mg=o(kg());var Tg=Object.getOwnPropertyNames,_g=Object.getOwnPropertySymbols,Cg=Object.prototype.hasOwnProperty;function Dg(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function Ig(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var u=t(e,r,n);return o.delete(e),o.delete(r),u}}function Ng(t){return Tg(t).concat(_g(t))}var Bg=Object.hasOwn||function(t,e){return Cg.call(t,e)};function Rg(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var Lg=Object.getOwnPropertyDescriptor,zg=Object.keys;function Fg(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function Ug(t,e){return Rg(t.getTime(),e.getTime())}function $g(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function Wg(t,e){return t===e}function qg(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var o,i,a=new Array(n),u=t.entries(),c=0;(o=u.next())&&!o.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;)if(a[f])f++;else{var p=o.value,h=i.value;if(r.equals(p[0],h[0],c,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;c++}return!0}var Vg=Rg;function Xg(t,e,r){var n=zg(t),o=n.length;if(zg(e).length!==o)return!1;for(;o-- >0;)if(!Qg(t,e,r,n[o]))return!1;return!0}function Hg(t,e,r){var n,o,i,a=Ng(t),u=a.length;if(Ng(e).length!==u)return!1;for(;u-- >0;){if(!Qg(t,e,r,n=a[u]))return!1;if(o=Lg(t,n),i=Lg(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable))return!1}return!0}function Gg(t,e){return Rg(t.valueOf(),e.valueOf())}function Kg(t,e){return t.source===e.source&&t.flags===e.flags}function Yg(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var o,i,a=new Array(n),u=t.values();(o=u.next())&&!o.done;){for(var c=e.values(),l=!1,s=0;(i=c.next())&&!i.done;){if(!a[s]&&r.equals(o.value,i.value,o.value,i.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function Zg(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function Jg(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function Qg(t,e,r,n){return!("_owner"!==n&&"__o"!==n&&"__v"!==n||!t.$$typeof&&!e.$$typeof)||Bg(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var tw=Array.isArray,ew="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rw=Object.assign,nw=Object.prototype.toString.call.bind(Object.prototype.toString);var ow=iw();function iw(t){void 0===t&&(t={});var e,r=t.circular,n=void 0!==r&&r,o=t.createInternalComparator,i=t.createState,a=t.strict,u=void 0!==a&&a,c=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?Hg:Fg,areDatesEqual:Ug,areErrorsEqual:$g,areFunctionsEqual:Wg,areMapsEqual:n?Dg(qg,Hg):qg,areNumbersEqual:Vg,areObjectsEqual:n?Hg:Xg,arePrimitiveWrappersEqual:Gg,areRegExpsEqual:Kg,areSetsEqual:n?Dg(Yg,Hg):Yg,areTypedArraysEqual:n?Hg:Zg,areUrlsEqual:Jg};if(r&&(o=rw({},o,r(o))),e){var i=Ig(o.areArraysEqual),a=Ig(o.areMapsEqual),u=Ig(o.areObjectsEqual),c=Ig(o.areSetsEqual);o=rw({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:c})}return o}(t),l=function(t){var e=t.areArraysEqual,r=t.areDatesEqual,n=t.areErrorsEqual,o=t.areFunctionsEqual,i=t.areMapsEqual,a=t.areNumbersEqual,u=t.areObjectsEqual,c=t.arePrimitiveWrappersEqual,l=t.areRegExpsEqual,s=t.areSetsEqual,f=t.areTypedArraysEqual,p=t.areUrlsEqual;return function(t,h,y){if(t===h)return!0;if(null==t||null==h)return!1;var d=typeof t;if(d!==typeof h)return!1;if("object"!==d)return"number"===d?a(t,h,y):"function"===d&&o(t,h,y);var v=t.constructor;if(v!==h.constructor)return!1;if(v===Object)return u(t,h,y);if(tw(t))return e(t,h,y);if(null!=ew&&ew(t))return f(t,h,y);if(v===Date)return r(t,h,y);if(v===RegExp)return l(t,h,y);if(v===Map)return i(t,h,y);if(v===Set)return s(t,h,y);var m=nw(t);return"[object Date]"===m?r(t,h,y):"[object RegExp]"===m?l(t,h,y):"[object Map]"===m?i(t,h,y):"[object Set]"===m?s(t,h,y):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof h.then&&u(t,h,y):"[object URL]"===m?p(t,h,y):"[object Error]"===m?n(t,h,y):"[object Arguments]"===m?u(t,h,y):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&c(t,h,y)}}(c);return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var u=n(),c=u.cache,l=void 0===c?e?new WeakMap:void 0:c,s=u.meta;return r(t,a,{cache:l,equals:o,meta:s,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:n,comparator:l,createState:i,equals:o?o(l):(e=l,function(t,r,n,o,i,a,u){return e(t,r,u)}),strict:u})}function aw(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame((function n(o){r<0&&(r=o),o-r>e?(t(o),r=-1):function(t){"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(t)}(n)}))}function uw(t){return(uw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function cw(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return lw(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lw(t,e)}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function sw(){var t=function(){return null},e=!1,r=function r(n){if(!e){if(Array.isArray(n)){if(!n.length)return;var o=cw(n),i=o[0],a=o.slice(1);return"number"==typeof i?void aw(r.bind(null,a),i):(r(i),void aw(r.bind(null,a)))}"object"===uw(n)&&t(n),"function"==typeof n&&n()}};return{stop:function(){e=!0},start:function(t){e=!1,r(t)},subscribe:function(e){return t=e,function(){t=function(){return null}}}}}function fw(t){return(fw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function hw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pw(Object(r),!0).forEach((function(e){yw(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pw(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function yw(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==fw(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!==fw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===fw(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}iw({strict:!0}),iw({circular:!0}),iw({circular:!0,strict:!0}),iw({createInternalComparator:function(){return Rg}}),iw({strict:!0,createInternalComparator:function(){return Rg}}),iw({circular:!0,createInternalComparator:function(){return Rg}}),iw({circular:!0,createInternalComparator:function(){return Rg},strict:!0});var dw=function(t){return t},vw=function(t,e){return Object.keys(e).reduce((function(r,n){return hw(hw({},r),{},yw({},n,t(n,e[n])))}),{})},mw=function(t,e,r){return t.map((function(t){return"".concat((n=t,n.replace(/([A-Z])/g,(function(t){return"-".concat(t.toLowerCase())})))," ").concat(e,"ms ").concat(r);var n})).join(",")};function bw(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||ww(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function gw(t){return function(t){if(Array.isArray(t))return xw(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ww(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ww(t,e){if(t){if("string"==typeof t)return xw(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?xw(t,e):void 0}}function xw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Ow=1e-4,jw=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},Sw=function(t,e){return t.map((function(t,r){return t*Math.pow(e,r)})).reduce((function(t,e){return t+e}))},Pw=function(t,e){return function(r){var n=jw(t,e);return Sw(n,r)}},Aw=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0],o=e[1],i=e[2],a=e[3];if(1===e.length)switch(e[0]){case"linear":n=0,o=0,i=1,a=1;break;case"ease":n=.25,o=.1,i=.25,a=1;break;case"ease-in":n=.42,o=0,i=1,a=1;break;case"ease-out":n=.42,o=0,i=.58,a=1;break;case"ease-in-out":n=0,o=0,i=.58,a=1;break;default:var u=e[0].split("(");if("cubic-bezier"===u[0]&&4===u[1].split(")")[0].split(",").length){var c=bw(u[1].split(")")[0].split(",").map((function(t){return parseFloat(t)})),4);n=c[0],o=c[1],i=c[2],a=c[3]}}var l,s,f=Pw(n,i),p=Pw(o,a),h=(l=n,s=i,function(t){var e=jw(l,s),r=[].concat(gw(e.map((function(t,e){return t*e})).slice(1)),[0]);return Sw(r,t)}),y=function(t){for(var e,r=t>1?1:t,n=r,o=0;o<8;++o){var i=f(n)-r,a=h(n);if(Math.abs(i-r)<Ow||a<Ow)return p(n);n=(e=n-i/a)>1?1:e<0?0:e}return p(n)};return y.isStepper=!1,y},Ew=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Aw(n);case"spring":return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,u=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,u=n*a/1e3+t;return Math.abs(u-e)<Ow&&Math.abs(i)<Ow?[e,0]:[u,i]};return u.isStepper=!0,u.dt=a,u}();default:if("cubic-bezier"===n.split("(")[0])return Aw(n)}return"function"==typeof n?n:null};function kw(t){return(kw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Mw(t){return function(t){if(Array.isArray(t))return Nw(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Iw(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function _w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Tw(Object(r),!0).forEach((function(e){Cw(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Tw(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Cw(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==kw(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!==kw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===kw(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Dw(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||Iw(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Iw(t,e){if(t){if("string"==typeof t)return Nw(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Nw(t,e):void 0}}function Nw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Bw=function(t,e,r){return t+(e-t)*r},Rw=function(t){return t.from!==t.to},Lw=function t(e,r,n){var o=vw((function(t,r){if(Rw(r)){var n=Dw(e(r.from,r.to,r.velocity),2),o=n[0],i=n[1];return _w(_w({},r),{},{from:o,velocity:i})}return r}),r);return n<1?vw((function(t,e){return Rw(e)?_w(_w({},e),{},{velocity:Bw(e.velocity,o[t].velocity,n),from:Bw(e.from,o[t].from,n)}):e}),r):t(e,o,n-1)};const zw=function(t,e,r,n,o){var i,a,u,c,l=(i=t,a=e,[Object.keys(i),Object.keys(a)].reduce((function(t,e){return t.filter((function(t){return e.includes(t)}))}))),s=l.reduce((function(r,n){return _w(_w({},r),{},Cw({},n,[t[n],e[n]]))}),{}),f=l.reduce((function(r,n){return _w(_w({},r),{},Cw({},n,{from:t[n],velocity:0,to:e[n]}))}),{}),p=-1,h=function(){return null};return h=r.isStepper?function(n){u||(u=n);var i=(n-u)/r.dt;f=Lw(r,f,i),o(_w(_w(_w({},t),e),vw((function(t,e){return e.from}),f))),u=n,Object.values(f).filter(Rw).length&&(p=requestAnimationFrame(h))}:function(i){c||(c=i);var a=(i-c)/n,u=vw((function(t,e){return Bw.apply(void 0,Mw(e).concat([r(a)]))}),s);if(o(_w(_w(_w({},t),e),u)),a<1)p=requestAnimationFrame(h);else{var l=vw((function(t,e){return Bw.apply(void 0,Mw(e).concat([r(1)]))}),s);o(_w(_w(_w({},t),e),l))}},function(){return requestAnimationFrame(h),function(){cancelAnimationFrame(p)}}};function Fw(t){return(Fw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Uw=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function $w(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Ww(t){return function(t){if(Array.isArray(t))return qw(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return qw(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return qw(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Vw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Xw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Vw(Object(r),!0).forEach((function(e){Hw(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Vw(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Hw(t,e,r){return(e=Kw(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Gw(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Kw(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function Kw(t){var e=function(t,e){if("object"!==Fw(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!==Fw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Fw(e)?e:String(e)}function Yw(t,e){return(Yw=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Zw(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=tx(t);if(e){var o=tx(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return Jw(this,r)}}function Jw(t,e){if(e&&("object"===Fw(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Qw(t)}function Qw(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function tx(t){return(tx=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var ex=function(){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Yw(t,e)}(n,e.PureComponent);var t=Zw(n);function n(e,r){var o;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n);var i=(o=t.call(this,e,r)).props,a=i.isActive,u=i.attributeName,c=i.from,l=i.to,s=i.steps,f=i.children,p=i.duration;if(o.handleStyleChange=o.handleStyleChange.bind(Qw(o)),o.changeStyle=o.changeStyle.bind(Qw(o)),!a||p<=0)return o.state={style:{}},"function"==typeof f&&(o.state={style:l}),Jw(o);if(s&&s.length)o.state={style:s[0].style};else if(c){if("function"==typeof f)return o.state={style:c},Jw(o);o.state={style:u?Hw({},u,c):c}}else o.state={style:{}};return o}return Gw(n,[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,u=e.from,c=this.state.style;if(n)if(r){if(!(ow(t.to,a)&&t.canBegin&&t.isActive)){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=l||i?u:t.to;if(this.state&&c){var f={style:o?Hw({},o,s):s};(o&&c[o]!==s||!o&&c!==s)&&this.setState(f)}this.runAnimation(Xw(Xw({},this.props),{},{from:s,begin:0}))}}else{var p={style:o?Hw({},o,a):a};this.state&&c&&(o&&c[o]!==a||!o&&c!==a)&&this.setState(p)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,u=t.onAnimationEnd,c=t.onAnimationStart,l=zw(r,n,Ew(i),o,this.changeStyle);this.manager.start([c,a,function(){e.stopJSAnimation=l()},o,u])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,u=i.duration,c=void 0===u?0:u;return this.manager.start([o].concat(Ww(r.reduce((function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,u=void 0===a?"ease":a,c=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(c);if("function"==typeof u||"spring"===u)return[].concat(Ww(t),[e.runJSAnimation.bind(e,{from:f.style,to:c,duration:i,easing:u}),i]);var h=mw(p,i,u),y=Xw(Xw(Xw({},f.style),c),{},{transition:h});return[].concat(Ww(t),[y,i,s]).filter(dw)}),[a,Math.max(c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=sw());var e=t.begin,r=t.duration,n=t.attributeName,o=t.to,i=t.easing,a=t.onAnimationStart,u=t.onAnimationEnd,c=t.steps,l=t.children,s=this.manager;if(this.unSubscribe=s.subscribe(this.handleStyleChange),"function"!=typeof i&&"function"!=typeof l&&"spring"!==i)if(c.length>1)this.runStepAnimation(t);else{var f=n?Hw({},n,o):o,p=mw(Object.keys(f),r,i);s.start([a,e,Xw(Xw({},f),{},{transition:p}),r,u])}else this.runJSAnimation(t)}},{key:"render",value:function(){var t=this.props,n=t.children;t.begin;var o=t.duration;t.attributeName,t.easing;var i=t.isActive;t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart;var a=$w(t,Uw),u=e.Children.count(n),c=this.state.style;if("function"==typeof n)return n(c);if(!i||0===u||o<=0)return n;var l=function(t){var r=t.props,n=r.style,o=void 0===n?{}:n,i=r.className;return e.cloneElement(t,Xw(Xw({},a),{},{style:Xw(Xw({},o),c),className:i}))};return 1===u?l(e.Children.only(n)):r.createElement("div",null,e.Children.map(n,(function(t){return l(t)})))}}]),n}();function rx(t){return(rx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nx(){return nx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},nx.apply(this,arguments)}function ox(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return ix(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ix(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ix(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ax(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ux(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ax(Object(r),!0).forEach((function(e){cx(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ax(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function cx(t,e,r){var n;return n=function(t,e){if("object"!=rx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=rx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==rx(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}ex.displayName="Animate",ex.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ex.propTypes={from:Mg.oneOfType([Mg.object,Mg.string]),to:Mg.oneOfType([Mg.object,Mg.string]),attributeName:Mg.string,duration:Mg.number,begin:Mg.number,easing:Mg.oneOfType([Mg.string,Mg.func]),steps:Mg.arrayOf(Mg.shape({duration:Mg.number.isRequired,style:Mg.object.isRequired,easing:Mg.oneOfType([Mg.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),Mg.func]),properties:Mg.arrayOf("string"),onAnimationEnd:Mg.func})),children:Mg.oneOfType([Mg.node,Mg.func]),isActive:Mg.bool,canBegin:Mg.bool,onAnimationEnd:Mg.func,shouldReAnimate:Mg.bool,onAnimationStart:Mg.func,onAnimationReStart:Mg.func};var lx=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),u=n>=0?1:-1,c=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+u*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+c*s[0],",").concat(e)),i+="L ".concat(t+r-c*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+u*s[1])),i+="L ".concat(t+r,",").concat(e+n-u*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-c*s[2],",").concat(e+n)),i+="L ".concat(t+c*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-u*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+c*p,",").concat(e,"\n            L ").concat(t+r-c*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+u*p,"\n            L ").concat(t+r,",").concat(e+n-u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-c*p,",").concat(e+n,"\n            L ").concat(t+c*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-u*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},sx=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,u=e.height;if(Math.abs(a)>0&&Math.abs(u)>0){var c=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+u),f=Math.max(i,i+u);return r>=c&&r<=l&&n>=s&&n<=f}return!1},fx={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},px=function(n){var o=ux(ux({},fx),n),i=e.useRef(),a=ox(e.useState(-1),2),u=a[0],c=a[1];e.useEffect((function(){if(i.current&&i.current.getTotalLength)try{var t=i.current.getTotalLength();t&&c(t)}catch(e){}}),[]);var l=o.x,s=o.y,f=o.width,p=o.height,h=o.radius,y=o.className,d=o.animationEasing,v=o.animationDuration,m=o.animationBegin,b=o.isAnimationActive,g=o.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||0===f||0===p)return null;var w=t("recharts-rectangle",y);return g?r.createElement(ex,{canBegin:u>0,from:{width:f,height:p,x:l,y:s},to:{width:f,height:p,x:l,y:s},duration:v,animationEasing:d,isActive:g},(function(t){var e=t.width,n=t.height,a=t.x,c=t.y;return r.createElement(ex,{canBegin:u>0,from:"0px ".concat(-1===u?1:u,"px"),to:"".concat(u,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:b,easing:d},r.createElement("path",nx({},Tr(o,!0),{className:w,d:lx(a,c,e,n,h),ref:i})))})):r.createElement("path",nx({},Tr(o,!0),{className:w,d:lx(l,s,f,p,h)}))},hx=["points","className","baseLinePoints","connectNulls"];function yx(){return yx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},yx.apply(this,arguments)}function dx(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function vx(t){return function(t){if(Array.isArray(t))return mx(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return mx(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mx(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function mx(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var bx=function(t){return t&&t.x===+t.x&&t.y===+t.y},gx=function(t,e){var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach((function(t){bx(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])})),bx(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e}(t);e&&(r=[r.reduce((function(t,e){return[].concat(vx(t),vx(e))}),[])]);var n=r.map((function(t){return t.reduce((function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)}),"")})).join("");return 1===r.length?"".concat(n,"Z"):n},wx=function(e){var n=e.points,o=e.className,i=e.baseLinePoints,a=e.connectNulls,u=dx(e,hx);if(!n||!n.length)return null;var c=t("recharts-polygon",o);if(i&&i.length){var l=u.stroke&&"none"!==u.stroke,s=function(t,e,r){var n=gx(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(gx(e.reverse(),r).slice(1))}(n,i,a);return r.createElement("g",{className:c},r.createElement("path",yx({},Tr(u,!0),{fill:"Z"===s.slice(-1)?u.fill:"none",stroke:"none",d:s})),l?r.createElement("path",yx({},Tr(u,!0),{fill:"none",d:gx(n,a)})):null,l?r.createElement("path",yx({},Tr(u,!0),{fill:"none",d:gx(i,a)})):null)}var f=gx(n,a);return r.createElement("path",yx({},Tr(u,!0),{fill:"Z"===f.slice(-1)?u.fill:"none",className:c,d:f}))};function xx(){return xx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xx.apply(this,arguments)}var Ox=function(e){var n=e.cx,o=e.cy,i=e.r,a=e.className,u=t("recharts-dot",a);return n===+n&&o===+o&&i===+i?r.createElement("circle",xx({},Tr(e,!1),yr(e),{className:u,cx:n,cy:o,r:i})):null};function jx(t){return(jx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Sx=["x","y","top","left","width","height","className"];function Px(){return Px=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Px.apply(this,arguments)}function Ax(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ex(t,e,r){var n;return n=function(t,e){if("object"!=jx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=jx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==jx(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function kx(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var Mx,Tx,_x=function(t,e,r,n,o,i){return"M".concat(t,",").concat(o,"v").concat(n,"M").concat(i,",").concat(e,"h").concat(r)},Cx=function(e){var n=e.x,o=void 0===n?0:n,i=e.y,a=void 0===i?0:i,u=e.top,c=void 0===u?0:u,l=e.left,s=void 0===l?0:l,f=e.width,p=void 0===f?0:f,h=e.height,y=void 0===h?0:h,d=e.className,v=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ax(Object(r),!0).forEach((function(e){Ex(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ax(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({x:o,y:a,top:c,left:s,width:p,height:y},kx(e,Sx));return Qe(o)&&Qe(a)&&Qe(p)&&Qe(y)&&Qe(c)&&Qe(s)?r.createElement("path",Px({},Tr(v,!0),{className:t("recharts-cross",d),d:_x(o,a,p,y,c,s)})):null};const Dx=o(function(){if(Tx)return Mx;Tx=1;var t=tv(),e=ev(),r=Yu();return Mx=function(n,o){return n&&n.length?t(n,r(o,2),e):void 0}}());var Ix,Nx;const Bx=o(function(){if(Nx)return Ix;Nx=1;var t=tv(),e=Yu(),r=uv();return Ix=function(n,o){return n&&n.length?t(n,e(o,2),r):void 0}}());var Rx=["cx","cy","angle","ticks","axisLine"],Lx=["ticks","tick","angle","tickFormatter","stroke"];function zx(t){return(zx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Fx(){return Fx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Fx.apply(this,arguments)}function Ux(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function $x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ux(Object(r),!0).forEach((function(e){Kx(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ux(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Wx(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function qx(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Yx(n.key),n)}}function Vx(t,e,r){return e=Hx(e),function(t,e){if(e&&("object"===zx(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Xx()?Reflect.construct(e,r||[],Hx(t).constructor):e.apply(t,r))}function Xx(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Xx=function(){return!!t})()}function Hx(t){return(Hx=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Gx(t,e){return(Gx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Kx(t,e,r){return(e=Yx(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Yx(t){var e=function(t,e){if("object"!=zx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=zx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==zx(e)?e:e+""}var Zx=function(){function n(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),Vx(this,n,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Gx(t,e)}(n,e.PureComponent),o=n,a=[{key:"renderTickItem",value:function(t,e,n){return r.isValidElement(t)?r.cloneElement(t,e):Re(t)?t(e):r.createElement(pf,Fx({},e,{className:"recharts-polar-radius-axis-tick-value"}),n)}}],(i=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle,o=r.cx,i=r.cy;return xb(o,i,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=Dx(o,(function(t){return t.coordinate||0}));return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:Bx(o,(function(t){return t.coordinate||0})).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,n=t.cy,o=t.angle,i=t.ticks,a=t.axisLine,u=Wx(t,Rx),c=i.reduce((function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]}),[1/0,-1/0]),l=xb(e,n,c[0],o),s=xb(e,n,c[1],o),f=$x($x($x({},Tr(u,!1)),{},{fill:"none"},Tr(a,!1)),{},{x1:l.x,y1:l.y,x2:s.x,y2:s.y});return r.createElement("line",Fx({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var e=this,o=this.props,i=o.ticks,a=o.tick,u=o.angle,c=o.tickFormatter,l=o.stroke,s=Wx(o,Lx),f=this.getTickTextAnchor(),p=Tr(s,!1),h=Tr(a,!1),y=i.map((function(o,i){var s=e.getTickValueCoord(o),y=$x($x($x($x({textAnchor:f,transform:"rotate(".concat(90-u,", ").concat(s.x,", ").concat(s.y,")")},p),{},{stroke:"none",fill:l},h),{},{index:i},s),{},{payload:o});return r.createElement(nn,Fx({className:t("recharts-polar-radius-axis-tick",Ab(a)),key:"tick-".concat(o.coordinate)},dr(e.props,o,i)),n.renderTickItem(a,y,c?c(o.value,i):o.value))}));return r.createElement(nn,{className:"recharts-polar-radius-axis-ticks"},y)}},{key:"render",value:function(){var e=this.props,n=e.ticks,o=e.axisLine,i=e.tick;return n&&n.length?r.createElement(nn,{className:t("recharts-polar-radius-axis",this.props.className)},o&&this.renderAxisLine(),i&&this.renderTicks(),Rb.renderCallByParent(this.props,this.getViewBox())):null}}])&&qx(o.prototype,i),a&&qx(o,a),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,i,a}();function Jx(t){return(Jx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Qx(){return Qx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Qx.apply(this,arguments)}function tO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function eO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tO(Object(r),!0).forEach((function(e){uO(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tO(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function rO(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,cO(n.key),n)}}function nO(t,e,r){return e=iO(e),function(t,e){if(e&&("object"===Jx(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,oO()?Reflect.construct(e,r||[],iO(t).constructor):e.apply(t,r))}function oO(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(oO=function(){return!!t})()}function iO(t){return(iO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function aO(t,e){return(aO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uO(t,e,r){return(e=cO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function cO(t){var e=function(t,e){if("object"!=Jx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=Jx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==Jx(e)?e:e+""}Kx(Zx,"displayName","PolarRadiusAxis"),Kx(Zx,"axisType","radiusAxis"),Kx(Zx,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var lO,sO,fO,pO,hO=Math.PI/180,yO=function(){function n(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),nO(this,n,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&aO(t,e)}(n,e.PureComponent),o=n,a=[{key:"renderTickItem",value:function(t,e,n){return r.isValidElement(t)?r.cloneElement(t,e):Re(t)?t(e):r.createElement(pf,Qx({},e,{className:"recharts-polar-angle-axis-tick-value"}),n)}}],(i=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,o=e.radius,i=e.orientation,a=e.tickSize||8,u=xb(r,n,o,t.coordinate),c=xb(r,n,o+("inner"===i?-1:1)*a,t.coordinate);return{x1:u.x,y1:u.y,x2:c.x,y2:c.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*hO);return r>1e-5?"outer"===e?"start":"end":r<-1e-5?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,n=t.cy,o=t.radius,i=t.axisLine,a=t.axisLineType,u=eO(eO({},Tr(this.props,!1)),{},{fill:"none"},Tr(i,!1));if("circle"===a)return r.createElement(Ox,Qx({className:"recharts-polar-angle-axis-line"},u,{cx:e,cy:n,r:o}));var c=this.props.ticks.map((function(t){return xb(e,n,o,t.coordinate)}));return r.createElement(wx,Qx({className:"recharts-polar-angle-axis-line"},u,{points:c}))}},{key:"renderTicks",value:function(){var e=this,o=this.props,i=o.ticks,a=o.tick,u=o.tickLine,c=o.tickFormatter,l=o.stroke,s=Tr(this.props,!1),f=Tr(a,!1),p=eO(eO({},s),{},{fill:"none"},Tr(u,!1)),h=i.map((function(o,i){var h=e.getTickLineCoord(o),y=eO(eO(eO({textAnchor:e.getTickTextAnchor(o)},s),{},{stroke:"none",fill:l},f),{},{index:i,payload:o,x:h.x2,y:h.y2});return r.createElement(nn,Qx({className:t("recharts-polar-angle-axis-tick",Ab(a)),key:"tick-".concat(o.coordinate)},dr(e.props,o,i)),u&&r.createElement("line",Qx({className:"recharts-polar-angle-axis-tick-line"},p,h)),a&&n.renderTickItem(a,y,c?c(o.value,i):o.value))}));return r.createElement(nn,{className:"recharts-polar-angle-axis-ticks"},h)}},{key:"render",value:function(){var e=this.props,n=e.ticks,o=e.radius,i=e.axisLine;return o<=0||!n||!n.length?null:r.createElement(nn,{className:t("recharts-polar-angle-axis",this.props.className)},i&&this.renderAxisLine(),this.renderTicks())}}])&&rO(o.prototype,i),a&&rO(o,a),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,i,a}();uO(yO,"displayName","PolarAngleAxis"),uO(yO,"axisType","angleAxis"),uO(yO,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});const dO=o(function(){if(pO)return fO;pO=1;var t=ie(),e=function(){if(sO)return lO;sO=1;var t=Nu()(Object.getPrototypeOf,Object);return lO=t}(),r=ae(),n=Function.prototype,o=Object.prototype,i=n.toString,a=o.hasOwnProperty,u=i.call(Object);return fO=function(n){if(!r(n)||"[object Object]"!=t(n))return!1;var o=e(n);if(null===o)return!0;var c=a.call(o,"constructor")&&o.constructor;return"function"==typeof c&&c instanceof c&&i.call(c)==u}}());var vO,mO;const bO=o(function(){if(mO)return vO;mO=1;var t=ie(),e=ae();return vO=function(r){return!0===r||!1===r||e(r)&&"[object Boolean]"==t(r)}}());function gO(t){return(gO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function wO(){return wO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},wO.apply(this,arguments)}function xO(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return OO(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return OO(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function OO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function jO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function SO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?jO(Object(r),!0).forEach((function(e){PO(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):jO(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function PO(t,e,r){var n;return n=function(t,e){if("object"!=gO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=gO(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==gO(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var AO,EO=function(t,e,r,n,o){var i,a=r-n;return i="M ".concat(t,",").concat(e),i+="L ".concat(t+r,",").concat(e),i+="L ".concat(t+r-a/2,",").concat(e+o),i+="L ".concat(t+r-a/2-n,",").concat(e+o),i+="L ".concat(t,",").concat(e," Z")},kO={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},MO=function(n){var o=SO(SO({},kO),n),i=e.useRef(),a=xO(e.useState(-1),2),u=a[0],c=a[1];e.useEffect((function(){if(i.current&&i.current.getTotalLength)try{var t=i.current.getTotalLength();t&&c(t)}catch(e){}}),[]);var l=o.x,s=o.y,f=o.upperWidth,p=o.lowerWidth,h=o.height,y=o.className,d=o.animationEasing,v=o.animationDuration,m=o.animationBegin,b=o.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||h!==+h||0===f&&0===p||0===h)return null;var g=t("recharts-trapezoid",y);return b?r.createElement(ex,{canBegin:u>0,from:{upperWidth:0,lowerWidth:0,height:h,x:l,y:s},to:{upperWidth:f,lowerWidth:p,height:h,x:l,y:s},duration:v,animationEasing:d,isActive:b},(function(t){var e=t.upperWidth,n=t.lowerWidth,a=t.height,c=t.x,l=t.y;return r.createElement(ex,{canBegin:u>0,from:"0px ".concat(-1===u?1:u,"px"),to:"".concat(u,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:d},r.createElement("path",wO({},Tr(o,!0),{className:g,d:EO(c,l,e,n,a),ref:i})))})):r.createElement("g",null,r.createElement("path",wO({},Tr(o,!0),{className:g,d:EO(l,s,f,p,h)})))},TO=["option","shapeType","propTransformer","activeClassName","isActive"];function _O(t){return(_O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function CO(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function DO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function IO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?DO(Object(r),!0).forEach((function(e){NO(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):DO(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function NO(t,e,r){var n;return n=function(t,e){if("object"!=_O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=_O(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==_O(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function BO(t,e){return IO(IO({},e),t)}function RO(t){var e=t.shapeType,n=t.elementProps;switch(e){case"rectangle":return r.createElement(px,n);case"trapezoid":return r.createElement(MO,n);case"sector":return r.createElement(cg,n);case"symbols":if(function(t){return"symbols"===t}(e))return r.createElement(So,n);break;default:return null}}function LO(t){var n,o=t.option,i=t.shapeType,a=t.propTransformer,u=void 0===a?BO:a,c=t.activeClassName,l=void 0===c?"recharts-active-shape":c,s=t.isActive,f=CO(t,TO);if(e.isValidElement(o))n=e.cloneElement(o,IO(IO({},f),function(t){return e.isValidElement(t)?t.props:t}(o)));else if(Re(o))n=o(f);else if(dO(o)&&!bO(o)){var p=u(o,f);n=r.createElement(RO,{shapeType:i,elementProps:p})}else{var h=f;n=r.createElement(RO,{shapeType:i,elementProps:h})}return s?r.createElement(nn,{className:l},n):n}function zO(t,e){return null!=e&&"trapezoids"in t.props}function FO(t,e){return null!=e&&"sectors"in t.props}function UO(t,e){return null!=e&&"points"in t.props}function $O(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function WO(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function qO(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function VO(t){var e=t.activeTooltipItem,r=t.graphicalItem,n=t.itemData,o=function(t,e){var r;return zO(t,e)?r="trapezoids":FO(t,e)?r="sectors":UO(t,e)&&(r="points"),r}(r,e),i=function(t,e){var r,n;return zO(t,e)?null===(r=e.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:FO(t,e)?null===(n=e.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:UO(t,e)?e.payload:{}}(r,e),a=n.filter((function(t,n){var a=vv(i,t),u=r.props[o].filter((function(t){var n=function(t,e){var r;return zO(t,e)?r=$O:FO(t,e)?r=WO:UO(t,e)&&(r=qO),r}(r,e);return n(t,e)})),c=r.props[o].indexOf(u[u.length-1]);return a&&n===c}));return n.indexOf(a[a.length-1])}function XO(t){return(XO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function HO(){return HO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},HO.apply(this,arguments)}function GO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function KO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?GO(Object(r),!0).forEach((function(e){ej(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):GO(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function YO(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,rj(n.key),n)}}function ZO(t,e,r){return e=QO(e),function(t,e){if(e&&("object"===XO(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,JO()?Reflect.construct(e,r||[],QO(t).constructor):e.apply(t,r))}function JO(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(JO=function(){return!!t})()}function QO(t){return(QO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tj(t,e){return(tj=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ej(t,e,r){return(e=rj(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rj(t){var e=function(t,e){if("object"!=XO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=XO(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==XO(e)?e:e+""}var nj,oj,ij,aj,uj,cj,lj,sj,fj=function(){function n(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),ej(e=ZO(this,n,[t]),"pieRef",null),ej(e,"sectorRefs",[]),ej(e,"id",rr("recharts-pie-")),ej(e,"handleAnimationEnd",(function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),Re(t)&&t()})),ej(e,"handleAnimationStart",(function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),Re(t)&&t()})),e.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tj(t,e)}(n,e.PureComponent),o=n,a=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,n,o){if(r.isValidElement(e))return r.cloneElement(e,n);if(Re(e))return e(n);var i=t("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return r.createElement(Sg,HO({},n,{key:o,type:"linear",className:i}))}},{key:"renderLabelItem",value:function(e,n,o){if(r.isValidElement(e))return r.cloneElement(e,n);var i=o;if(Re(e)&&(i=e(n),r.isValidElement(i)))return i;var a=t("recharts-pie-label-text","boolean"==typeof e||Re(e)?"":e.className);return r.createElement(pf,HO({},n,{alignmentBaseline:"middle",className:a}),i)}}],(i=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var e=this.props,o=e.label,i=e.labelLine,a=e.dataKey,u=e.valueKey,c=Tr(this.props,!1),l=Tr(o,!1),s=Tr(i,!1),f=o&&o.offsetRadius||20,p=t.map((function(t,e){var p=(t.startAngle+t.endAngle)/2,h=xb(t.cx,t.cy,t.outerRadius+f,p),y=KO(KO(KO(KO({},c),t),{},{stroke:"none"},l),{},{index:e,textAnchor:n.getTextAnchor(h.x,t.cx)},h),d=KO(KO(KO(KO({},c),t),{},{fill:"none",stroke:t.fill},s),{},{index:e,points:[xb(t.cx,t.cy,t.outerRadius,p),h]}),v=a;return De(a)&&De(u)?v="value":De(a)&&(v=u),r.createElement(nn,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(e)},i&&n.renderLabelLineItem(i,d,"line"),n.renderLabelItem(o,y,$m(t,v)))}));return r.createElement(nn,{className:"recharts-pie-labels"},p)}},{key:"renderSectorsStatically",value:function(t){var e=this,n=this.props,o=n.activeShape,i=n.blendStroke,a=n.inactiveShape;return t.map((function(n,u){if(0===(null==n?void 0:n.startAngle)&&0===(null==n?void 0:n.endAngle)&&1!==t.length)return null;var c=e.isActiveIndex(u),l=a&&e.hasActiveIndex()?a:null,s=c?o:l,f=KO(KO({},n),{},{stroke:i?n.fill:n.stroke,tabIndex:-1});return r.createElement(nn,HO({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},dr(e.props,n,u),{key:"sector-".concat(null==n?void 0:n.startAngle,"-").concat(null==n?void 0:n.endAngle,"-").concat(n.midAngle,"-").concat(u)}),r.createElement(LO,HO({option:s,isActive:c,shapeType:"sector"},f)))}))}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,n=e.sectors,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,u=e.animationEasing,c=e.animationId,l=this.state,s=l.prevSectors,f=l.prevIsAnimationActive;return r.createElement(ex,{begin:i,duration:a,isActive:o,easing:u,from:{t:0},to:{t:1},key:"pie-".concat(c,"-").concat(f),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var o=e.t,i=[],a=(n&&n[0]).startAngle;return n.forEach((function(t,e){var r=s&&s[e],n=e>0?Te(t,"paddingAngle",0):0;if(r){var u=ir(r.endAngle-r.startAngle,t.endAngle-t.startAngle),c=KO(KO({},t),{},{startAngle:a+n,endAngle:a+u(o)+n});i.push(c),a=c.endAngle}else{var l=t.endAngle,f=t.startAngle,p=ir(0,l-f)(o),h=KO(KO({},t),{},{startAngle:a+n,endAngle:a+p+n});i.push(h),a=h.endAngle}})),r.createElement(nn,null,t.renderSectorsStatically(i))}))}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return!(r&&e&&e.length)||n&&vv(n,e)?this.renderSectorsStatically(e):this.renderSectorsWithAnimation()}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,n=this.props,o=n.hide,i=n.sectors,a=n.className,u=n.label,c=n.cx,l=n.cy,s=n.innerRadius,f=n.outerRadius,p=n.isAnimationActive,h=this.state.isAnimationFinished;if(o||!i||!i.length||!Qe(c)||!Qe(l)||!Qe(s)||!Qe(f))return null;var y=t("recharts-pie",a);return r.createElement(nn,{tabIndex:this.props.rootTabIndex,className:y,ref:function(t){e.pieRef=t}},this.renderSectors(),u&&this.renderLabels(i),Rb.renderCallByParent(this.props,null,!1),(!p||h)&&Qb.renderCallByParent(this.props,i,!1))}}])&&YO(o.prototype,i),a&&YO(o,a),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,i,a}();function pj(){if(aj)return ij;aj=1;var t=bs(),e=1/0;return ij=function(r){return r?(r=t(r))===e||r===-1/0?17976931348623157e292*(r<0?-1:1):r==r?r:0:0===r?r:0}}function hj(){if(cj)return uj;cj=1;var t=function(){if(oj)return nj;oj=1;var t=Math.ceil,e=Math.max;return nj=function(r,n,o,i){for(var a=-1,u=e(t((n-r)/(o||1)),0),c=Array(u);u--;)c[i?u:++a]=r,r+=o;return c}}(),e=bl(),r=pj();return uj=function(n){return function(o,i,a){return a&&"number"!=typeof a&&e(o,i,a)&&(i=a=void 0),o=r(o),void 0===i?(i=o,o=0):i=r(i),a=void 0===a?o<i?1:-1:r(a),t(o,i,a,n)}}}AO=fj,ej(fj,"displayName","Pie"),ej(fj,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!Xl,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),ej(fj,"parseDeltaAngle",(function(t,e){return Ze(e-t)*Math.min(Math.abs(e-t),360)})),ej(fj,"getRealPieData",(function(t){var e=t.data,r=t.children,n=Tr(t,!1),o=Pr(r,ks);return e&&e.length?e.map((function(t,e){return KO(KO(KO({payload:t},n),t),o&&o[e]&&o[e].props)})):o&&o.length?o.map((function(t){return KO(KO({},n),t.props)})):[]})),ej(fj,"parseCoordinateOfPie",(function(t,e){var r=e.top,n=e.left,o=e.width,i=e.height,a=Ob(o,i);return{cx:n+nr(t.cx,o,o/2),cy:r+nr(t.cy,i,i/2),innerRadius:nr(t.innerRadius,a,0),outerRadius:nr(t.outerRadius,a,.8*a),maxRadius:t.maxRadius||Math.sqrt(o*o+i*i)/2}})),ej(fj,"getComposedData",(function(t){var e=t.item,r=t.offset,n=void 0!==e.type.defaultProps?KO(KO({},e.type.defaultProps),e.props):e.props,o=AO.getRealPieData(n);if(!o||!o.length)return null;var i=n.cornerRadius,a=n.startAngle,u=n.endAngle,c=n.paddingAngle,l=n.dataKey,s=n.nameKey,f=n.valueKey,p=n.tooltipType,h=Math.abs(n.minAngle),y=AO.parseCoordinateOfPie(n,r),d=AO.parseDeltaAngle(a,u),v=Math.abs(d),m=l;De(l)&&De(f)?(on(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),m="value"):De(l)&&(on(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),m=f);var b,g,w=o.filter((function(t){return 0!==$m(t,m,0)})).length,x=v-w*h-(v>=360?w:w-1)*c,O=o.reduce((function(t,e){var r=$m(e,m,0);return t+(Qe(r)?r:0)}),0);O>0&&(b=o.map((function(t,e){var r,n=$m(t,m,0),o=$m(t,s,e),u=(Qe(n)?n:0)/O,l=(r=e?g.endAngle+Ze(d)*c*(0!==n?1:0):a)+Ze(d)*((0!==n?h:0)+u*x),f=(r+l)/2,v=(y.innerRadius+y.outerRadius)/2,b=[{name:o,value:n,payload:t,dataKey:m,type:p}],w=xb(y.cx,y.cy,v,f);return g=KO(KO(KO({percent:u,cornerRadius:i,name:o,tooltipPayload:b,midAngle:f,middleRadius:v,tooltipPosition:w},t),y),{},{value:$m(t,m),startAngle:r,endAngle:l,payload:t,paddingAngle:Ze(d)*c})})));return KO(KO({},y),{},{sectors:b,data:o})}));const yj=o(function(){if(sj)return lj;sj=1;var t=hj()();return lj=t}());function dj(t){return(dj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function vj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function mj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?vj(Object(r),!0).forEach((function(e){bj(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):vj(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function bj(t,e,r){var n;return n=function(t,e){if("object"!=dj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=dj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==dj(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var gj=["Webkit","Moz","O","ms"];function wj(t){return(wj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function xj(){return xj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xj.apply(this,arguments)}function Oj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function jj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Oj(Object(r),!0).forEach((function(e){Mj(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Oj(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Sj(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Tj(n.key),n)}}function Pj(t,e,r){return e=Ej(e),function(t,e){if(e&&("object"===wj(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Aj()?Reflect.construct(e,r||[],Ej(t).constructor):e.apply(t,r))}function Aj(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Aj=function(){return!!t})()}function Ej(t){return(Ej=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function kj(t,e){return(kj=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Mj(t,e,r){return(e=Tj(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Tj(t){var e=function(t,e){if("object"!=wj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=wj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==wj(e)?e:e+""}var _j,Cj,Dj,Ij,Nj=function(t){return t.changedTouches&&!!t.changedTouches.length},Bj=function(){function n(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),Mj(e=Pj(this,n,[t]),"handleDrag",(function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)})),Mj(e,"handleTouchMove",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])})),Mj(e,"handleDragEnd",(function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},(function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})})),e.detachDragEndListener()})),Mj(e,"handleLeaveWrapper",(function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))})),Mj(e,"handleEnterSlideOrTraveller",(function(){e.setState({isTextActive:!0})})),Mj(e,"handleLeaveSlideOrTraveller",(function(){e.setState({isTextActive:!1})})),Mj(e,"handleSlideDragStart",(function(t){var r=Nj(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()})),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&kj(t,e)}(n,e.PureComponent),o=n,a=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,n=t.y,o=t.width,i=t.height,a=t.stroke,u=Math.floor(n+i/2)-1;return r.createElement(r.Fragment,null,r.createElement("rect",{x:e,y:n,width:o,height:i,fill:a,stroke:"none"}),r.createElement("line",{x1:e+1,y1:u,x2:e+o-1,y2:u,fill:"none",stroke:"#fff"}),r.createElement("line",{x1:e+1,y1:u+2,x2:e+o-1,y2:u+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){return r.isValidElement(t)?r.cloneElement(t,e):Re(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,u=t.startIndex,c=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return jj({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var u=e.length,c=Uf().domain(yj(0,u)).range([o,o+i-a]),l=c.domain().map((function(t){return c(t)}));return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:l}}({data:r,width:n,x:o,travellerWidth:i,startIndex:u,endIndex:c}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map((function(t){return e.scale(t)}));return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=0,n=t.length-1;n-r>1;){var o=Math.floor((r+n)/2);t[o]>e?n=o:r=o}return e>=t[n]?n:r}}],(i=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,u=i.data.length-1,c=Math.min(e,r),l=Math.max(e,r),s=n.getIndexInRange(o,c),f=n.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===u?u:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=$m(r[t],o,t);return Re(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,u=i.width,c=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+u-c-o,a+u-c-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});h.startIndex===l&&h.endIndex===s||!f||f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=Nj(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],u=this.props,c=u.x,l=u.width,s=u.travellerWidth,f=u.onChange,p=u.gap,h=u.data,y={startX:this.state.startX,endX:this.state.endX},d=t.pageX-r;d>0?d=Math.min(d,c+l-s-a):d<0&&(d=Math.max(d,c-a)),y[n]=a+d;var v=this.getIndex(y),m=v.startIndex,b=v.endIndex;this.setState(Mj(Mj({},n,a+d),"brushMoveStartX",t.pageX),(function(){var t;f&&(t=h.length-1,("startX"===n&&(o>i?m%p===0:b%p===0)||o<i&&b===t||"endX"===n&&(o>i?b%p===0:m%p===0)||o>i&&b===t)&&f(v))}))}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,u=this.state[e],c=o.indexOf(u);if(-1!==c){var l=c+t;if(!(-1===l||l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(Mj({},e,s),(function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))}))}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,n=t.y,o=t.width,i=t.height,a=t.fill,u=t.stroke;return r.createElement("rect",{stroke:u,fill:a,x:e,y:n,width:o,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,n=t.x,o=t.y,i=t.width,a=t.height,u=t.data,c=t.children,l=t.padding,s=e.Children.only(c);return s?r.cloneElement(s,{x:n,y:o,width:i,height:a,margin:l,compact:!0,data:u}):null}},{key:"renderTravellerLayer",value:function(t,e){var o,i,a=this,u=this.props,c=u.y,l=u.travellerWidth,s=u.height,f=u.traveller,p=u.ariaLabel,h=u.data,y=u.startIndex,d=u.endIndex,v=Math.max(t,this.props.x),m=jj(jj({},Tr(this.props,!1)),{},{x:v,y:c,width:l,height:s}),b=p||"Min value: ".concat(null===(o=h[y])||void 0===o?void 0:o.name,", Max value: ").concat(null===(i=h[d])||void 0===i?void 0:i.name);return r.createElement(nn,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var n=this.props,o=n.y,i=n.height,a=n.stroke,u=n.travellerWidth,c=Math.min(t,e)+u,l=Math.max(Math.abs(e-t)-u,0);return r.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:c,y:o,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,n=t.endIndex,o=t.y,i=t.height,a=t.travellerWidth,u=t.stroke,c=this.state,l=c.startX,s=c.endX,f={pointerEvents:"none",fill:u};return r.createElement(nn,{className:"recharts-brush-texts"},r.createElement(pf,xj({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:o+i/2},f),this.getTextOfTick(e)),r.createElement(pf,xj({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:o+i/2},f),this.getTextOfTick(n)))}},{key:"render",value:function(){var e=this.props,n=e.data,o=e.className,i=e.children,a=e.x,u=e.y,c=e.width,l=e.height,s=e.alwaysShowText,f=this.state,p=f.startX,h=f.endX,y=f.isTextActive,d=f.isSlideMoving,v=f.isTravellerMoving,m=f.isTravellerFocused;if(!n||!n.length||!Qe(a)||!Qe(u)||!Qe(c)||!Qe(l)||c<=0||l<=0)return null;var b,g,w,x,O=t("recharts-brush",o),j=1===r.Children.count(i),S=(g="none",w=(b="userSelect").replace(/(\w)/,(function(t){return t.toUpperCase()})),(x=gj.reduce((function(t,e){return mj(mj({},t),{},bj({},e+w,g))}),{}))[b]=g,x);return r.createElement(nn,{className:O,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:S},this.renderBackground(),j&&this.renderPanorama(),this.renderSlide(p,h),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(h,"endX"),(y||d||v||m||s)&&this.renderText())}}])&&Sj(o.prototype,i),a&&Sj(o,a),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,i,a}();Mj(Bj,"displayName","Brush"),Mj(Bj,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});const Rj=o(function(){if(Ij)return Dj;Ij=1;var t=Di(),e=Yu(),r=function(){if(Cj)return _j;Cj=1;var t=sl();return _j=function(e,r){var n;return t(e,(function(t,e,o){return!(n=r(t,e,o))})),!!n}}(),n=ee(),o=bl();return Dj=function(i,a,u){var c=n(i)?t:r;return u&&o(i,a,u)&&(a=void 0),c(i,e(a,3))}}());var Lj,zj,Fj,Uj,$j=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e};const Wj=o(function(){if(Uj)return Fj;Uj=1;var t=function(){if(zj)return Lj;zj=1;var t=dl();return Lj=function(e,r,n){"__proto__"==r&&t?t(e,r,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[r]=n}}(),e=ll(),r=Yu();return Fj=function(n,o){var i={};return o=r(o,3),e(n,(function(e,r,n){t(i,r,o(e,r,n))})),i}}());var qj,Vj,Xj,Hj,Gj,Kj;const Yj=o(function(){if(Kj)return Gj;Kj=1;var t=Vj?qj:(Vj=1,qj=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}),e=function(){if(Hj)return Xj;Hj=1;var t=sl();return Xj=function(e,r){var n=!0;return t(e,(function(t,e,o){return n=!!r(t,e,o)})),n}}(),r=Yu(),n=ee(),o=bl();return Gj=function(i,a,u){var c=n(i)?t:e;return u&&o(i,a,u)&&(a=void 0),c(i,r(a,3))}}());var Zj=["x","y"];function Jj(t){return(Jj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Qj(){return Qj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Qj.apply(this,arguments)}function tS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function eS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tS(Object(r),!0).forEach((function(e){rS(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tS(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function rS(t,e,r){var n;return n=function(t,e){if("object"!=Jj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=Jj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Jj(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nS(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function oS(t,e){var r=t.x,n=t.y,o=nS(t,Zj),i="".concat(r),a=parseInt(i,10),u="".concat(n),c=parseInt(u,10),l="".concat(e.height||o.height),s=parseInt(l,10),f="".concat(e.width||o.width),p=parseInt(f,10);return eS(eS(eS(eS(eS({},e),o),a?{x:a}:{}),c?{y:c}:{}),{},{height:s,width:p,name:e.name,radius:e.radius})}function iS(t){return r.createElement(LO,Qj({shapeType:"rectangle",propTransformer:oS,activeClassName:"recharts-active-bar"},t))}var aS,uS=["value","background"];function cS(t){return(cS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lS(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function sS(){return sS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},sS.apply(this,arguments)}function fS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function pS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fS(Object(r),!0).forEach((function(e){bS(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fS(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function hS(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,gS(n.key),n)}}function yS(t,e,r){return e=vS(e),function(t,e){if(e&&("object"===cS(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,dS()?Reflect.construct(e,r||[],vS(t).constructor):e.apply(t,r))}function dS(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(dS=function(){return!!t})()}function vS(t){return(vS=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function mS(t,e){return(mS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function bS(t,e,r){return(e=gS(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function gS(t){var e=function(t,e){if("object"!=cS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=cS(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==cS(e)?e:e+""}var wS=function(){function n(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n);for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return bS(t=yS(this,n,[].concat(r)),"state",{isAnimationFinished:!1}),bS(t,"id",rr("recharts-bar-")),bS(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()})),bS(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()})),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&mS(t,e)}(n,e.PureComponent),o=n,a=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],(i=[{key:"renderRectanglesStatically",value:function(t){var e=this,n=this.props,o=n.shape,i=n.dataKey,a=n.activeIndex,u=n.activeBar,c=Tr(this.props,!1);return t&&t.map((function(t,n){var l=n===a,s=l?u:o,f=pS(pS(pS({},c),t),{},{isActive:l,option:s,index:n,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return r.createElement(nn,sS({className:"recharts-bar-rectangle"},dr(e.props,t,n),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(n)}),r.createElement(iS,f))}))}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,n=e.data,o=e.layout,i=e.isAnimationActive,a=e.animationBegin,u=e.animationDuration,c=e.animationEasing,l=e.animationId,s=this.state.prevData;return r.createElement(ex,{begin:a,duration:u,isActive:i,easing:c,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var i=e.t,a=n.map((function(t,e){var r=s&&s[e];if(r){var n=ir(r.x,t.x),a=ir(r.y,t.y),u=ir(r.width,t.width),c=ir(r.height,t.height);return pS(pS({},t),{},{x:n(i),y:a(i),width:u(i),height:c(i)})}if("horizontal"===o){var l=ir(0,t.height)(i);return pS(pS({},t),{},{y:t.y+t.height-l,height:l})}var f=ir(0,t.width)(i);return pS(pS({},t),{},{width:f})}));return r.createElement(nn,null,t.renderRectanglesStatically(a))}))}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&vv(n,e)?this.renderRectanglesStatically(e):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var t=this,e=this.props,n=e.data,o=e.dataKey,i=e.activeIndex,a=Tr(this.props.background,!1);return n.map((function(e,n){e.value;var u=e.background,c=lS(e,uS);if(!u)return null;var l=pS(pS(pS(pS(pS({},c),{},{fill:"#eee"},u),a),dr(t.props,e,n)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:o,index:n,className:"recharts-bar-background-rectangle"});return r.createElement(iS,sS({key:"background-bar-".concat(n),option:t.props.background,isActive:n===i},l))}))}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,o=n.data,i=n.xAxis,a=n.yAxis,u=n.layout,c=Pr(n.children,Tm);if(!c)return null;var l="vertical"===u?o[0].height/2:o[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:$m(t,e)}},f={clipPath:t?"url(#clipPath-".concat(e,")"):null};return r.createElement(nn,f,c.map((function(t){return r.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:u,offset:l,dataPointFormatter:s})})))}},{key:"render",value:function(){var e=this.props,n=e.hide,o=e.data,i=e.className,a=e.xAxis,u=e.yAxis,c=e.left,l=e.top,s=e.width,f=e.height,p=e.isAnimationActive,h=e.background,y=e.id;if(n||!o||!o.length)return null;var d=this.state.isAnimationFinished,v=t("recharts-bar",i),m=a&&a.allowDataOverflow,b=u&&u.allowDataOverflow,g=m||b,w=De(y)?this.id:y;return r.createElement(nn,{className:v},m||b?r.createElement("defs",null,r.createElement("clipPath",{id:"clipPath-".concat(w)},r.createElement("rect",{x:m?c:c-s/2,y:b?l:l-f/2,width:m?s:2*s,height:b?f:2*f}))):null,r.createElement(nn,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(w,")"):null},h?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(g,w),(!p||d)&&Qb.renderCallByParent(this.props,o))}}])&&hS(o.prototype,i),a&&hS(o,a),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,i,a}();function xS(t){return(xS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function OS(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,AS(n.key),n)}}function jS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function SS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?jS(Object(r),!0).forEach((function(e){PS(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):jS(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function PS(t,e,r){return(e=AS(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function AS(t){var e=function(t,e){if("object"!=xS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=xS(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==xS(e)?e:e+""}aS=wS,bS(wS,"displayName","Bar"),bS(wS,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!Xl,animationBegin:0,animationDuration:400,animationEasing:"ease"}),bS(wS,"getComposedData",(function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,u=t.xAxisTicks,c=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null}(n,r);if(!h)return null;var y=e.layout,d=r.type.defaultProps,v=void 0!==d?pS(pS({},d),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,w="horizontal"===y?a:i,x=l?w.scale.domain():null,O=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]}({numericAxis:w}),j=Pr(b,ks),S=f.map((function(t,e){var n,f,p,d,v,b;l?n=function(t,e){if(!e||2!==e.length||!Qe(e[0])||!Qe(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!Qe(t[0])||t[0]<r)&&(o[0]=r),(!Qe(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o}(l[s+e],x):(n=$m(t,m),Array.isArray(n)||(n=[O,n]));var w=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||vm(),e)}}(g,aS.defaultProps.minPointSize)(n[1],e);if("horizontal"===y){var S,P=[a.scale(n[0]),a.scale(n[1])],A=P[0],E=P[1];f=ib({axis:i,ticks:u,bandSize:o,offset:h.offset,entry:t,index:e}),p=null!==(S=null!=E?E:A)&&void 0!==S?S:void 0,d=h.size;var k=A-E;if(v=Number.isNaN(k)?0:k,b={x:f,y:a.y,width:d,height:a.height},Math.abs(w)>0&&Math.abs(v)<Math.abs(w)){var M=Ze(v||w)*(Math.abs(w)-Math.abs(v));p-=M,v+=M}}else{var T=[i.scale(n[0]),i.scale(n[1])],_=T[0],C=T[1];if(f=_,p=ib({axis:a,ticks:c,bandSize:o,offset:h.offset,entry:t,index:e}),d=C-_,v=h.size,b={x:i.x,y:p,width:i.width,height:v},Math.abs(w)>0&&Math.abs(d)<Math.abs(w))d+=Ze(d||w)*(Math.abs(w)-Math.abs(d))}return pS(pS(pS({},t),{},{x:f,y:p,width:d,height:v,value:l?n:n[1],payload:t,background:b},j&&j[e]&&j[e].props),{},{tooltipPayload:[pb(r,t)],tooltipPosition:{x:f+d/2,y:p+v/2}})}));return pS({data:S,layout:y},p)}));var ES=function(t,e,r,n,o){var i=t.width,a=t.height,u=t.layout,c=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!Ar(c,wS);return l.reduce((function(i,a){var c,l,p,h,y,d=e[a],v=d.orientation,m=d.domain,b=d.padding,g=void 0===b?{}:b,w=d.mirror,x=d.reversed,O="".concat(v).concat(w?"Mirror":"");if("number"===d.type&&("gap"===d.padding||"no-gap"===d.padding)){var j=m[1]-m[0],S=1/0,P=d.categoricalDomain.sort(ur);if(P.forEach((function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))})),Number.isFinite(S)){var A=S/j,E="vertical"===d.layout?r.height:r.width;if("gap"===d.padding&&(c=A*E/2),"no-gap"===d.padding){var k=nr(t.barCategoryGap,A*E),M=A*E/2;c=M-k-(M-k)/E*k}}}l="xAxis"===n?[r.left+(g.left||0)+(c||0),r.left+r.width-(g.right||0)-(c||0)]:"yAxis"===n?"horizontal"===u?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(c||0),r.top+r.height-(g.bottom||0)-(c||0)]:d.range,x&&(l=[l[1],l[0]]);var T=Jm(d,o,f),_=T.scale,C=T.realScaleType;_.domain(m).range(l),tb(_);var D=nb(_,SS(SS({},d),{},{realScaleType:C}));"xAxis"===n?(y="top"===v&&!w||"bottom"===v&&w,p=r.left,h=s[O]-y*d.height):"yAxis"===n&&(y="left"===v&&!w||"right"===v&&w,p=s[O]-y*d.width,h=r.top);var I=SS(SS(SS({},d),D),{},{realScaleType:C,x:p,y:h,scale:_,width:"xAxis"===n?r.width:d.width,height:"yAxis"===n?r.height:d.height});return I.bandSize=sb(I,D),d.hide||"xAxis"!==n?d.hide||(s[O]+=(y?-1:1)*I.width):s[O]+=(y?-1:1)*I.height,SS(SS({},i),{},PS({},a,I))}),{})},kS=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},MS=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scale=e}return e=t,r=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],n=[{key:"create",value:function(e){return new t(e)}}],r&&OS(e.prototype,r),n&&OS(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();PS(MS,"EPS",1e-4);var TS=function(t){var e=Object.keys(t).reduce((function(e,r){return SS(SS({},e),{},PS({},r,MS.create(t[r])))}),{});return SS(SS({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return Wj(t,(function(t,r){return e[r].apply(t,{bandAware:n,position:o})}))},isInRange:function(t){return Yj(t,(function(t,r){return e[r].isInRange(t)}))}})};var _S,CS,DS,IS,NS,BS,RS,LS;function zS(){if(BS)return NS;BS=1;var t=Zu(),e=Yu(),r=function(){if(IS)return DS;IS=1;var t=pj();return DS=function(e){var r=t(e),n=r%1;return r==r?n?r-n:r:0}}(),n=Math.max;return NS=function(o,i,a){var u=null==o?0:o.length;if(!u)return-1;var c=null==a?0:r(a);return c<0&&(c=n(u+c,0)),t(o,e(i,3),c)}}const FS=o(function(){if(LS)return RS;LS=1;var t=function(){if(CS)return _S;CS=1;var t=Yu(),e=Ru(),r=Lu();return _S=function(n){return function(o,i,a){var u=Object(o);if(!e(o)){var c=t(i,3);o=r(o),i=function(t){return c(u[t],t,u)}}var l=n(o,i,a);return l>-1?u[c?o[l]:l]:void 0}}}()(zS());return RS=t}());var US=o(Oe())((function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}}),(function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")})),$S=e.createContext(void 0),WS=e.createContext(void 0),qS=e.createContext(void 0),VS=e.createContext({}),XS=e.createContext(void 0),HS=e.createContext(0),GS=e.createContext(0),KS=function(t){var e=t.state,n=e.xAxisMap,o=e.yAxisMap,i=e.offset,a=t.clipPathId,u=t.children,c=t.width,l=t.height,s=US(i);return r.createElement($S.Provider,{value:n},r.createElement(WS.Provider,{value:o},r.createElement(VS.Provider,{value:i},r.createElement(qS.Provider,{value:s},r.createElement(XS.Provider,{value:a},r.createElement(HS.Provider,{value:l},r.createElement(GS.Provider,{value:c},u)))))))},YS=function(t){var r=e.useContext($S);null==r&&vm();var n=r[t];return null==n&&vm(),n},ZS=function(t){var r=e.useContext(WS);null==r&&vm();var n=r[t];return null==n&&vm(),n},JS=function(){return e.useContext(GS)},QS=function(){return e.useContext(HS)};function tP(t){return(tP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eP(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lP(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function rP(t,e,r){return e=oP(e),function(t,e){if(e&&("object"===tP(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,nP()?Reflect.construct(e,r||[],oP(t).constructor):e.apply(t,r))}function nP(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(nP=function(){return!!t})()}function oP(t){return(oP=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function iP(t,e){return(iP=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function aP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function uP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?aP(Object(r),!0).forEach((function(e){cP(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):aP(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function cP(t,e,r){return(e=lP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lP(t){var e=function(t,e){if("object"!=tP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=tP(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==tP(e)?e:e+""}function sP(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return fP(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fP(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fP(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function pP(){return pP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},pP.apply(this,arguments)}function hP(n){var o=n.x,i=n.y,a=n.segment,u=n.xAxisId,c=n.yAxisId,l=n.shape,s=n.className,f=n.alwaysShow,p=e.useContext(XS),h=YS(u),y=ZS(c),d=e.useContext(qS);if(!p||!d)return null;on(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=function(t,e,r,n,o,i,a,u,c){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=c.y,y=t.y.apply(h,{position:i});if($j(c,"discard")&&!t.y.isInRange(y))return null;var d=[{x:l+f,y:y},{x:l,y:y}];return"left"===u?d.reverse():d}if(e){var v=c.x,m=t.x.apply(v,{position:i});if($j(c,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=c.segment.map((function(e){return t.apply(e,{position:i})}));return $j(c,"discard")&&Rj(g,(function(e){return!t.isInRange(e)}))?null:g}return null}(TS({x:h.scale,y:y.scale}),tr(o),tr(i),a&&2===a.length,d,n.position,h.orientation,y.orientation,n);if(!v)return null;var m=sP(v,2),b=m[0],g=b.x,w=b.y,x=m[1],O=x.x,j=x.y,S=uP(uP({clipPath:$j(n,"hidden")?"url(#".concat(p,")"):void 0},Tr(n,!0)),{},{x1:g,y1:w,x2:O,y2:j});return r.createElement(nn,{className:t("recharts-reference-line",s)},function(t,e){return r.isValidElement(t)?r.cloneElement(t,e):Re(t)?t(e):r.createElement("line",pP({},e,{className:"recharts-reference-line-line"}))}(l,S),Rb.renderCallByParent(n,function(t){var e=t.x1,r=t.y1,n=t.x2,o=t.y2;return kS({x:e,y:r},{x:n,y:o})}({x1:g,y1:w,x2:O,y2:j})))}var yP=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),rP(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&iP(t,e)}(t,r.Component),eP(t,[{key:"render",value:function(){return r.createElement(hP,this.props)}}])}();function dP(){return dP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},dP.apply(this,arguments)}function vP(t){return(vP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function mP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function bP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?mP(Object(r),!0).forEach((function(e){SP(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):mP(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function gP(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,PP(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function wP(t,e,r){return e=OP(e),function(t,e){if(e&&("object"===vP(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,xP()?Reflect.construct(e,r||[],OP(t).constructor):e.apply(t,r))}function xP(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(xP=function(){return!!t})()}function OP(t){return(OP=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function jP(t,e){return(jP=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function SP(t,e,r){return(e=PP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function PP(t){var e=function(t,e){if("object"!=vP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=vP(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==vP(e)?e:e+""}cP(yP,"displayName","ReferenceLine"),cP(yP,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var AP=function(){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),wP(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&jP(t,e)}(e,r.Component),gP(e,[{key:"render",value:function(){var n=this.props,o=n.x,i=n.y,a=n.r,u=n.alwaysShow,c=n.clipPathId,l=tr(o),s=tr(i);if(on(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!l||!s)return null;var f=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=TS({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return $j(t,"discard")&&!i.isInRange(a)?null:a}(this.props);if(!f)return null;var p=f.x,h=f.y,y=this.props,d=y.shape,v=y.className,m=bP(bP({clipPath:$j(this.props,"hidden")?"url(#".concat(c,")"):void 0},Tr(this.props,!0)),{},{cx:p,cy:h});return r.createElement(nn,{className:t("recharts-reference-dot",v)},e.renderDot(d,m),Rb.renderCallByParent(this.props,{x:p-a,y:h-a,width:2*a,height:2*a}))}}])}();function EP(){return EP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},EP.apply(this,arguments)}function kP(t){return(kP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function MP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function TP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?MP(Object(r),!0).forEach((function(e){BP(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):MP(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function _P(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,RP(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function CP(t,e,r){return e=IP(e),function(t,e){if(e&&("object"===kP(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,DP()?Reflect.construct(e,r||[],IP(t).constructor):e.apply(t,r))}function DP(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(DP=function(){return!!t})()}function IP(t){return(IP=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function NP(t,e){return(NP=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function BP(t,e,r){return(e=RP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function RP(t){var e=function(t,e){if("object"!=kP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=kP(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==kP(e)?e:e+""}SP(AP,"displayName","ReferenceDot"),SP(AP,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),SP(AP,"renderDot",(function(t,e){return r.isValidElement(t)?r.cloneElement(t,e):Re(t)?t(e):r.createElement(Ox,dP({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))}));var LP=function(){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),CP(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&NP(t,e)}(e,r.Component),_P(e,[{key:"render",value:function(){var n=this.props,o=n.x1,i=n.x2,a=n.y1,u=n.y2,c=n.className,l=n.alwaysShow,s=n.clipPathId;on(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var f=tr(o),p=tr(i),h=tr(a),y=tr(u),d=this.props.shape;if(!(f||p||h||y||d))return null;var v=function(t,e,r,n,o){var i=o.x1,a=o.x2,u=o.y1,c=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=TS({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(u,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!$j(o,"discard")||f.isInRange(p)&&f.isInRange(h)?kS(p,h):null}(f,p,h,y,this.props);if(!v&&!d)return null;var m=$j(this.props,"hidden")?"url(#".concat(s,")"):void 0;return r.createElement(nn,{className:t("recharts-reference-area",c)},e.renderRect(d,TP(TP({clipPath:m},Tr(this.props,!0)),v)),Rb.renderCallByParent(this.props,v))}}])}();function zP(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e)n.push(t[o]);return n}function FP(t,e,r){return function(t){var e=t.width,r=t.height,n=function(t){return(t%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),o=n*Math.PI/180,i=Math.atan(r/e),a=o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o);return Math.abs(a)}({width:t.width+e.width,height:t.height+e.height},r)}function UP(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function $P(t){return($P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function WP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function qP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?WP(Object(r),!0).forEach((function(e){VP(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):WP(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function VP(t,e,r){var n;return n=function(t,e){if("object"!=$P(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=$P(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==$P(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function XP(t,e,r){var n=t.tick,o=t.ticks,i=t.viewBox,a=t.minTickGap,u=t.orientation,c=t.interval,l=t.tickFormatter,s=t.unit,f=t.angle;if(!o||!o.length||!n)return[];if(Qe(c)||Xl)return function(t,e){return zP(t,e+1)}(o,"number"==typeof c&&Qe(c)?c:0);var p=[],h="top"===u||"bottom"===u?"width":"height",y=s&&"width"===h?Bs(s,{fontSize:e,letterSpacing:r}):{width:0,height:0},d=function(t,n){var o=Re(l)?l(t.value,n):t.value;return"width"===h?FP(Bs(o,{fontSize:e,letterSpacing:r}),y,f):Bs(o,{fontSize:e,letterSpacing:r})[h]},v=o.length>=2?Ze(o[1].coordinate-o[0].coordinate):1,m=function(t,e,r){var n="width"===r,o=t.x,i=t.y,a=t.width,u=t.height;return 1===e?{start:n?o:i,end:n?o+a:i+u}:{start:n?o+a:i+u,end:n?o:i}}(i,v,h);return"equidistantPreserveStart"===c?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),u=e.start,c=e.end,l=0,s=1,f=u,p=function(){var e=null==n?void 0:n[l];if(void 0===e)return{v:zP(n,s)};var i,a=l,p=function(){return void 0===i&&(i=r(e,a)),i},h=e.coordinate,y=0===l||UP(t,h,p,f,c);y||(l=0,f=u,s+=1),y&&(f=h+t*(p()/2+o),l+=s)};s<=a.length;)if(i=p())return i.v;return[]}(v,m,d,o,a):(p="preserveStart"===c||"preserveStartEnd"===c?function(t,e,r,n,o,i){var a=(n||[]).slice(),u=a.length,c=e.start,l=e.end;if(i){var s=n[u-1],f=r(s,u-1),p=t*(s.coordinate+t*f/2-l);a[u-1]=s=qP(qP({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),UP(t,s.tickCoord,(function(){return f}),c,l)&&(l=s.tickCoord-t*(f/2+o),a[u-1]=qP(qP({},s),{},{isShow:!0}))}for(var h=i?u-1:u,y=function(e){var n,i=a[e],u=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*u()/2-c);a[e]=i=qP(qP({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=qP(qP({},i),{},{tickCoord:i.coordinate});UP(t,i.tickCoord,u,c,l)&&(c=i.tickCoord+t*(u()/2+o),a[e]=qP(qP({},i),{},{isShow:!0}))},d=0;d<h;d++)y(d);return a}(v,m,d,o,a,"preserveStartEnd"===c):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,u=e.start,c=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-c);i[e]=l=qP(qP({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=qP(qP({},l),{},{tickCoord:l.coordinate});UP(t,l.tickCoord,s,u,c)&&(c=l.tickCoord-t*(s()/2+o),i[e]=qP(qP({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(v,m,d,o,a),p.filter((function(t){return t.isShow})))}BP(LP,"displayName","ReferenceArea"),BP(LP,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),BP(LP,"renderRect",(function(t,e){return r.isValidElement(t)?r.cloneElement(t,e):Re(t)?t(e):r.createElement(px,EP({},e,{className:"recharts-reference-area-rect"}))}));var HP=["viewBox"],GP=["viewBox"],KP=["ticks"];function YP(t){return(YP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ZP(){return ZP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ZP.apply(this,arguments)}function JP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function QP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?JP(Object(r),!0).forEach((function(e){aA(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):JP(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function tA(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function eA(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uA(n.key),n)}}function rA(t,e,r){return e=oA(e),function(t,e){if(e&&("object"===YP(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,nA()?Reflect.construct(e,r||[],oA(t).constructor):e.apply(t,r))}function nA(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(nA=function(){return!!t})()}function oA(t){return(oA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function iA(t,e){return(iA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function aA(t,e,r){return(e=uA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uA(t){var e=function(t,e){if("object"!=YP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=YP(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==YP(e)?e:e+""}var cA=function(){function n(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),(e=rA(this,n,[t])).state={fontSize:"",letterSpacing:""},e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&iA(t,e)}(n,e.Component),o=n,a=[{key:"renderTickItem",value:function(t,e,n){return r.isValidElement(t)?r.cloneElement(t,e):Re(t)?t(e):r.createElement(pf,ZP({},e,{className:"recharts-cartesian-axis-tick-value"}),n)}}],(i=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=tA(t,HP),o=this.props,i=o.viewBox,a=tA(o,GP);return!cr(r,i)||!cr(n,a)||!cr(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,u=this.props,c=u.x,l=u.y,s=u.width,f=u.height,p=u.orientation,h=u.tickSize,y=u.mirror,d=u.tickMargin,v=y?-1:1,m=t.tickSize||h,b=Qe(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!y*f)-v*m)-v*d,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=c+ +!y*s)-v*m)-v*d,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=c+ +y*s)+v*m)+v*d,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+ +y*f)+v*m)+v*d,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var e=this.props,n=e.x,o=e.y,i=e.width,a=e.height,u=e.orientation,c=e.mirror,l=e.axisLine,s=QP(QP(QP({},Tr(this.props,!1)),Tr(l,!1)),{},{fill:"none"});if("top"===u||"bottom"===u){var f=+("top"===u&&!c||"bottom"===u&&c);s=QP(QP({},s),{},{x1:n,y1:o+f*a,x2:n+i,y2:o+f*a})}else{var p=+("left"===u&&!c||"right"===u&&c);s=QP(QP({},s),{},{x1:n+p*i,y1:o,x2:n+p*i,y2:o+a})}return r.createElement("line",ZP({},s,{className:t("recharts-cartesian-axis-line",Te(l,"className"))}))}},{key:"renderTicks",value:function(e,o,i){var a=this,u=this.props,c=u.tickLine,l=u.stroke,s=u.tick,f=u.tickFormatter,p=u.unit,h=XP(QP(QP({},this.props),{},{ticks:e}),o,i),y=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),v=Tr(this.props,!1),m=Tr(s,!1),b=QP(QP({},v),{},{fill:"none"},Tr(c,!1)),g=h.map((function(e,o){var i=a.getTickLineCoord(e),u=i.line,g=i.tick,w=QP(QP(QP(QP({textAnchor:y,verticalAnchor:d},v),{},{stroke:"none",fill:l},m),g),{},{index:o,payload:e,visibleTicksCount:h.length,tickFormatter:f});return r.createElement(nn,ZP({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},dr(a.props,e,o)),c&&r.createElement("line",ZP({},b,u,{className:t("recharts-cartesian-axis-tick-line",Te(c,"className"))})),s&&n.renderTickItem(s,w,"".concat(Re(f)?f(e.value,o):e.value).concat(p||"")))}));return r.createElement("g",{className:"recharts-cartesian-axis-ticks"},g)}},{key:"render",value:function(){var e=this,n=this.props,o=n.axisLine,i=n.width,a=n.height,u=n.ticksGenerator,c=n.className;if(n.hide)return null;var l=this.props,s=l.ticks,f=tA(l,KP),p=s;return Re(u)&&(p=s&&s.length>0?u(this.props):u(f)),i<=0||a<=0||!p||!p.length?null:r.createElement(nn,{className:t("recharts-cartesian-axis",c),ref:function(t){e.layerReference=t}},o&&this.renderAxisLine(),this.renderTicks(p,this.state.fontSize,this.state.letterSpacing),Rb.renderCallByParent(this.props))}}])&&eA(o.prototype,i),a&&eA(o,a),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,i,a}();aA(cA,"displayName","CartesianAxis"),aA(cA,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var lA=["x1","y1","x2","y2","key"],sA=["offset"];function fA(t){return(fA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pA(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function hA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pA(Object(r),!0).forEach((function(e){yA(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pA(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function yA(t,e,r){var n;return n=function(t,e){if("object"!=fA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=fA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==fA(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dA(){return dA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},dA.apply(this,arguments)}function vA(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var mA=function(t){var e=t.fill;if(!e||"none"===e)return null;var n=t.fillOpacity,o=t.x,i=t.y,a=t.width,u=t.height,c=t.ry;return r.createElement("rect",{x:o,y:i,ry:c,width:a,height:u,stroke:"none",fill:e,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function bA(t,e){var n;if(r.isValidElement(t))n=r.cloneElement(t,e);else if(Re(t))n=t(e);else{var o=e.x1,i=e.y1,a=e.x2,u=e.y2,c=e.key,l=vA(e,lA),s=Tr(l,!1);s.offset;var f=vA(s,sA);n=r.createElement("line",dA({},f,{x1:o,y1:i,x2:a,y2:u,fill:"none",key:c}))}return n}function gA(t){var e=t.x,n=t.width,o=t.horizontal,i=void 0===o||o,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var u=a.map((function(r,o){var a=hA(hA({},t),{},{x1:e,y1:r,x2:e+n,y2:r,key:"line-".concat(o),index:o});return bA(i,a)}));return r.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function wA(t){var e=t.y,n=t.height,o=t.vertical,i=void 0===o||o,a=t.verticalPoints;if(!i||!a||!a.length)return null;var u=a.map((function(r,o){var a=hA(hA({},t),{},{x1:r,y1:e,x2:r,y2:e+n,key:"line-".concat(o),index:o});return bA(i,a)}));return r.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function xA(t){var e=t.horizontalFill,n=t.fillOpacity,o=t.x,i=t.y,a=t.width,u=t.height,c=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=c.map((function(t){return Math.round(t+i-i)})).sort((function(t,e){return t-e}));i!==s[0]&&s.unshift(0);var f=s.map((function(t,c){var l=!s[c+1]?i+u-t:s[c+1]-t;if(l<=0)return null;var f=c%e.length;return r.createElement("rect",{key:"react-".concat(c),y:t,x:o,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})}));return r.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function OA(t){var e=t.vertical,n=void 0===e||e,o=t.verticalFill,i=t.fillOpacity,a=t.x,u=t.y,c=t.width,l=t.height,s=t.verticalPoints;if(!n||!o||!o.length)return null;var f=s.map((function(t){return Math.round(t+a-a)})).sort((function(t,e){return t-e}));a!==f[0]&&f.unshift(0);var p=f.map((function(t,e){var n=!f[e+1]?a+c-t:f[e+1]-t;if(n<=0)return null;var s=e%o.length;return r.createElement("rect",{key:"react-".concat(e),x:t,y:u,width:n,height:l,stroke:"none",fill:o[s],fillOpacity:i,className:"recharts-cartesian-grid-bg"})}));return r.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},p)}var jA=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return Gm(XP(hA(hA(hA({},cA.defaultProps),r),{},{ticks:Km(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},SA=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return Gm(XP(hA(hA(hA({},cA.defaultProps),r),{},{ticks:Km(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},PA={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function AA(t){var n,o,i,a,u,c,l,s,f=JS(),p=QS(),h=e.useContext(VS),y=hA(hA({},t),{},{stroke:null!==(n=t.stroke)&&void 0!==n?n:PA.stroke,fill:null!==(o=t.fill)&&void 0!==o?o:PA.fill,horizontal:null!==(i=t.horizontal)&&void 0!==i?i:PA.horizontal,horizontalFill:null!==(a=t.horizontalFill)&&void 0!==a?a:PA.horizontalFill,vertical:null!==(u=t.vertical)&&void 0!==u?u:PA.vertical,verticalFill:null!==(c=t.verticalFill)&&void 0!==c?c:PA.verticalFill,x:Qe(t.x)?t.x:h.left,y:Qe(t.y)?t.y:h.top,width:Qe(t.width)?t.width:h.width,height:Qe(t.height)?t.height:h.height}),d=y.x,v=y.y,m=y.width,b=y.height,g=y.syncWithTicks,w=y.horizontalValues,x=y.verticalValues,O=(l=e.useContext($S),or(l)),j=(s=e.useContext(WS),FS(s,(function(t){return Yj(t.domain,Number.isFinite)}))||or(s));if(!Qe(m)||m<=0||!Qe(b)||b<=0||!Qe(d)||d!==+d||!Qe(v)||v!==+v)return null;var S=y.verticalCoordinatesGenerator||jA,P=y.horizontalCoordinatesGenerator||SA,A=y.horizontalPoints,E=y.verticalPoints;if((!A||!A.length)&&Re(P)){var k=w&&w.length,M=P({yAxis:j?hA(hA({},j),{},{ticks:k?w:j.ticks}):void 0,width:f,height:p,offset:h},!!k||g);on(Array.isArray(M),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(fA(M),"]")),Array.isArray(M)&&(A=M)}if((!E||!E.length)&&Re(S)){var T=x&&x.length,_=S({xAxis:O?hA(hA({},O),{},{ticks:T?x:O.ticks}):void 0,width:f,height:p,offset:h},!!T||g);on(Array.isArray(_),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(fA(_),"]")),Array.isArray(_)&&(E=_)}return r.createElement("g",{className:"recharts-cartesian-grid"},r.createElement(mA,{fill:y.fill,fillOpacity:y.fillOpacity,x:y.x,y:y.y,width:y.width,height:y.height,ry:y.ry}),r.createElement(gA,dA({},y,{offset:h,horizontalPoints:A,xAxis:O,yAxis:j})),r.createElement(wA,dA({},y,{offset:h,verticalPoints:E,xAxis:O,yAxis:j})),r.createElement(xA,dA({},y,{horizontalPoints:A})),r.createElement(OA,dA({},y,{verticalPoints:E})))}AA.displayName="CartesianGrid";var EA=["type","layout","connectNulls","ref"],kA=["key"];function MA(t){return(MA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function TA(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function _A(){return _A=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},_A.apply(this,arguments)}function CA(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function DA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?CA(Object(r),!0).forEach((function(e){UA(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):CA(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function IA(t){return function(t){if(Array.isArray(t))return NA(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return NA(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return NA(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function NA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function BA(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,$A(n.key),n)}}function RA(t,e,r){return e=zA(e),function(t,e){if(e&&("object"===MA(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,LA()?Reflect.construct(e,r||[],zA(t).constructor):e.apply(t,r))}function LA(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(LA=function(){return!!t})()}function zA(t){return(zA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function FA(t,e){return(FA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function UA(t,e,r){return(e=$A(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function $A(t){var e=function(t,e){if("object"!=MA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=MA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==MA(e)?e:e+""}var WA=function(){function n(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n);for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return UA(t=RA(this,n,[].concat(r)),"state",{isAnimationFinished:!0,totalLength:0}),UA(t,"generateSimpleStrokeDasharray",(function(t,e){return"".concat(e,"px ").concat(t-e,"px")})),UA(t,"getStrokeDasharray",(function(e,r,o){var i=o.reduce((function(t,e){return t+e}));if(!i)return t.generateSimpleStrokeDasharray(r,e);for(var a=Math.floor(e/i),u=e%i,c=r-e,l=[],s=0,f=0;s<o.length;f+=o[s],++s)if(f+o[s]>u){l=[].concat(IA(o.slice(0,s)),[u-f]);break}var p=l.length%2==0?[0,c]:[c];return[].concat(IA(n.repeat(o,a)),IA(l),p).map((function(t){return"".concat(t,"px")})).join(", ")})),UA(t,"id",rr("recharts-line-")),UA(t,"pathRef",(function(e){t.mainCurve=e})),UA(t,"handleAnimationEnd",(function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()})),UA(t,"handleAnimationStart",(function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()})),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&FA(t,e)}(n,e.PureComponent),o=n,a=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(IA(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(IA(n),IA(r));return n}},{key:"renderDotItem",value:function(e,n){var o;if(r.isValidElement(e))o=r.cloneElement(e,n);else if(Re(e))o=e(n);else{var i=n.key,a=TA(n,kA),u=t("recharts-line-dot","boolean"!=typeof e?e.className:"");o=r.createElement(Ox,_A({key:i},a,{className:u}))}return o}}],(i=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,o=n.points,i=n.xAxis,a=n.yAxis,u=n.layout,c=Pr(n.children,Tm);if(!c)return null;var l=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:$m(t.payload,e)}},s={clipPath:t?"url(#clipPath-".concat(e,")"):null};return r.createElement(nn,s,c.map((function(t){return r.cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:u,dataPointFormatter:l})})))}},{key:"renderDots",value:function(t,e,o){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,a=i.dot,u=i.points,c=i.dataKey,l=Tr(this.props,!1),s=Tr(a,!0),f=u.map((function(t,e){var r=DA(DA(DA({key:"dot-".concat(e),r:3},l),s),{},{index:e,cx:t.x,cy:t.y,value:t.value,dataKey:c,payload:t.payload,points:u});return n.renderDotItem(a,r)})),p={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(o,")"):null};return r.createElement(nn,_A({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,n,o){var i=this.props,a=i.type,u=i.layout,c=i.connectNulls;i.ref;var l=TA(i,EA),s=DA(DA(DA({},Tr(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(n,")"):null,points:t},o),{},{type:a,layout:u,connectNulls:c});return r.createElement(Sg,_A({},s,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var n=this,o=this.props,i=o.points,a=o.strokeDasharray,u=o.isAnimationActive,c=o.animationBegin,l=o.animationDuration,s=o.animationEasing,f=o.animationId,p=o.animateNewValues,h=o.width,y=o.height,d=this.state,v=d.prevPoints,m=d.totalLength;return r.createElement(ex,{begin:c,duration:l,isActive:u,easing:s,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(r){var o=r.t;if(v){var u=v.length/i.length,c=i.map((function(t,e){var r=Math.floor(e*u);if(v[r]){var n=v[r],i=ir(n.x,t.x),a=ir(n.y,t.y);return DA(DA({},t),{},{x:i(o),y:a(o)})}if(p){var c=ir(2*h,t.x),l=ir(y/2,t.y);return DA(DA({},t),{},{x:c(o),y:l(o)})}return DA(DA({},t),{},{x:t.x,y:t.y})}));return n.renderCurveStatically(c,t,e)}var l,s=ir(0,m)(o);if(a){var f="".concat(a).split(/[,\s]+/gim).map((function(t){return parseFloat(t)}));l=n.getStrokeDasharray(s,m,f)}else l=n.generateSimpleStrokeDasharray(m,s);return n.renderCurveStatically(i,t,e,{strokeDasharray:l})}))}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,u=i.totalLength;return o&&n&&n.length&&(!a&&u>0||!vv(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var e,n=this.props,o=n.hide,i=n.dot,a=n.points,u=n.className,c=n.xAxis,l=n.yAxis,s=n.top,f=n.left,p=n.width,h=n.height,y=n.isAnimationActive,d=n.id;if(o||!a||!a.length)return null;var v=this.state.isAnimationFinished,m=1===a.length,b=t("recharts-line",u),g=c&&c.allowDataOverflow,w=l&&l.allowDataOverflow,x=g||w,O=De(d)?this.id:d,j=null!==(e=Tr(i,!1))&&void 0!==e?e:{r:3,strokeWidth:2},S=j.r,P=void 0===S?3:S,A=j.strokeWidth,E=void 0===A?2:A,k=(Mr(i)?i:{}).clipDot,M=void 0===k||k,T=2*P+E;return r.createElement(nn,{className:b},g||w?r.createElement("defs",null,r.createElement("clipPath",{id:"clipPath-".concat(O)},r.createElement("rect",{x:g?f:f-p/2,y:w?s:s-h/2,width:g?p:2*p,height:w?h:2*h})),!M&&r.createElement("clipPath",{id:"clipPath-dots-".concat(O)},r.createElement("rect",{x:f-T/2,y:s-T/2,width:p+T,height:h+T}))):null,!m&&this.renderCurve(x,O),this.renderErrorBar(x,O),(m||i)&&this.renderDots(x,M,O),(!y||v)&&Qb.renderCallByParent(this.props,a))}}])&&BA(o.prototype,i),a&&BA(o,a),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,i,a}();UA(WA,"displayName","Line"),UA(WA,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!Xl,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),UA(WA,"getComposedData",(function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,u=t.bandSize,c=t.displayedData,l=t.offset,s=e.layout;return DA({points:c.map((function(t,e){var c=$m(t,a);return"horizontal"===s?{x:ob({axis:r,ticks:o,bandSize:u,entry:t,index:e}),y:De(c)?null:n.scale(c),value:c,payload:t}:{x:De(c)?null:r.scale(c),y:ob({axis:n,ticks:i,bandSize:u,entry:t,index:e}),value:c,payload:t}})),layout:s},l)}));var qA,VA=["layout","type","stroke","connectNulls","isRange","ref"],XA=["key"];function HA(t){return(HA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function GA(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function KA(){return KA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},KA.apply(this,arguments)}function YA(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ZA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?YA(Object(r),!0).forEach((function(e){nE(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):YA(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function JA(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,oE(n.key),n)}}function QA(t,e,r){return e=eE(e),function(t,e){if(e&&("object"===HA(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,tE()?Reflect.construct(e,r||[],eE(t).constructor):e.apply(t,r))}function tE(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(tE=function(){return!!t})()}function eE(t){return(eE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function rE(t,e){return(rE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nE(t,e,r){return(e=oE(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function oE(t){var e=function(t,e){if("object"!=HA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=HA(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==HA(e)?e:e+""}var iE=function(){function n(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n);for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return nE(t=QA(this,n,[].concat(r)),"state",{isAnimationFinished:!0}),nE(t,"id",rr("recharts-area-")),nE(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),Re(e)&&e()})),nE(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),Re(e)&&e()})),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&rE(t,e)}(n,e.PureComponent),o=n,a=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],(i=[{key:"renderDots",value:function(t,e,o){var i=this.props.isAnimationActive,a=this.state.isAnimationFinished;if(i&&!a)return null;var u=this.props,c=u.dot,l=u.points,s=u.dataKey,f=Tr(this.props,!1),p=Tr(c,!0),h=l.map((function(t,e){var r=ZA(ZA(ZA({key:"dot-".concat(e),r:3},f),p),{},{index:e,cx:t.x,cy:t.y,dataKey:s,value:t.value,payload:t.payload,points:l});return n.renderDotItem(c,r)})),y={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(o,")"):null};return r.createElement(nn,KA({className:"recharts-area-dots"},y),h)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,n=e.baseLine,o=e.points,i=e.strokeWidth,a=o[0].x,u=o[o.length-1].x,c=t*Math.abs(a-u),l=rv(o.map((function(t){return t.y||0})));return Qe(n)&&"number"==typeof n?l=Math.max(n,l):n&&Array.isArray(n)&&n.length&&(l=Math.max(rv(n.map((function(t){return t.y||0}))),l)),Qe(l)?r.createElement("rect",{x:a<u?a:a-c,y:0,width:c,height:Math.floor(l+(i?parseInt("".concat(i),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,n=e.baseLine,o=e.points,i=e.strokeWidth,a=o[0].y,u=o[o.length-1].y,c=t*Math.abs(a-u),l=rv(o.map((function(t){return t.x||0})));return Qe(n)&&"number"==typeof n?l=Math.max(n,l):n&&Array.isArray(n)&&n.length&&(l=Math.max(rv(n.map((function(t){return t.x||0}))),l)),Qe(l)?r.createElement("rect",{x:0,y:a<u?a:a-c,width:l+(i?parseInt("".concat(i),10):1),height:Math.floor(c)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,n,o){var i=this.props,a=i.layout,u=i.type,c=i.stroke,l=i.connectNulls,s=i.isRange;i.ref;var f=GA(i,VA);return r.createElement(nn,{clipPath:n?"url(#clipPath-".concat(o,")"):null},r.createElement(Sg,KA({},Tr(f,!0),{points:t,connectNulls:l,type:u,baseLine:e,layout:a,stroke:"none",className:"recharts-area-area"})),"none"!==c&&r.createElement(Sg,KA({},Tr(this.props,!1),{className:"recharts-area-curve",layout:a,type:u,connectNulls:l,fill:"none",points:t})),"none"!==c&&s&&r.createElement(Sg,KA({},Tr(this.props,!1),{className:"recharts-area-curve",layout:a,type:u,connectNulls:l,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var n=this,o=this.props,i=o.points,a=o.baseLine,u=o.isAnimationActive,c=o.animationBegin,l=o.animationDuration,s=o.animationEasing,f=o.animationId,p=this.state,h=p.prevPoints,y=p.prevBaseLine;return r.createElement(ex,{begin:c,duration:l,isActive:u,easing:s,from:{t:0},to:{t:1},key:"area-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(o){var u=o.t;if(h){var c,l=h.length/i.length,s=i.map((function(t,e){var r=Math.floor(e*l);if(h[r]){var n=h[r],o=ir(n.x,t.x),i=ir(n.y,t.y);return ZA(ZA({},t),{},{x:o(u),y:i(u)})}return t}));return c=Qe(a)&&"number"==typeof a?ir(y,a)(u):De(a)||Ke(a)?ir(y,0)(u):a.map((function(t,e){var r=Math.floor(e*l);if(y[r]){var n=y[r],o=ir(n.x,t.x),i=ir(n.y,t.y);return ZA(ZA({},t),{},{x:o(u),y:i(u)})}return t})),n.renderAreaStatically(s,c,t,e)}return r.createElement(nn,null,r.createElement("defs",null,r.createElement("clipPath",{id:"animationClipPath-".concat(e)},n.renderClipRect(u))),r.createElement(nn,{clipPath:"url(#animationClipPath-".concat(e,")")},n.renderAreaStatically(i,a,t,e)))}))}},{key:"renderArea",value:function(t,e){var r=this.props,n=r.points,o=r.baseLine,i=r.isAnimationActive,a=this.state,u=a.prevPoints,c=a.prevBaseLine,l=a.totalLength;return i&&n&&n.length&&(!u&&l>0||!vv(u,n)||!vv(c,o))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(n,o,t,e)}},{key:"render",value:function(){var e,n=this.props,o=n.hide,i=n.dot,a=n.points,u=n.className,c=n.top,l=n.left,s=n.xAxis,f=n.yAxis,p=n.width,h=n.height,y=n.isAnimationActive,d=n.id;if(o||!a||!a.length)return null;var v=this.state.isAnimationFinished,m=1===a.length,b=t("recharts-area",u),g=s&&s.allowDataOverflow,w=f&&f.allowDataOverflow,x=g||w,O=De(d)?this.id:d,j=null!==(e=Tr(i,!1))&&void 0!==e?e:{r:3,strokeWidth:2},S=j.r,P=void 0===S?3:S,A=j.strokeWidth,E=void 0===A?2:A,k=(Mr(i)?i:{}).clipDot,M=void 0===k||k,T=2*P+E;return r.createElement(nn,{className:b},g||w?r.createElement("defs",null,r.createElement("clipPath",{id:"clipPath-".concat(O)},r.createElement("rect",{x:g?l:l-p/2,y:w?c:c-h/2,width:g?p:2*p,height:w?h:2*h})),!M&&r.createElement("clipPath",{id:"clipPath-dots-".concat(O)},r.createElement("rect",{x:l-T/2,y:c-T/2,width:p+T,height:h+T}))):null,m?null:this.renderArea(x,O),(i||m)&&this.renderDots(x,M,O),(!y||v)&&Qb.renderCallByParent(this.props,a))}}])&&JA(o.prototype,i),a&&JA(o,a),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,i,a}();function aE(t){return(aE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uE(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hE(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function cE(t,e,r){return e=sE(e),function(t,e){if(e&&("object"===aE(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,lE()?Reflect.construct(e,r||[],sE(t).constructor):e.apply(t,r))}function lE(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(lE=function(){return!!t})()}function sE(t){return(sE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fE(t,e){return(fE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pE(t,e,r){return(e=hE(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hE(t){var e=function(t,e){if("object"!=aE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=aE(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==aE(e)?e:e+""}function yE(){return yE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},yE.apply(this,arguments)}function dE(e){var n=e.xAxisId,o=JS(),i=QS(),a=YS(n);return null==a?null:r.createElement(cA,yE({},a,{className:t("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:o,height:i},ticksGenerator:function(t){return Km(t,!0)}}))}qA=iE,nE(iE,"displayName","Area"),nE(iE,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!Xl,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),nE(iE,"getBaseValue",(function(t,e,r,n){var o=t.layout,i=t.baseValue,a=e.props.baseValue,u=null!=a?a:i;if(Qe(u)&&"number"==typeof u)return u;var c="horizontal"===o?n:r,l=c.scale.domain();if("number"===c.type){var s=Math.max(l[0],l[1]),f=Math.min(l[0],l[1]);return"dataMin"===u?f:"dataMax"===u||s<0?s:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===u?l[0]:"dataMax"===u?l[1]:l[0]})),nE(iE,"getComposedData",(function(t){var e,r=t.props,n=t.item,o=t.xAxis,i=t.yAxis,a=t.xAxisTicks,u=t.yAxisTicks,c=t.bandSize,l=t.dataKey,s=t.stackedData,f=t.dataStartIndex,p=t.displayedData,h=t.offset,y=r.layout,d=s&&s.length,v=qA.getBaseValue(r,n,o,i),m="horizontal"===y,b=!1,g=p.map((function(t,e){var r;d?r=s[f+e]:(r=$m(t,l),Array.isArray(r)?b=!0:r=[v,r]);var n=null==r[1]||d&&null==$m(t,l);return m?{x:ob({axis:o,ticks:a,bandSize:c,entry:t,index:e}),y:n?null:i.scale(r[1]),value:r,payload:t}:{x:n?null:o.scale(r[1]),y:ob({axis:i,ticks:u,bandSize:c,entry:t,index:e}),value:r,payload:t}}));return e=d||b?g.map((function(t){var e=Array.isArray(t.value)?t.value[0]:null;return m?{x:t.x,y:null!=e&&null!=t.y?i.scale(e):null}:{x:null!=e?o.scale(e):null,y:t.y}})):m?i.scale(v):o.scale(v),ZA({points:g,baseLine:e,layout:y,isRange:b},h)})),nE(iE,"renderDotItem",(function(e,n){var o;if(r.isValidElement(e))o=r.cloneElement(e,n);else if(Re(e))o=e(n);else{var i=t("recharts-area-dot","boolean"!=typeof e?e.className:""),a=n.key,u=GA(n,XA);o=r.createElement(Ox,KA({},u,{key:a,className:i}))}return o}));var vE=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),cE(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fE(t,e)}(t,r.Component),uE(t,[{key:"render",value:function(){return r.createElement(dE,this.props)}}])}();function mE(t){return(mE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function bE(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,SE(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function gE(t,e,r){return e=xE(e),function(t,e){if(e&&("object"===mE(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,wE()?Reflect.construct(e,r||[],xE(t).constructor):e.apply(t,r))}function wE(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(wE=function(){return!!t})()}function xE(t){return(xE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function OE(t,e){return(OE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function jE(t,e,r){return(e=SE(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function SE(t){var e=function(t,e){if("object"!=mE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=mE(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==mE(e)?e:e+""}function PE(){return PE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},PE.apply(this,arguments)}pE(vE,"displayName","XAxis"),pE(vE,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var AE=function(e){var n=e.yAxisId,o=JS(),i=QS(),a=ZS(n);return null==a?null:r.createElement(cA,PE({},a,{className:t("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:o,height:i},ticksGenerator:function(t){return Km(t,!0)}}))},EE=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),gE(this,t,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&OE(t,e)}(t,r.Component),bE(t,[{key:"render",value:function(){return r.createElement(AE,this.props)}}])}();function kE(t){return function(t){if(Array.isArray(t))return ME(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return ME(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ME(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ME(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}jE(EE,"displayName","YAxis"),jE(EE,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var TE,_E=function(t,e,r,n,o){var i=Pr(t,yP),a=Pr(t,AP),u=[].concat(kE(i),kE(a)),c=Pr(t,LP),l="".concat(n,"Id"),s=n[0],f=e;if(u.length&&(f=u.reduce((function(t,e){if(e.props[l]===r&&$j(e.props,"extendDomain")&&Qe(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t}),f)),c.length){var p="".concat(s,"1"),h="".concat(s,"2");f=c.reduce((function(t,e){if(e.props[l]===r&&$j(e.props,"extendDomain")&&Qe(e.props[p])&&Qe(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t}),f)}return o&&o.length&&(f=o.reduce((function(t,e){return Qe(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t}),f)),f},CE={exports:{}};var DE=new(o((TE||(TE=1,function(t){var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var u=new o(n,i||t,a),c=r?r+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],u]:t._events[c].push(u):(t._events[c]=u,t._eventsCount++),t}function a(t,e){0===--t._eventsCount?t._events=new n:delete t._events[e]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),u.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},u.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=new Array(i);o<i;o++)a[o]=n[o].fn;return a},u.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},u.prototype.emit=function(t,e,n,o,i,a){var u=r?r+t:t;if(!this._events[u])return!1;var c,l,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,c=new Array(f-1);l<f;l++)c[l-1]=arguments[l];s.fn.apply(s.context,c)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!c)for(p=1,c=new Array(f-1);p<f;p++)c[p-1]=arguments[p];s[l].fn.apply(s[l].context,c)}}return!0},u.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},u.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},u.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==e||o&&!u.once||n&&u.context!==n||a(this,i);else{for(var c=0,l=[],s=u.length;c<s;c++)(u[c].fn!==e||o&&!u[c].once||n&&u[c].context!==n)&&l.push(u[c]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},u.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,t.exports=u}(CE)),CE.exports))),IE="recharts.syncMouseEvents";function NE(t){return(NE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function BE(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,LE(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function RE(t,e,r){return(e=LE(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function LE(t){var e=function(t,e){if("object"!=NE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=NE(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==NE(e)?e:e+""}var zE=function(){return BE((function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),RE(this,"activeIndex",0),RE(this,"coordinateList",[]),RE(this,"layout","horizontal")}),[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,u=void 0===a?null:a,c=t.offset,l=void 0===c?null:c,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=u?u:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){var t,e;if("horizontal"===this.layout&&0!==this.coordinateList.length){var r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,u=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,c=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=n+a+u,s=o+this.offset.top+i/2+c;this.mouseHandlerCallback({pageX:l,pageY:s})}}}])}();function FE(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[xb(e,r,n,o),xb(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function UE(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return FE(e);var u=e.cx,c=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=xb(u,c,l,f),h=xb(u,c,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}function $E(t){return($E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function WE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function qE(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?WE(Object(r),!0).forEach((function(e){VE(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):WE(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function VE(t,e,r){var n;return n=function(t,e){if("object"!=$E(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=$E(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==$E(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function XE(r){var n,o,i,a=r.element,u=r.tooltipEventType,c=r.isActive,l=r.activeCoordinate,s=r.activePayload,f=r.offset,p=r.activeTooltipIndex,h=r.tooltipAxisBandSize,y=r.layout,d=r.chartName,v=null!==(n=a.props.cursor)&&void 0!==n?n:null===(o=a.type.defaultProps)||void 0===o?void 0:o.cursor;if(!a||!v||!c||!l||"ScatterChart"!==d&&"axis"!==u)return null;var m=Sg;if("ScatterChart"===d)i=l,m=Cx;else if("BarChart"===d)i=function(t,e,r,n){var o=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===t?e.x-o:r.left+.5,y:"horizontal"===t?r.top+.5:e.y-o,width:"horizontal"===t?n:r.width-1,height:"horizontal"===t?r.height-1:n}}(y,l,f,h),m=px;else if("radial"===y){var b=FE(l),g=b.cx,w=b.cy,x=b.radius;i={cx:g,cy:w,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:x,outerRadius:x},m=cg}else i={points:UE(y,l,f)},m=Sg;var O=qE(qE(qE(qE({stroke:"#ccc",pointerEvents:"none"},f),i),Tr(v,!1)),{},{payload:s,payloadIndex:p,className:t("recharts-tooltip-cursor",v.className)});return e.isValidElement(v)?e.cloneElement(v,O):e.createElement(m,O)}var HE=["item"],GE=["children","className","width","height","style","compact","title","desc"];function KE(t){return(KE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function YE(){return YE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},YE.apply(this,arguments)}function ZE(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e);else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,o=s}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||ik(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function JE(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function QE(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,sk(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function tk(t,e,r){return e=rk(e),function(t,e){if(e&&("object"===KE(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ek()?Reflect.construct(e,r||[],rk(t).constructor):e.apply(t,r))}function ek(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(ek=function(){return!!t})()}function rk(t){return(rk=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function nk(t,e){return(nk=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ok(t){return function(t){if(Array.isArray(t))return ak(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ik(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ik(t,e){if(t){if("string"==typeof t)return ak(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ak(t,e):void 0}}function ak(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function uk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ck(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uk(Object(r),!0).forEach((function(e){lk(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uk(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function lk(t,e,r){return(e=sk(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sk(t){var e=function(t,e){if("object"!=KE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=KE(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==KE(e)?e:e+""}var fk={xAxis:["bottom","top"],yAxis:["left","right"]},pk={width:"100%",height:"100%"},hk={x:0,y:0};function yk(t){return t}var dk=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce((function(t,e){var r=e.props.data;return r&&r.length?[].concat(ok(t),ok(r)):t}),[]);return i.length>0?i:t&&t.length&&Qe(n)&&Qe(o)?t.slice(n,o+1):[]};function vk(t){return"number"===t?[0,"auto"]:void 0}var mk=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=dk(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce((function(o,u){var c,l,s=null!==(c=u.props.data)&&void 0!==c?c:e;(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory)?l=ar(void 0===s?a:s,i.dataKey,n):l=s&&s[r]||a[r];return l?[].concat(ok(o),[pb(u,l)]):o}),[])},bk=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i=function(t,e){return"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius}(o,r),a=t.orderedTooltipTicks,u=t.tooltipAxis,c=t.tooltipTicks,l=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&Math.abs(Math.abs(o.range[1]-o.range[0])-360)<=1e-6)for(var u=o.range,c=0;c<a;c++){var l=c>0?n[c-1].coordinate:n[a-1].coordinate,s=n[c].coordinate,f=c>=a-1?n[0].coordinate:n[c+1].coordinate,p=void 0;if(Ze(s-l)!==Ze(f-s)){var h=[];if(Ze(f-s)===Ze(u[1]-u[0])){p=f;var y=s+u[1]-u[0];h[0]=Math.min(y,(y+l)/2),h[1]=Math.max(y,(y+l)/2)}else{p=l;var d=f+u[1]-u[0];h[0]=Math.min(s,(d+s)/2),h[1]=Math.max(s,(d+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[c].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[c].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i}(i,a,c,u);if(l>=0&&c){var s=c[l]&&c[l].value,f=mk(t,e,l,s),p=function(t,e,r,n){var o=e.find((function(t){return t&&t.index===r}));if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return ck(ck(ck({},n),xb(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var u=o.coordinate,c=n.angle;return ck(ck(ck({},n),xb(n.cx,n.cy,u,c)),{},{angle:c,radius:u})}return hk}(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},gk=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=Hm(l,o);return r.reduce((function(e,r){var h,y=void 0!==r.type.defaultProps?ck(ck({},r.type.defaultProps),r.props):r.props,d=y.type,v=y.dataKey,m=y.allowDataOverflow,b=y.allowDuplicatedCategory,g=y.scale,w=y.ticks,x=y.includeHidden,O=y[i];if(e[O])return e;var j,S,P,A=dk(t.data,{graphicalItems:n.filter((function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===O})),dataStartIndex:u,dataEndIndex:c}),E=A.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&Qe(n)&&Qe(o))return!0}return!1})(y.domain,m,d)&&(j=lb(y.domain,null,m),!p||"number"!==d&&"auto"===g||(P=Wm(A,v,"category")));var k=vk(d);if(!j||0===j.length){var M,T=null!==(M=y.domain)&&void 0!==M?M:k;if(v){if(j=Wm(A,v,d),"category"===d&&p){var _=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1}(j);b&&_?(S=j,j=yj(0,E)):b||(j=fb(T,j,r).reduce((function(t,e){return t.indexOf(e)>=0?t:[].concat(ok(t),[e])}),[]))}else if("category"===d)j=b?j.filter((function(t){return""!==t&&!De(t)})):fb(T,j,r).reduce((function(t,e){return t.indexOf(e)>=0||""===e||De(e)?t:[].concat(ok(t),[e])}),[]);else if("number"===d){var C=function(t,e,r,n,o){var i=e.map((function(e){return Vm(t,e,r,o,n)})).filter((function(t){return!De(t)}));return i&&i.length?i.reduce((function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}),[1/0,-1/0]):null}(A,n.filter((function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===O&&(x||!o)})),v,o,l);C&&(j=C)}!p||"number"!==d&&"auto"===g||(P=Wm(A,v,"category"))}else j=p?yj(0,E):a&&a[O]&&a[O].hasStack&&"number"===d?"expand"===f?[0,1]:ab(a[O].stackGroups,u,c):Xm(A,n.filter((function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===O&&(x||!r)})),d,l,!0);if("number"===d)j=_E(s,j,O,o,w),T&&(j=lb(T,j,m));else if("category"===d&&T){var D=T;j.every((function(t){return D.indexOf(t)>=0}))&&(j=D)}}return ck(ck({},e),{},lk({},O,ck(ck({},y),{},{axisType:o,domain:j,categoricalDomain:P,duplicateDomain:S,originalDomain:null!==(h=y.domain)&&void 0!==h?h:k,isCategorical:p,layout:l})))}),{})},wk=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=Pr(l,o),p={};return f&&f.length?p=gk(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c}):i&&i.length&&(p=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.layout,s=t.children,f=dk(t.data,{graphicalItems:r,dataStartIndex:u,dataEndIndex:c}),p=f.length,h=Hm(l,o),y=-1;return r.reduce((function(t,e){var d,v=(void 0!==e.type.defaultProps?ck(ck({},e.type.defaultProps),e.props):e.props)[i],m=vk("number");return t[v]?t:(y++,h?d=yj(0,p):a&&a[v]&&a[v].hasStack?(d=ab(a[v].stackGroups,u,c),d=_E(s,d,v,o)):(d=lb(m,Xm(f,r.filter((function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===v&&!o})),"number",l),n.defaultProps.allowDataOverflow),d=_E(s,d,v,o)),ck(ck({},t),{},lk({},v,ck(ck({axisType:o},n.defaultProps),{},{hide:!0,orientation:Te(fk,"".concat(o,".").concat(y%2),null),domain:d,originalDomain:m,isCategorical:h,layout:l}))))}),{})}(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c})),p},xk=function(t){var e=t.children,r=t.defaultShowTooltip,n=Ar(e,Bj),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:Boolean(r)}},Ok=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},jk=function(t,e){var r=t.props,n=(t.graphicalItems,t.xAxisMap),o=void 0===n?{}:n,i=t.yAxisMap,a=void 0===i?{}:i,u=r.width,c=r.height,l=r.children,s=r.margin||{},f=Ar(l,Bj),p=Ar(l,al),h=Object.keys(a).reduce((function(t,e){var r=a[e],n=r.orientation;return r.mirror||r.hide?t:ck(ck({},t),{},lk({},n,t[n]+r.width))}),{left:s.left||0,right:s.right||0}),y=Object.keys(o).reduce((function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:ck(ck({},t),{},lk({},n,Te(t,"".concat(n))+r.height))}),{top:s.top||0,bottom:s.bottom||0}),d=ck(ck({},y),h),v=d.bottom;f&&(d.bottom+=f.props.height||Bj.defaultProps.height),p&&e&&(d=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,u=i-(a.left||0)-(a.right||0),c=Nm({children:o,legendWidth:u});if(c){var l=n||{},s=l.width,f=l.height,p=c.align,h=c.verticalAlign,y=c.layout;if(("vertical"===y||"horizontal"===y&&"middle"===h)&&"center"!==p&&Qe(t[p]))return Fm(Fm({},t),{},Um({},p,t[p]+(s||0)));if(("horizontal"===y||"vertical"===y&&"center"===p)&&"middle"!==h&&Qe(t[h]))return Fm(Fm({},t),{},Um({},h,t[h]+(f||0)))}return t}(d,0,r,e));var m=u-d.left-d.right,b=c-d.top-d.bottom;return ck(ck({brushBottom:v},d),{},{width:Math.max(m,0),height:Math.max(b,0)})},Sk=function(t,e){return"xAxis"===e?t[e].width:"yAxis"===e?t[e].height:void 0},Pk=function(n){var o=n.chartName,i=n.GraphicalChild,a=n.defaultTooltipEventType,u=void 0===a?"axis":a,c=n.validateTooltipEventTypes,l=void 0===c?["axis"]:c,s=n.axisComponents,f=n.legendContent,p=n.formatAxisMap,h=n.defaultProps,y=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,u=e.dataEndIndex,c=t.barSize,l=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,y=Ok(l),d=y.numericAxisName,v=y.cateAxisName,m=function(t){return!(!t||!t.length)&&t.some((function(t){var e=xr(t&&t.type);return e&&e.indexOf("Bar")>=0}))}(r),b=[];return r.forEach((function(r,y){var g=dk(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:u}),w=void 0!==r.type.defaultProps?ck(ck({},r.type.defaultProps),r.props):r.props,x=w.dataKey,O=w.maxBarSize,j=w["".concat(d,"Id")],S=w["".concat(v,"Id")],P=s.reduce((function(t,r){var n=e["".concat(r.axisType,"Map")],o=w["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||vm();var i=n[o];return ck(ck({},t),{},lk(lk({},r.axisType,i),"".concat(r.axisType,"Ticks"),Km(i)))}),{}),A=P[v],E=P["".concat(v,"Ticks")],k=n&&n[j]&&n[j].hasStack&&function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?Fm(Fm({},t.type.defaultProps),t.props):t.props).stackId;if(tr(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null}(r,n[j].stackGroups),M=xr(r.type).indexOf("Bar")>=0,T=sb(A,E),_=[],C=m&&function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),u=0,c=a.length;u<c;u++)for(var l=o[a[u]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],y=h.items,d=h.cateAxisId,v=y.filter((function(t){return xr(t.type).indexOf("Bar")>=0}));if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?Fm(Fm({},m),v[0].props):v[0].props,g=b.barSize,w=b[d];i[w]||(i[w]=[]);var x=De(g)?e:g;i[w].push({item:v[0],stackList:v.slice(1),barSize:De(x)?void 0:nr(x,r,0)})}}return i}({barSize:c,stackGroups:n,totalSize:Sk(P,v)});if(M){var D,I,N=De(O)?h:O,B=null!==(D=null!==(I=sb(A,E,!0))&&void 0!==I?I:N)&&void 0!==D?D:0;_=function(t){var e=t.barGap,r=t.barCategoryGap,n=t.bandSize,o=t.sizeList,i=void 0===o?[]:o,a=t.maxBarSize,u=i.length;if(u<1)return null;var c,l=nr(e,n,0,!0),s=[];if(i[0].barSize===+i[0].barSize){var f=!1,p=n/u,h=i.reduce((function(t,e){return t+e.barSize||0}),0);(h+=(u-1)*l)>=n&&(h-=(u-1)*l,l=0),h>=n&&p>0&&(f=!0,h=u*(p*=.9));var y={offset:((n-h)/2|0)-l,size:0};c=i.reduce((function(t,e){var r={item:e.item,position:{offset:y.offset+y.size+l,size:f?p:e.barSize}},n=[].concat(Rm(t),[r]);return y=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach((function(t){n.push({item:t,position:y})})),n}),s)}else{var d=nr(r,n,0,!0);n-2*d-(u-1)*l<=0&&(l=0);var v=(n-2*d-(u-1)*l)/u;v>1&&(v>>=0);var m=a===+a?Math.min(v,a):v;c=i.reduce((function(t,e,r){var n=[].concat(Rm(t),[{item:e.item,position:{offset:d+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach((function(t){n.push({item:t,position:n[n.length-1].position})})),n}),s)}return c}({barGap:f,barCategoryGap:p,bandSize:B!==T?B:T,sizeList:C[S],maxBarSize:N}),B!==T&&(_=_.map((function(t){return ck(ck({},t),{},{position:ck(ck({},t.position),{},{offset:t.position.offset-B/2})})})))}var R,L,z=r&&r.type&&r.type.getComposedData;z&&b.push({props:ck(ck({},z(ck(ck({},P),{},{displayedData:g,props:t,dataKey:x,item:r,bandSize:T,barPosition:_,offset:o,stackedData:k,layout:l,dataStartIndex:a,dataEndIndex:u}))),{},lk(lk(lk({key:r.key||"item-".concat(y)},d,P[d]),v,P[v]),"animationId",i)),childIndex:(R=r,L=t.children,Sr(L).indexOf(R)),item:r})})),b},d=function(t,e){var r=t.props,n=t.dataStartIndex,a=t.dataEndIndex,u=t.updateId;if(!Er({props:r}))return null;var c=r.children,l=r.layout,f=r.stackOffset,h=r.data,d=r.reverseStackOrder,v=Ok(l),m=v.numericAxisName,b=v.cateAxisName,g=Pr(c,i),w=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce((function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?Fm(Fm({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var u=i[r],c=t[u]||{hasStack:!1,stackGroups:{}};if(tr(a)){var l=c.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),c.hasStack=!0,c.stackGroups[a]=l}else c.stackGroups[rr("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return Fm(Fm({},t),{},Um({},u,c))}),{});return Object.keys(a).reduce((function(e,i){var u=a[i];return u.hasStack&&(u.stackGroups=Object.keys(u.stackGroups).reduce((function(e,i){var a=u.stackGroups[i];return Fm(Fm({},e),{},Um({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:rb(t,a.items,o)}))}),{})),Fm(Fm({},e),{},Um({},i,u))}),{})}(h,g,"".concat(m,"Id"),"".concat(b,"Id"),f,d),x=s.reduce((function(t,e){var o="".concat(e.axisType,"Map");return ck(ck({},t),{},lk({},o,wk(r,ck(ck({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&w,dataStartIndex:n,dataEndIndex:a}))))}),{}),O=jk(ck(ck({},x),{},{props:r,graphicalItems:g}),null==e?void 0:e.legendBBox);Object.keys(x).forEach((function(t){x[t]=p(r,x[t],O,t.replace("Map",""),o)}));var j,S,P=x["".concat(b,"Map")],A=(j=or(P),{tooltipTicks:S=Km(j,!1,!0),orderedTooltipTicks:gl(S,(function(t){return t.coordinate})),tooltipAxis:j,tooltipAxisBandSize:sb(j,S)}),E=y(r,ck(ck({},x),{},{dataStartIndex:n,dataEndIndex:a,updateId:u,graphicalItems:g,stackGroups:w,offset:O}));return ck(ck({formattedGraphicalItems:E,graphicalItems:g,offset:O,stackGroups:w},A),x)},v=function(){function n(i){var a,u,c;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),lk(c=tk(this,n,[i]),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),lk(c,"accessibilityManager",new zE),lk(c,"handleLegendBBoxUpdate",(function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;c.setState(ck({legendBBox:t},d({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},ck(ck({},c.state),{},{legendBBox:t}))))}})),lk(c,"handleReceiveSyncEvent",(function(t,e,r){if(c.props.syncId===t){if(r===c.eventEmitterSymbol&&"function"!=typeof c.props.syncMethod)return;c.applySyncEvent(e)}})),lk(c,"handleBrushChange",(function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState((function(){return ck({dataStartIndex:e,dataEndIndex:r},d({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))})),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}})),lk(c,"handleMouseEnter",(function(t){var e=c.getMouseInfo(t);if(e){var r=ck(ck({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;Re(n)&&n(r,t)}})),lk(c,"triggeredAfterMouseMove",(function(t){var e=c.getMouseInfo(t),r=e?ck(ck({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;Re(n)&&n(r,t)})),lk(c,"handleItemMouseEnter",(function(t){c.setState((function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}}))})),lk(c,"handleItemMouseLeave",(function(){c.setState((function(){return{isTooltipActive:!1}}))})),lk(c,"handleMouseMove",(function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)})),lk(c,"handleMouseLeave",(function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;Re(r)&&r(e,t)})),lk(c,"handleOuterEvent",(function(t){var e,r=function(t){var e=t&&t.type;return e&&wr[e]?wr[e]:null}(t),n=Te(c.props,"".concat(r));r&&Re(n)&&n(null!==(e=/.*touch.*/i.test(r)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))&&void 0!==e?e:{},t)})),lk(c,"handleClick",(function(t){var e=c.getMouseInfo(t);if(e){var r=ck(ck({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;Re(n)&&n(r,t)}})),lk(c,"handleMouseDown",(function(t){var e=c.props.onMouseDown;Re(e)&&e(c.getMouseInfo(t),t)})),lk(c,"handleMouseUp",(function(t){var e=c.props.onMouseUp;Re(e)&&e(c.getMouseInfo(t),t)})),lk(c,"handleTouchMove",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])})),lk(c,"handleTouchStart",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])})),lk(c,"handleTouchEnd",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])})),lk(c,"handleDoubleClick",(function(t){var e=c.props.onDoubleClick;Re(e)&&e(c.getMouseInfo(t),t)})),lk(c,"handleContextMenu",(function(t){var e=c.props.onContextMenu;Re(e)&&e(c.getMouseInfo(t),t)})),lk(c,"triggerSyncEvent",(function(t){void 0!==c.props.syncId&&DE.emit(IE,c.props.syncId,t,c.eventEmitterSymbol)})),lk(c,"applySyncEvent",(function(t){var e=c.props,r=e.layout,n=e.syncMethod,o=c.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(ck({dataStartIndex:i,dataEndIndex:a},d({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var y=0;y<h.length;y++)if(h[y].value===t.activeLabel){s=y;break}}var v=ck(ck({},p),{},{x:p.left,y:p.top}),m=Math.min(u,v.x+v.width),b=Math.min(l,v.y+v.height),g=h[s]&&h[s].value,w=mk(c.state,c.props.data,s),x=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:hk;c.setState(ck(ck({},t),{},{activeLabel:g,activeCoordinate:x,activePayload:w,activeTooltipIndex:s}))}else c.setState(t)})),lk(c,"renderCursor",(function(t){var e,n=c.state,i=n.isTooltipActive,a=n.activeCoordinate,u=n.activePayload,l=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!==(e=t.props.active)&&void 0!==e?e:i,y=c.props.layout,d=t.key||"_recharts-cursor";return r.createElement(XE,{key:d,activeCoordinate:a,activePayload:u,activeTooltipIndex:s,chartName:o,element:t,isActive:h,layout:y,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})})),lk(c,"renderPolarAxis",(function(r,n,o){var i=Te(r,"type.axisType"),a=Te(c.state,"".concat(i,"Map")),u=r.type.defaultProps,l=void 0!==u?ck(ck({},u),r.props):r.props,s=a&&a[l["".concat(i,"Id")]];return e.cloneElement(r,ck(ck({},s),{},{className:t(i,s.className),key:r.key||"".concat(n,"-").concat(o),ticks:Km(s,!0)}))})),lk(c,"renderPolarGrid",(function(t){var r=t.props,n=r.radialLines,o=r.polarAngles,i=r.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=or(u),f=or(l),p=f.cx,h=f.cy,y=f.innerRadius,d=f.outerRadius;return e.cloneElement(t,{polarAngles:Array.isArray(o)?o:Km(f,!0).map((function(t){return t.coordinate})),polarRadius:Array.isArray(i)?i:Km(s,!0).map((function(t){return t.coordinate})),cx:p,cy:h,innerRadius:y,outerRadius:d,key:t.key||"polar-grid",radialLines:n})})),lk(c,"renderLegend",(function(){var t=c.state.formattedGraphicalItems,r=c.props,n=r.children,o=r.width,i=r.height,a=c.props.margin||{},u=o-(a.left||0)-(a.right||0),l=Nm({children:n,formattedGraphicalItems:t,legendWidth:u,legendContent:f});if(!l)return null;var s=l.item,p=JE(l,HE);return e.cloneElement(s,ck(ck({},p),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))})),lk(c,"renderTooltip",(function(){var t,r=c.props,n=r.children,o=r.accessibilityLayer,i=Ar(n,vs);if(!i)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!==(t=i.props.active)&&void 0!==t?t:u;return e.cloneElement(i,{viewBox:ck(ck({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:o})})),lk(c,"renderBrush",(function(t){var r=c.props,n=r.margin,o=r.data,i=c.state,a=i.offset,u=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return e.cloneElement(t,{key:t.key||"_recharts-brush",onChange:Zm(c.handleBrushChange,t.props.onChange),data:o,x:Qe(t.props.x)?t.props.x:a.left,y:Qe(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(n.bottom||0),width:Qe(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})})),lk(c,"renderReferenceElement",(function(t,r,n){if(!t)return null;var o=c.clipPathId,i=c.state,a=i.xAxisMap,u=i.yAxisMap,l=i.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,y=f.yAxisId,d=void 0===y?s.yAxisId:y;return e.cloneElement(t,{key:t.key||"".concat(r,"-").concat(n),xAxis:a[h],yAxis:u[d],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:o})})),lk(c,"renderActivePoints",(function(t){var e=t.item,r=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,u=[],c=e.props.key,l=void 0!==e.item.type.defaultProps?ck(ck({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=ck(ck({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:qm(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},Tr(s,!1)),yr(s));return u.push(n.renderActiveDot(s,f,"".concat(c,"-activePoint-").concat(i))),o?u.push(n.renderActiveDot(s,ck(ck({},f),{},{cx:o.x,cy:o.y}),"".concat(c,"-basePoint-").concat(i))):a&&u.push(null),u})),lk(c,"renderGraphicChild",(function(t,r,n){var o=c.filterFormatItem(t,r,n);if(!o)return null;var i=c.getTooltipEventType(),a=c.state,u=a.isTooltipActive,l=a.tooltipAxis,s=a.activeTooltipIndex,f=a.activeLabel,p=Ar(c.props.children,vs),h=o.props,y=h.points,d=h.isRange,v=h.baseLine,m=void 0!==o.item.type.defaultProps?ck(ck({},o.item.type.defaultProps),o.item.props):o.item.props,b=m.activeDot,g=m.hide,w=m.activeBar,x=m.activeShape,O=Boolean(!g&&u&&p&&(b||w||x)),j={};"axis"!==i&&p&&"click"===p.props.trigger?j={onClick:Zm(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==i&&(j={onMouseLeave:Zm(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:Zm(c.handleItemMouseEnter,t.props.onMouseEnter)});var S=e.cloneElement(t,ck(ck({},o.props),j));if(O){if(!(s>=0)){var P,A=(null!==(P=c.getItemByXY(c.state.activeCoordinate))&&void 0!==P?P:{graphicalItem:S}).graphicalItem,E=A.item,k=void 0===E?t:E,M=A.childIndex,T=ck(ck(ck({},o.props),j),{},{activeIndex:M});return[e.cloneElement(k,T),null,null]}var _,C;if(l.dataKey&&!l.allowDuplicatedCategory){var D="function"==typeof l.dataKey?function(t){return"function"==typeof l.dataKey?l.dataKey(t.payload):null}:"payload.".concat(l.dataKey.toString());_=ar(y,D,f),C=d&&v&&ar(v,D,f)}else _=null==y?void 0:y[s],C=d&&v&&v[s];if(x||w){var I=void 0!==t.props.activeIndex?t.props.activeIndex:s;return[e.cloneElement(t,ck(ck(ck({},o.props),j),{},{activeIndex:I})),null,null]}if(!De(_))return[S].concat(ok(c.renderActivePoints({item:o,activePoint:_,basePoint:C,childIndex:s,isRange:d})))}return d?[S,null,null]:[S,null]})),lk(c,"renderCustomized",(function(t,r,n){return e.cloneElement(t,ck(ck({key:"recharts-customized-".concat(n)},c.props),c.state))})),lk(c,"renderMap",{CartesianGrid:{handler:yk,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:yk},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:yk},YAxis:{handler:yk},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!==(a=i.id)&&void 0!==a?a:rr("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=ws(c.triggeredAfterMouseMove,null!==(u=i.throttleDelay)&&void 0!==u?u:1e3/60),c.state={},c}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&nk(t,e)}(n,e.Component),QE(n,[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=Ar(e,vs);if(i){var a=i.props.defaultIndex;if(!("number"!=typeof a||a<0||a>this.state.tooltipTicks.length-1)){var u=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=mk(this.state,r,a,u),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find((function(t){return"Scatter"===t.item.type.name}));p&&(f=ck(ck({},f),p.props.points[a].tooltipPosition),c=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:u,activePayload:c,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;var r,n;(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin)&&this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}});return null}},{key:"componentDidUpdate",value:function(t){_r([Ar(t.children,vs)],[Ar(this.props.children,vs)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=Ar(this.props.children,vs);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return l.indexOf(e)>=0?e:u}return u}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e,r=this.container,n=r.getBoundingClientRect(),o={top:(e=n).top+window.scrollY-document.documentElement.clientTop,left:e.left+window.scrollX-document.documentElement.clientLeft},i={chartX:Math.round(t.pageX-o.left),chartY:Math.round(t.pageY-o.top)},a=n.width/r.offsetWidth||1,u=this.inRange(i.chartX,i.chartY,a);if(!u)return null;var c=this.state,l=c.xAxisMap,s=c.yAxisMap,f=this.getTooltipEventType(),p=bk(this.state,this.props.data,this.props.layout,u);if("axis"!==f&&l&&s){var h=or(l).scale,y=or(s).scale,d=h&&h.invert?h.invert(i.chartX):null,v=y&&y.invert?y.invert(i.chartY):null;return ck(ck({},i),{},{xValue:d,yValue:v},p)}return p?ck(ck({},i),p):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var u=this.state,c=u.angleAxisMap,l=u.radiusAxisMap;if(c&&l){var s=or(c);return Pb({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=Ar(t,vs),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),ck(ck({},yr(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){DE.on(IE,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){DE.removeListener(IE,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===xr(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,n=e.left,o=e.top,i=e.height,a=e.width;return r.createElement("defs",null,r.createElement("clipPath",{id:t},r.createElement("rect",{x:n,y:o,height:i,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce((function(t,e){var r=ZE(e,2),n=r[0],o=r[1];return ck(ck({},t),{},lk({},n,o.scale))}),{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce((function(t,e){var r=ZE(e,2),n=r[0],o=r[1];return ck(ck({},t),{},lk({},n,o.scale))}),{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],u=a.props,c=a.item,l=void 0!==c.type.defaultProps?ck(ck({},c.type.defaultProps),c.props):c.props,s=xr(c.type);if("Bar"===s){var f=(u.data||[]).find((function(e){return sx(t,e)}));if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(u.data||[]).find((function(e){return Pb(t,e)}));if(p)return{graphicalItem:a,payload:p}}else if(zO(a,n)||FO(a,n)||UO(a,n)){var h=VO({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),y=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:ck(ck({},a),{},{childIndex:y}),payload:UO(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var e=this;if(!Er(this))return null;var n,o,i=this.props,a=i.children,u=i.className,c=i.width,l=i.height,s=i.style,f=i.compact,p=i.title,h=i.desc,y=JE(i,GE),d=Tr(y,!1);if(f)return r.createElement(KS,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},r.createElement(Rr,YE({},d,{width:c,height:l,title:p,desc:h}),this.renderClipPath(),Dr(a,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(n=this.props.tabIndex)&&void 0!==n?n:0,d.role=null!==(o=this.props.role)&&void 0!==o?o:"application",d.onKeyDown=function(t){e.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){e.accessibilityManager.focus()});var v=this.parseEventsOfWrapper();return r.createElement(KS,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},r.createElement("div",YE({className:t("recharts-wrapper",u),style:ck({position:"relative",cursor:"default",width:c,height:l},s)},v,{ref:function(t){e.container=t}}),r.createElement(Rr,YE({},d,{width:c,height:l,title:p,desc:h,style:pk}),this.renderClipPath(),Dr(a,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}();lk(v,"displayName",o),lk(v,"defaultProps",ck({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},h)),lk(v,"getDerivedStateFromProps",(function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,u=t.layout,c=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=xk(t);return ck(ck(ck({},p),{},{updateId:0},d(ck(ck({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:u,prevStackOffset:c,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||u!==e.prevLayout||c!==e.prevStackOffset||!cr(l,e.prevMargin)){var h=xk(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=ck(ck({},bk(e,n,u)),{},{updateId:e.updateId+1}),m=ck(ck(ck({},h),y),v);return ck(ck(ck({},m),d(ck({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:u,prevStackOffset:c,prevMargin:l,prevChildren:o})}if(!_r(o,e.prevChildren)){var b,g,w,x,O=Ar(o,Bj),j=O&&null!==(b=null===(g=O.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:s,S=O&&null!==(w=null===(x=O.props)||void 0===x?void 0:x.endIndex)&&void 0!==w?w:f,P=j!==s||S!==f,A=!De(n)&&!P?e.updateId:e.updateId+1;return ck(ck({updateId:A},d(ck(ck({props:t},e),{},{updateId:A,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null})),lk(v,"renderActiveDot",(function(t,n,o){var i;return i=e.isValidElement(t)?e.cloneElement(t,n):Re(t)?t(n):r.createElement(Ox,n),r.createElement(nn,{className:"recharts-active-dot",key:o},i)}));var m=e.forwardRef((function(t,e){return r.createElement(v,YE({},t,{ref:e}))}));return m.displayName=v.displayName,m},Ak=Pk({chartName:"LineChart",GraphicalChild:WA,axisComponents:[{axisType:"xAxis",AxisComp:vE},{axisType:"yAxis",AxisComp:EE}],formatAxisMap:ES}),Ek=Pk({chartName:"BarChart",GraphicalChild:wS,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:vE},{axisType:"yAxis",AxisComp:EE}],formatAxisMap:ES}),kk=Pk({chartName:"PieChart",GraphicalChild:fj,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:yO},{axisType:"radiusAxis",AxisComp:Zx}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,u=t.startAngle,c=t.endAngle,l=nr(t.cx,i,i/2),s=nr(t.cy,a,a/2),f=Ob(i,a,r),p=nr(t.innerRadius,f,0),h=nr(t.outerRadius,f,.8*f);return Object.keys(e).reduce((function(t,r){var i,a=e[r],f=a.domain,y=a.reversed;if(De(a.range))"angleAxis"===n?i=[u,c]:"radiusAxis"===n&&(i=[p,h]),y&&(i=[i[1],i[0]]);else{var d=mb(i=a.range,2);u=d[0],c=d[1]}var v=Jm(a,o),m=v.realScaleType,b=v.scale;b.domain(f).range(i),tb(b);var g=nb(b,db(db({},a),{},{realScaleType:m})),w=db(db(db({},a),g),{},{range:i,radius:h,realScaleType:m,scale:b,cx:l,cy:s,innerRadius:p,outerRadius:h,startAngle:u,endAngle:c});return db(db({},t),{},vb({},r,w))}),{})},defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),Mk=Pk({chartName:"AreaChart",GraphicalChild:iE,axisComponents:[{axisType:"xAxis",AxisComp:vE},{axisType:"yAxis",AxisComp:EE}],formatAxisMap:ES});export{Mk as A,Ek as B,AA as C,Ak as L,kk as P,Es as R,vs as T,vE as X,EE as Y,al as a,WA as b,fj as c,ks as d,wS as e,yP as f,iE as g};
