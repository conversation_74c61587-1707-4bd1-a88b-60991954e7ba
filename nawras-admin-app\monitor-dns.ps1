# DNS Propagation Monitor for partner.nawrasinchina.com
# This script monitors DNS propagation status

$DOMAIN = "partner.nawrasinchina.com"
$EXPECTED_IP = "*************"

Write-Host "🌐 DNS Propagation Monitor" -ForegroundColor Blue
Write-Host "=========================" -ForegroundColor Blue
Write-Host "Domain: $DOMAIN" -ForegroundColor Yellow
Write-Host "Expected IP: $EXPECTED_IP" -ForegroundColor Yellow
Write-Host ""

# Function to test DNS from different servers
function Test-DNSServers {
    $dnsServers = @(
        @{ Name = "Google Primary"; Server = "*******" }
        @{ Name = "Google Secondary"; Server = "*******" }
        @{ Name = "Cloudflare Primary"; Server = "*******" }
        @{ Name = "Cloudflare Secondary"; Server = "*******" }
        @{ Name = "OpenDNS Primary"; Server = "**************" }
        @{ Name = "Quad9"; Server = "*******" }
    )
    
    Write-Host "🔍 Testing DNS Resolution from Multiple Servers:" -ForegroundColor Cyan
    Write-Host ""
    
    $successCount = 0
    $totalCount = $dnsServers.Count
    
    foreach ($dns in $dnsServers) {
        try {
            $result = Resolve-DnsName -Name $DOMAIN -Server $dns.Server -Type A -ErrorAction Stop
            $resolvedIP = $result.IPAddress
            
            if ($resolvedIP -eq $EXPECTED_IP) {
                Write-Host "   ✅ $($dns.Name): $resolvedIP" -ForegroundColor Green
                $successCount++
            } else {
                Write-Host "   ❌ $($dns.Name): $resolvedIP (incorrect)" -ForegroundColor Red
            }
        } catch {
            Write-Host "   ⏳ $($dns.Name): Not resolved yet" -ForegroundColor Yellow
        }
    }
    
    $propagationPercent = [math]::Round(($successCount / $totalCount) * 100, 1)
    Write-Host ""
    Write-Host "📊 Propagation Status: $successCount/$totalCount servers ($propagationPercent%)" -ForegroundColor $(
        if ($propagationPercent -eq 100) { "Green" }
        elseif ($propagationPercent -ge 50) { "Yellow" }
        else { "Red" }
    )
    
    return $propagationPercent
}

# Function to test website access
function Test-WebsiteAccess {
    Write-Host ""
    Write-Host "🌐 Testing Website Access:" -ForegroundColor Cyan
    
    # Test via domain
    try {
        $domainResponse = Invoke-WebRequest -Uri "http://$DOMAIN" -TimeoutSec 10 -UseBasicParsing
        Write-Host "   ✅ Domain Access: HTTP $($domainResponse.StatusCode)" -ForegroundColor Green
        
        # Test API via domain
        try {
            $apiResponse = Invoke-WebRequest -Uri "http://$DOMAIN/api/expenses" -TimeoutSec 5 -UseBasicParsing
            Write-Host "   ✅ API via Domain: HTTP $($apiResponse.StatusCode)" -ForegroundColor Green
        } catch {
            # Try with port 3001
            try {
                $apiResponse = Invoke-WebRequest -Uri "http://$DOMAIN`:3001/api/expenses" -TimeoutSec 5 -UseBasicParsing
                Write-Host "   ✅ API via Domain:3001: HTTP $($apiResponse.StatusCode)" -ForegroundColor Green
            } catch {
                Write-Host "   ⚠️ API via Domain: Not accessible" -ForegroundColor Yellow
            }
        }
        
        return $true
    } catch {
        Write-Host "   ⏳ Domain Access: Not yet accessible" -ForegroundColor Yellow
        return $false
    }
}

# Function to check online DNS propagation tools
function Show-OnlineTools {
    Write-Host ""
    Write-Host "🔗 Online DNS Propagation Checkers:" -ForegroundColor Blue
    Write-Host "   • https://dnschecker.org/#A/$DOMAIN" -ForegroundColor Cyan
    Write-Host "   • https://whatsmydns.net/#A/$DOMAIN" -ForegroundColor Cyan
    Write-Host "   • https://www.whatsmydns.net/#A/$DOMAIN" -ForegroundColor Cyan
    Write-Host "   • https://dnspropagation.net/$DOMAIN" -ForegroundColor Cyan
}

# Main monitoring loop
$startTime = Get-Date
Write-Host "⏰ Monitoring started at: $($startTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Blue
Write-Host ""

# Initial test
$propagationPercent = Test-DNSServers
$websiteWorking = Test-WebsiteAccess

if ($propagationPercent -eq 100 -and $websiteWorking) {
    Write-Host ""
    Write-Host "🎉 SUCCESS: DNS fully propagated and website accessible!" -ForegroundColor Green
    Write-Host "✅ Your domain $DOMAIN is working perfectly!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⏳ DNS propagation in progress..." -ForegroundColor Yellow
    
    if ($propagationPercent -ge 50) {
        Write-Host "🟡 Good progress! Most DNS servers have updated." -ForegroundColor Yellow
        Write-Host "   Expected completion: 5-30 minutes" -ForegroundColor Yellow
    } elseif ($propagationPercent -ge 25) {
        Write-Host "🟠 Partial propagation detected." -ForegroundColor Yellow
        Write-Host "   Expected completion: 15-60 minutes" -ForegroundColor Yellow
    } else {
        Write-Host "🔴 Early stage propagation." -ForegroundColor Red
        Write-Host "   Expected completion: 30 minutes - 6 hours" -ForegroundColor Red
    }
}

Show-OnlineTools

Write-Host ""
Write-Host "📋 PROPAGATION TIMELINE:" -ForegroundColor Blue
Write-Host "   • 0-5 minutes: DNS provider updates" -ForegroundColor White
Write-Host "   • 5-30 minutes: Major DNS servers update" -ForegroundColor White
Write-Host "   • 30 minutes - 2 hours: Regional propagation" -ForegroundColor White
Write-Host "   • 2-6 hours: Global propagation" -ForegroundColor White
Write-Host "   • Up to 48 hours: Complete worldwide propagation" -ForegroundColor White
Write-Host ""

Write-Host "🔄 To monitor continuously, run this script again in 10-15 minutes" -ForegroundColor Blue
Write-Host ""

# Show current status summary
Write-Host "📊 CURRENT STATUS SUMMARY:" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "✅ Droplet cleanup: COMPLETED" -ForegroundColor Green
Write-Host "✅ DNS A record: CONFIGURED" -ForegroundColor Green
Write-Host "✅ Website functionality: VERIFIED" -ForegroundColor Green
Write-Host "📊 DNS propagation: $propagationPercent% complete" -ForegroundColor $(
    if ($propagationPercent -eq 100) { "Green" }
    elseif ($propagationPercent -ge 50) { "Yellow" }
    else { "Red" }
)
Write-Host "🌐 Domain access: $(if ($websiteWorking) { 'WORKING' } else { 'PENDING' })" -ForegroundColor $(if ($websiteWorking) { "Green" } else { "Yellow" })
Write-Host "💰 Monthly savings: ~$6" -ForegroundColor Green
Write-Host ""

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
