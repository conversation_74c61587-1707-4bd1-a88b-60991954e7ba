# 🚀 DigitalOcean API Solution for Nawras Admin

## 🎯 Problem Summary
- Website at *************** not working
- Need to add subdomain partner.nawrasinchina.com
- Two DigitalOcean droplets available:
  - `nawras-admin-app` (***************)
  - `nawras-admin-deployed` (*************)

## 🔑 API Token Configured
Your DigitalOcean personal access token is configured in the scripts:
`***********************************************************************`

## 🛠️ Available Tools

### 1. Quick Status Check (Windows)
```powershell
# Run in PowerShell
.\check-droplets.ps1
```

### 2. Quick Status Check (Linux/Mac/WSL)
```bash
# Run in terminal
./check-droplets.sh
```

### 3. Complete Fix Script (Linux/Mac/WSL)
```bash
# Comprehensive fix including DNS setup
./fix-droplets.sh
```

### 4. Interactive API Management (Linux/Mac/WSL)
```bash
# Full DigitalOcean API management tool
./digitalocean-api.sh
```

### 5. Windows Batch Runner
```cmd
# Automatically detects and runs appropriate script
run-fix.bat
```

## 🔧 What Each Script Does

### check-droplets.ps1 / check-droplets.sh
- ✅ Lists all your droplets with status
- ✅ Tests connectivity (ping, HTTP, API)
- ✅ Shows IP addresses and regions
- ✅ Identifies issues

### fix-droplets.sh (Comprehensive)
- ✅ Diagnoses droplet problems
- ✅ Powers on droplets if needed
- ✅ Tests web services
- ✅ Creates/updates DNS A record for subdomain
- ✅ Deploys application automatically
- ✅ Configures nginx and SSL

### digitalocean-api.sh (Interactive)
- ✅ Full droplet management (power on/off, reboot)
- ✅ Domain and DNS record management
- ✅ Create subdomain A records
- ✅ Monitor droplet actions
- ✅ Interactive menu system

## 🚀 Quick Start Instructions

### Step 1: Check Current Status
**Windows:**
```powershell
cd nawras-admin-app
.\check-droplets.ps1
```

**Linux/Mac/WSL:**
```bash
cd nawras-admin-app
./check-droplets.sh
```

### Step 2: Run Complete Fix (Recommended)
**If you have WSL/Git Bash:**
```bash
./fix-droplets.sh
```

**Windows PowerShell (manual steps):**
1. Run `.\check-droplets.ps1` to see status
2. Use DigitalOcean dashboard to power on droplets if needed
3. Manually create DNS A record (see DNS section below)

### Step 3: Verify Results
After running the fix:
- ✅ Check http://************* or http://***************
- ✅ Wait 5-30 minutes for DNS propagation
- ✅ Check http://partner.nawrasinchina.com

## 🌐 DNS Configuration

The scripts will automatically create an A record:
```
Type: A
Name: partner
Value: [Best available droplet IP]
TTL: 300 seconds
```

**Manual DNS Setup (if scripts fail):**
1. Go to DigitalOcean DNS dashboard
2. Find domain `nawrasinchina.com`
3. Add A record:
   - Name: `partner`
   - Value: `*************` (or `***************`)
   - TTL: `300`

## 🔍 Troubleshooting

### If Droplets Are Off:
```bash
# Power on using API
./digitalocean-api.sh
# Choose option 3, enter droplet ID
```

### If DNS Doesn't Work:
1. Check if domain is in DigitalOcean DNS
2. Verify A record exists and points to correct IP
3. Wait for DNS propagation (up to 48 hours, usually 5-30 minutes)
4. Test with: `nslookup partner.nawrasinchina.com`

### If Website Still Not Loading:
```bash
# SSH to working droplet
ssh root@*************

# Check services
docker ps
systemctl status nginx

# Restart services
cd /opt/nawras-admin
docker-compose restart
systemctl restart nginx
```

## 📊 Expected API Responses

### Successful Droplet List:
```json
{
  "droplets": [
    {
      "id": 123456789,
      "name": "nawras-admin-deployed",
      "status": "active",
      "networks": {
        "v4": [
          {
            "ip_address": "*************",
            "type": "public"
          }
        ]
      }
    }
  ]
}
```

### Successful DNS Record Creation:
```json
{
  "domain_record": {
    "id": 987654321,
    "type": "A",
    "name": "partner",
    "data": "*************",
    "ttl": 300
  }
}
```

## 🎯 Success Criteria

After running the solution:
- ✅ Droplets are powered on and accessible
- ✅ Website loads at IP address
- ✅ API responds at :3001 port
- ✅ DNS A record created for subdomain
- ✅ partner.nawrasinchina.com resolves correctly
- ✅ Full expense tracking functionality available

## 🆘 Support

If the automated scripts don't work:
1. Run the status check script first
2. Check DigitalOcean dashboard manually
3. Use the interactive API script for manual control
4. Contact DigitalOcean support if droplets won't start

The scripts use the official DigitalOcean API v2 and should handle most common issues automatically.
