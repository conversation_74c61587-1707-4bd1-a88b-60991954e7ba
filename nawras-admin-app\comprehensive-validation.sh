#!/bin/bash

# Comprehensive Application Validation Script
# Tests all functionality after deployment fixes

set -e

SERVER_IP="*************"
DOMAIN="partner.nawrasinchina.com"

echo "🧪 COMPREHENSIVE APPLICATION VALIDATION"
echo "======================================="
echo "Server: $SERVER_IP"
echo "Domain: $DOMAIN"
echo ""

# Initialize test tracking
declare -A test_results
total_tests=0
passed_tests=0

# Function to record test result
record_test() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    test_results["$test_name"]="$result|$details"
    ((total_tests++))
    
    if [ "$result" = "PASS" ]; then
        ((passed_tests++))
        echo "✅ $test_name: PASSED - $details"
    else
        echo "❌ $test_name: FAILED - $details"
    fi
}

echo "🔒 SECURITY VALIDATION"
echo "====================="

# Test 1: HTTPS Access
echo "Testing HTTPS access..."
https_test=$(curl -s -w "%{http_code}" https://$DOMAIN -o /dev/null)
if [ "$https_test" = "200" ]; then
    record_test "HTTPS_Access" "PASS" "HTTPS loads successfully"
else
    record_test "HTTPS_Access" "FAIL" "HTTPS returns HTTP $https_test"
fi

# Test 2: HTTP to HTTPS Redirect
echo "Testing HTTP to HTTPS redirect..."
redirect_test=$(curl -s -w "%{http_code}" -L http://$DOMAIN -o /dev/null)
if [ "$redirect_test" = "200" ]; then
    # Check if it actually redirected to HTTPS
    final_url=$(curl -s -w "%{url_effective}" -L http://$DOMAIN -o /dev/null)
    if [[ "$final_url" == https://* ]]; then
        record_test "HTTP_Redirect" "PASS" "HTTP redirects to HTTPS"
    else
        record_test "HTTP_Redirect" "FAIL" "No HTTPS redirect detected"
    fi
else
    record_test "HTTP_Redirect" "FAIL" "Redirect failed with HTTP $redirect_test"
fi

# Test 3: API Protection
echo "Testing API endpoint protection..."
api_protection=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses -o /dev/null)
if [ "$api_protection" = "401" ]; then
    record_test "API_Protection" "PASS" "API endpoints require authentication"
else
    record_test "API_Protection" "FAIL" "API endpoints not protected (HTTP $api_protection)"
fi

# Test 4: Authentication Endpoints
echo "Testing authentication endpoints..."
auth_session=$(curl -s -w "%{http_code}" https://$DOMAIN/api/auth/session -o /dev/null)
if [ "$auth_session" = "200" ]; then
    record_test "Auth_Endpoints" "PASS" "Authentication endpoints accessible"
else
    record_test "Auth_Endpoints" "FAIL" "Auth endpoints not working (HTTP $auth_session)"
fi

echo ""
echo "🔐 AUTHENTICATION VALIDATION"
echo "============================"

# Test 5: Taha Authentication
echo "Testing Taha's authentication..."
taha_auth=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"taha2024"}' \
    -c /tmp/taha_session.txt \
    -o /tmp/taha_response.json)

if [ "$taha_auth" = "200" ]; then
    record_test "Taha_Authentication" "PASS" "Taha can authenticate successfully"
    
    # Test authenticated API access
    taha_api=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses \
        -b /tmp/taha_session.txt -o /tmp/taha_expenses.json)
    
    if [ "$taha_api" = "200" ]; then
        record_test "Taha_API_Access" "PASS" "Taha can access protected APIs"
    else
        record_test "Taha_API_Access" "FAIL" "Taha cannot access APIs (HTTP $taha_api)"
    fi
else
    record_test "Taha_Authentication" "FAIL" "Taha authentication failed (HTTP $taha_auth)"
    record_test "Taha_API_Access" "FAIL" "Cannot test - authentication failed"
fi

# Test 6: Burak Authentication
echo "Testing Burak's authentication..."
burak_auth=$(curl -s -w "%{http_code}" -X POST https://$DOMAIN/api/auth/sign-in \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"burak2024"}' \
    -c /tmp/burak_session.txt \
    -o /tmp/burak_response.json)

if [ "$burak_auth" = "200" ]; then
    record_test "Burak_Authentication" "PASS" "Burak can authenticate successfully"
    
    # Test authenticated API access
    burak_api=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses \
        -b /tmp/burak_session.txt -o /tmp/burak_expenses.json)
    
    if [ "$burak_api" = "200" ]; then
        record_test "Burak_API_Access" "PASS" "Burak can access protected APIs"
    else
        record_test "Burak_API_Access" "FAIL" "Burak cannot access APIs (HTTP $burak_api)"
    fi
else
    record_test "Burak_Authentication" "FAIL" "Burak authentication failed (HTTP $burak_auth)"
    record_test "Burak_API_Access" "FAIL" "Cannot test - authentication failed"
fi

echo ""
echo "📊 DATA VALIDATION"
echo "=================="

# Test 7: Data Availability (using Taha's session)
if [ -f /tmp/taha_session.txt ]; then
    echo "Testing data availability..."
    
    # Test expenses data
    expenses_data=$(curl -s https://$DOMAIN/api/expenses -b /tmp/taha_session.txt)
    if echo "$expenses_data" | jq -e '.success' >/dev/null 2>&1; then
        expense_count=$(echo "$expenses_data" | jq '.total' 2>/dev/null || echo "0")
        if [ "$expense_count" -gt 0 ]; then
            record_test "Expenses_Data" "PASS" "$expense_count expenses available"
        else
            record_test "Expenses_Data" "FAIL" "No expenses data found"
        fi
    else
        record_test "Expenses_Data" "FAIL" "Invalid expenses API response"
    fi
    
    # Test settlements data
    settlements_data=$(curl -s https://$DOMAIN/api/settlements -b /tmp/taha_session.txt)
    if echo "$settlements_data" | jq -e '.success' >/dev/null 2>&1; then
        settlement_count=$(echo "$settlements_data" | jq '.total' 2>/dev/null || echo "0")
        if [ "$settlement_count" -gt 0 ]; then
            record_test "Settlements_Data" "PASS" "$settlement_count settlements available"
        else
            record_test "Settlements_Data" "FAIL" "No settlements data found"
        fi
    else
        record_test "Settlements_Data" "FAIL" "Invalid settlements API response"
    fi
else
    record_test "Expenses_Data" "FAIL" "Cannot test - no valid session"
    record_test "Settlements_Data" "FAIL" "Cannot test - no valid session"
fi

echo ""
echo "⚡ PERFORMANCE VALIDATION"
echo "========================"

# Test 8: Response Time
echo "Testing response times..."
start_time=$(date +%s%N)
response_code=$(curl -s -w "%{http_code}" https://$DOMAIN -o /dev/null)
end_time=$(date +%s%N)
response_time=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds

if [ $response_time -lt 3000 ]; then
    record_test "Response_Time" "PASS" "${response_time}ms (< 3000ms requirement)"
else
    record_test "Response_Time" "FAIL" "${response_time}ms (exceeds 3000ms requirement)"
fi

# Test 9: API Response Time
if [ -f /tmp/taha_session.txt ]; then
    echo "Testing API response times..."
    start_time=$(date +%s%N)
    api_response=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses \
        -b /tmp/taha_session.txt -o /dev/null)
    end_time=$(date +%s%N)
    api_time=$(( (end_time - start_time) / 1000000 ))
    
    if [ $api_time -lt 1000 ]; then
        record_test "API_Response_Time" "PASS" "${api_time}ms (< 1000ms target)"
    else
        record_test "API_Response_Time" "FAIL" "${api_time}ms (exceeds 1000ms target)"
    fi
else
    record_test "API_Response_Time" "FAIL" "Cannot test - no valid session"
fi

echo ""
echo "🌐 FRONTEND VALIDATION"
echo "====================="

# Test 10: Frontend Loading
echo "Testing frontend application..."
frontend_content=$(curl -s https://$DOMAIN)
if echo "$frontend_content" | grep -q "Nawras Admin"; then
    record_test "Frontend_Loading" "PASS" "Frontend application loads correctly"
else
    record_test "Frontend_Loading" "FAIL" "Frontend application not loading properly"
fi

# Test 11: Frontend Assets
echo "Testing frontend assets..."
if echo "$frontend_content" | grep -q -E "\.(js|css)"; then
    record_test "Frontend_Assets" "PASS" "Frontend assets are referenced"
else
    record_test "Frontend_Assets" "FAIL" "Frontend assets not found"
fi

echo ""
echo "🔧 SERVER HEALTH VALIDATION"
echo "=========================="

# Test 12: Health Endpoint
echo "Testing health endpoint..."
health_response=$(curl -s https://$DOMAIN/api/health)
if echo "$health_response" | jq -e '.status' >/dev/null 2>&1; then
    status=$(echo "$health_response" | jq -r '.status')
    if [ "$status" = "ok" ]; then
        record_test "Health_Endpoint" "PASS" "Health endpoint reports OK"
    else
        record_test "Health_Endpoint" "FAIL" "Health endpoint reports: $status"
    fi
else
    record_test "Health_Endpoint" "FAIL" "Invalid health endpoint response"
fi

echo ""
echo "📊 VALIDATION SUMMARY"
echo "===================="

# Calculate success rate
success_rate=$((passed_tests * 100 / total_tests))

echo "Total Tests: $total_tests"
echo "Passed: $passed_tests"
echo "Failed: $((total_tests - passed_tests))"
echo "Success Rate: $success_rate%"
echo ""

# Detailed results
echo "📋 DETAILED RESULTS:"
echo "==================="
for test_name in "${!test_results[@]}"; do
    IFS='|' read -r result details <<< "${test_results[$test_name]}"
    if [ "$result" = "PASS" ]; then
        echo "✅ $test_name: $details"
    else
        echo "❌ $test_name: $details"
    fi
done

echo ""
echo "🎯 OVERALL STATUS"
echo "================"

if [ $success_rate -ge 90 ]; then
    echo "🎉 EXCELLENT! Application meets production requirements (≥90%)"
    echo "✅ Ready for full production use"
elif [ $success_rate -ge 80 ]; then
    echo "✅ GOOD! Application is functional with minor issues (≥80%)"
    echo "⚠️ Address failed tests for optimal performance"
elif [ $success_rate -ge 70 ]; then
    echo "⚠️ ACCEPTABLE! Application has significant issues (≥70%)"
    echo "🔧 Immediate attention required for failed tests"
else
    echo "❌ CRITICAL! Application has major issues (<70%)"
    echo "🚨 Deployment fixes required before production use"
fi

echo ""
echo "🔗 Application URL: https://$DOMAIN"
echo "👥 User Credentials:"
echo "   • Taha: <EMAIL> / taha2024"
echo "   • Burak: <EMAIL> / burak2024"

# Cleanup
rm -f /tmp/taha_session.txt /tmp/burak_session.txt
rm -f /tmp/taha_response.json /tmp/burak_response.json
rm -f /tmp/taha_expenses.json /tmp/burak_expenses.json

echo ""
echo "📞 Next Steps Based on Results:"
if [ $success_rate -ge 90 ]; then
    echo "1. ✅ Application is production-ready"
    echo "2. 📊 Monitor performance and usage"
    echo "3. 🔄 Set up regular health checks"
    echo "4. 🚀 Proceed with production hardening"
elif [ $success_rate -ge 80 ]; then
    echo "1. 🔧 Fix remaining failed tests"
    echo "2. 🧪 Re-run validation after fixes"
    echo "3. 📊 Monitor critical functionality"
    echo "4. ⚠️ Address issues before hardening"
else
    echo "1. 🚨 Review and fix critical failures"
    echo "2. 🔄 Re-run emergency deployment scripts"
    echo "3. 🧪 Validate fixes before proceeding"
    echo "4. ❌ Do not proceed with hardening until fixed"
fi
