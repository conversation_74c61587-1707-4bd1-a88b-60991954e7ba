{"name": "nawras-admin-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "server": "node \"C:\\Users\\<USER>\\Documents\\augment-projects\\partner\\nawras-admin-app\\server\\index.js\"", "dev:full": "concurrently \"npm run server\" \"npm run dev\"", "build": "tsc -b && vite build", "build:analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "preview:dist": "npm run build && npm run preview", "type-check": "tsc --noEmit", "start:prod": "npm run build && npm run preview", "build:full": "npm run build && npm run server"}, "dependencies": {"@tanstack/react-query": "^5.77.2", "bcryptjs": "^3.0.2", "better-auth": "^1.2.8", "clsx": "^2.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "drizzle-orm": "^0.43.1", "express": "^4.21.2", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^2.15.3", "tailwindcss": "^3.4.17", "wouter": "^3.7.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tanstack/react-query-devtools": "^5.79.0", "@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.2", "@types/node": "^22.15.21", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/recharts": "^1.8.29", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "terser": "^5.39.2", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}