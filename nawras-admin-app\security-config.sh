#!/bin/bash

# Security Implementation Script for Production Server
# Run this on the production server: *************

echo "🔒 SECURITY IMPLEMENTATION"
echo "=========================="
echo "Server: *************"
echo "Domain: partner.nawrasinchina.com"
echo ""

# Step 1: Install Certbot for SSL
echo "📜 Step 1: Installing Certbot for SSL Certificate"
echo "================================================="

# Update package list
apt update

# Install certbot and nginx plugin
apt install -y certbot python3-certbot-nginx

if [ $? -eq 0 ]; then
    echo "✅ Certbot installed successfully"
else
    echo "❌ Failed to install Certbot"
    exit 1
fi

# Step 2: Configure Nginx with Security Headers
echo "🛡️ Step 2: Configuring Nginx with Security Headers"
echo "=================================================="

# Backup current nginx configuration
cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)

# Create new nginx configuration with security headers
cat > /etc/nginx/nginx.conf << 'EOF'
user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
    worker_connections 768;
    # multi_accept on;
}

http {
    ##
    # Basic Settings
    ##
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    ##
    # SSL Settings
    ##
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;

    ##
    # Logging Settings
    ##
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    ##
    # Gzip Settings
    ##
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    ##
    # Security Headers
    ##
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    ##
    # HTTP Server (Redirect to HTTPS)
    ##
    server {
        listen 80;
        server_name partner.nawrasinchina.com;
        
        # Redirect all HTTP requests to HTTPS
        return 301 https://$server_name$request_uri;
    }

    ##
    # HTTPS Server
    ##
    server {
        listen 443 ssl http2;
        server_name partner.nawrasinchina.com;

        # SSL Certificate (will be configured by Certbot)
        # ssl_certificate /etc/letsencrypt/live/partner.nawrasinchina.com/fullchain.pem;
        # ssl_certificate_key /etc/letsencrypt/live/partner.nawrasinchina.com/privkey.pem;

        # Security Headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        # Proxy to Node.js application
        location / {
            proxy_pass http://localhost:3001;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Important for authentication cookies
            proxy_set_header Cookie $http_cookie;
        }

        # API routes with specific headers
        location /api/ {
            proxy_pass http://localhost:3001;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Important for authentication
            proxy_set_header Cookie $http_cookie;
            
            # CORS headers for API
            add_header Access-Control-Allow-Origin "https://partner.nawrasinchina.com" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
            add_header Access-Control-Allow-Credentials "true" always;
        }

        # Handle preflight requests
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }
    }
}
EOF

echo "✅ Nginx configuration updated with security headers"

# Test nginx configuration
nginx -t
if [ $? -eq 0 ]; then
    echo "✅ Nginx configuration is valid"
else
    echo "❌ Nginx configuration is invalid"
    echo "🔄 Restoring backup..."
    cp /etc/nginx/nginx.conf.backup.$(date +%Y%m%d_%H%M%S) /etc/nginx/nginx.conf
    exit 1
fi

# Step 3: Obtain SSL Certificate
echo "🔐 Step 3: Obtaining SSL Certificate"
echo "===================================="

# Stop nginx temporarily for standalone mode
systemctl stop nginx

# Obtain SSL certificate
certbot certonly --standalone -d partner.nawrasinchina.com --non-interactive --agree-tos --email <EMAIL>

if [ $? -eq 0 ]; then
    echo "✅ SSL certificate obtained successfully"
    
    # Update nginx configuration with SSL certificate paths
    sed -i 's|# ssl_certificate /etc/letsencrypt/live/partner.nawrasinchina.com/fullchain.pem;|ssl_certificate /etc/letsencrypt/live/partner.nawrasinchina.com/fullchain.pem;|' /etc/nginx/nginx.conf
    sed -i 's|# ssl_certificate_key /etc/letsencrypt/live/partner.nawrasinchina.com/privkey.pem;|ssl_certificate_key /etc/letsencrypt/live/partner.nawrasinchina.com/privkey.pem;|' /etc/nginx/nginx.conf
    
    echo "✅ Nginx configuration updated with SSL certificate paths"
else
    echo "❌ Failed to obtain SSL certificate"
    echo "🔄 Starting nginx without SSL..."
fi

# Step 4: Start and Enable Services
echo "🚀 Step 4: Starting and Enabling Services"
echo "=========================================="

# Start nginx
systemctl start nginx
systemctl enable nginx

if [ $? -eq 0 ]; then
    echo "✅ Nginx started and enabled"
else
    echo "❌ Failed to start Nginx"
    exit 1
fi

# Test nginx status
systemctl status nginx --no-pager

# Step 5: Configure Firewall
echo "🔥 Step 5: Configuring Firewall"
echo "==============================="

# Allow HTTP and HTTPS traffic
ufw allow 'Nginx Full'
ufw allow ssh
ufw --force enable

echo "✅ Firewall configured"

# Step 6: Set up SSL Certificate Auto-Renewal
echo "🔄 Step 6: Setting up SSL Certificate Auto-Renewal"
echo "=================================================="

# Add cron job for certificate renewal
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

echo "✅ SSL certificate auto-renewal configured"

# Step 7: Final Security Tests
echo "🧪 Step 7: Running Security Tests"
echo "================================="

# Test HTTP to HTTPS redirect
echo "Testing HTTP to HTTPS redirect..."
http_response=$(curl -s -I http://partner.nawrasinchina.com | head -1)
if echo "$http_response" | grep -q "301\|302"; then
    echo "✅ HTTP to HTTPS redirect working"
else
    echo "⚠️ HTTP to HTTPS redirect may not be working"
fi

# Test HTTPS access
echo "Testing HTTPS access..."
https_response=$(curl -s -I https://partner.nawrasinchina.com | head -1)
if echo "$https_response" | grep -q "200"; then
    echo "✅ HTTPS access working"
else
    echo "⚠️ HTTPS access may not be working"
fi

# Test security headers
echo "Testing security headers..."
headers=$(curl -s -I https://partner.nawrasinchina.com)

if echo "$headers" | grep -q "Strict-Transport-Security"; then
    echo "✅ HSTS header present"
else
    echo "⚠️ HSTS header missing"
fi

if echo "$headers" | grep -q "X-XSS-Protection"; then
    echo "✅ XSS Protection header present"
else
    echo "⚠️ XSS Protection header missing"
fi

if echo "$headers" | grep -q "Content-Security-Policy"; then
    echo "✅ CSP header present"
else
    echo "⚠️ CSP header missing"
fi

echo ""
echo "🎉 SECURITY IMPLEMENTATION COMPLETE!"
echo "===================================="
echo ""
echo "✅ Completed Tasks:"
echo "   • SSL certificate installed"
echo "   • Security headers configured"
echo "   • HTTP to HTTPS redirect enabled"
echo "   • Firewall configured"
echo "   • Auto-renewal set up"
echo ""
echo "🔒 Security Features:"
echo "   • HTTPS/TLS 1.2+ encryption"
echo "   • HSTS (HTTP Strict Transport Security)"
echo "   • XSS Protection"
echo "   • Content Security Policy"
echo "   • X-Frame-Options"
echo "   • X-Content-Type-Options"
echo ""
echo "🌐 Your application is now secure:"
echo "   • HTTPS: https://partner.nawrasinchina.com"
echo "   • HTTP redirects to HTTPS automatically"
echo "   • Security headers protect against common attacks"
echo ""
