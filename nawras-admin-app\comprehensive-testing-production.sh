#!/bin/bash

# Comprehensive Testing Suite for Production
# Tests all application functionality on partner.nawrasinchina.com

DOMAIN="partner.nawrasinchina.com"
SERVER_IP="*************"

echo "🧪 COMPREHENSIVE TESTING SUITE - PRODUCTION"
echo "==========================================="
echo "Domain: $DOMAIN"
echo "Server: $SERVER_IP"
echo ""

# Initialize test results
declare -A test_results
total_tests=0
passed_tests=0

# Function to record test result
record_test() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    test_results["$test_name"]="$result|$details"
    ((total_tests++))
    
    if [ "$result" = "PASS" ]; then
        ((passed_tests++))
        echo "✅ $test_name: PASSED - $details"
    else
        echo "❌ $test_name: FAILED - $details"
    fi
}

# Test Suite 1: Security Tests
echo "🔒 TEST SUITE 1: SECURITY VALIDATION"
echo "===================================="

# Test 1.1: HTTPS Certificate
echo "Testing HTTPS certificate..."
if curl -s -I https://$DOMAIN | grep -q "HTTP/2 200\|HTTP/1.1 200"; then
    cert_info=$(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -subject 2>/dev/null)
    record_test "HTTPS_Certificate" "PASS" "Valid SSL certificate"
else
    record_test "HTTPS_Certificate" "FAIL" "SSL certificate not working"
fi

# Test 1.2: Security Headers
echo "Testing security headers..."
response=$(curl -s -I https://$DOMAIN)

# HSTS Header
if echo "$response" | grep -qi "strict-transport-security"; then
    record_test "HSTS_Header" "PASS" "HSTS header present"
else
    record_test "HSTS_Header" "FAIL" "HSTS header missing"
fi

# XSS Protection
if echo "$response" | grep -qi "x-xss-protection"; then
    record_test "XSS_Protection" "PASS" "XSS protection header present"
else
    record_test "XSS_Protection" "FAIL" "XSS protection header missing"
fi

# Content Security Policy
if echo "$response" | grep -qi "content-security-policy"; then
    record_test "CSP_Header" "PASS" "CSP header present"
else
    record_test "CSP_Header" "FAIL" "CSP header missing"
fi

# Test 1.3: HTTP to HTTPS Redirect
echo "Testing HTTP to HTTPS redirect..."
redirect_response=$(curl -s -I http://$DOMAIN)
if echo "$redirect_response" | grep -q "301\|302"; then
    redirect_location=$(echo "$redirect_response" | grep -i "location:" | cut -d' ' -f2 | tr -d '\r')
    if [[ "$redirect_location" == https://* ]]; then
        record_test "HTTP_Redirect" "PASS" "Properly redirects to HTTPS"
    else
        record_test "HTTP_Redirect" "FAIL" "Redirects but not to HTTPS"
    fi
else
    record_test "HTTP_Redirect" "FAIL" "No redirect configured"
fi

echo ""

# Test Suite 2: Application Functionality
echo "🌐 TEST SUITE 2: APPLICATION FUNCTIONALITY"
echo "=========================================="

# Test 2.1: Main Application
echo "Testing main application..."
main_response=$(curl -s -w "%{http_code}" https://$DOMAIN -o /dev/null)
if [ "$main_response" = "200" ]; then
    record_test "Main_Application" "PASS" "Application loads successfully"
else
    record_test "Main_Application" "FAIL" "Application failed to load (HTTP $main_response)"
fi

# Test 2.2: Health Check
echo "Testing health check endpoint..."
health_response=$(curl -s -w "%{http_code}" https://$DOMAIN/api/health -o /tmp/health_check.json)
if [ "$health_response" = "200" ]; then
    if command -v jq >/dev/null 2>&1; then
        if jq -e '.status == "ok"' /tmp/health_check.json >/dev/null 2>&1; then
            record_test "Health_Check" "PASS" "Health check endpoint working"
        else
            record_test "Health_Check" "FAIL" "Health check returns invalid status"
        fi
    else
        record_test "Health_Check" "PASS" "Health check accessible (jq not available)"
    fi
else
    record_test "Health_Check" "FAIL" "Health check not accessible (HTTP $health_response)"
fi

# Test 2.3: Authentication Required for API
echo "Testing API protection..."
expenses_response=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses -o /tmp/expenses_test.json)
if [ "$expenses_response" = "401" ]; then
    record_test "API_Protection" "PASS" "API properly requires authentication"
elif [ "$expenses_response" = "200" ]; then
    if command -v jq >/dev/null 2>&1; then
        if jq -e '.success == false' /tmp/expenses_test.json >/dev/null 2>&1; then
            record_test "API_Protection" "PASS" "API returns error for unauthenticated access"
        else
            record_test "API_Protection" "FAIL" "API allows unauthenticated access"
        fi
    else
        record_test "API_Protection" "PASS" "API accessible but may be protected (jq not available)"
    fi
else
    record_test "API_Protection" "FAIL" "Unexpected API response (HTTP $expenses_response)"
fi

echo ""

# Test Suite 3: Dashboard Components
echo "📊 TEST SUITE 3: DASHBOARD COMPONENTS"
echo "===================================="

# Test 3.1: Dashboard Content
echo "Testing dashboard content..."
dashboard_content=$(curl -s https://$DOMAIN)

# Check for React app indicators
if echo "$dashboard_content" | grep -q "react\|React\|vite\|Vite"; then
    record_test "React_App" "PASS" "React application detected"
else
    record_test "React_App" "FAIL" "React application not detected"
fi

# Check for dashboard components
if echo "$dashboard_content" | grep -q "BalanceSummary\|ExpenseChart\|CategoryBreakdown\|RecentExpenses"; then
    record_test "Dashboard_Components" "PASS" "Dashboard components detected in build"
else
    record_test "Dashboard_Components" "FAIL" "Dashboard components not detected"
fi

# Test 3.2: Authentication Components
echo "Testing authentication components..."
if echo "$dashboard_content" | grep -q "AuthProvider\|ProtectedRoute\|LoginPage"; then
    record_test "Auth_Components" "PASS" "Authentication components detected"
else
    record_test "Auth_Components" "FAIL" "Authentication components not detected"
fi

echo ""

# Test Suite 4: Performance Tests
echo "⚡ TEST SUITE 4: PERFORMANCE VALIDATION"
echo "======================================"

# Test 4.1: Response Time
echo "Testing response times..."
times=()
for i in {1..3}; do
    time=$(curl -s -w "%{time_total}" https://$DOMAIN -o /dev/null)
    times+=($time)
done

# Calculate average
total=0
for time in "${times[@]}"; do
    total=$(echo "$total + $time" | bc -l 2>/dev/null || echo "0")
done
average=$(echo "scale=3; $total / ${#times[@]}" | bc -l 2>/dev/null || echo "0")

if (( $(echo "$average < 3.0" | bc -l 2>/dev/null || echo "0") )); then
    record_test "Response_Time" "PASS" "Average: ${average}s (< 3s)"
else
    record_test "Response_Time" "FAIL" "Average: ${average}s (> 3s)"
fi

# Test 4.2: API Performance
echo "Testing API performance..."
api_time=$(curl -s -w "%{time_total}" https://$DOMAIN/api/health -o /dev/null)
if (( $(echo "$api_time < 2.0" | bc -l 2>/dev/null || echo "0") )); then
    record_test "API_Performance" "PASS" "API response: ${api_time}s (< 2s)"
else
    record_test "API_Performance" "FAIL" "API response: ${api_time}s (> 2s)"
fi

echo ""

# Test Suite 5: Mobile Responsiveness
echo "📱 TEST SUITE 5: MOBILE RESPONSIVENESS"
echo "====================================="

# Test 5.1: Mobile Viewport
echo "Testing mobile viewport..."
mobile_content=$(curl -s -H "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)" https://$DOMAIN)
if echo "$mobile_content" | grep -q "viewport.*width=device-width"; then
    record_test "Mobile_Viewport" "PASS" "Mobile viewport meta tag present"
else
    record_test "Mobile_Viewport" "FAIL" "Mobile viewport meta tag missing"
fi

# Test 5.2: Responsive CSS
echo "Testing responsive CSS..."
if echo "$mobile_content" | grep -q "sm:\|md:\|lg:\|xl:"; then
    record_test "Responsive_CSS" "PASS" "Responsive CSS classes detected"
else
    record_test "Responsive_CSS" "FAIL" "Responsive CSS classes not detected"
fi

echo ""

# Test Suite 6: Error Handling
echo "🚨 TEST SUITE 6: ERROR HANDLING"
echo "==============================="

# Test 6.1: 404 Handling
echo "Testing 404 error handling..."
not_found_response=$(curl -s -w "%{http_code}" https://$DOMAIN/nonexistent-page -o /dev/null)
if [ "$not_found_response" = "404" ] || [ "$not_found_response" = "200" ]; then
    record_test "404_Handling" "PASS" "404 errors handled properly"
else
    record_test "404_Handling" "FAIL" "404 errors not handled (HTTP $not_found_response)"
fi

# Test 6.2: API Error Handling
echo "Testing API error handling..."
api_error_response=$(curl -s -w "%{http_code}" https://$DOMAIN/api/nonexistent -o /dev/null)
if [ "$api_error_response" = "404" ] || [ "$api_error_response" = "401" ]; then
    record_test "API_Error_Handling" "PASS" "API errors handled properly"
else
    record_test "API_Error_Handling" "FAIL" "API errors not handled (HTTP $api_error_response)"
fi

echo ""

# Generate Comprehensive Test Report
echo "📋 COMPREHENSIVE TEST REPORT"
echo "============================"
echo ""

# Calculate success rate
if [ $total_tests -gt 0 ]; then
    success_rate=$(echo "scale=1; $passed_tests * 100 / $total_tests" | bc -l 2>/dev/null || echo "0")
else
    success_rate="0"
fi

echo "📊 Test Summary:"
echo "   Total Tests: $total_tests"
echo "   Passed: $passed_tests"
echo "   Failed: $((total_tests - passed_tests))"
echo "   Success Rate: $success_rate%"
echo ""

# Determine overall status
if (( $(echo "$success_rate >= 90" | bc -l 2>/dev/null || echo "0") )); then
    overall_status="🟢 EXCELLENT"
elif (( $(echo "$success_rate >= 80" | bc -l 2>/dev/null || echo "0") )); then
    overall_status="🟡 GOOD"
elif (( $(echo "$success_rate >= 70" | bc -l 2>/dev/null || echo "0") )); then
    overall_status="🟠 FAIR"
else
    overall_status="🔴 NEEDS IMPROVEMENT"
fi

echo "🎯 Overall Status: $overall_status"
echo ""

# Show failed tests
if [ $((total_tests - passed_tests)) -gt 0 ]; then
    echo "❌ Failed Tests:"
    for test_name in "${!test_results[@]}"; do
        IFS='|' read -r result details <<< "${test_results[$test_name]}"
        if [ "$result" = "FAIL" ]; then
            echo "   • $test_name: $details"
        fi
    done
    echo ""
fi

# Recommendations
echo "💡 Recommendations:"
if (( $(echo "$success_rate >= 90" | bc -l 2>/dev/null || echo "0") )); then
    echo "   ✅ Application is ready for production!"
    echo "   ✅ All critical systems are functioning properly"
    echo "   ✅ Security measures are in place"
    echo "   ✅ Performance is excellent"
else
    echo "   ⚠️ Address failed tests before full production deployment"
    echo "   ⚠️ Review security implementations"
    echo "   ⚠️ Optimize performance if needed"
fi

# Cleanup temporary files
rm -f /tmp/health_check.json /tmp/expenses_test.json

echo ""
echo "🎉 COMPREHENSIVE TESTING COMPLETE!"
echo "================================="

# Exit with appropriate code
if (( $(echo "$success_rate >= 80" | bc -l 2>/dev/null || echo "0") )); then
    exit 0
else
    exit 1
fi
