# Manual Deployment Commands for Nawras Admin
# Execute these commands one by one in the DigitalOcean console

# 1. Update system and install Node.js
apt-get update -y
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs nginx

# 2. Install PM2
npm install -g pm2

# 3. Create application directory
mkdir -p /opt/nawras-admin
cd /opt/nawras-admin

# 4. Create package.json
cat > package.json << 'EOF'
{
  "name": "nawras-admin-backend",
  "version": "1.0.0",
  "main": "server.js",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5"
  },
  "scripts": {
    "start": "node server.js"
  }
}
EOF

# 5. Install dependencies
npm install

# 6. Create server.js (IMPORTANT: Copy the entire server.js content from the deployment files)

# 7. Start backend with PM2
pm2 start server.js --name "nawras-backend"
pm2 startup
pm2 save

# 8. Configure Nginx
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80;
    server_name _;
    
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
}
EOF

# 9. Configure firewall
ufw allow 22
ufw allow 80
ufw allow 3001
ufw --force enable

# 10. Restart services
systemctl restart nginx
systemctl enable nginx

# 11. Test deployment
curl http://localhost:3001/api/health
curl http://localhost/api/health
