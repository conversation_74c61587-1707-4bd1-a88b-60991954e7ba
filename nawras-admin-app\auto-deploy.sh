#!/bin/bash

# Nawras Admin Auto-Deployment Script
# This script will be executed on the DigitalOcean droplet

set -e
export DEBIAN_FRONTEND=noninteractive

echo "🚀 Starting Nawras Admin Auto-Deployment..."
echo "📍 Server IP: ***************"
echo "⏰ Started at: $(date)"

# Update system
echo "📦 Updating system packages..."
apt-get update -y
apt-get upgrade -y

# Install Node.js 18
echo "📦 Installing Node.js 18..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Install additional packages
echo "📦 Installing additional packages..."
apt-get install -y nginx git curl wget unzip

# Install PM2 globally
echo "📦 Installing PM2..."
npm install -g pm2

# Create application directory
echo "📁 Creating application directory..."
mkdir -p /opt/nawras-admin
cd /opt/nawras-admin

# Create backend package.json
echo "📝 Creating backend package.json..."
cat > package.json << 'PACKAGE_EOF'
{
  "name": "nawras-admin-backend",
  "version": "1.0.0",
  "description": "Nawras Admin Backend API",
  "main": "server.js",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5"
  },
  "scripts": {
    "start": "node server.js",
    "dev": "node server.js"
  }
}
PACKAGE_EOF

# Install backend dependencies
echo "📦 Installing backend dependencies..."
npm install

# Create backend server
echo "🔧 Creating backend server..."
cat > server.js << 'SERVER_EOF'
const express = require('express');
const cors = require('cors');
const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://***************', 'http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));
app.use(express.json());

// In-memory storage with comprehensive sample data
let expenses = [
  {id: 1, amount: 25.50, description: "Lunch at restaurant", category: "Food", paidById: "taha", date: "2024-01-15", createdAt: new Date().toISOString()},
  {id: 2, amount: 60.99, description: "Grocery shopping", category: "Groceries", paidById: "burak", date: "2024-01-16", createdAt: new Date().toISOString()},
  {id: 3, amount: 15.75, description: "Coffee and snacks", category: "Food", paidById: "taha", date: "2024-01-17", createdAt: new Date().toISOString()},
  {id: 4, amount: 120.00, description: "Monthly utilities", category: "Utilities", paidById: "burak", date: "2024-01-18", createdAt: new Date().toISOString()},
  {id: 5, amount: 45.30, description: "Gas station", category: "Transportation", paidById: "taha", date: "2024-01-19", createdAt: new Date().toISOString()}
];

let settlements = [
  {id: 1, amount: 30.00, paidBy: "taha", paidTo: "burak", description: "Settlement for shared expenses", date: "2024-01-17", createdAt: new Date().toISOString()},
  {id: 2, amount: 25.75, paidBy: "burak", paidTo: "taha", description: "Reimbursement for utilities", date: "2024-01-18", createdAt: new Date().toISOString()},
  {id: 3, amount: 50.00, paidBy: "taha", paidTo: "burak", description: "Monthly settlement", date: "2024-01-20", createdAt: new Date().toISOString()}
];

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    server: 'production',
    expenses_count: expenses.length,
    settlements_count: settlements.length
  });
});

app.get('/api/expenses', (req, res) => {
  const { paidBy, category, limit } = req.query;
  let filteredExpenses = [...expenses];
  
  if (paidBy) filteredExpenses = filteredExpenses.filter(e => e.paidById === paidBy);
  if (category) filteredExpenses = filteredExpenses.filter(e => e.category === category);
  if (limit) filteredExpenses = filteredExpenses.slice(0, parseInt(limit));
  
  res.json({ 
    success: true, 
    data: filteredExpenses, 
    total: filteredExpenses.length,
    timestamp: new Date().toISOString()
  });
});

app.get('/api/expenses/:id', (req, res) => {
  const expense = expenses.find(e => e.id === parseInt(req.params.id));
  if (!expense) return res.status(404).json({ success: false, error: 'Expense not found' });
  res.json({ success: true, data: expense });
});

app.post('/api/expenses', (req, res) => {
  const { amount, description, category, paidById, date } = req.body;
  if (!amount || !description || !category || !paidById || !date) {
    return res.status(400).json({ success: false, error: 'Missing required fields: amount, description, category, paidById, date' });
  }
  const expense = {id: expenses.length + 1, amount: parseFloat(amount), description, category, paidById, date, createdAt: new Date().toISOString()};
  expenses.push(expense);
  res.json({ success: true, data: expense });
});

app.get('/api/settlements', (req, res) => {
  res.json({ success: true, data: settlements, total: settlements.length, timestamp: new Date().toISOString() });
});

app.get('/api/settlements/:id', (req, res) => {
  const settlement = settlements.find(s => s.id === parseInt(req.params.id));
  if (!settlement) return res.status(404).json({ success: false, error: 'Settlement not found' });
  res.json({ success: true, data: settlement });
});

app.post('/api/settlements', (req, res) => {
  const { amount, paidBy, paidTo, description, date } = req.body;
  if (!amount || !paidBy || !paidTo || !date) {
    return res.status(400).json({ success: false, error: 'Missing required fields: amount, paidBy, paidTo, date' });
  }
  const settlement = {id: settlements.length + 1, amount: parseFloat(amount), paidBy, paidTo, description: description || '', date, createdAt: new Date().toISOString()};
  settlements.push(settlement);
  res.json({ success: true, data: settlement });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err.stack);
  res.status(500).json({ success: false, error: 'Internal server error' });
});

app.use('/api/*', (req, res) => res.status(404).json({ success: false, error: 'API endpoint not found' }));

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Nawras Admin API server running on port ${PORT}`);
  console.log(`📊 Health check: http://***************:${PORT}/api/health`);
  console.log(`🌐 CORS enabled for frontend communication`);
  console.log(`📈 Sample data loaded: ${expenses.length} expenses, ${settlements.length} settlements`);
});
SERVER_EOF

echo "✅ Backend server created successfully!"

# Start backend with PM2
echo "🔥 Starting backend with PM2..."
pm2 start server.js --name "nawras-backend" --watch
pm2 startup
pm2 save

echo "✅ Backend deployed and running!"

# Test backend
echo "🧪 Testing backend API..."
sleep 3
curl -f http://localhost:3001/api/health || echo "❌ Backend health check failed"

# Configure firewall
echo "🔒 Configuring firewall..."
ufw allow 22
ufw allow 80
ufw allow 443
ufw allow 3001
ufw --force enable

# Configure Nginx
echo "🌐 Configuring Nginx..."
cat > /etc/nginx/sites-available/default << 'NGINX_EOF'
server {
    listen 80;
    server_name _;
    root /var/www/html;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # API proxy to backend
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}
NGINX_EOF

# Test nginx configuration
nginx -t

# Restart nginx
systemctl restart nginx
systemctl enable nginx

echo "✅ Nginx configured and started!"

echo "🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!"
echo "=================================="
echo "🌐 Frontend: http://***************"
echo "🔗 Backend API: http://***************:3001"
echo "📊 Health Check: http://***************:3001/api/health"
echo "📊 Expenses: http://***************/api/expenses"
echo "📊 Settlements: http://***************/api/settlements"
echo ""
echo "✅ Backend: Running with PM2"
echo "✅ Frontend: Nginx serving on port 80"
echo "✅ Firewall: Configured"
echo "✅ API Proxy: Configured"
echo ""
echo "⏰ Completed at: $(date)"
