#!/bin/bash

# Nawras Admin Deployment Script for DigitalOcean
# Server IP: ***************

SERVER_IP="***************"
APP_NAME="nawras-admin"

echo "🚀 Starting deployment to DigitalOcean..."

# Create deployment directory on server
echo "📁 Creating deployment directory..."
ssh -o StrictHostKeyChecking=no root@$SERVER_IP "mkdir -p /opt/$APP_NAME"

# Upload application files
echo "📤 Uploading application files..."
rsync -avz --exclude 'node_modules' --exclude '.git' --exclude 'dist' . root@$SERVER_IP:/opt/$APP_NAME/

# Connect to server and deploy
echo "🐳 Deploying with Docker..."
ssh -o StrictHostKeyChecking=no root@$SERVER_IP << EOF
cd /opt/$APP_NAME

# Stop existing containers
docker-compose down 2>/dev/null || true

# Build and start containers
docker-compose up --build -d

# Show status
docker-compose ps

echo "✅ Deployment complete!"
echo "🌐 Frontend: http://$SERVER_IP"
echo "🔗 API: http://$SERVER_IP:3001"
EOF

echo "🎉 Deployment finished!"
echo "🌐 Your app is available at: http://$SERVER_IP"
