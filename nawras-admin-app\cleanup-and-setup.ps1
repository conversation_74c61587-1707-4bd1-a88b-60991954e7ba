# Cleanup Unnecessary Droplet and Setup DNS
# Based on the analysis, we'll delete the non-working droplet and setup DNS

$DO_TOKEN = "***********************************************************************"
$API_BASE = "https://api.digitalocean.com/v2"

# Droplet Analysis Results:
# nawras-admin-app (ID: 498520148, IP: ***************) - NOT WORKING ❌
# nawras-admin-deployed (ID: 498524048, IP: *************) - WORKING ✅

$WORKING_DROPLET_ID = "498524048"
$WORKING_DROPLET_IP = "*************"
$WORKING_DROPLET_NAME = "nawras-admin-deployed"

$UNUSED_DROPLET_ID = "498520148"
$UNUSED_DROPLET_IP = "***************"
$UNUSED_DROPLET_NAME = "nawras-admin-app"

$DOMAIN = "nawrasinchina.com"
$SUBDOMAIN = "partner"

Write-Host "🎯 Nawras Admin - Cleanup and DNS Setup" -ForegroundColor Blue
Write-Host "=======================================" -ForegroundColor Blue
Write-Host ""

# Set up headers
$headers = @{
    "Authorization" = "Bearer $DO_TOKEN"
    "Content-Type" = "application/json"
}

Write-Host "📊 Analysis Results:" -ForegroundColor Yellow
Write-Host "✅ WORKING: $WORKING_DROPLET_NAME ($WORKING_DROPLET_IP)" -ForegroundColor Green
Write-Host "   - HTTP: Responding" -ForegroundColor Green
Write-Host "   - API: Responding" -ForegroundColor Green
Write-Host "   - Cost: ~$6/month" -ForegroundColor Green
Write-Host ""
Write-Host "❌ NOT WORKING: $UNUSED_DROPLET_NAME ($UNUSED_DROPLET_IP)" -ForegroundColor Red
Write-Host "   - HTTP: Not responding" -ForegroundColor Red
Write-Host "   - API: Not responding" -ForegroundColor Red
Write-Host "   - Cost: ~$6/month (WASTED)" -ForegroundColor Red
Write-Host ""

Write-Host "💰 RECOMMENDATION: Delete the non-working droplet to save ~$6/month" -ForegroundColor Yellow
Write-Host ""

# Ask user for confirmation
$deleteConfirm = Read-Host "Do you want to DELETE the non-working droplet '$UNUSED_DROPLET_NAME'? (yes/no)"

if ($deleteConfirm -eq "yes") {
    Write-Host ""
    Write-Host "🗑️ Deleting unused droplet..." -ForegroundColor Red
    
    try {
        $deleteResponse = Invoke-RestMethod -Uri "$API_BASE/droplets/$UNUSED_DROPLET_ID" -Headers $headers -Method Delete
        Write-Host "✅ Droplet '$UNUSED_DROPLET_NAME' deleted successfully!" -ForegroundColor Green
        Write-Host "💰 You'll save approximately $6/month" -ForegroundColor Green
    } catch {
        Write-Host "❌ Error deleting droplet: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⏭️ Skipping droplet deletion" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🌐 Setting up DNS for subdomain..." -ForegroundColor Blue

# Check if domain exists in DigitalOcean
try {
    $domainResponse = Invoke-RestMethod -Uri "$API_BASE/domains/$DOMAIN" -Headers $headers -Method Get
    Write-Host "✅ Domain '$DOMAIN' found in your DigitalOcean account" -ForegroundColor Green
    
    # Get existing DNS records
    $recordsResponse = Invoke-RestMethod -Uri "$API_BASE/domains/$DOMAIN/records" -Headers $headers -Method Get
    
    # Check if subdomain A record already exists
    $existingRecord = $recordsResponse.domain_records | Where-Object { $_.name -eq $SUBDOMAIN -and $_.type -eq "A" }
    
    if ($existingRecord) {
        Write-Host "📝 Updating existing A record for '$SUBDOMAIN.$DOMAIN'..." -ForegroundColor Yellow
        
        $updateData = @{
            type = "A"
            name = $SUBDOMAIN
            data = $WORKING_DROPLET_IP
            ttl = 300
        } | ConvertTo-Json
        
        try {
            $updateResponse = Invoke-RestMethod -Uri "$API_BASE/domains/$DOMAIN/records/$($existingRecord.id)" -Headers $headers -Method Put -Body $updateData
            Write-Host "✅ A record updated successfully!" -ForegroundColor Green
            Write-Host "   $SUBDOMAIN.$DOMAIN → $WORKING_DROPLET_IP" -ForegroundColor Green
        } catch {
            Write-Host "❌ Error updating A record: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "🆕 Creating new A record for '$SUBDOMAIN.$DOMAIN'..." -ForegroundColor Yellow
        
        $createData = @{
            type = "A"
            name = $SUBDOMAIN
            data = $WORKING_DROPLET_IP
            ttl = 300
        } | ConvertTo-Json
        
        try {
            $createResponse = Invoke-RestMethod -Uri "$API_BASE/domains/$DOMAIN/records" -Headers $headers -Method Post -Body $createData
            Write-Host "✅ A record created successfully!" -ForegroundColor Green
            Write-Host "   Record ID: $($createResponse.domain_record.id)" -ForegroundColor Green
            Write-Host "   $SUBDOMAIN.$DOMAIN → $WORKING_DROPLET_IP" -ForegroundColor Green
        } catch {
            Write-Host "❌ Error creating A record: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "❌ Domain '$DOMAIN' not found in your DigitalOcean account" -ForegroundColor Red
    Write-Host "💡 You need to:" -ForegroundColor Yellow
    Write-Host "   1. Add the domain to DigitalOcean DNS" -ForegroundColor Yellow
    Write-Host "   2. Update your domain's nameservers to DigitalOcean's:" -ForegroundColor Yellow
    Write-Host "      - ns1.digitalocean.com" -ForegroundColor Yellow
    Write-Host "      - ns2.digitalocean.com" -ForegroundColor Yellow
    Write-Host "      - ns3.digitalocean.com" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔗 Add domain here: https://cloud.digitalocean.com/networking/domains" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "🎉 Setup Complete!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Your website URLs:" -ForegroundColor Blue
Write-Host "   Primary: http://$WORKING_DROPLET_IP" -ForegroundColor Green
Write-Host "   Subdomain: http://$SUBDOMAIN.$DOMAIN (after DNS propagation)" -ForegroundColor Yellow
Write-Host ""
Write-Host "⏰ DNS Propagation Timeline:" -ForegroundColor Blue
Write-Host "   • 0-5 minutes: DigitalOcean DNS updates" -ForegroundColor Green
Write-Host "   • 5-30 minutes: Local ISP DNS updates" -ForegroundColor Yellow
Write-Host "   • 2-6 hours: Global DNS propagation" -ForegroundColor Yellow
Write-Host "   • Up to 48 hours: Complete worldwide propagation" -ForegroundColor Red
Write-Host ""
Write-Host "🧪 Test DNS propagation:" -ForegroundColor Blue
Write-Host "   nslookup $SUBDOMAIN.$DOMAIN" -ForegroundColor Cyan
Write-Host "   dig $SUBDOMAIN.$DOMAIN" -ForegroundColor Cyan
Write-Host ""
Write-Host "💰 Monthly Savings:" -ForegroundColor Green
if ($deleteConfirm -eq "yes") {
    Write-Host "   ✅ ~$6/month saved by deleting unused droplet" -ForegroundColor Green
} else {
    Write-Host "   ⚠️ ~$6/month still being charged for unused droplet" -ForegroundColor Yellow
}
Write-Host ""

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
