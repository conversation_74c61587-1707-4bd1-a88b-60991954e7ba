# Security Configuration

## Content Security Policy (CSP)

The application implements a Content Security Policy to prevent XSS attacks:

```
default-src 'self' http: https: data: blob: 'unsafe-inline'
```

## Security Headers

The following security headers are configured in nginx:

- `X-Frame-Options: SAMEORIGIN` - Prevents clickjacking
- `X-XSS-Protection: 1; mode=block` - Enables XSS filtering
- `X-Content-Type-Options: nosniff` - Prevents MIME type sniffing
- `Referrer-Policy: no-referrer-when-downgrade` - Controls referrer information

## Data Security

### Local Storage
- User settings are stored in browser localStorage
- No sensitive financial data is transmitted to external servers
- Settings can be exported/imported securely

### API Security
- CORS is properly configured
- Input validation on all API endpoints
- Error messages don't expose sensitive information

### Privacy Features
- Analytics sharing is opt-in
- Data retention policies are user-configurable
- No external tracking without consent

## Production Security Checklist

- [ ] Enable HTTPS in production
- [ ] Configure proper CORS origins
- [ ] Set secure cookie flags
- [ ] Enable CSP in strict mode
- [ ] Regular security updates
- [ ] Monitor for vulnerabilities
- [ ] Implement rate limiting
- [ ] Use environment variables for secrets

## Development Security

- Environment variables are properly configured
- No secrets in source code
- TypeScript provides type safety
- ESLint catches potential security issues
