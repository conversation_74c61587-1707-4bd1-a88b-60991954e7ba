import type { Config } from 'tailwindcss'

export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  safelist: [
    // Layout classes
    'bg-white', 'bg-gray-50', 'bg-gray-100', 'bg-gray-200',
    'bg-blue-50', 'bg-blue-100', 'bg-green-50', 'bg-green-100',
    'bg-purple-50', 'bg-purple-100', 'bg-orange-50', 'bg-orange-100',
    'bg-red-50', 'bg-red-100',

    // Text colors
    'text-blue-600', 'text-blue-700', 'text-green-600', 'text-green-700',
    'text-purple-600', 'text-purple-700', 'text-orange-600', 'text-orange-700',
    'text-red-500', 'text-red-600', 'text-gray-400', 'text-gray-500', 'text-gray-600',
    'text-gray-700', 'text-gray-800', 'text-gray-900',

    // Border and rounded
    'border-gray-200', 'border-gray-300', 'rounded-lg', 'rounded-full', 'shadow-sm',
    'shadow-md', 'shadow-lg',

    // Spacing
    'p-3', 'p-4', 'p-6', 'px-3', 'px-4', 'px-6', 'py-2', 'py-3', 'py-4',
    'm-1', 'm-2', 'm-3', 'mb-1', 'mb-2', 'mb-3', 'mb-4', 'mt-1', 'mt-2', 'mt-3',
    'space-y-3', 'space-y-4', 'space-y-6',

    // Sizing
    'h-3', 'h-4', 'h-5', 'h-8', 'h-10', 'h-12', 'w-3', 'w-4', 'w-5', 'w-8', 'w-10',
    'w-12', 'w-16', 'w-20', 'w-1/2', 'w-3/4',

    // Typography
    'text-xs', 'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl',
    'font-medium', 'font-semibold', 'font-bold',

    // Animation
    'animate-pulse', 'animate-spin',

    // Hover states
    'hover:text-blue-700', 'hover:text-green-700', 'hover:bg-gray-50',
    'hover:bg-blue-50', 'hover:bg-green-50',
  ],
  theme: {
    extend: {},
  },
  plugins: [],
} satisfies Config
