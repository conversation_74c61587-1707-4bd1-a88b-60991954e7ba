# Nawras Admin MVP – Granular Step-by-Step Task List

> Each task below is atomic, testable, and has a clear start and end. The goal is to allow an LLM-based agent to execute each task independently, with verification between steps.

---

## 1. 🛠️ Project Setup

### Task 1: Initialize Vite React + TS Project

* **Start**: Run `npm create vite@latest` with React + TypeScript
* **End**: App runs with `npm run dev`, showing default Vite page

### Task 2: Install Core Dependencies

* **Start**: Add libraries: `react-query`, `wouter`, `tailwindcss`, `clsx`, `shadcn/ui`, `lucide-react`, `recharts`, `drizzle-orm`, `express`
* **End**: Packages installed and listed in `package.json`

### Task 3: Set Up TailwindCSS

* **Start**: Configure `tailwind.config.ts` and import CSS into `main.tsx`
* **End**: Tailwind utility classes work in a basic `div`

### Task 4: Setup Project Folder Structure

* **Start**: Create folders: `/components`, `/features`, `/hooks`, `/pages`, `/services`, `/routes`, `/types`, `/lib`, `/styles`
* **End**: Empty scaffold for architecture is in place

---

## 2. 🧱 Core Layout + Routing

### Task 5: Add Wouter Routing

* **Start**: Install Wouter and create `pages/index.tsx`
* **End**: Navigating to `/` renders homepage component

### Task 6: Implement Sidebar Navigation

* **Start**: Create `/components/Sidebar.tsx` with dummy routes
* **End**: Sidebar with clickable nav to `/`, `/add-expense`, etc.

### Task 7: Setup MobileNav Component

* **Start**: Create `/components/MobileNav.tsx`
* **End**: Displayed only on small screens with route icons

### Task 8: Build Basic Dashboard Layout

* **Start**: Create `/pages/index.tsx` with `<Sidebar />`, `<MobileNav />`
* **End**: Layout renders navigation + empty content area

---

## 3. 🧮 Expense Data Flow

### Task 9: Create Fake Expense API Route (Express)

* **Start**: Set up `/api/expenses` returning mock JSON
* **End**: Fetching from endpoint returns expense list

### Task 10: Create Expense Types

* **Start**: Define `Expense` type in `/types/expense.ts`
* **End**: Type includes `id`, `amount`, `description`, `category`, `paidById`, `date`

### Task 11: Create React Query Hook `useExpenses`

* **Start**: Add hook to `/hooks/useExpenses.ts`
* **End**: Returns expense data from mock API

### Task 12: Display `RecentExpenses` Table

* **Start**: Create component rendering paginated table
* **End**: Shows first 4 expenses from query

### Task 13: Add Filters to RecentExpenses

* **Start**: Add dropdown + search input for filtering by description/category/date
* **End**: Table updates results based on input

---

## 4. 💵 Balance Summary

### Task 14: Create `BalanceSummary` Component

* **Start**: Add 4 cards: Your Expenses, Partner's Expenses, Combined, Net Balance
* **End**: Static values displayed in dashboard

### Task 15: Implement Balance Calculation Logic

* **Start**: In `lib/calculateBalance.ts`, compute values from expenses
* **End**: Component shows real-time balance updates

---

## 5. 📊 Charts

### Task 16: Create `ExpenseChart` Component

* **Start**: Render monthly bar chart using Recharts
* **End**: Shows monthly breakdown by partner

### Task 17: Create `CategoryBreakdown` Component

* **Start**: Progress bar visualization of top 4 categories
* **End**: Display based on current expense data

---

## 6. ➕ Add Expense Flow

### Task 18: Create Add Expense Page

* **Start**: Route `/add-expense`, renders form
* **End**: Inputs: amount, description, category, date, who paid

### Task 19: Add Expense Submit Logic

* **Start**: Form posts to mock `/api/expenses` with new item
* **End**: Appends to local state and revalidates query

### Task 20: Receipt Upload (optional)

* **Start**: Add input type `file`
* **End**: Preview or save base64 (no backend logic yet)

---

## 7. 🔍 History Page

### Task 21: Build `/history` Page

* **Start**: Create table component with full list of expenses
* **End**: Supports filters + sort by date/amount

### Task 22: Add Export Button

* **Start**: Add button to download CSV of filtered data
* **End**: CSV generates using current search state

---

## 8. 📄 Reports Page

### Task 23: Build `/reports` Page

* **Start**: Create charts: Monthly, category-wise, partner split
* **End**: Uses mock data to populate charts

---

## 9. 🤝 Settlement Logic

### Task 24: Create Settlement Page `/settlement`

* **Start**: Form to enter amount paid to settle balance
* **End**: Posts mock record to `/api/settlements`

### Task 25: Show Settlement History

* **Start**: Table of all settlements with amount, date, who paid
* **End**: Shown below form

---

## 10. ⚙️ Settings Page

### Task 26: Build `/settings` Page

* **Start**: Tabs for category manager, preferences, export/import
* **End**: Add/remove custom categories with icon/color

---

## 11. 🔐 Auth & Hosting

### Task 27: Setup Login with Cookie-Based Session

* **Start**: Dummy login screen with hardcoded Taha/Burak
* **End**: Auth state stored in cookie; user context tracks session

### Task 28: Protect Routes via Context

* **Start**: Add `RequireAuth` wrapper for all routes
* **End**: Only logged-in user can access routes

### Task 29: Deploy to DigitalOcean App Platform

* **Start**: Push project to GitHub with `main` branch
* **End**: Live at `https://partner.nawrasinchina.com`

---

> ✅ Each task is scoped for automation and human QA in between. Let me know when you’re ready to start assigning them one-by-one.
