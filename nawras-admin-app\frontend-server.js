const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 3002;

// Serve static files
app.use(express.static(__dirname));

// Serve the main frontend
app.get('/', (req, res) => {
    const frontendPath = path.join(__dirname, 'nawras-frontend.html');
    
    if (fs.existsSync(frontendPath)) {
        res.sendFile(frontendPath);
    } else {
        res.send(`<!DOCTYPE html>
<html>
<head>
    <title>Nawras Admin - Frontend Server</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f8f9fa; padding: 50px; text-align: center; }
        .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto; }
        .success { background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px; text-decoration: none; display: inline-block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Frontend Server Active</h1>
        <div class="success">
            <h3>✅ Custom Frontend Ready</h3>
            <p>Frontend server running on port ${PORT}</p>
            <p>Connected to API on port 3001</p>
        </div>
        <p><strong>Status:</strong> Frontend server operational</p>
        <p><strong>API Connection:</strong> http://partner.nawrasinchina.com:3001</p>
        <a href="/frontend" class="btn">Open Nawras Admin</a>
    </div>
</body>
</html>`);
    }
});

// Alternative route for the frontend
app.get('/frontend', (req, res) => {
    const frontendPath = path.join(__dirname, 'nawras-frontend.html');
    
    if (fs.existsSync(frontendPath)) {
        res.sendFile(frontendPath);
    } else {
        res.send('<h1>Frontend file not found</h1><p>Please upload nawras-frontend.html</p>');
    }
});

// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        server: 'frontend-server',
        port: PORT,
        api_connection: 'http://partner.nawrasinchina.com:3001',
        message: 'Custom frontend server operational'
    });
});

app.listen(PORT, () => {
    console.log('🎉 CUSTOM FRONTEND SERVER STARTED');
    console.log(`✅ Frontend server running on port ${PORT}`);
    console.log(`✅ Connected to API on port 3001`);
    console.log(`✅ Supabase integration via API`);
    console.log(`🌐 Access: http://partner.nawrasinchina.com:${PORT}`);
    console.log(`🌐 Direct: http://partner.nawrasinchina.com:${PORT}/frontend`);
});