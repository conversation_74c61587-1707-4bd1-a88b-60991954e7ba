#!/bin/bash

# Dashboard Components Implementation Script
# Creates all missing dashboard components with live data integration

echo "📊 DASHBOARD COMPONENTS IMPLEMENTATION"
echo "====================================="
echo ""

# Step 1: Create BalanceSummary Component
echo "💰 Step 1: Creating BalanceSummary Component..."

cat > src/features/dashboard/BalanceSummary.tsx << 'EOF'
import React from 'react';
import { useExpenses } from '../../hooks/useExpenses';
import { useSettlements } from '../../hooks/useSettlements';
import { DollarSign, TrendingUp, TrendingDown, Users } from 'lucide-react';

interface BalanceCardProps {
  title: string;
  amount: number;
  icon: React.ReactNode;
  color: string;
  subtitle?: string;
}

const BalanceCard: React.FC<BalanceCardProps> = ({ title, amount, icon, color, subtitle }) => (
  <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-500 mb-1">{title}</p>
        <p className={`text-2xl font-bold ${color}`}>
          ${Math.abs(amount).toFixed(2)}
        </p>
        {subtitle && (
          <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
        )}
      </div>
      <div className={`p-3 rounded-full ${color.includes('blue') ? 'bg-blue-100' : 
                                         color.includes('green') ? 'bg-green-100' : 
                                         color.includes('red') ? 'bg-red-100' : 'bg-gray-100'}`}>
        {icon}
      </div>
    </div>
  </div>
);

export const BalanceSummary: React.FC = () => {
  const { data: expenses = [], isLoading: expensesLoading } = useExpenses();
  const { data: settlements = [], isLoading: settlementsLoading } = useSettlements();

  if (expensesLoading || settlementsLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-8 bg-gray-200 rounded mb-1"></div>
            <div className="h-3 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  // Calculate balances
  const tahaExpenses = expenses
    .filter(e => e.paidById === 'taha')
    .reduce((sum, e) => sum + e.amount, 0);
  
  const burakExpenses = expenses
    .filter(e => e.paidById === 'burak')
    .reduce((sum, e) => sum + e.amount, 0);
  
  const totalExpenses = tahaExpenses + burakExpenses;
  
  // Calculate settlements
  const totalSettlements = settlements.reduce((sum, s) => sum + s.amount, 0);
  
  // Calculate net balance (who owes whom)
  const rawBalance = tahaExpenses - burakExpenses;
  const netBalance = Math.abs(rawBalance);
  const balanceOwer = rawBalance > 0 ? 'Burak owes Taha' : rawBalance < 0 ? 'Taha owes Burak' : 'Balanced';

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <BalanceCard
        title="Taha's Expenses"
        amount={tahaExpenses}
        icon={<DollarSign className="h-6 w-6 text-blue-600" />}
        color="text-blue-600"
        subtitle={`${expenses.filter(e => e.paidById === 'taha').length} transactions`}
      />
      
      <BalanceCard
        title="Burak's Expenses"
        amount={burakExpenses}
        icon={<DollarSign className="h-6 w-6 text-green-600" />}
        color="text-green-600"
        subtitle={`${expenses.filter(e => e.paidById === 'burak').length} transactions`}
      />
      
      <BalanceCard
        title="Total Combined"
        amount={totalExpenses}
        icon={<Users className="h-6 w-6 text-gray-600" />}
        color="text-gray-900"
        subtitle={`${expenses.length} total expenses`}
      />
      
      <BalanceCard
        title="Current Balance"
        amount={netBalance}
        icon={rawBalance > 0 ? 
          <TrendingUp className="h-6 w-6 text-red-600" /> : 
          rawBalance < 0 ? 
          <TrendingDown className="h-6 w-6 text-red-600" /> :
          <DollarSign className="h-6 w-6 text-green-600" />
        }
        color={rawBalance === 0 ? "text-green-600" : "text-red-600"}
        subtitle={balanceOwer}
      />
    </div>
  );
};
EOF

echo "✅ BalanceSummary component created"

# Step 2: Create ExpenseChart Component
echo "📈 Step 2: Creating ExpenseChart Component..."

cat > src/features/dashboard/ExpenseChart.tsx << 'EOF'
import React, { useMemo } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useExpenses } from '../../hooks/useExpenses';

interface ChartData {
  month: string;
  taha: number;
  burak: number;
  total: number;
}

export const ExpenseChart: React.FC = () => {
  const { data: expenses = [], isLoading } = useExpenses();

  const chartData = useMemo(() => {
    if (!expenses.length) return [];

    // Group expenses by month
    const monthlyData: { [key: string]: { taha: number; burak: number } } = {};

    expenses.forEach(expense => {
      const date = new Date(expense.date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const monthName = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });

      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { taha: 0, burak: 0 };
      }

      if (expense.paidById === 'taha') {
        monthlyData[monthKey].taha += expense.amount;
      } else if (expense.paidById === 'burak') {
        monthlyData[monthKey].burak += expense.amount;
      }
    });

    // Convert to chart format and sort by date
    return Object.entries(monthlyData)
      .map(([monthKey, data]) => {
        const [year, month] = monthKey.split('-');
        const monthName = new Date(parseInt(year), parseInt(month) - 1).toLocaleDateString('en-US', { 
          year: 'numeric', 
          month: 'short' 
        });
        
        return {
          month: monthName,
          taha: Math.round(data.taha * 100) / 100,
          burak: Math.round(data.burak * 100) / 100,
          total: Math.round((data.taha + data.burak) * 100) / 100,
        };
      })
      .sort((a, b) => new Date(a.month).getTime() - new Date(b.month).getTime());
  }, [expenses]);

  if (isLoading) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="h-4 bg-gray-200 rounded mb-4 w-1/3"></div>
        <div className="h-64 bg-gray-100 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!chartData.length) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Expense Trends</h3>
        <div className="h-64 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <p className="text-lg mb-2">No expense data available</p>
            <p className="text-sm">Add some expenses to see the chart</p>
          </div>
        </div>
      </div>
    );
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900 mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: ${entry.value.toFixed(2)}
            </p>
          ))}
          <p className="text-sm text-gray-600 mt-1 pt-1 border-t">
            Total: ${payload.reduce((sum: number, entry: any) => sum + entry.value, 0).toFixed(2)}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Monthly Expense Trends</h3>
        <div className="text-sm text-gray-500">
          Last {chartData.length} month{chartData.length !== 1 ? 's' : ''}
        </div>
      </div>
      
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="month" 
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
            />
            <YAxis 
              stroke="#6b7280"
              fontSize={12}
              tickLine={false}
              tickFormatter={(value) => `$${value}`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar 
              dataKey="taha" 
              name="Taha" 
              fill="#3b82f6" 
              radius={[2, 2, 0, 0]}
            />
            <Bar 
              dataKey="burak" 
              name="Burak" 
              fill="#10b981" 
              radius={[2, 2, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};
EOF

echo "✅ ExpenseChart component created"

# Step 3: Create CategoryBreakdown Component
echo "🏷️ Step 3: Creating CategoryBreakdown Component..."

cat > src/features/dashboard/CategoryBreakdown.tsx << 'EOF'
import React, { useMemo } from 'react';
import { useExpenses } from '../../hooks/useExpenses';

interface CategoryData {
  name: string;
  amount: number;
  percentage: number;
  count: number;
  color: string;
}

const categoryColors: { [key: string]: string } = {
  'Food': '#ef4444',
  'Utilities': '#3b82f6',
  'Transportation': '#10b981',
  'Groceries': '#f59e0b',
  'Entertainment': '#8b5cf6',
  'Healthcare': '#ec4899',
  'Shopping': '#06b6d4',
  'Other': '#6b7280',
};

export const CategoryBreakdown: React.FC = () => {
  const { data: expenses = [], isLoading } = useExpenses();

  const categoryData = useMemo(() => {
    if (!expenses.length) return [];

    // Group expenses by category
    const categoryTotals: { [key: string]: { amount: number; count: number } } = {};
    let totalAmount = 0;

    expenses.forEach(expense => {
      const category = expense.category || 'Other';
      if (!categoryTotals[category]) {
        categoryTotals[category] = { amount: 0, count: 0 };
      }
      categoryTotals[category].amount += expense.amount;
      categoryTotals[category].count += 1;
      totalAmount += expense.amount;
    });

    // Convert to array and calculate percentages
    return Object.entries(categoryTotals)
      .map(([name, data]) => ({
        name,
        amount: data.amount,
        count: data.count,
        percentage: totalAmount > 0 ? (data.amount / totalAmount) * 100 : 0,
        color: categoryColors[name] || categoryColors['Other'],
      }))
      .sort((a, b) => b.amount - a.amount);
  }, [expenses]);

  if (isLoading) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="h-4 bg-gray-200 rounded mb-4 w-1/2"></div>
        <div className="space-y-3">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-3 bg-gray-200 rounded mb-2"></div>
              <div className="h-2 bg-gray-100 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!categoryData.length) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Expense Categories</h3>
        <div className="text-center text-gray-500 py-8">
          <p className="text-lg mb-2">No categories to display</p>
          <p className="text-sm">Add some expenses to see category breakdown</p>
        </div>
      </div>
    );
  }

  const totalAmount = categoryData.reduce((sum, cat) => sum + cat.amount, 0);

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Expense Categories</h3>
        <div className="text-sm text-gray-500">
          Total: ${totalAmount.toFixed(2)}
        </div>
      </div>

      <div className="space-y-4">
        {categoryData.map((category, index) => (
          <div key={category.name} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: category.color }}
                ></div>
                <span className="text-sm font-medium text-gray-900">
                  {category.name}
                </span>
                <span className="text-xs text-gray-500">
                  ({category.count} expense{category.count !== 1 ? 's' : ''})
                </span>
              </div>
              <div className="text-right">
                <div className="text-sm font-semibold text-gray-900">
                  ${category.amount.toFixed(2)}
                </div>
                <div className="text-xs text-gray-500">
                  {category.percentage.toFixed(1)}%
                </div>
              </div>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="h-2 rounded-full transition-all duration-300 ease-in-out"
                style={{
                  width: `${category.percentage}%`,
                  backgroundColor: category.color,
                }}
              ></div>
            </div>
          </div>
        ))}
      </div>

      {categoryData.length > 4 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            Showing top {Math.min(categoryData.length, 6)} categories
          </p>
        </div>
      )}
    </div>
  );
};
EOF

echo "✅ CategoryBreakdown component created"

# Step 4: Create RecentExpenses Component
echo "📋 Step 4: Creating RecentExpenses Component..."

cat > src/features/dashboard/RecentExpenses.tsx << 'EOF'
import React from 'react';
import { useExpenses } from '../../hooks/useExpenses';
import { Calendar, User, Tag, ArrowRight } from 'lucide-react';
import { Link } from 'wouter';

export const RecentExpenses: React.FC = () => {
  const { data: expenses = [], isLoading } = useExpenses();
  
  // Get the 5 most recent expenses
  const recentExpenses = expenses
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5);

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="h-5 bg-gray-200 rounded w-1/3"></div>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex justify-between items-center">
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-100 rounded w-1/2"></div>
                  </div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Recent Expenses</h3>
          <Link href="/history">
            <a className="text-sm text-blue-600 hover:text-blue-800 flex items-center space-x-1">
              <span>View all</span>
              <ArrowRight className="h-4 w-4" />
            </a>
          </Link>
        </div>
      </div>

      <div className="p-6">
        {recentExpenses.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p className="text-lg mb-2">No expenses yet</p>
            <p className="text-sm mb-4">Start by adding your first expense</p>
            <Link href="/add-expense">
              <a className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                Add Expense
              </a>
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {recentExpenses.map((expense) => (
              <div key={expense.id} className="flex justify-between items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="text-sm font-medium text-gray-900">
                      {expense.description}
                    </h4>
                    <span className="text-sm font-semibold text-gray-900">
                      ${expense.amount.toFixed(2)}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Tag className="h-3 w-3" />
                      <span>{expense.category}</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <User className="h-3 w-3" />
                      <span className="capitalize">{expense.paidById}</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3" />
                      <span>{new Date(expense.date).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
EOF

echo "✅ RecentExpenses component created"

# Step 5: Update dashboard index file
echo "📝 Step 5: Updating dashboard index file..."

cat > src/features/dashboard/index.ts << 'EOF'
// Dashboard feature components
export { BalanceSummary } from './BalanceSummary';
export { ExpenseChart } from './ExpenseChart';
export { CategoryBreakdown } from './CategoryBreakdown';
export { RecentExpenses } from './RecentExpenses';
EOF

echo "✅ Dashboard index updated"

# Step 6: Install required dependencies
echo "📦 Step 6: Installing required dependencies..."

# Check if recharts is installed
if ! grep -q "recharts" package.json; then
    echo "Installing recharts..."
    npm install recharts
else
    echo "✅ Recharts already installed"
fi

# Check if lucide-react is installed
if ! grep -q "lucide-react" package.json; then
    echo "Installing lucide-react..."
    npm install lucide-react
else
    echo "✅ Lucide-react already installed"
fi

echo ""
echo "🎉 DASHBOARD COMPONENTS IMPLEMENTATION COMPLETE!"
echo "=============================================="
echo ""
echo "✅ Created Components:"
echo "   • BalanceSummary.tsx - Live balance calculations with cards"
echo "   • ExpenseChart.tsx - Monthly expense trends with Recharts"
echo "   • CategoryBreakdown.tsx - Category spending visualization"
echo "   • RecentExpenses.tsx - Latest expenses with navigation"
echo ""
echo "✅ Features Implemented:"
echo "   • Real-time data integration with React Query"
echo "   • Responsive design with TailwindCSS"
echo "   • Loading states and empty states"
echo "   • Interactive charts and progress bars"
echo "   • Navigation links to other pages"
echo ""
echo "📋 Next Steps:"
echo "   1. Update your main dashboard page to import these components"
echo "   2. Build and deploy the application"
echo "   3. Test all dashboard functionality"
echo ""
