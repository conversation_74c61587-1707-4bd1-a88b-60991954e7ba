﻿#!/bin/bash
# Emergency deployment fix commands

echo "🚨 Starting emergency deployment fix..."

# Stop current services
echo "Stopping current services..."
pkill -f 'node.*server' || echo 'No Node.js servers to stop'
systemctl stop nginx || echo 'Nginx already stopped'

# Create application directory
mkdir -p /opt/nawras-admin
cd /opt/nawras-admin

# Install dependencies
echo "Installing dependencies..."
npm install express@4.21.2 cookie-parser bcryptjs cors

# Configure nginx
echo "Configuring nginx..."
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80;
    server_name partner.nawrasinchina.com *************;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Proxy all requests to Node.js application
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \System.Management.Automation.Internal.Host.InternalHost;
        proxy_set_header X-Real-IP \;
        proxy_set_header X-Forwarded-For \;
        proxy_set_header X-Forwarded-Proto \;
        proxy_cache_bypass \;
    }
}
EOF

# Test nginx configuration
nginx -t

# Start application
echo "Starting application..."
nohup node server-simple.cjs > app.log 2>&1 &

# Start nginx
systemctl start nginx

echo "✅ Emergency deployment fix completed!"
