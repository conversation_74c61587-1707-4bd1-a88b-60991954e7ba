# 🚀 **PRODUCTION DEPLOYMENT INSTRUCTIONS**

## 📋 **DEPLOYMENT PACKAGE READY**

The deployment package has been created with all necessary files:
- ✅ Built application (`dist/` folder)
- ✅ Updated server with authentication (`server-simple.cjs`)
- ✅ Package configuration (`package.json`)

## 🔧 **MANUAL DEPLOYMENT STEPS**

### **Step 1: Upload Files to Server**
```bash
# SSH to the server
ssh root@*************

# Navigate to application directory
cd /opt/nawras-admin

# Create backup
mkdir -p backups/$(date +%Y%m%d_%H%M%S)
cp -r dist backups/$(date +%Y%m%d_%H%M%S)/dist.backup
cp server-simple.js backups/$(date +%Y%m%d_%H%M%S)/server-simple.js.backup
```

### **Step 2: Upload New Files**
From your local machine, upload the deployment package:
```bash
# Upload the built application
scp -r deployment-package/dist/* root@*************:/opt/nawras-admin/dist/

# Upload the new server file
scp deployment-package/server-simple.cjs root@*************:/opt/nawras-admin/

# Upload package.json
scp deployment-package/package.json root@*************:/opt/nawras-admin/
```

### **Step 3: Install Dependencies on Server**
```bash
# SSH to server
ssh root@*************

# Navigate to app directory
cd /opt/nawras-admin

# Install new dependencies
npm install express@4.21.2 cookie-parser bcryptjs
npm install --save-dev @types/cookie-parser @types/bcryptjs

# Verify installation
npm list express cookie-parser bcryptjs
```

### **Step 4: Update Server Configuration**
```bash
# Stop current server (if running with PM2)
pm2 stop nawras-admin

# Or if running with systemd
systemctl stop nawras-admin

# Or kill the process manually
pkill -f "node.*server"
```

### **Step 5: Start New Server**
```bash
# Test the new server first
cd /opt/nawras-admin
node server-simple.cjs

# If test is successful, start with PM2
pm2 start server-simple.cjs --name nawras-admin

# Or create systemd service
cat > /etc/systemd/system/nawras-admin.service << 'EOF'
[Unit]
Description=Nawras Admin Application
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/nawras-admin
ExecStart=/usr/bin/node server-simple.cjs
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
systemctl enable nawras-admin
systemctl start nawras-admin
systemctl status nawras-admin
```

## 🧪 **DEPLOYMENT VALIDATION**

### **Test 1: Server Health Check**
```bash
curl http://*************:3001/api/health
# Expected: {"status":"ok","timestamp":"...","version":"1.0.0"}
```

### **Test 2: Authentication Endpoints**
```bash
# Test session endpoint
curl http://*************:3001/api/auth/session
# Expected: {"success":true,"data":null}

# Test sign-in endpoint
curl -X POST http://*************:3001/api/auth/sign-in \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"taha2024"}'
# Expected: {"success":true,"data":{"session":...,"user":...}}
```

### **Test 3: Protected API Endpoints**
```bash
# Test expenses endpoint (should require auth)
curl http://*************:3001/api/expenses
# Expected: {"success":false,"error":"Unauthorized"}
```

### **Test 4: Frontend Application**
```bash
# Test main application
curl http://*************:3001/
# Expected: HTML content with React application
```

## 🔒 **SECURITY CONFIGURATION**

### **Update Nginx Configuration**
```bash
# Edit nginx configuration
nano /etc/nginx/nginx.conf

# Add authentication-friendly configuration
server {
    listen 80;
    server_name partner.nawrasinchina.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name partner.nawrasinchina.com;
    
    # SSL configuration (will be added in Phase 4)
    
    # Proxy to Node.js application
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Important for authentication cookies
        proxy_set_header Cookie $http_cookie;
    }
    
    # API routes
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Important for authentication
        proxy_set_header Cookie $http_cookie;
    }
}

# Test and reload nginx
nginx -t
systemctl reload nginx
```

## 🆘 **ROLLBACK PROCEDURE**

If deployment fails:
```bash
# Stop new server
pm2 stop nawras-admin
# or
systemctl stop nawras-admin

# Restore backup
cd /opt/nawras-admin
cp -r backups/YYYYMMDD_HHMMSS/dist.backup/* dist/
cp backups/YYYYMMDD_HHMMSS/server-simple.js.backup server-simple.js

# Start old server
node server-simple.js
# or
pm2 start server-simple.js --name nawras-admin
```

## ✅ **SUCCESS CRITERIA**

Deployment is successful when:
- ✅ Server starts without errors
- ✅ Health check returns 200 OK
- ✅ Authentication endpoints respond correctly
- ✅ Frontend loads and shows login page
- ✅ Both users can authenticate successfully
- ✅ Protected API endpoints require authentication
- ✅ Application maintains existing functionality

## 📞 **SUPPORT**

If you encounter issues:
1. Check server logs: `journalctl -u nawras-admin -f`
2. Check nginx logs: `tail -f /var/log/nginx/error.log`
3. Test endpoints manually with curl
4. Use rollback procedure if needed

**The deployment package is ready for production deployment!**
