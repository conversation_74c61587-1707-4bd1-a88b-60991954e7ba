#!/bin/bash

# Nawras Admin - DigitalOcean Droplet Fix Script
# This script will diagnose and fix your droplet issues using the DO API

set -e

# Configuration
DO_TOKEN="***********************************************************************"
API_BASE="https://api.digitalocean.com/v2"
DOMAIN="nawrasinchina.com"
SUBDOMAIN="partner"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    local color="$1"
    local message="$2"
    echo -e "${color}${message}${NC}"
}

api_call() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    
    if [ -n "$data" ]; then
        curl -s -X "$method" \
            -H "Authorization: Bearer $DO_TOKEN" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_BASE$endpoint"
    else
        curl -s -X "$method" \
            -H "Authorization: Bearer $DO_TOKEN" \
            "$API_BASE$endpoint"
    fi
}

# Step 1: Diagnose droplets
diagnose_droplets() {
    print_status "$BLUE" "🔍 Step 1: Diagnosing your DigitalOcean droplets..."
    
    response=$(api_call "GET" "/droplets")
    
    # Parse droplet information
    echo "$response" | jq -r '.droplets[] | 
        "ID: \(.id) | Name: \(.name) | Status: \(.status) | IP: \(.networks.v4[]? | select(.type=="public") | .ip_address) | Region: \(.region.name)"' > /tmp/droplets.txt
    
    print_status "$GREEN" "Found droplets:"
    cat /tmp/droplets.txt
    
    # Extract specific droplet info
    NAWRAS_APP_ID=$(echo "$response" | jq -r '.droplets[] | select(.name=="nawras-admin-app") | .id')
    NAWRAS_APP_IP=$(echo "$response" | jq -r '.droplets[] | select(.name=="nawras-admin-app") | .networks.v4[]? | select(.type=="public") | .ip_address')
    NAWRAS_APP_STATUS=$(echo "$response" | jq -r '.droplets[] | select(.name=="nawras-admin-app") | .status')
    
    NAWRAS_DEPLOYED_ID=$(echo "$response" | jq -r '.droplets[] | select(.name=="nawras-admin-deployed") | .id')
    NAWRAS_DEPLOYED_IP=$(echo "$response" | jq -r '.droplets[] | select(.name=="nawras-admin-deployed") | .networks.v4[]? | select(.type=="public") | .ip_address')
    NAWRAS_DEPLOYED_STATUS=$(echo "$response" | jq -r '.droplets[] | select(.name=="nawras-admin-deployed") | .status')
    
    echo ""
    print_status "$YELLOW" "📊 Droplet Analysis:"
    
    if [ "$NAWRAS_APP_ID" != "null" ] && [ -n "$NAWRAS_APP_ID" ]; then
        print_status "$BLUE" "nawras-admin-app:"
        echo "  ID: $NAWRAS_APP_ID"
        echo "  IP: $NAWRAS_APP_IP"
        echo "  Status: $NAWRAS_APP_STATUS"
        
        # Test connectivity
        if ping -c 1 "$NAWRAS_APP_IP" > /dev/null 2>&1; then
            print_status "$GREEN" "  ✅ Reachable"
        else
            print_status "$RED" "  ❌ Not reachable"
        fi
    else
        print_status "$RED" "❌ nawras-admin-app droplet not found"
    fi
    
    if [ "$NAWRAS_DEPLOYED_ID" != "null" ] && [ -n "$NAWRAS_DEPLOYED_ID" ]; then
        print_status "$BLUE" "nawras-admin-deployed:"
        echo "  ID: $NAWRAS_DEPLOYED_ID"
        echo "  IP: $NAWRAS_DEPLOYED_IP"
        echo "  Status: $NAWRAS_DEPLOYED_STATUS"
        
        # Test connectivity
        if ping -c 1 "$NAWRAS_DEPLOYED_IP" > /dev/null 2>&1; then
            print_status "$GREEN" "  ✅ Reachable"
        else
            print_status "$RED" "  ❌ Not reachable"
        fi
    else
        print_status "$RED" "❌ nawras-admin-deployed droplet not found"
    fi
}

# Step 2: Fix droplet issues
fix_droplets() {
    print_status "$BLUE" "🔧 Step 2: Fixing droplet issues..."
    
    # Power on droplets if they're off
    if [ "$NAWRAS_APP_STATUS" = "off" ] && [ "$NAWRAS_APP_ID" != "null" ]; then
        print_status "$YELLOW" "🔌 Powering on nawras-admin-app..."
        data='{"type": "power_on"}'
        api_call "POST" "/droplets/$NAWRAS_APP_ID/actions" "$data"
        sleep 10
    fi
    
    if [ "$NAWRAS_DEPLOYED_STATUS" = "off" ] && [ "$NAWRAS_DEPLOYED_ID" != "null" ]; then
        print_status "$YELLOW" "🔌 Powering on nawras-admin-deployed..."
        data='{"type": "power_on"}'
        api_call "POST" "/droplets/$NAWRAS_DEPLOYED_ID/actions" "$data"
        sleep 10
    fi
    
    # Wait for droplets to be ready
    print_status "$YELLOW" "⏳ Waiting for droplets to be ready..."
    sleep 30
}

# Step 3: Test web services
test_web_services() {
    print_status "$BLUE" "🌐 Step 3: Testing web services..."
    
    if [ "$NAWRAS_APP_IP" != "null" ] && [ -n "$NAWRAS_APP_IP" ]; then
        print_status "$YELLOW" "Testing nawras-admin-app ($NAWRAS_APP_IP)..."
        
        # Test HTTP
        if curl -s -I "http://$NAWRAS_APP_IP" | head -1 | grep -q "200\|301\|302"; then
            print_status "$GREEN" "✅ HTTP service responding"
        else
            print_status "$RED" "❌ HTTP service not responding"
        fi
        
        # Test API
        if curl -s -I "http://$NAWRAS_APP_IP:3001/api/expenses" | head -1 | grep -q "200\|404"; then
            print_status "$GREEN" "✅ API service responding"
        else
            print_status "$RED" "❌ API service not responding"
        fi
    fi
    
    if [ "$NAWRAS_DEPLOYED_IP" != "null" ] && [ -n "$NAWRAS_DEPLOYED_IP" ]; then
        print_status "$YELLOW" "Testing nawras-admin-deployed ($NAWRAS_DEPLOYED_IP)..."
        
        # Test HTTP
        if curl -s -I "http://$NAWRAS_DEPLOYED_IP" | head -1 | grep -q "200\|301\|302"; then
            print_status "$GREEN" "✅ HTTP service responding"
        else
            print_status "$RED" "❌ HTTP service not responding"
        fi
        
        # Test API
        if curl -s -I "http://$NAWRAS_DEPLOYED_IP:3001/api/expenses" | head -1 | grep -q "200\|404"; then
            print_status "$GREEN" "✅ API service responding"
        else
            print_status "$RED" "❌ API service not responding"
        fi
    fi
}

# Step 4: Setup DNS
setup_dns() {
    print_status "$BLUE" "🔗 Step 4: Setting up DNS for subdomain..."
    
    # Determine which IP to use (prefer deployed over app)
    TARGET_IP=""
    if [ "$NAWRAS_DEPLOYED_IP" != "null" ] && [ -n "$NAWRAS_DEPLOYED_IP" ]; then
        TARGET_IP="$NAWRAS_DEPLOYED_IP"
        print_status "$GREEN" "Using nawras-admin-deployed IP: $TARGET_IP"
    elif [ "$NAWRAS_APP_IP" != "null" ] && [ -n "$NAWRAS_APP_IP" ]; then
        TARGET_IP="$NAWRAS_APP_IP"
        print_status "$YELLOW" "Using nawras-admin-app IP: $TARGET_IP"
    else
        print_status "$RED" "❌ No valid IP address found"
        return 1
    fi
    
    # Check if domain exists
    domain_response=$(api_call "GET" "/domains/$DOMAIN")
    if echo "$domain_response" | jq -e '.domain' > /dev/null; then
        print_status "$GREEN" "✅ Domain $DOMAIN found"
        
        # Check if subdomain record already exists
        records_response=$(api_call "GET" "/domains/$DOMAIN/records")
        existing_record=$(echo "$records_response" | jq -r ".domain_records[] | select(.name==\"$SUBDOMAIN\" and .type==\"A\") | .id")
        
        if [ "$existing_record" != "null" ] && [ -n "$existing_record" ]; then
            print_status "$YELLOW" "📝 Updating existing A record..."
            
            # Update existing record
            data="{
                \"type\": \"A\",
                \"name\": \"$SUBDOMAIN\",
                \"data\": \"$TARGET_IP\",
                \"ttl\": 300
            }"
            
            update_response=$(api_call "PUT" "/domains/$DOMAIN/records/$existing_record" "$data")
            
            if echo "$update_response" | jq -e '.domain_record' > /dev/null; then
                print_status "$GREEN" "✅ A record updated successfully"
            else
                print_status "$RED" "❌ Failed to update A record"
            fi
        else
            print_status "$YELLOW" "🆕 Creating new A record..."
            
            # Create new record
            data="{
                \"type\": \"A\",
                \"name\": \"$SUBDOMAIN\",
                \"data\": \"$TARGET_IP\",
                \"ttl\": 300
            }"
            
            create_response=$(api_call "POST" "/domains/$DOMAIN/records" "$data")
            
            if echo "$create_response" | jq -e '.domain_record' > /dev/null; then
                record_id=$(echo "$create_response" | jq -r '.domain_record.id')
                print_status "$GREEN" "✅ A record created successfully. Record ID: $record_id"
            else
                print_status "$RED" "❌ Failed to create A record"
                echo "$create_response" | jq '.'
            fi
        fi
    else
        print_status "$RED" "❌ Domain $DOMAIN not found in your DigitalOcean account"
        print_status "$YELLOW" "💡 You may need to add the domain to DigitalOcean DNS first"
    fi
}

# Step 5: Deploy application
deploy_application() {
    print_status "$BLUE" "🚀 Step 5: Deploying application..."
    
    # Choose the best droplet for deployment
    if [ "$NAWRAS_DEPLOYED_IP" != "null" ] && [ -n "$NAWRAS_DEPLOYED_IP" ]; then
        DEPLOY_IP="$NAWRAS_DEPLOYED_IP"
        print_status "$GREEN" "Deploying to nawras-admin-deployed: $DEPLOY_IP"
    elif [ "$NAWRAS_APP_IP" != "null" ] && [ -n "$NAWRAS_APP_IP" ]; then
        DEPLOY_IP="$NAWRAS_APP_IP"
        print_status "$YELLOW" "Deploying to nawras-admin-app: $DEPLOY_IP"
    else
        print_status "$RED" "❌ No available droplet for deployment"
        return 1
    fi
    
    print_status "$YELLOW" "📤 Uploading deployment files..."
    
    # Upload files to the droplet
    rsync -avz --exclude 'node_modules' --exclude '.git' --exclude 'dist' . root@$DEPLOY_IP:/opt/nawras-admin/ || {
        print_status "$RED" "❌ Failed to upload files. Check SSH access."
        return 1
    }
    
    print_status "$YELLOW" "🐳 Running deployment on server..."
    
    # Run deployment commands on the server
    ssh -o StrictHostKeyChecking=no root@$DEPLOY_IP << EOF
cd /opt/nawras-admin

# Stop existing containers
docker-compose down 2>/dev/null || true

# Build and start containers
docker-compose up --build -d

# Configure nginx
cp nginx.conf /etc/nginx/nginx.conf
nginx -t && systemctl restart nginx

# Show status
docker-compose ps
systemctl status nginx --no-pager
EOF
    
    if [ $? -eq 0 ]; then
        print_status "$GREEN" "✅ Deployment completed successfully"
    else
        print_status "$RED" "❌ Deployment failed"
    fi
}

# Main execution
main() {
    print_status "$BLUE" "🚀 Nawras Admin - DigitalOcean Fix Script"
    print_status "$BLUE" "============================================"
    
    # Check dependencies
    if ! command -v jq &> /dev/null; then
        print_status "$RED" "❌ jq is required but not installed. Installing..."
        # Try to install jq
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y jq
        elif command -v yum &> /dev/null; then
            sudo yum install -y jq
        elif command -v brew &> /dev/null; then
            brew install jq
        else
            print_status "$RED" "❌ Please install jq manually and run this script again"
            exit 1
        fi
    fi
    
    # Execute steps
    diagnose_droplets
    echo ""
    
    fix_droplets
    echo ""
    
    test_web_services
    echo ""
    
    setup_dns
    echo ""
    
    # Ask user if they want to deploy
    read -p "Do you want to deploy the application? (y/n): " deploy_choice
    if [[ $deploy_choice =~ ^[Yy]$ ]]; then
        deploy_application
    fi
    
    echo ""
    print_status "$GREEN" "🎉 Fix script completed!"
    print_status "$BLUE" "📋 Summary:"
    echo "• Check your droplets in DigitalOcean dashboard"
    echo "• Test website: http://$TARGET_IP"
    echo "• Test subdomain: http://$SUBDOMAIN.$DOMAIN (after DNS propagation)"
    echo "• DNS propagation may take 5-30 minutes"
    
    # Cleanup
    rm -f /tmp/droplets.txt
}

# Run the main function
main
