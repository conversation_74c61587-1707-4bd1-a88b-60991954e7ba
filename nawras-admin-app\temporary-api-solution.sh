#!/bin/bash

# Temporary API Solution - Serve static responses via nginx
# This will get the system working while we fix the Node.js backend

echo "🔧 TEMPORARY API SOLUTION"
echo "========================"
echo "Setting up static API responses via nginx..."

# Create API directories
mkdir -p /var/www/api/auth
mkdir -p /var/www/api

# Create health endpoint response
cat > /var/www/api/health.json << 'EOF'
{
  "status": "ok",
  "timestamp": "2025-06-02T16:30:00.000Z",
  "version": "1.0.0",
  "server": "nginx-static"
}
EOF

# Create session endpoint response (no session)
cat > /var/www/api/auth/session.json << 'EOF'
{
  "success": true,
  "data": null
}
EOF

# Create nginx configuration with static API responses
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80;
    server_name partner.nawrasinchina.com 138.197.8.183;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Serve frontend
    location / {
        root /opt/nawras-admin/dist;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Health endpoint
    location = /api/health {
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        alias /var/www/api/health.json;
    }
    
    # Session endpoint
    location = /api/auth/session {
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        alias /var/www/api/auth/session.json;
    }
    
    # Protected endpoints - return 401
    location ~ ^/api/(expenses|settlements) {
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        return 401 '{"success":false,"error":"Authentication required - backend server not available"}';
    }
    
    # Sign-in endpoint - temporary mock
    location = /api/auth/sign-in {
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            return 204;
        }
        
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        return 503 '{"success":false,"error":"Authentication service temporarily unavailable - backend server not running"}';
    }
    
    # Default API response
    location /api/ {
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        return 503 '{"success":false,"error":"Backend server not available - please contact administrator"}';
    }
}
EOF

# Test and reload nginx
echo "Testing nginx configuration..."
nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx configuration valid"
    systemctl reload nginx
    echo "✅ Nginx reloaded"
else
    echo "❌ Nginx configuration invalid"
    exit 1
fi

# Test the endpoints
echo ""
echo "Testing temporary API endpoints..."

echo "Health endpoint:"
curl -s http://localhost/api/health

echo ""
echo "Session endpoint:"
curl -s http://localhost/api/auth/session

echo ""
echo "Protected endpoint (should return 401):"
curl -s http://localhost/api/expenses

echo ""
echo "✅ Temporary API solution deployed!"
echo ""
echo "📋 Status:"
echo "   • Frontend: ✅ Working"
echo "   • Health API: ✅ Working (static)"
echo "   • Session API: ✅ Working (static)"
echo "   • Protected APIs: ⚠️  Returns 401 (as expected)"
echo "   • Authentication: ❌ Not working (backend needed)"
echo ""
echo "🔧 Next Steps:"
echo "   1. Fix Node.js backend server"
echo "   2. Replace static responses with dynamic backend"
echo "   3. Implement proper authentication"
echo ""
echo "🌐 Test URLs:"
echo "   • Frontend: http://partner.nawrasinchina.com"
echo "   • Health: http://partner.nawrasinchina.com/api/health"
echo "   • Session: http://partner.nawrasinchina.com/api/auth/session"
