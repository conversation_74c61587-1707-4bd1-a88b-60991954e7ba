# Backend Dockerfile for Express API server

FROM node:18-alpine

WORKDIR /app

# Copy package files (if they exist)
COPY package*.json ./

# Install dependencies (if package.json exists)
RUN if [ -f package.json ]; then npm ci --only=production; fi

# Copy server files
COPY . .

# Expose port
EXPOSE 3001

# Set environment
ENV NODE_ENV=production
ENV PORT=3001

# Start the server
CMD ["node", "index.js"]
