#!/bin/bash

# Nawras Admin - Fix Deployment and Add Subdomain
# This script will fix the website and configure the subdomain

set -e

# Configuration
SERVER_IP="***************"
DOMAIN="partner.nawrasinchina.com"
APP_NAME="nawras-admin"

echo "🔧 Fixing Nawras Admin deployment and adding subdomain..."
echo "🌐 Server IP: $SERVER_IP"
echo "🔗 Domain: $DOMAIN"

# Function to run commands on the server
run_on_server() {
    ssh -o StrictHostKeyChecking=no root@$SERVER_IP "$1"
}

# Function to upload files to server
upload_files() {
    echo "📤 Uploading application files..."
    rsync -avz --exclude 'node_modules' --exclude '.git' --exclude 'dist' . root@$SERVER_IP:/opt/$APP_NAME/
}

echo "🚀 Step 1: Preparing server environment..."

# Install required packages
run_on_server "
apt-get update -y
apt-get install -y docker.io docker-compose nginx certbot python3-certbot-nginx ufw
systemctl enable docker
systemctl start docker
"

echo "🔥 Step 2: Configuring firewall..."

# Configure firewall
run_on_server "
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3001/tcp
ufw --force enable
"

echo "📁 Step 3: Setting up application directory..."

# Create app directory
run_on_server "mkdir -p /opt/$APP_NAME"

# Upload files
upload_files

echo "🐳 Step 4: Deploying with Docker..."

# Deploy the application
run_on_server "
cd /opt/$APP_NAME

# Stop existing containers
docker-compose down 2>/dev/null || true

# Remove old containers and images
docker system prune -f

# Build and start containers
docker-compose up --build -d

# Wait for containers to start
sleep 10

# Check container status
docker-compose ps
"

echo "🌐 Step 5: Configuring Nginx..."

# Configure Nginx
run_on_server "
# Stop nginx if running
systemctl stop nginx 2>/dev/null || true

# Copy our nginx config
cp /opt/$APP_NAME/nginx.conf /etc/nginx/nginx.conf

# Test nginx configuration
nginx -t

# Start nginx
systemctl enable nginx
systemctl start nginx
"

echo "🔒 Step 6: Setting up SSL certificate..."

# Setup SSL certificate
run_on_server "
# Get SSL certificate for the domain
certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email <EMAIL> --redirect

# Test certificate renewal
certbot renew --dry-run
"

echo "🧪 Step 7: Testing deployment..."

# Test the deployment
echo "Testing HTTP endpoints..."
curl -f http://$SERVER_IP/health || echo "❌ HTTP health check failed"
curl -f http://$SERVER_IP:3001/api/expenses || echo "❌ API health check failed"

echo "✅ Deployment complete!"
echo ""
echo "🎉 Your website is now available at:"
echo "🔗 HTTP: http://$SERVER_IP"
echo "🔗 HTTPS: https://$DOMAIN"
echo "🔗 API: http://$SERVER_IP:3001/api"
echo ""
echo "📋 Next steps:"
echo "1. Update your DNS A record for $DOMAIN to point to $SERVER_IP"
echo "2. Wait for DNS propagation (5-30 minutes)"
echo "3. Test the domain: https://$DOMAIN"
echo ""
echo "🔍 Troubleshooting commands:"
echo "ssh root@$SERVER_IP 'docker-compose -f /opt/$APP_NAME/docker-compose.yml logs'"
echo "ssh root@$SERVER_IP 'systemctl status nginx'"
echo "ssh root@$SERVER_IP 'certbot certificates'"
