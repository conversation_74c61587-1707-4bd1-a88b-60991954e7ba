import{r as e,g as t}from"./vendor-CFHJfABC.js";function r(e,t){for(var r=0;r<t.length;r++){const n=t[r];if("string"!=typeof n&&!Array.isArray(n))for(const t in n)if("default"!==t&&!(t in e)){const r=Object.getOwnPropertyDescriptor(n,t);r&&Object.defineProperty(e,t,r.get?r:{enumerable:!0,get:()=>n[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var n=e();const o=t(n),s=r({__proto__:null,default:o},[n]);var a,c,i={exports:{}},u={};var l=(c||(c=1,i.exports=function(){if(a)return u;a=1;var t=e(),r="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=t.useState,o=t.useEffect,s=t.useLayoutEffect,c=t.useDebugValue;function i(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!r(e,n)}catch(o){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),a=n({inst:{value:r,getSnapshot:t}}),u=a[0].inst,l=a[1];return s((function(){u.value=r,u.getSnapshot=t,i(u)&&l({inst:u})}),[e,r,t]),o((function(){return i(u)&&l({inst:u}),e((function(){i(u)&&l({inst:u})}))}),[e]),c(r),r};return u.useSyncExternalStore=void 0!==t.useSyncExternalStore?t.useSyncExternalStore:l,u}()),i.exports);const f=s.useInsertionEffect,h=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement)?n.useLayoutEffect:n.useEffect,d=f||h,p=e=>{const t=n.useRef([e,(...e)=>t[0](...e)]).current;return d((()=>{t[0]=e})),t[1]},y="pushState",v="replaceState",m=["popstate",y,v,"hashchange"],E=e=>{for(const t of m)addEventListener(t,e);return()=>{for(const t of m)removeEventListener(t,e)}},g=(e,t)=>l.useSyncExternalStore(E,e,t),b=()=>location.search,w=()=>location.pathname,S=({ssrPath:e}={})=>g(w,e?()=>e:w),x=(e,{replace:t=!1,state:r=null}={})=>history[t?v:y](r,"",e),O=Symbol.for("wouter_v3");if("undefined"!=typeof history&&void 0===window[O]){for(const e of[y,v]){const t=history[e];history[e]=function(){const r=t.apply(this,arguments),n=new Event(e);return n.arguments=arguments,dispatchEvent(n),r}}Object.defineProperty(window,O,{value:!0})}const k=(e="")=>"/"===e?"":e,j=(e="",t)=>((e,t)=>t.toLowerCase().indexOf(e.toLowerCase())?"~"+t:t.slice(e.length)||"/")(C(k(e)),C(t)),C=e=>{try{return decodeURI(e)}catch(t){return e}},P={hook:(e={})=>[S(e),x],searchHook:({ssrSearch:e=""}={})=>g(b,(()=>e)),parser:function(e,t){if(e instanceof RegExp)return{keys:!1,pattern:e};var r,n,o,s,a=[],c="",i=e.split("/");for(i[0]||i.shift();o=i.shift();)"*"===(r=o[0])?(a.push(r),c+="?"===o[1]?"(?:/(.*))?":"/(.*)"):":"===r?(n=o.indexOf("?",1),s=o.indexOf(".",1),a.push(o.substring(1,~n?n:~s?s:o.length)),c+=~n&&!~s?"(?:/([^/]+?))?":"/([^/]+?)",~s&&(c+=(~n?"?":"")+"\\"+o.substring(s))):c+="/"+o;return{keys:a,pattern:new RegExp("^"+c+(t?"(?=$|/)":"/?$"),"i")}},base:"",ssrPath:void 0,ssrSearch:void 0,ssrContext:void 0,hrefs:e=>e},R=n.createContext(P),L=()=>n.useContext(R),_={},A=n.createContext(_),K=e=>{const[t,r]=e.hook(e);return[j(e.base,t),p(((t,n)=>r(((e,t)=>"~"===e[0]?e.slice(1):k(t)+e)(t,e.base),n)))]},D=()=>K(L()),V=(e,t,r,n)=>{const{pattern:o,keys:s}=t instanceof RegExp?{keys:!1,pattern:t}:e(t||"*",n),a=o.exec(r)||[],[c,...i]=a;return void 0!==c?[!0,(()=>{const e=!1!==s?Object.fromEntries(s.map(((e,t)=>[e,i[t]]))):a.groups;let t={...i};return e&&Object.assign(t,e),t})(),...n?[c]:[]]:[!1,null]},I=({children:e,...t})=>{const r=L(),o=t.hook?P:r;let s=o;const[a,c]=t.ssrPath?.split("?")??[];c&&(t.ssrSearch=c,t.ssrPath=a),t.hrefs=t.hrefs??t.hook?.hrefs;let i=n.useRef({}),u=i.current,l=u;for(let n in o){const e="base"===n?o[n]+(t[n]||""):t[n]||o[n];u===l&&e!==l[n]&&(i.current=l={...l}),l[n]=e,e!==o[n]&&(s=l)}return n.createElement(R.Provider,{value:s,children:e})},M=({children:e,component:t},r)=>t?n.createElement(t,{params:r}):"function"==typeof e?e(r):e,N=({path:e,nest:t,match:r,...o})=>{const s=L(),[a]=K(s),[c,i,u]=r??V(s.parser,e,a,t),l=(e=>{let t=n.useRef(_);const r=t.current;return t.current=Object.keys(e).length!==Object.keys(r).length||Object.entries(e).some((([e,t])=>t!==r[e]))?e:r})({...n.useContext(A),...i});if(!c)return null;const f=u?n.createElement(I,{base:u},M(o,l)):M(o,l);return n.createElement(A.Provider,{value:l,children:f})},$=n.forwardRef(((e,t)=>{const r=L(),[o,s]=K(r),{to:a="",href:c=a,onClick:i,asChild:u,children:l,className:f,replace:h,state:d,...y}=e,v=p((t=>{t.ctrlKey||t.metaKey||t.altKey||t.shiftKey||0!==t.button||(i?.(t),t.defaultPrevented||(t.preventDefault(),s(c,e)))})),m=r.hrefs("~"===c[0]?c.slice(1):r.base+c,r);return u&&n.isValidElement(l)?n.cloneElement(l,{onClick:v,href:m}):n.createElement("a",{...y,onClick:v,href:m,className:f?.call?f(o===c):f,children:l,ref:t})})),z=e=>Array.isArray(e)?e.flatMap((e=>z(e&&e.type===n.Fragment?e.props.children:e))):[e],F=({children:e,location:t})=>{const r=L(),[o]=K(r);for(const s of z(e)){let e=0;if(n.isValidElement(s)&&(e=V(r.parser,s.props.path,t||o,s.props.nest))[0])return n.cloneElement(s,{match:e})}return null};export{$ as L,o as R,F as S,N as a,n as r,D as u};
