#!/bin/bash

# Simple deployment fix script
# This script will be uploaded and executed on the server

echo "🚀 Simple Deployment Fix Starting..."
echo "===================================="

# Stop all node processes
echo "Stopping all node processes..."
pkill -f node || echo "No node processes found"
pm2 stop all || echo "No PM2 processes found"
pm2 delete all || echo "No PM2 processes to delete"

# Wait for processes to stop
sleep 3

# Check if server-simple.cjs exists
if [ ! -f "/opt/nawras-admin/server-simple.cjs" ]; then
    echo "❌ server-simple.cjs not found!"
    exit 1
fi

echo "✅ server-simple.cjs found"

# Start the authenticated server
echo "Starting authenticated server..."
cd /opt/nawras-admin

# Start with PM2 if available, otherwise use nohup
if command -v pm2 >/dev/null 2>&1; then
    pm2 start server-simple.cjs --name nawras-admin-auth
    pm2 save
    echo "✅ Server started with PM2"
else
    nohup node server-simple.cjs > auth-server.log 2>&1 &
    echo "✅ Server started with nohup"
fi

# Wait for server to start
sleep 5

# Test the server
echo "Testing server..."
if curl -s http://localhost:3001/api/health | grep -q '"status":"ok"'; then
    echo "✅ Authenticated server is running correctly"
    echo "Health response:"
    curl -s http://localhost:3001/api/health
else
    echo "❌ Server test failed"
    echo "Health response:"
    curl -s http://localhost:3001/api/health
fi

echo ""
echo "Testing authentication requirement..."
if curl -s http://localhost:3001/api/expenses | grep -q '"success":false\|401'; then
    echo "✅ API endpoints are protected"
else
    echo "❌ API endpoints are not protected"
    echo "Expenses response:"
    curl -s http://localhost:3001/api/expenses
fi

echo ""
echo "🎉 Simple deployment fix completed!"
echo "Check the results above to verify success."
