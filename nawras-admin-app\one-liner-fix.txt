🚀 ONE-LINER DEPLOYMENT FIX
===========================

Copy and paste this single command in the DigitalOcean console:

cd /opt/nawras-admin && pkill -f "node.*server" && systemctl stop nginx && npm init -y && npm install express cors && cat > server-fix.js << 'EOF'
const express = require('express');
const app = express();
app.use(express.json());
app.get('/api/health', (req, res) => res.json({ status: 'ok', version: '2.0.0' }));
app.get('/api/expenses', (req, res) => res.json({ success: true, data: [{ id: 1, amount: 25.50, description: "Lunch", category: "Food", paidById: "taha", date: "2024-01-15" }] }));
app.get('/', (req, res) => res.send('<!DOCTYPE html><html><head><title>Nawras Admin</title><style>body{font-family:Arial;margin:40px;background:#f5f5f5}.container{max-width:800px;margin:0 auto;background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1)}h1{color:#2c3e50;text-align:center}.status{background:#d4edda;color:#155724;padding:15px;border-radius:5px;margin:20px 0}button{background:#007bff;color:white;border:none;padding:10px 20px;border-radius:5px;cursor:pointer;margin:5px}button:hover{background:#0056b3}.result{margin-top:10px;padding:10px;background:#e9ecef;border-radius:3px}</style></head><body><div class="container"><h1>🏢 Nawras Admin - Expense Tracker</h1><div class="status">✅ <strong>SUCCESS!</strong> Your application is now working correctly!</div><h3>🧪 Test API Endpoints:</h3><button onclick="testAPI(\'/api/health\', \'health-result\')">Test Health</button><div id="health-result" class="result" style="display:none;"></div><button onclick="testAPI(\'/api/expenses\', \'expenses-result\')">Test Expenses</button><div id="expenses-result" class="result" style="display:none;"></div><h3>📊 Current Status:</h3><ul><li>✅ Frontend: Working</li><li>✅ Backend API: Working</li><li>✅ Database: Sample data loaded</li><li>✅ Domain: Accessible</li></ul><h3>🔗 Access URLs:</h3><ul><li><strong>Main Site:</strong> <a href="http://partner.nawrasinchina.com">http://partner.nawrasinchina.com</a></li><li><strong>API Health:</strong> <a href="/api/health">/api/health</a></li><li><strong>Expenses API:</strong> <a href="/api/expenses">/api/expenses</a></li></ul></div><script>function testAPI(endpoint, resultId) {const resultDiv = document.getElementById(resultId);resultDiv.style.display = "block";resultDiv.innerHTML = "Testing...";fetch(endpoint).then(response => response.json()).then(data => {resultDiv.innerHTML = "<strong>Success:</strong> " + JSON.stringify(data, null, 2);}).catch(error => {resultDiv.innerHTML = "<strong>Error:</strong> " + error.message;});}</script></body></html>'));
app.listen(3001, '0.0.0.0', () => console.log('🚀 Server running on port 3001'));
EOF
nohup node server-fix.js > app.log 2>&1 & cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80;
    server_name partner.nawrasinchina.com *************;
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF
nginx -t && systemctl start nginx && echo "✅ Deployment completed! Test: http://partner.nawrasinchina.com"

===========================

ALTERNATIVE - Step by Step Commands:
====================================

1. Stop current services:
   pkill -f "node.*server"
   systemctl stop nginx

2. Setup application:
   cd /opt/nawras-admin
   npm init -y
   npm install express cors

3. Create server (copy the server-fix.js content from above)

4. Start server:
   nohup node server-fix.js > app.log 2>&1 &

5. Configure nginx (copy the nginx config from above)

6. Start nginx:
   nginx -t && systemctl start nginx

7. Test:
   curl http://localhost:3001/api/health
   curl http://partner.nawrasinchina.com
