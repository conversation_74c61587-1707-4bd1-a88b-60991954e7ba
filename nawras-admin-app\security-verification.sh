#!/bin/bash

# Security Verification Script
# Comprehensive testing of security implementations

DOMAIN="partner.nawrasinchina.com"
SERVER_IP="*************"

echo "🔍 SECURITY VERIFICATION TESTS"
echo "=============================="
echo "Domain: $DOMAIN"
echo "Server: $SERVER_IP"
echo ""

# Test 1: HTTPS Certificate Validation
echo "🔐 Test 1: HTTPS Certificate Validation"
echo "----------------------------------------"

cert_info=$(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)

if [ $? -eq 0 ]; then
    echo "✅ SSL Certificate: Valid and accessible"
    echo "$cert_info"
    
    # Check certificate expiry
    expiry_date=$(echo "$cert_info" | grep "notAfter" | cut -d= -f2)
    echo "📅 Certificate expires: $expiry_date"
else
    echo "❌ SSL Certificate: Invalid or inaccessible"
fi

echo ""

# Test 2: HTTPS Response and Headers
echo "🛡️ Test 2: Security Headers Validation"
echo "--------------------------------------"

response=$(curl -s -I https://$DOMAIN)

# Check HTTP status
if echo "$response" | grep -q "HTTP/2 200\|HTTP/1.1 200"; then
    echo "✅ HTTPS Status: 200 OK"
else
    echo "❌ HTTPS Status: Failed"
    echo "$response" | head -1
fi

# Security headers checklist
declare -A security_headers=(
    ["strict-transport-security"]="HSTS (HTTP Strict Transport Security)"
    ["x-xss-protection"]="XSS Protection"
    ["x-content-type-options"]="Content Type Options"
    ["x-frame-options"]="Frame Options (Clickjacking Protection)"
    ["content-security-policy"]="Content Security Policy"
    ["referrer-policy"]="Referrer Policy"
)

echo ""
echo "Security Headers Analysis:"
for header in "${!security_headers[@]}"; do
    if echo "$response" | grep -qi "$header"; then
        echo "✅ ${security_headers[$header]}: Present"
    else
        echo "❌ ${security_headers[$header]}: Missing"
    fi
done

echo ""

# Test 3: HTTP to HTTPS Redirect
echo "🔄 Test 3: HTTP to HTTPS Redirect"
echo "---------------------------------"

redirect_response=$(curl -s -I http://$DOMAIN)
if echo "$redirect_response" | grep -q "301\|302"; then
    redirect_location=$(echo "$redirect_response" | grep -i "location:" | cut -d' ' -f2 | tr -d '\r')
    if [[ "$redirect_location" == https://* ]]; then
        echo "✅ HTTP Redirect: Properly redirects to HTTPS"
        echo "   Redirect to: $redirect_location"
    else
        echo "⚠️ HTTP Redirect: Redirects but not to HTTPS"
        echo "   Redirect to: $redirect_location"
    fi
else
    echo "❌ HTTP Redirect: No redirect configured"
fi

echo ""

# Test 4: SSL Configuration Quality
echo "🔒 Test 4: SSL Configuration Quality"
echo "-----------------------------------"

# Test SSL protocols and ciphers
ssl_test=$(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null)

if echo "$ssl_test" | grep -q "Protocol.*TLSv1.3\|Protocol.*TLSv1.2"; then
    protocol=$(echo "$ssl_test" | grep "Protocol" | head -1)
    echo "✅ SSL Protocol: $protocol"
else
    echo "❌ SSL Protocol: Weak or outdated protocol"
fi

if echo "$ssl_test" | grep -q "Cipher.*"; then
    cipher=$(echo "$ssl_test" | grep "Cipher" | head -1)
    echo "✅ SSL Cipher: $cipher"
else
    echo "⚠️ SSL Cipher: Could not determine cipher"
fi

echo ""

# Test 5: Application Functionality Over HTTPS
echo "🌐 Test 5: Application Functionality Over HTTPS"
echo "-----------------------------------------------"

# Test main page
main_page=$(curl -s -w "%{http_code}" https://$DOMAIN -o /dev/null)
if [ "$main_page" = "200" ]; then
    echo "✅ Main Page: Loads successfully over HTTPS"
else
    echo "❌ Main Page: Failed to load (HTTP $main_page)"
fi

# Test API endpoints
api_expenses=$(curl -s -w "%{http_code}" https://$DOMAIN/api/expenses -o /dev/null)
if [ "$api_expenses" = "200" ]; then
    echo "✅ API Expenses: Accessible over HTTPS"
else
    echo "❌ API Expenses: Failed (HTTP $api_expenses)"
fi

api_settlements=$(curl -s -w "%{http_code}" https://$DOMAIN/api/settlements -o /dev/null)
if [ "$api_settlements" = "200" ]; then
    echo "✅ API Settlements: Accessible over HTTPS"
else
    echo "❌ API Settlements: Failed (HTTP $api_settlements)"
fi

echo ""

# Test 6: Performance Impact
echo "⚡ Test 6: Performance Impact Assessment"
echo "---------------------------------------"

# Test response times
echo "Measuring response times..."

times=()
for i in {1..5}; do
    time=$(curl -s -w "%{time_total}" https://$DOMAIN -o /dev/null)
    times+=($time)
    echo "  Test $i: ${time}s"
done

# Calculate average
total=0
for time in "${times[@]}"; do
    total=$(echo "$total + $time" | bc -l)
done
average=$(echo "scale=3; $total / ${#times[@]}" | bc -l)

echo "📊 Average Response Time: ${average}s"

if (( $(echo "$average < 1.0" | bc -l) )); then
    echo "✅ Performance: Excellent (< 1s)"
elif (( $(echo "$average < 2.0" | bc -l) )); then
    echo "✅ Performance: Good (< 2s)"
else
    echo "⚠️ Performance: Needs optimization (> 2s)"
fi

echo ""

# Test 7: Security Scan Summary
echo "📋 Test 7: Security Scan Summary"
echo "--------------------------------"

# Count passed tests
passed_tests=0
total_tests=0

# HTTPS Certificate
if echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates >/dev/null 2>&1; then
    ((passed_tests++))
fi
((total_tests++))

# Security Headers (count each header)
for header in "${!security_headers[@]}"; do
    if echo "$response" | grep -qi "$header"; then
        ((passed_tests++))
    fi
    ((total_tests++))
done

# HTTP Redirect
if echo "$redirect_response" | grep -q "301\|302"; then
    ((passed_tests++))
fi
((total_tests++))

# Application functionality
if [ "$main_page" = "200" ]; then
    ((passed_tests++))
fi
((total_tests++))

if [ "$api_expenses" = "200" ]; then
    ((passed_tests++))
fi
((total_tests++))

# Calculate security score
security_score=$(echo "scale=1; $passed_tests * 100 / $total_tests" | bc -l)

echo "🎯 Security Score: $passed_tests/$total_tests ($security_score%)"

if (( $(echo "$security_score >= 90" | bc -l) )); then
    echo "🟢 Security Status: EXCELLENT"
elif (( $(echo "$security_score >= 80" | bc -l) )); then
    echo "🟡 Security Status: GOOD"
elif (( $(echo "$security_score >= 70" | bc -l) )); then
    echo "🟠 Security Status: FAIR"
else
    echo "🔴 Security Status: NEEDS IMPROVEMENT"
fi

echo ""

# Test 8: External Security Validation
echo "🌐 Test 8: External Security Validation"
echo "--------------------------------------"

echo "🔗 Recommended external security tests:"
echo "   • SSL Labs: https://www.ssllabs.com/ssltest/analyze.html?d=$DOMAIN"
echo "   • Security Headers: https://securityheaders.com/?q=$DOMAIN"
echo "   • Mozilla Observatory: https://observatory.mozilla.org/analyze/$DOMAIN"

echo ""
echo "🎉 SECURITY VERIFICATION COMPLETE!"
echo "================================="

if (( $(echo "$security_score >= 90" | bc -l) )); then
    echo "✅ Your application is SECURE and ready for production!"
else
    echo "⚠️ Some security improvements needed. Review failed tests above."
fi

echo ""
echo "📊 Summary:"
echo "   • HTTPS: $([ "$main_page" = "200" ] && echo "✅ Working" || echo "❌ Failed")"
echo "   • Security Headers: $(echo "$response" | grep -qi "strict-transport-security" && echo "✅ Implemented" || echo "❌ Missing")"
echo "   • Performance: $([ $(echo "$average < 2.0" | bc -l) = 1 ] && echo "✅ Good" || echo "⚠️ Slow")"
echo "   • Overall Score: $security_score%"
