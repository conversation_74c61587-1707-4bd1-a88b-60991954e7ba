{"name": "nawras-admin-partner", "version": "4.0.0", "description": "Nawras Admin Partner - Expense Tracking System", "main": "server.js", "scripts": {"start": "node server.js", "build": "tsc --noEmit && vite build", "build:simple": "vite build", "dev": "vite", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"express": "^4.21.2", "cors": "^2.8.5", "cookie-parser": "^1.4.6", "bcryptjs": "^2.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "wouter": "^3.0.0", "@tanstack/react-query": "^4.24.6", "@tanstack/react-query-devtools": "^4.24.6", "lucide-react": "^0.312.0", "recharts": "^2.5.0", "clsx": "^1.2.1", "tailwind-merge": "^2.2.0"}, "devDependencies": {"nodemon": "^3.0.1", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^3.1.0", "vite": "^4.1.0", "typescript": "^4.9.4", "tailwindcss": "^3.2.6", "autoprefixer": "^10.4.13", "postcss": "^8.4.21"}, "engines": {"node": ">=18.0.0"}, "keywords": ["expense-tracker", "nodejs", "express", "digitalocean"], "author": "<PERSON><PERSON><PERSON>min", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/nawras-admin/nawras-admin-partner.git"}}