{"name": "nawras-admin-partner", "version": "5.0.0", "description": "Nawras Admin Partner - Expense Tracking System", "main": "server.js", "scripts": {"start": "node server.js", "build": "echo 'Build completed successfully'", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.21.2", "cors": "^2.8.5", "cookie-parser": "^1.4.6", "bcryptjs": "^2.4.3"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}, "keywords": ["expense-tracker", "nodejs", "express", "digitalocean"], "author": "<PERSON><PERSON><PERSON>min", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/nawras-admin/nawras-admin-partner.git"}}