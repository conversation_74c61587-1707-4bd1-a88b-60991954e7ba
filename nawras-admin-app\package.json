{"name": "nawras-admin-partner", "version": "2.1.0", "description": "Nawras Admin Partner - Expense Tracking System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "nodemon server.js", "dev:client": "vite", "build": "vite build --mode production", "build:simple": "vite build", "build:server": "node server.js", "preview": "vite preview", "test": "echo 'No tests specified'", "postinstall": "echo 'Dependencies installed successfully'"}, "dependencies": {"express": "^4.21.2", "cors": "^2.8.5", "cookie-parser": "^1.4.6", "bcryptjs": "^2.4.3"}, "devDependencies": {"nodemon": "^3.0.1", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^3.1.0", "vite": "^4.1.0", "typescript": "^4.9.4", "tailwindcss": "^3.2.6", "autoprefixer": "^10.4.13", "postcss": "^8.4.21", "concurrently": "^7.6.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["expense-tracker", "nodejs", "express", "digitalocean"], "author": "<PERSON><PERSON><PERSON>min", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/nawras-admin/nawras-admin-partner.git"}}