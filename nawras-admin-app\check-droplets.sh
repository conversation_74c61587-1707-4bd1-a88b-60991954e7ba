#!/bin/bash

# Quick DigitalOcean Droplet Status Check
# Uses your personal access token to check droplet status

DO_TOKEN="***********************************************************************"
API_BASE="https://api.digitalocean.com/v2"

echo "🔍 Checking your DigitalOcean droplets..."
echo "=========================================="

# Make API call to get droplets
response=$(curl -s -X GET \
    -H "Authorization: Bearer $DO_TOKEN" \
    "$API_BASE/droplets")

# Check if we have jq, if not use basic parsing
if command -v jq &> /dev/null; then
    echo "📊 Droplet Status:"
    echo "$response" | jq -r '.droplets[] | 
        "Name: \(.name)
         ID: \(.id)
         Status: \(.status)
         IP: \(.networks.v4[]? | select(.type=="public") | .ip_address)
         Region: \(.region.name)
         Size: \(.size.slug)
         Created: \(.created_at)
         ----------------------------------------"'
    
    echo ""
    echo "🌐 Testing connectivity..."
    
    # Test each droplet
    echo "$response" | jq -r '.droplets[] | "\(.name)|\(.networks.v4[]? | select(.type=="public") | .ip_address)"' | while IFS='|' read -r name ip; do
        if [ -n "$ip" ] && [ "$ip" != "null" ]; then
            echo -n "Testing $name ($ip): "
            if ping -c 1 -W 3 "$ip" > /dev/null 2>&1; then
                echo "✅ Reachable"
                
                # Test HTTP
                echo -n "  HTTP: "
                if curl -s -I --connect-timeout 5 "http://$ip" | head -1 | grep -q "HTTP"; then
                    echo "✅ Responding"
                else
                    echo "❌ Not responding"
                fi
                
                # Test API
                echo -n "  API: "
                if curl -s -I --connect-timeout 5 "http://$ip:3001" | head -1 | grep -q "HTTP"; then
                    echo "✅ Responding"
                else
                    echo "❌ Not responding"
                fi
            else
                echo "❌ Not reachable"
            fi
        fi
    done
    
else
    echo "⚠️  jq not found, showing raw response:"
    echo "$response"
fi

echo ""
echo "💡 Next steps:"
echo "1. If droplets are 'off', power them on using DigitalOcean dashboard"
echo "2. If droplets are 'active' but not reachable, check firewall settings"
echo "3. Run the fix script: ./fix-droplets.sh"
echo "4. Check DigitalOcean dashboard: https://cloud.digitalocean.com/droplets"
