# PowerShell script to check DigitalOcean droplets
# Uses your personal access token

$DO_TOKEN = "***********************************************************************"
$API_BASE = "https://api.digitalocean.com/v2"

Write-Host "🔍 Checking your DigitalOcean droplets..." -ForegroundColor Blue
Write-Host "==========================================" -ForegroundColor Blue

# Set up headers
$headers = @{
    "Authorization" = "Bearer $DO_TOKEN"
    "Content-Type" = "application/json"
}

try {
    # Make API call to get droplets
    $response = Invoke-RestMethod -Uri "$API_BASE/droplets" -Headers $headers -Method Get
    
    Write-Host "📊 Droplet Status:" -ForegroundColor Green
    Write-Host ""
    
    foreach ($droplet in $response.droplets) {
        $publicIP = $droplet.networks.v4 | Where-Object { $_.type -eq "public" } | Select-Object -First 1
        
        Write-Host "Name: $($droplet.name)" -ForegroundColor Yellow
        Write-Host "ID: $($droplet.id)"
        Write-Host "Status: $($droplet.status)" -ForegroundColor $(if ($droplet.status -eq "active") { "Green" } else { "Red" })
        Write-Host "IP: $($publicIP.ip_address)"
        Write-Host "Region: $($droplet.region.name)"
        Write-Host "Size: $($droplet.size.slug)"
        Write-Host "Created: $($droplet.created_at)"
        Write-Host "----------------------------------------"
        Write-Host ""
        
        # Test connectivity if IP exists
        if ($publicIP.ip_address) {
            $ip = $publicIP.ip_address
            Write-Host "🌐 Testing connectivity to $($droplet.name) ($ip)..." -ForegroundColor Cyan
            
            # Test ping
            $pingResult = Test-Connection -ComputerName $ip -Count 1 -Quiet
            if ($pingResult) {
                Write-Host "  ✅ Ping: Reachable" -ForegroundColor Green
                
                # Test HTTP
                try {
                    $httpResponse = Invoke-WebRequest -Uri "http://$ip" -TimeoutSec 5 -UseBasicParsing
                    Write-Host "  ✅ HTTP: Responding (Status: $($httpResponse.StatusCode))" -ForegroundColor Green
                } catch {
                    Write-Host "  ❌ HTTP: Not responding" -ForegroundColor Red
                }
                
                # Test API
                try {
                    $apiResponse = Invoke-WebRequest -Uri "http://$ip`:3001/api/expenses" -TimeoutSec 5 -UseBasicParsing
                    Write-Host "  ✅ API: Responding (Status: $($apiResponse.StatusCode))" -ForegroundColor Green
                } catch {
                    Write-Host "  ❌ API: Not responding" -ForegroundColor Red
                }
            } else {
                Write-Host "  ❌ Ping: Not reachable" -ForegroundColor Red
            }
            Write-Host ""
        }
    }
    
    Write-Host "💡 Next steps:" -ForegroundColor Yellow
    Write-Host "1. If droplets are 'off', power them on using DigitalOcean dashboard"
    Write-Host "2. If droplets are 'active' but not reachable, check firewall settings"
    Write-Host "3. Check DigitalOcean dashboard: https://cloud.digitalocean.com/droplets"
    Write-Host "4. For manual fixes, use the bash scripts if you have WSL/Git Bash"
    
} catch {
    Write-Host "❌ Error accessing DigitalOcean API:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host ""
    Write-Host "Please check:" -ForegroundColor Yellow
    Write-Host "1. Your API token is correct"
    Write-Host "2. You have internet connectivity"
    Write-Host "3. The token has the required permissions"
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
