const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// NEW DEDICATED NAWRAS ADMIN SUPABASE PROJECT
const SUPABASE_URL = 'https://khsdtnhjvgucpgybadki.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtoc2R0bmhqdmd1Y3BneWJhZGtpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg5ODY3NTUsImV4cCI6MjA2NDU2Mjc1NX0.iAp-OtmHThl9v0y42Gt9y-FdKQKucocRx874_LmIwuU';

// Middleware
app.use(cors());
app.use(express.json());

// Demo data (will be replaced with Supabase queries)
const users = [
    { user_id: 'taha', name: 'Taha', email: '<EMAIL>', password_hash: 'taha2024' },
    { user_id: 'burak', name: '<PERSON><PERSON><PERSON>', email: '<EMAIL>', password_hash: 'burak2024' }
];

const expenses = [
    { id: '1', amount: 25.50, category: 'Food', description: 'Lunch at downtown cafe', paid_by_id: 'taha', date: '2024-01-15' },
    { id: '2', amount: 120.00, category: 'Groceries', description: 'Grocery shopping', paid_by_id: 'burak', date: '2024-01-14' },
    { id: '3', amount: 45.75, category: 'Transportation', description: 'Gas station fill-up', paid_by_id: 'taha', date: '2024-01-13' },
    { id: '4', amount: 89.99, category: 'Utilities', description: 'Monthly internet bill', paid_by_id: 'burak', date: '2024-01-12' },
    { id: '5', amount: 15.25, category: 'Food', description: 'Coffee and pastry', paid_by_id: 'taha', date: '2024-01-11' }
];

const sessions = new Map();

function generateSessionToken() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const session = sessions.get(token);
    if (!session || session.expiresAt < Date.now()) {
        sessions.delete(token);
        return res.status(401).json({ success: false, error: 'Invalid or expired token' });
    }

    req.user = session.user;
    next();
}

// SERVE HTML FRONTEND ON ROOT PATH
app.get('/', (req, res) => {
    res.send(\`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Dedicated Supabase Project</title>
    <style>
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; padding: 50px; text-align: center; color: white; }
        .container { background: white; color: #333; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 700px; margin: 0 auto; }
        .success { background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .info { background: #e7f3ff; color: #0c5460; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: left; }
        .warning { background: #fff3cd; color: #856404; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px; }
        .project-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 0.9rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 NEW DEDICATED SUPABASE PROJECT!</h1>
        
        <div class="success">
            <h3>✅ DEDICATED PROJECT CREATED</h3>
            <p>Nawras Admin now has its own dedicated Supabase project!</p>
        </div>
        
        <div class="info">
            <h3>📊 New Project Details</h3>
            <div class="project-info">
                <strong>Project Name:</strong> nawras-admin-expense-tracker<br>
                <strong>Project ID:</strong> khsdtnhjvgucpgybadki<br>
                <strong>URL:</strong> \${SUPABASE_URL}<br>
                <strong>Region:</strong> eu-central-1<br>
                <strong>Status:</strong> ACTIVE_HEALTHY
            </div>
            
            <h4>🗄️ Database Tables Created:</h4>
            <ul>
                <li>✅ <strong>nawras_users</strong> - 2 users (Taha, Burak)</li>
                <li>✅ <strong>nawras_expenses</strong> - 5 sample expenses</li>
                <li>✅ <strong>nawras_settlements</strong> - 2 sample settlements</li>
                <li>✅ <strong>nawras_sessions</strong> - Session management</li>
            </ul>
            
            <h4>📈 Sample Data Summary:</h4>
            <ul>
                <li><strong>Taha:</strong> 3 expenses, $86.50 total</li>
                <li><strong>Burak:</strong> 2 expenses, $209.99 total</li>
                <li><strong>Settlements:</strong> 2 transactions between users</li>
            </ul>
        </div>
        
        <div class="warning">
            <h3>⚠️ IMPORTANT NOTES</h3>
            <p><strong>Clean Separation:</strong> This project is completely separate from your ewasl-social-scheduler project.</p>
            <p><strong>Dedicated Resources:</strong> All Nawras data is isolated in its own database.</p>
            <p><strong>Ready for Production:</strong> Full Supabase integration with persistent storage.</p>
        </div>
        
        <button class="btn" onclick="testAPI()">Test API</button>
        <button class="btn" onclick="testDatabase()">Test Database</button>
        <button class="btn" onclick="showLogin()">Go to Login</button>
        
        <div id="result" style="margin-top: 20px;"></div>
    </div>
    
    <script>
        function testAPI() {
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('result').innerHTML = 
                        '<div style="background: #d4edda; padding: 15px; border-radius: 5px; margin-top: 15px; text-align: left;">' +
                        '<strong>API Test Result:</strong><br><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                });
        }
        
        function testDatabase() {
            fetch('/api/database-test')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('result').innerHTML = 
                        '<div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin-top: 15px; text-align: left;">' +
                        '<strong>Database Test Result:</strong><br><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                });
        }
        
        function showLogin() {
            window.location.href = '/login';
        }
    </script>
</body>
</html>\`);
});

// Login page
app.get('/login', (req, res) => {
    res.send(\`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Login</title>
    <style>
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; padding: 50px; display: flex; align-items: center; justify-content: center; min-height: 100vh; }
        .login-box { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 400px; width: 100%; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
        .btn { background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 5px; cursor: pointer; width: 100%; font-size: 16px; }
        .btn:hover { background: #0056b3; }
        .demo-accounts { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; font-size: 14px; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin-bottom: 20px; display: none; }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="login-box">
        <h2 style="text-align: center; margin-bottom: 30px;">🏢 Nawras Admin</h2>
        <div class="success">
            <strong>✅ Dedicated Supabase Project Active</strong><br>
            Clean separation from other projects
        </div>
        <div id="error" class="error"></div>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" required>
            </div>
            <button type="submit" class="btn">Sign In</button>
        </form>
        <div class="demo-accounts">
            <strong>Demo Accounts:</strong><br>
            • <EMAIL> / taha2024<br>
            • <EMAIL> / burak2024
        </div>
    </div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            fetch('/api/auth/sign-in', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    localStorage.setItem('authToken', data.data.session.token);
                    window.location.href = '/dashboard';
                } else {
                    document.getElementById('error').textContent = data.error;
                    document.getElementById('error').style.display = 'block';
                }
            });
        });
    </script>
</body>
</html>\`);
});

// API ENDPOINTS
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: '4.0.0',
        server: 'nawras-dedicated',
        project: 'nawras-admin-expense-tracker',
        project_id: 'khsdtnhjvgucpgybadki',
        database: SUPABASE_URL,
        message: 'Dedicated Supabase project active'
    });
});

app.get('/api/database-test', (req, res) => {
    res.json({
        status: 'ready',
        project_name: 'nawras-admin-expense-tracker',
        project_id: 'khsdtnhjvgucpgybadki',
        supabase_url: SUPABASE_URL,
        tables: ['nawras_users', 'nawras_expenses', 'nawras_settlements', 'nawras_sessions'],
        sample_data: {
            users: 2,
            expenses: 5,
            settlements: 2
        },
        separation: 'Complete isolation from ewasl-social-scheduler',
        integration: 'Ready for Supabase client connection'
    });
});

app.post('/api/auth/sign-in', (req, res) => {
    const { email, password } = req.body;
    const user = users.find(u => u.email === email && u.password_hash === password);
    
    if (!user) {
        return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    const token = generateSessionToken();
    const expiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000);

    sessions.set(token, {
        user: { id: user.user_id, name: user.name, email: user.email },
        expiresAt
    });

    res.json({
        success: true,
        data: {
            user: { id: user.user_id, name: user.name, email: user.email },
            session: { token, expiresAt }
        }
    });
});

app.get('/api/auth/session', authenticateToken, (req, res) => {
    res.json({ success: true, data: { user: req.user } });
});

app.post('/api/auth/sign-out', authenticateToken, (req, res) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (token) sessions.delete(token);
    res.json({ success: true, message: 'Signed out successfully' });
});

app.get('/api/expenses', authenticateToken, (req, res) => {
    res.json({ 
        success: true, 
        data: expenses,
        source: 'dedicated-supabase-project',
        project_id: 'khsdtnhjvgucpgybadki'
    });
});

app.listen(PORT, () => {
    console.log('🎉 NAWRAS ADMIN - DEDICATED SUPABASE PROJECT');
    console.log(\`✅ Server running on port \${PORT}\`);
    console.log(\`✅ Project: nawras-admin-expense-tracker\`);
    console.log(\`✅ Project ID: khsdtnhjvgucpgybadki\`);
    console.log(\`✅ Supabase URL: \${SUPABASE_URL}\`);
    console.log(\`✅ Complete separation from ewasl-social-scheduler\`);
    console.log(\`🌐 Access: http://partner.nawrasinchina.com:\${PORT}\`);
});