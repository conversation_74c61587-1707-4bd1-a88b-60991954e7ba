# Environment Configuration Example
# Copy this file to .env.local and update the values

# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api
VITE_APP_NAME=Nawras Admin
VITE_APP_VERSION=1.0.0

# Development Settings
VITE_ENABLE_DEV_TOOLS=true
VITE_LOG_LEVEL=debug

# Production Settings (for production builds)
# VITE_API_BASE_URL=https://your-api-domain.com/api
# VITE_ENABLE_DEV_TOOLS=false
# VITE_LOG_LEVEL=error

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false

# Security
VITE_ENABLE_CSP=true
VITE_SECURE_COOKIES=false
