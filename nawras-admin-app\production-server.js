const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Production data
const users = [
    { id: 'taha', name: 'Taha', email: '<EMAIL>', password: 'taha2024' },
    { id: 'burak', name: '<PERSON><PERSON><PERSON>', email: '<EMAIL>', password: 'burak2024' }
];

const sessions = new Map();
const expenses = [
    { id: 1, amount: 25.50, category: 'Food', paidById: 'taha', date: '2024-01-15', description: 'Lunch at downtown cafe' },
    { id: 2, amount: 120.00, category: 'Groceries', paidById: 'burak', date: '2024-01-14', description: 'Grocery shopping' },
    { id: 3, amount: 45.75, category: 'Transportation', paidById: 'taha', date: '2024-01-13', description: 'Gas station fill-up' },
    { id: 4, amount: 89.99, category: 'Utilities', paidById: 'burak', date: '2024-01-12', description: 'Monthly internet bill' },
    { id: 5, amount: 15.25, category: 'Food', paidById: 'taha', date: '2024-01-11', description: 'Coffee and pastry' }
];

const settlements = [
    { id: 1, amount: 50.00, paidBy: 'taha', paidTo: 'burak', date: '2024-01-10', description: 'Settlement for shared expenses' },
    { id: 2, amount: 30.00, paidBy: 'burak', paidTo: 'taha', date: '2024-01-05', description: 'Dinner split payment' }
];

// Helper functions
function generateSessionToken() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const session = sessions.get(token);
    if (!session || session.expiresAt < Date.now()) {
        sessions.delete(token);
        return res.status(401).json({ success: false, error: 'Invalid or expired token' });
    }

    req.user = session.user;
    next();
}

// SERVE COMPLETE FRONTEND ON ROOT PATH
app.get('/', (req, res) => {
    res.send(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Expense Tracker</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
        }
        .login-container { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
            padding: 3rem; 
            max-width: 400px; 
            width: 100%; 
        }
        .app-container { 
            background: white; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
            overflow: hidden; 
            width: 100%; 
            max-width: 1200px; 
            min-height: 600px; 
            display: flex; 
        }
        .sidebar { 
            background: #2c3e50; 
            color: white; 
            width: 250px; 
            padding: 2rem 0; 
            display: flex; 
            flex-direction: column; 
        }
        .logo { 
            padding: 0 2rem 2rem; 
            border-bottom: 1px solid #34495e; 
            margin-bottom: 2rem; 
        }
        .logo h1 { 
            font-size: 1.5rem; 
            font-weight: 600; 
        }
        .nav-item { 
            padding: 1rem 2rem; 
            cursor: pointer; 
            transition: background 0.3s; 
            display: flex; 
            align-items: center; 
            gap: 0.75rem; 
        }
        .nav-item:hover, .nav-item.active { 
            background: #34495e; 
        }
        .main-content { 
            flex: 1; 
            padding: 2rem; 
            background: #f8f9fa; 
        }
        .header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 2rem; 
        }
        .header h2 { 
            color: #2c3e50; 
            font-size: 2rem; 
            font-weight: 600; 
        }
        .header p { 
            color: #6c757d; 
            margin-top: 0.5rem; 
        }
        .form-group { margin-bottom: 1rem; }
        .form-group label { 
            display: block; 
            margin-bottom: 0.5rem; 
            color: #2c3e50; 
            font-weight: 500; 
        }
        .form-group input { 
            width: 100%; 
            padding: 0.75rem; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            font-size: 1rem; 
        }
        .btn { 
            background: #667eea; 
            color: white; 
            border: none; 
            padding: 0.75rem 1.5rem; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 1rem; 
            transition: background 0.3s; 
        }
        .btn:hover { background: #5a6fd8; }
        .btn-primary { 
            background: #007bff; 
            width: 100%; 
            padding: 1rem; 
        }
        .btn-primary:hover { background: #0056b3; }
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 1.5rem; 
            margin-bottom: 2rem; 
        }
        .stat-card { 
            background: white; 
            padding: 1.5rem; 
            border-radius: 10px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .stat-card h3 { 
            color: #6c757d; 
            font-size: 0.9rem; 
            margin-bottom: 0.5rem; 
            text-transform: uppercase; 
            letter-spacing: 0.5px; 
        }
        .stat-value { 
            font-size: 2rem; 
            font-weight: 600; 
            margin-bottom: 0.5rem; 
        }
        .stat-subtitle { 
            color: #6c757d; 
            font-size: 0.9rem; 
        }
        .error-message { 
            background: #f8d7da; 
            color: #721c24; 
            padding: 1rem; 
            border-radius: 5px; 
            margin-bottom: 1rem; 
            border: 1px solid #f5c6cb; 
            display: none; 
        }
        .success-message { 
            background: #d4edda; 
            color: #155724; 
            padding: 1rem; 
            border-radius: 5px; 
            margin-bottom: 1rem; 
            border: 1px solid #c3e6cb; 
            display: none; 
        }
        .user-info { 
            margin-top: auto; 
            padding: 1rem 2rem; 
            border-top: 1px solid #34495e; 
        }
        .user-info p { 
            margin-bottom: 0.5rem; 
            font-size: 0.9rem; 
        }
        .logout-btn { 
            background: #dc3545; 
            color: white; 
            border: none; 
            padding: 0.5rem 1rem; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 0.9rem; 
        }
        .logout-btn:hover { background: #c82333; }
        .expenses-list { 
            background: white; 
            border-radius: 10px; 
            padding: 1.5rem; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .expense-item { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            padding: 1rem 0; 
            border-bottom: 1px solid #eee; 
        }
        .expense-item:last-child { border-bottom: none; }
        .expense-details h4 { 
            color: #2c3e50; 
            margin-bottom: 0.25rem; 
        }
        .expense-details p { 
            color: #6c757d; 
            font-size: 0.9rem; 
        }
        .expense-amount { 
            font-size: 1.25rem; 
            font-weight: 600; 
            color: #2c3e50; 
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <h2 style="text-align: center; margin-bottom: 2rem; color: #2c3e50;">Nawras Admin Login</h2>
        <div id="loginError" class="error-message"></div>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn-primary">Sign In</button>
        </form>
        <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 5px; font-size: 0.9rem;">
            <strong>Demo Accounts:</strong><br>
            • <EMAIL> / taha2024<br>
            • <EMAIL> / burak2024
        </div>
        <div style="margin-top: 1rem; padding: 1rem; background: #d4edda; border-radius: 5px; font-size: 0.9rem;">
            <strong>✅ PRODUCTION READY!</strong><br>
            Complete authentication system deployed<br>
            <strong>Access:</strong> http://partner.nawrasinchina.com:3001
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="app-container" style="display: none;">
        <div class="sidebar">
            <div class="logo"><h1>Nawras Admin</h1></div>
            <div class="nav-item active">📊 Dashboard</div>
            <div class="nav-item">💰 Add Expense</div>
            <div class="nav-item">🤝 Settlement</div>
            <div class="nav-item">📋 History</div>
            <div class="nav-item">📈 Reports</div>
            <div class="nav-item">⚙️ Settings</div>
            <div class="user-info">
                <p><strong id="userName">User</strong></p>
                <p id="userEmail"><EMAIL></p>
                <button class="logout-btn" onclick="logout()">Sign Out</button>
            </div>
        </div>
        <div class="main-content">
            <div class="header">
                <div>
                    <h2>Dashboard</h2>
                    <p>Welcome back! Here's your expense overview.</p>
                </div>
                <button class="btn">+ Add Expense</button>
            </div>
            <div id="errorMessage" class="error-message"></div>
            <div id="successMessage" class="success-message"></div>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Taha's Expenses</h3>
                    <div class="stat-value" style="color: #007bff;" id="tahaExpenses">$0.00</div>
                    <div class="stat-subtitle" id="tahaCount">0 expenses</div>
                </div>
                <div class="stat-card">
                    <h3>Burak's Expenses</h3>
                    <div class="stat-value" style="color: #28a745;" id="burakExpenses">$0.00</div>
                    <div class="stat-subtitle" id="burakCount">0 expenses</div>
                </div>
                <div class="stat-card">
                    <h3>Combined Total</h3>
                    <div class="stat-value" style="color: #6f42c1;" id="totalExpenses">$0.00</div>
                    <div class="stat-subtitle" id="totalCount">0 total expenses</div>
                </div>
                <div class="stat-card">
                    <h3>Net Balance</h3>
                    <div class="stat-value" style="color: #fd7e14;" id="netBalance">$0.00</div>
                    <div class="stat-subtitle">All settled!</div>
                </div>
            </div>
            <div class="expenses-list">
                <h3 style="margin-bottom: 1rem;">Recent Expenses</h3>
                <div id="expensesList">
                    <div style="text-align: center; padding: 2rem; color: #6c757d;">Loading expenses...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;

        // Initialize app
        if (authToken) { 
            checkSession(); 
        } else { 
            showLogin(); 
        }

        function showLogin() {
            document.getElementById('loginScreen').style.display = 'block';
            document.getElementById('mainApp').style.display = 'none';
        }

        function showApp() {
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('mainApp').style.display = 'flex';
            loadDashboardData();
        }

        function showError(message, elementId) {
            elementId = elementId || 'loginError';
            const errorEl = document.getElementById(elementId);
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            setTimeout(function() { 
                errorEl.style.display = 'none'; 
            }, 5000);
        }

        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.style.display = 'block';
            setTimeout(function() { 
                successEl.style.display = 'none'; 
            }, 3000);
        }

        function checkSession() {
            fetch('/api/auth/session', {
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .then(function(response) { 
                return response.json(); 
            })
            .then(function(data) {
                if (data.success && data.data) {
                    currentUser = data.data.user;
                    updateUserInfo();
                    showApp();
                } else {
                    localStorage.removeItem('authToken');
                    authToken = null;
                    showLogin();
                }
            })
            .catch(function(error) {
                console.error('Session check failed:', error);
                localStorage.removeItem('authToken');
                authToken = null;
                showLogin();
            });
        }

        function updateUserInfo() {
            if (currentUser) {
                document.getElementById('userName').textContent = currentUser.name;
                document.getElementById('userEmail').textContent = currentUser.email;
            }
        }

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const submitBtn = document.querySelector('.btn-primary');
            submitBtn.textContent = 'Signing in...';
            submitBtn.disabled = true;
            
            fetch('/api/auth/sign-in', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: email, password: password })
            })
            .then(function(response) { 
                return response.json(); 
            })
            .then(function(data) {
                if (data.success) {
                    authToken = data.data.session.token;
                    currentUser = data.data.user;
                    localStorage.setItem('authToken', authToken);
                    updateUserInfo();
                    showApp();
                    showSuccess('Successfully signed in!');
                } else {
                    showError(data.error || 'Login failed');
                }
            })
            .catch(function(error) {
                console.error('Login error:', error);
                showError('Network error. Please try again.');
            })
            .finally(function() {
                submitBtn.textContent = 'Sign In';
                submitBtn.disabled = false;
            });
        });

        function logout() {
            fetch('/api/auth/sign-out', {
                method: 'POST',
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .catch(function(error) {
                console.error('Logout error:', error);
            });
            
            localStorage.removeItem('authToken');
            authToken = null;
            currentUser = null;
            showLogin();
        }

        function loadDashboardData() {
            fetch('/api/expenses', {
                headers: { 'Authorization': 'Bearer ' + authToken }
            })
            .then(function(response) {
                if (response.status === 401) {
                    logout();
                    return;
                }
                return response.json();
            })
            .then(function(expensesData) {
                if (expensesData && expensesData.success) {
                    updateExpensesDisplay(expensesData.data);
                } else {
                    showError('Failed to load expenses', 'errorMessage');
                }
            })
            .catch(function(error) {
                console.error('Failed to load dashboard data:', error);
                showError('Failed to load data. Please refresh the page.', 'errorMessage');
            });
        }

        function updateExpensesDisplay(expenses) {
            let tahaTotal = 0, burakTotal = 0, tahaCount = 0, burakCount = 0;
            
            for (let i = 0; i < expenses.length; i++) {
                const expense = expenses[i];
                if (expense.paidById === 'taha') {
                    tahaTotal += expense.amount;
                    tahaCount++;
                } else if (expense.paidById === 'burak') {
                    burakTotal += expense.amount;
                    burakCount++;
                }
            }
            
            const total = tahaTotal + burakTotal;
            const balance = Math.abs(tahaTotal - burakTotal);
            
            document.getElementById('tahaExpenses').textContent = '$' + tahaTotal.toFixed(2);
            document.getElementById('tahaCount').textContent = tahaCount + ' expenses';
            document.getElementById('burakExpenses').textContent = '$' + burakTotal.toFixed(2);
            document.getElementById('burakCount').textContent = burakCount + ' expenses';
            document.getElementById('totalExpenses').textContent = '$' + total.toFixed(2);
            document.getElementById('totalCount').textContent = expenses.length + ' total expenses';
            document.getElementById('netBalance').textContent = '$' + balance.toFixed(2);
            
            const expensesList = document.getElementById('expensesList');
            if (expenses.length === 0) {
                expensesList.innerHTML = '<div style="text-align: center; padding: 2rem; color: #6c757d;">No expenses found</div>';
            } else {
                let html = '';
                for (let i = 0; i < expenses.length; i++) {
                    const expense = expenses[i];
                    html += '<div class="expense-item">';
                    html += '<div class="expense-details">';
                    html += '<h4>' + expense.description + '</h4>';
                    html += '<p>' + expense.category + ' • Paid by ' + (expense.paidById === 'taha' ? 'Taha' : 'Burak') + ' • ' + expense.date + '</p>';
                    html += '</div>';
                    html += '<div class="expense-amount">$' + expense.amount.toFixed(2) + '</div>';
                    html += '</div>';
                }
                expensesList.innerHTML = html;
            }
        }
    </script>
</body>
</html>`);
});

// API ENDPOINTS

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        server: 'production-ready',
        message: 'Complete authentication system with frontend',
        uptime: process.uptime(),
        memory: process.memoryUsage()
    });
});

// Authentication endpoints
app.post('/api/auth/sign-in', (req, res) => {
    const { email, password } = req.body;

    // Input validation
    if (!email || !password) {
        return res.status(400).json({ 
            success: false, 
            error: 'Email and password are required' 
        });
    }

    const user = users.find(u => u.email === email && u.password === password);
    if (!user) {
        return res.status(401).json({ 
            success: false, 
            error: 'Invalid credentials' 
        });
    }

    const token = generateSessionToken();
    const expiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days

    sessions.set(token, {
        user: { id: user.id, name: user.name, email: user.email },
        expiresAt,
        createdAt: Date.now()
    });

    console.log(`User ${user.name} signed in at ${new Date().toISOString()}`);

    res.json({
        success: true,
        data: {
            user: { id: user.id, name: user.name, email: user.email },
            session: { token, expiresAt }
        }
    });
});

app.get('/api/auth/session', authenticateToken, (req, res) => {
    res.json({
        success: true,
        data: {
            user: req.user
        }
    });
});

app.post('/api/auth/sign-out', authenticateToken, (req, res) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (token) {
        sessions.delete(token);
        console.log(`User signed out at ${new Date().toISOString()}`);
    }

    res.json({ 
        success: true, 
        message: 'Signed out successfully' 
    });
});

// Protected endpoints
app.get('/api/expenses', authenticateToken, (req, res) => {
    console.log(`Expenses accessed by ${req.user.name} at ${new Date().toISOString()}`);
    res.json({ 
        success: true, 
        data: expenses 
    });
});

app.post('/api/expenses', authenticateToken, (req, res) => {
    const { amount, category, paidById, date, description } = req.body;
    
    // Input validation
    if (!amount || !category || !paidById || !date || !description) {
        return res.status(400).json({
            success: false,
            error: 'All fields are required'
        });
    }

    const newExpense = {
        id: expenses.length + 1,
        amount: parseFloat(amount),
        category,
        paidById,
        date,
        description,
        createdAt: new Date().toISOString(),
        createdBy: req.user.id
    };
    
    expenses.push(newExpense);
    console.log(`New expense added by ${req.user.name}: $${amount} for ${description}`);
    
    res.json({ 
        success: true, 
        data: newExpense 
    });
});

app.get('/api/settlements', authenticateToken, (req, res) => {
    console.log(`Settlements accessed by ${req.user.name} at ${new Date().toISOString()}`);
    res.json({ 
        success: true, 
        data: settlements 
    });
});

// Session cleanup (run every hour)
setInterval(() => {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [token, session] of sessions.entries()) {
        if (session.expiresAt < now) {
            sessions.delete(token);
            cleanedCount++;
        }
    }
    
    if (cleanedCount > 0) {
        console.log(`Cleaned ${cleanedCount} expired sessions at ${new Date().toISOString()}`);
    }
}, 60 * 60 * 1000); // 1 hour

// Start server
app.listen(PORT, () => {
    console.log('🎉 NAWRAS ADMIN PRODUCTION SERVER STARTED');
    console.log(`✅ Server running on port ${PORT}`);
    console.log(`✅ Frontend serving on http://partner.nawrasinchina.com:${PORT}`);
    console.log(`✅ API endpoints available on /api/*`);
    console.log(`✅ Authentication system active`);
    console.log(`✅ Session management enabled`);
    console.log(`✅ Production ready with logging`);
    console.log(`📊 Memory usage: ${JSON.stringify(process.memoryUsage())}`);
});