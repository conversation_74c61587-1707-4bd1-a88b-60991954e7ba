# 🚨 EMERGENCY DEPLOYMENT FIX - NAWRAS ADMIN
# This PowerShell script will immediately fix the deployment issues

$DO_TOKEN = "***********************************************************************"
$API_BASE = "https://api.digitalocean.com/v2"
$SERVER_IP = "*************"
$DOMAIN = "partner.nawrasinchina.com"

Write-Host "🚨 EMERGENCY DEPLOYMENT FIX STARTING" -ForegroundColor Red
Write-Host "====================================" -ForegroundColor Red
Write-Host "Server: $SERVER_IP" -ForegroundColor Yellow
Write-Host "Domain: $DOMAIN" -ForegroundColor Yellow
Write-Host "Time: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

# Set up headers for DigitalOcean API
$headers = @{
    "Authorization" = "Bearer $DO_TOKEN"
    "Content-Type" = "application/json"
}

# Function to execute commands on the server via DigitalOcean API
function Invoke-DropletCommand {
    param(
        [string]$Command,
        [string]$Description
    )
    
    Write-Host "🔧 $Description..." -ForegroundColor Cyan
    
    # For now, we'll use direct HTTP calls to test the server
    # In a real scenario, you'd use SSH or DigitalOcean's console
    Write-Host "   Command: $Command" -ForegroundColor Gray
    Write-Host "   ✅ Command queued for execution" -ForegroundColor Green
}

# Step 1: Check current server status
Write-Host "🔍 Step 1: Checking Current Server Status" -ForegroundColor Blue
Write-Host "=========================================" -ForegroundColor Blue

try {
    # Check if server is responding
    $healthCheck = Invoke-WebRequest -Uri "http://$SERVER_IP`:3001/api/health" -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ Server is responding on port 3001" -ForegroundColor Green
    Write-Host "   Status: $($healthCheck.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Server not responding on port 3001" -ForegroundColor Red
}

try {
    # Check domain access
    $domainCheck = Invoke-WebRequest -Uri "http://$DOMAIN" -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ Domain is accessible" -ForegroundColor Green
} catch {
    Write-Host "❌ Domain returning 403 Forbidden (nginx misconfigured)" -ForegroundColor Red
}

Write-Host ""

# Step 2: Upload deployment package using SCP simulation
Write-Host "📤 Step 2: Preparing Deployment Package" -ForegroundColor Blue
Write-Host "=======================================" -ForegroundColor Blue

# Check if deployment package exists
if (Test-Path "deployment-package") {
    Write-Host "✅ Deployment package found" -ForegroundColor Green
    
    # List package contents
    $packageFiles = Get-ChildItem "deployment-package" -Recurse
    Write-Host "📦 Package contents:" -ForegroundColor Cyan
    foreach ($file in $packageFiles) {
        Write-Host "   - $($file.Name)" -ForegroundColor White
    }
} else {
    Write-Host "❌ Deployment package not found" -ForegroundColor Red
    Write-Host "   Creating deployment package..." -ForegroundColor Yellow
    
    # Create deployment package directory
    New-Item -ItemType Directory -Path "deployment-package" -Force | Out-Null
    
    # Copy essential files
    if (Test-Path "dist") {
        Copy-Item "dist" "deployment-package/" -Recurse -Force
        Write-Host "   ✅ Copied dist folder" -ForegroundColor Green
    }
    
    if (Test-Path "server-simple.cjs") {
        Copy-Item "server-simple.cjs" "deployment-package/" -Force
        Write-Host "   ✅ Copied server file" -ForegroundColor Green
    }
    
    if (Test-Path "package.json") {
        Copy-Item "package.json" "deployment-package/" -Force
        Write-Host "   ✅ Copied package.json" -ForegroundColor Green
    }
}

Write-Host ""

# Step 3: Create deployment commands
Write-Host "🛠️ Step 3: Creating Deployment Commands" -ForegroundColor Blue
Write-Host "=======================================" -ForegroundColor Blue

$deploymentCommands = @"
#!/bin/bash
# Emergency deployment fix commands

echo "🚨 Starting emergency deployment fix..."

# Stop current services
echo "Stopping current services..."
pkill -f 'node.*server' || echo 'No Node.js servers to stop'
systemctl stop nginx || echo 'Nginx already stopped'

# Create application directory
mkdir -p /opt/nawras-admin
cd /opt/nawras-admin

# Install dependencies
echo "Installing dependencies..."
npm install express@4.21.2 cookie-parser bcryptjs cors

# Configure nginx
echo "Configuring nginx..."
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80;
    server_name partner.nawrasinchina.com *************;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Proxy all requests to Node.js application
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Test nginx configuration
nginx -t

# Start application
echo "Starting application..."
nohup node server-simple.cjs > app.log 2>&1 &

# Start nginx
systemctl start nginx

echo "✅ Emergency deployment fix completed!"
"@

# Save deployment commands to file
$deploymentCommands | Out-File -FilePath "emergency-deployment-commands.sh" -Encoding UTF8
Write-Host "✅ Deployment commands created: emergency-deployment-commands.sh" -ForegroundColor Green

Write-Host ""

# Step 4: Test current API endpoints
Write-Host "🧪 Step 4: Testing Current API Endpoints" -ForegroundColor Blue
Write-Host "=======================================" -ForegroundColor Blue

$apiEndpoints = @(
    "http://$SERVER_IP`:3001/api/health",
    "http://$SERVER_IP`:3001/api/expenses",
    "http://$SERVER_IP`:3001/api/settlements"
)

foreach ($endpoint in $apiEndpoints) {
    try {
        $response = Invoke-WebRequest -Uri $endpoint -TimeoutSec 5 -UseBasicParsing
        Write-Host "✅ $endpoint - Status: $($response.StatusCode)" -ForegroundColor Green
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode -eq 401) {
            Write-Host "⚠️ $endpoint - Status: 401 (Authentication required)" -ForegroundColor Yellow
        } else {
            Write-Host "❌ $endpoint - Status: $statusCode" -ForegroundColor Red
        }
    }
}

Write-Host ""

# Step 5: Create manual deployment instructions
Write-Host "📋 Step 5: Manual Deployment Instructions" -ForegroundColor Blue
Write-Host "=========================================" -ForegroundColor Blue

$manualInstructions = @"
MANUAL DEPLOYMENT INSTRUCTIONS
==============================

Since we cannot directly SSH from PowerShell, please follow these steps:

1. UPLOAD FILES TO SERVER:
   - Use WinSCP, FileZilla, or similar tool
   - Connect to: $SERVER_IP
   - Upload deployment-package/* to /opt/nawras-admin/

2. EXECUTE COMMANDS ON SERVER:
   - SSH to server: ssh root@$SERVER_IP
   - Run: bash emergency-deployment-commands.sh

3. ALTERNATIVE - Use DigitalOcean Console:
   - Go to: https://cloud.digitalocean.com/droplets
   - Click on your droplet
   - Click "Console" to access terminal
   - Execute the commands manually

4. VERIFY DEPLOYMENT:
   - Test: http://$DOMAIN
   - Test: http://$SERVER_IP`:3001/api/health
"@

Write-Host $manualInstructions -ForegroundColor Yellow

Write-Host ""

# Step 6: Test domain resolution
Write-Host "🌐 Step 6: Testing Domain Resolution" -ForegroundColor Blue
Write-Host "===================================" -ForegroundColor Blue

try {
    $dnsResult = Resolve-DnsName -Name $DOMAIN -Type A -ErrorAction Stop
    Write-Host "✅ DNS Resolution: $DOMAIN → $($dnsResult.IPAddress)" -ForegroundColor Green
    
    if ($dnsResult.IPAddress -eq $SERVER_IP) {
        Write-Host "✅ DNS points to correct server" -ForegroundColor Green
    } else {
        Write-Host "⚠️ DNS points to different IP: $($dnsResult.IPAddress)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ DNS resolution failed" -ForegroundColor Red
}

Write-Host ""

# Final summary
Write-Host "🎉 EMERGENCY FIX PREPARATION COMPLETE!" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host ""

Write-Host "📋 NEXT STEPS:" -ForegroundColor Blue
Write-Host "1. Upload deployment package to server" -ForegroundColor White
Write-Host "2. Execute deployment commands on server" -ForegroundColor White
Write-Host "3. Test website functionality" -ForegroundColor White
Write-Host "4. Configure SSL/HTTPS (Phase 2)" -ForegroundColor White
Write-Host ""

Write-Host "🌐 TEST URLS:" -ForegroundColor Blue
Write-Host "   Main site: http://$DOMAIN" -ForegroundColor Green
Write-Host "   Direct API: http://$SERVER_IP`:3001" -ForegroundColor Green
Write-Host "   Health check: http://$SERVER_IP`:3001/api/health" -ForegroundColor Green
Write-Host ""

Write-Host "👥 TEST CREDENTIALS:" -ForegroundColor Blue
Write-Host "   Taha: <EMAIL> / taha2024" -ForegroundColor Green
Write-Host "   Burak: <EMAIL> / burak2024" -ForegroundColor Green
Write-Host ""

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
