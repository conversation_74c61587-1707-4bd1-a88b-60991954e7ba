#!/bin/bash

# Quick deployment script for DigitalOcean
# This script will be executed on the server

echo "🚀 Starting Nawras Admin deployment..."

# Update system
apt-get update -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Install PM2 for process management
npm install -g pm2

# Install Nginx
apt-get install -y nginx

# Create application directory
mkdir -p /opt/nawras-admin
cd /opt/nawras-admin

# Create backend server
cat > server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage with sample data
let expenses = [
  {
    id: 1,
    amount: 25.50,
    description: "Lunch at restaurant",
    category: "Food",
    paidById: "taha",
    date: "2024-01-15",
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    amount: 60.99,
    description: "Grocery shopping",
    category: "Groceries", 
    paidById: "burak",
    date: "2024-01-16",
    createdAt: new Date().toISOString()
  },
  {
    id: 3,
    amount: 15.75,
    description: "Coffee and snacks",
    category: "Food",
    paidById: "taha",
    date: "2024-01-17",
    createdAt: new Date().toISOString()
  }
];

let settlements = [
  {
    id: 1,
    amount: 30.00,
    paidBy: "taha",
    paidTo: "burak", 
    description: "Settlement for shared expenses",
    date: "2024-01-17",
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    amount: 25.75,
    paidBy: "burak",
    paidTo: "taha", 
    description: "Reimbursement for utilities",
    date: "2024-01-18",
    createdAt: new Date().toISOString()
  }
];

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

app.get('/api/expenses', (req, res) => {
  res.json({ 
    success: true, 
    data: expenses, 
    total: expenses.length,
    timestamp: new Date().toISOString()
  });
});

app.post('/api/expenses', (req, res) => {
  const expense = {
    id: expenses.length + 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  expenses.push(expense);
  res.json({ success: true, data: expense });
});

app.get('/api/settlements', (req, res) => {
  res.json({ 
    success: true, 
    data: settlements, 
    total: settlements.length,
    timestamp: new Date().toISOString()
  });
});

app.post('/api/settlements', (req, res) => {
  const settlement = {
    id: settlements.length + 1,
    ...req.body,
    createdAt: new Date().toISOString()
  };
  settlements.push(settlement);
  res.json({ success: true, data: settlement });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ success: false, error: 'Internal server error' });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Nawras Admin API server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
});
EOF

# Create package.json
cat > package.json << 'EOF'
{
  "name": "nawras-admin-backend",
  "version": "1.0.0",
  "description": "Nawras Admin Backend API",
  "main": "server.js",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5"
  },
  "scripts": {
    "start": "node server.js",
    "dev": "node server.js"
  }
}
EOF

# Install dependencies
npm install

# Start backend with PM2
pm2 start server.js --name "nawras-backend"
pm2 startup
pm2 save

# Configure Nginx for frontend
cat > /etc/nginx/sites-available/nawras-admin << 'EOF'
server {
    listen 80;
    server_name _;
    root /var/www/nawras-admin;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API proxy
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/nawras-admin /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Create web directory
mkdir -p /var/www/nawras-admin

# Test nginx configuration
nginx -t

# Restart nginx
systemctl restart nginx
systemctl enable nginx

echo "✅ Backend deployed and running on port 3001"
echo "✅ Nginx configured and running on port 80"
echo "🌐 API: http://***************:3001/api/health"
echo "🌐 Frontend will be available at: http://***************"
echo ""
echo "📝 Next steps:"
echo "1. Upload frontend build files to /var/www/nawras-admin"
echo "2. Test the application"
echo ""
echo "🎉 Deployment script completed!"
