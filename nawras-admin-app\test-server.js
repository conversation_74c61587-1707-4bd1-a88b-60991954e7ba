const express = require('express');
const app = express();
const PORT = 3030;

// CRITICAL: Serve <PERSON> on root path FIRST
app.get('/', (req, res) => {
    res.send(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - FRONTEND WORKING!</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            margin: 0; 
            padding: 50px; 
            text-align: center; 
            color: white; 
        }
        .container { 
            background: white; 
            color: #333; 
            padding: 40px; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1); 
            max-width: 600px; 
            margin: 0 auto; 
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 20px 0; 
            border: 1px solid #c3e6cb; 
        }
        .next-steps { 
            background: #e7f3ff; 
            color: #0c5460; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 20px 0; 
            text-align: left; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 SUCCESS!</h1>
        <h2>Nawras Admin Frontend is Working!</h2>
        
        <div class="success">
            <h3>✅ FRONTEND ISSUE FIXED!</h3>
            <p>The server is now properly serving HTML instead of JSON responses.</p>
        </div>
        
        <div class="next-steps">
            <h3>📋 Next Steps:</h3>
            <ul>
                <li>✅ HTML serving confirmed working</li>
                <li>🔄 Deploy complete frontend with authentication</li>
                <li>🔄 Move to port 3001</li>
                <li>🔄 Integrate Supabase database</li>
            </ul>
        </div>
        
        <p><strong>Test URL:</strong> http://partner.nawrasinchina.com:3030</p>
        <p><strong>Status:</strong> Phase 1 Step 1.3 Complete</p>
        
        <button onclick="testAPI()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">Test API</button>
        <div id="apiResult" style="margin-top: 20px;"></div>
    </div>

    <script>
        function testAPI() {
            fetch('/api/test')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('apiResult').innerHTML = 
                        '<div style="background: #d4edda; padding: 10px; border-radius: 5px; margin-top: 10px;">' +
                        '<strong>API Test Result:</strong> ' + JSON.stringify(data) + '</div>';
                })
                .catch(error => {
                    document.getElementById('apiResult').innerHTML = 
                        '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin-top: 10px;">' +
                        '<strong>API Error:</strong> ' + error.message + '</div>';
                });
        }
    </script>
</body>
</html>`);
});

// API endpoint for testing
app.get('/api/test', (req, res) => {
    res.json({
        status: 'ok',
        message: 'API working correctly',
        timestamp: new Date().toISOString(),
        server: 'test-server-3030'
    });
});

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        message: 'Test server working - HTML serving confirmed',
        port: PORT,
        timestamp: new Date().toISOString()
    });
});

app.listen(PORT, () => {
    console.log(`✅ TEST SERVER RUNNING ON PORT ${PORT}`);
    console.log(`🌐 Access: http://partner.nawrasinchina.com:${PORT}`);
    console.log(`✅ HTML serving confirmed working`);
});