#!/bin/bash

# Comprehensive diagnosis and fix script

echo "NAWRAS ADMIN - DIAGNOSIS AND FIX"
echo "================================="

# Step 1: System Check
echo "1. SYSTEM DIAGNOSIS"
echo "==================="

echo "Node.js version:"
node --version || echo "Node.js not found!"

echo "NPM version:"
npm --version || echo "NPM not found!"

echo "Current directory:"
pwd

echo "App directory contents:"
ls -la /opt/nawras-admin/ || echo "App directory not found!"

# Step 2: Install Node.js if missing
if ! command -v node &> /dev/null; then
    echo "Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
fi

# Step 3: Create app directory and files
echo ""
echo "2. SETTING UP APPLICATION"
echo "========================="

mkdir -p /opt/nawras-admin
cd /opt/nawras-admin

# Create package.json if missing
if [ ! -f "package.json" ]; then
    echo "Creating package.json..."
    cat > package.json << 'EOF'
{
  "name": "nawras-admin",
  "version": "1.0.0",
  "description": "Nawras Admin Expense Tracker",
  "main": "server-simple.cjs",
  "scripts": {
    "start": "node server-simple.cjs"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "cookie-parser": "^1.4.6"
  }
}
EOF
fi

# Install dependencies
echo "Installing dependencies..."
npm install --production

# Step 4: Create server file if missing
if [ ! -f "server-simple.cjs" ]; then
    echo "Creating server-simple.cjs..."
    cat > server-simple.cjs << 'EOF'
const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000', 'https://partner.nawrasinchina.com', 'http://partner.nawrasinchina.com'],
  credentials: true
}));
app.use(express.json());
app.use(cookieParser());

// Session storage
const sessions = new Map();

// Mock users
const mockUsers = [
  {
    id: "taha",
    email: "<EMAIL>",
    name: "Taha",
    password: "taha2024",
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: "burak",
    email: "<EMAIL>",
    name: "Burak",
    password: "burak2024",
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

// Authentication middleware
const requireAuth = (req, res, next) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') || req.cookies?.session;

  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  const session = sessions.get(sessionToken);
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    res.clearCookie('session');
    return res.status(401).json({
      success: false,
      error: 'Session expired'
    });
  }

  req.user = session.user;
  req.session = session;
  next();
};

// Health check endpoint (no auth required)
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Authentication routes
app.post('/api/auth/sign-in', (req, res) => {
  const { email, password } = req.body;
  const user = mockUsers.find(u => u.email === email && u.password === password);

  if (!user) {
    return res.status(401).json({
      success: false,
      error: 'Invalid email or password'
    });
  }

  const sessionToken = `session_${Date.now()}_${Math.random()}`;
  const session = {
    id: sessionToken,
    userId: user.id,
    user: {
      id: user.id,
      email: user.email,
      name: user.name,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    },
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    token: sessionToken,
  };

  sessions.set(sessionToken, session);

  res.cookie('session', sessionToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 7 * 24 * 60 * 60 * 1000,
  });

  res.json({
    success: true,
    data: { session, user: session.user }
  });
});

app.get('/api/auth/session', (req, res) => {
  const sessionToken = req.headers.authorization?.replace('Bearer ', '') || req.cookies?.session;

  if (!sessionToken || !sessions.has(sessionToken)) {
    return res.json({ success: true, data: null });
  }

  const session = sessions.get(sessionToken);
  if (new Date() > new Date(session.expiresAt)) {
    sessions.delete(sessionToken);
    res.clearCookie('session');
    return res.json({ success: true, data: null });
  }

  res.json({ success: true, data: session });
});

// Protected API routes
app.get('/api/expenses', requireAuth, (req, res) => {
  const expenses = [
    {id: 1, amount: 25.5, description: "Lunch at downtown cafe", category: "Food", paidById: "taha", date: "2024-01-15"},
    {id: 2, amount: 120, description: "Grocery shopping", category: "Groceries", paidById: "burak", date: "2024-01-14"},
    {id: 3, amount: 45.75, description: "Gas station fill-up", category: "Transportation", paidById: "taha", date: "2024-01-13"},
    {id: 4, amount: 89.99, description: "Monthly internet bill", category: "Utilities", paidById: "burak", date: "2024-01-12"},
    {id: 5, amount: 15.25, description: "Coffee and pastry", category: "Food", paidById: "taha", date: "2024-01-11"}
  ];
  
  res.json({
    success: true,
    data: expenses,
    total: expenses.length,
    timestamp: new Date().toISOString()
  });
});

app.get('/api/settlements', requireAuth, (req, res) => {
  const settlements = [
    {id: 1, amount: 30.00, paidBy: "taha", paidTo: "burak", description: "Settlement", date: "2024-01-17"},
    {id: 2, amount: 25.75, paidBy: "burak", paidTo: "taha", description: "Utilities", date: "2024-01-18"}
  ];
  
  res.json({
    success: true,
    data: settlements,
    total: settlements.length,
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Nawras Admin server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔐 Authentication enabled`);
  console.log(`👥 Users: <EMAIL>, <EMAIL>`);
});
EOF
fi

# Step 5: Start the server
echo ""
echo "3. STARTING SERVER"
echo "=================="

# Kill any existing processes
pkill -f node || echo "No existing node processes"

# Start server
echo "Starting authenticated server..."
export NODE_ENV=production
export PORT=3001

nohup node server-simple.cjs > /var/log/nawras-admin.log 2>&1 &
SERVER_PID=$!

echo "Server started with PID: $SERVER_PID"

# Wait for server to start
sleep 5

# Test server
echo ""
echo "4. TESTING SERVER"
echo "================="

echo "Testing health endpoint..."
if curl -s http://localhost:3001/api/health | grep -q '"status":"ok"'; then
    echo "✅ Health check PASSED"
    curl -s http://localhost:3001/api/health
else
    echo "❌ Health check FAILED"
    echo "Server log:"
    tail -20 /var/log/nawras-admin.log
fi

echo ""
echo "Testing authentication requirement..."
if curl -s http://localhost:3001/api/expenses | grep -q '401\|"success":false'; then
    echo "✅ Authentication requirement PASSED"
else
    echo "❌ Authentication requirement FAILED"
    echo "Expenses response:"
    curl -s http://localhost:3001/api/expenses
fi

echo ""
echo "DIAGNOSIS AND FIX COMPLETED!"
echo "============================"
