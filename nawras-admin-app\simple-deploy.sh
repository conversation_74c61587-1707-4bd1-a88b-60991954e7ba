#!/bin/bash

# Simple one-liner deployment for Nawras Admin
export DEBIAN_FRONTEND=noninteractive

# Update and install Node.js
apt-get update -y && curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && apt-get install -y nodejs nginx

# Install PM2
npm install -g pm2

# Create app directory and backend
mkdir -p /opt/nawras-admin && cd /opt/nawras-admin

# Create package.json
echo '{"name":"nawras-admin-backend","version":"1.0.0","main":"server.js","dependencies":{"express":"^4.18.2","cors":"^2.8.5"},"scripts":{"start":"node server.js"}}' > package.json

# Install dependencies
npm install

# Create server.js with inline content
cat > server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 3001;

app.use(cors());
app.use(express.json());

let expenses = [
  {id: 1, amount: 25.50, description: "Lunch", category: "Food", paidById: "taha", date: "2024-01-15", createdAt: new Date().toISOString()},
  {id: 2, amount: 60.99, description: "Groceries", category: "Groceries", paidById: "burak", date: "2024-01-16", createdAt: new Date().toISOString()},
  {id: 3, amount: 15.75, description: "Coffee", category: "Food", paidById: "taha", date: "2024-01-17", createdAt: new Date().toISOString()}
];

let settlements = [
  {id: 1, amount: 30.00, paidBy: "taha", paidTo: "burak", description: "Settlement", date: "2024-01-17", createdAt: new Date().toISOString()},
  {id: 2, amount: 25.75, paidBy: "burak", paidTo: "taha", description: "Utilities", date: "2024-01-18", createdAt: new Date().toISOString()}
];

app.get('/api/health', (req, res) => res.json({status: 'ok', timestamp: new Date().toISOString()}));
app.get('/api/expenses', (req, res) => res.json({success: true, data: expenses, total: expenses.length}));
app.post('/api/expenses', (req, res) => {
  const expense = {id: expenses.length + 1, ...req.body, createdAt: new Date().toISOString()};
  expenses.push(expense);
  res.json({success: true, data: expense});
});
app.get('/api/settlements', (req, res) => res.json({success: true, data: settlements, total: settlements.length}));
app.post('/api/settlements', (req, res) => {
  const settlement = {id: settlements.length + 1, ...req.body, createdAt: new Date().toISOString()};
  settlements.push(settlement);
  res.json({success: true, data: settlement});
});

app.listen(PORT, '0.0.0.0', () => console.log(`Server running on port ${PORT}`));
EOF

# Start with PM2
pm2 start server.js --name "nawras-backend"
pm2 startup
pm2 save

# Configure Nginx
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80;
    server_name _;
    
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location / {
        return 200 '<!DOCTYPE html><html><head><title>Nawras Admin</title></head><body style="font-family:Arial;text-align:center;padding:50px;"><h1>🚀 Nawras Admin</h1><h2>Backend API Running!</h2><p><a href="/api/health">Health Check</a> | <a href="/api/expenses">Expenses</a> | <a href="/api/settlements">Settlements</a></p></body></html>';
        add_header Content-Type text/html;
    }
}
EOF

# Configure firewall and restart services
ufw allow 22 && ufw allow 80 && ufw allow 3001 && ufw --force enable
systemctl restart nginx
systemctl enable nginx

echo "Deployment completed! Backend: http://***************:3001 Frontend: http://***************"
