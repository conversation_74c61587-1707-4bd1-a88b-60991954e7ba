const express = require('express');
const cors = require('cors');
const fs = require('fs');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage
const users = [
    { id: 'taha', name: 'Taha', email: '<EMAIL>', password: 'taha2024' },
    { id: 'burak', name: '<PERSON><PERSON><PERSON>', email: '<EMAIL>', password: 'burak2024' }
];

const sessions = new Map();
const expenses = [
    { id: 1, amount: 25.50, category: 'Food', paidById: 'taha', date: '2024-01-15', description: 'Lunch at downtown cafe' },
    { id: 2, amount: 120.00, category: 'Groceries', paidById: 'burak', date: '2024-01-14', description: 'Grocery shopping' },
    { id: 3, amount: 45.75, category: 'Transportation', paidById: 'taha', date: '2024-01-13', description: 'Gas station fill-up' },
    { id: 4, amount: 89.99, category: 'Utilities', paidById: 'burak', date: '2024-01-12', description: 'Monthly internet bill' },
    { id: 5, amount: 15.25, category: 'Food', paidById: 'taha', date: '2024-01-11', description: 'Coffee and pastry' }
];

const settlements = [
    { id: 1, amount: 50.00, paidBy: 'taha', paidTo: 'burak', date: '2024-01-10', description: 'Settlement for shared expenses' },
    { id: 2, amount: 30.00, paidBy: 'burak', paidTo: 'taha', date: '2024-01-05', description: 'Dinner split payment' }
];

// Helper functions
function generateSessionToken() {
    return 'session_' + Date.now() + '_' + Math.random();
}

function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const session = sessions.get(token);
    if (!session || session.expiresAt < Date.now()) {
        sessions.delete(token);
        return res.status(401).json({ success: false, error: 'Invalid or expired token' });
    }

    req.user = session.user;
    next();
}

// Serve the frontend
app.get('/', (req, res) => {
    try {
        const htmlContent = fs.readFileSync('/var/www/html/index.html', 'utf8');
        res.send(htmlContent);
    } catch (error) {
        // Fallback HTML if file doesn't exist
        res.send(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nawras Admin - Expense Tracker</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .container { background: white; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 3rem; max-width: 400px; width: 100%; text-align: center; }
        .btn { background: #007bff; color: white; border: none; padding: 1rem 2rem; border-radius: 5px; cursor: pointer; font-size: 1rem; text-decoration: none; display: inline-block; margin: 0.5rem; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="margin-bottom: 2rem; color: #2c3e50;">🎉 Nawras Admin</h1>
        <p style="margin-bottom: 2rem; color: #6c757d;">Port 80 Issue Resolved!</p>
        <p style="margin-bottom: 1rem;"><strong>✅ Server Running on Port 80</strong></p>
        <p style="margin-bottom: 1rem;"><strong>✅ Authentication System Active</strong></p>
        <p style="margin-bottom: 2rem;"><strong>✅ All APIs Working</strong></p>
        <a href="/api/health" class="btn">Check API Health</a>
        <p style="margin-top: 2rem; font-size: 0.9rem; color: #6c757d;">Frontend will load automatically once uploaded.</p>
    </div>
</body>
</html>`);
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        server: 'port-80-direct',
        message: 'Nginx issue resolved - serving directly on port 80'
    });
});

// Authentication endpoints
app.post('/api/auth/sign-in', (req, res) => {
    const { email, password } = req.body;

    const user = users.find(u => u.email === email && u.password === password);
    if (!user) {
        return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    const token = generateSessionToken();
    const expiresAt = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days

    sessions.set(token, {
        user: { id: user.id, name: user.name, email: user.email },
        expiresAt
    });

    res.json({
        success: true,
        data: {
            user: { id: user.id, name: user.name, email: user.email },
            session: { token, expiresAt }
        }
    });
});

app.get('/api/auth/session', authenticateToken, (req, res) => {
    res.json({
        success: true,
        data: {
            user: req.user
        }
    });
});

app.post('/api/auth/sign-out', authenticateToken, (req, res) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (token) {
        sessions.delete(token);
    }

    res.json({ success: true, message: 'Signed out successfully' });
});

// Protected endpoints
app.get('/api/expenses', authenticateToken, (req, res) => {
    res.json({ success: true, data: expenses });
});

app.post('/api/expenses', authenticateToken, (req, res) => {
    const { amount, category, paidById, date, description } = req.body;
    
    const newExpense = {
        id: expenses.length + 1,
        amount: parseFloat(amount),
        category,
        paidById,
        date,
        description
    };
    
    expenses.push(newExpense);
    res.json({ success: true, data: newExpense });
});

app.get('/api/settlements', authenticateToken, (req, res) => {
    res.json({ success: true, data: settlements });
});

// Start server on port 80
app.listen(80, () => {
    console.log('🎉 FINAL SERVER RUNNING ON PORT 80');
    console.log('✅ Nginx issue resolved');
    console.log('✅ Authentication system active');
    console.log('✅ Frontend serving on http://partner.nawrasinchina.com');
});