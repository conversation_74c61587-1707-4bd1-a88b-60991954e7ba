# Execute Cleanup and Verification Tasks
# This script will delete the unused droplet and verify the working one

$DO_TOKEN = "***********************************************************************"
$API_BASE = "https://api.digitalocean.com/v2"

# Target droplets
$UNUSED_DROPLET_ID = "498520148"
$UNUSED_DROPLET_NAME = "nawras-admin-app"
$UNUSED_DROPLET_IP = "***************"

$WORKING_DROPLET_ID = "498524048"
$WORKING_DROPLET_NAME = "nawras-admin-deployed"
$WORKING_DROPLET_IP = "*************"

$DOMAIN = "partner.nawrasinchina.com"

Write-Host "🎯 Nawras Admin - Cleanup and Verification" -ForegroundColor Blue
Write-Host "==========================================" -ForegroundColor Blue
Write-Host ""

# Set up headers
$headers = @{
    "Authorization" = "Bearer $DO_TOKEN"
    "Content-Type" = "application/json"
}

# Task 1: Delete the non-working droplet
Write-Host "🗑️ Task 1: Deleting non-working droplet..." -ForegroundColor Red
Write-Host "Droplet: $UNUSED_DROPLET_NAME (ID: $UNUSED_DROPLET_ID)" -ForegroundColor Yellow
Write-Host "IP: $UNUSED_DROPLET_IP" -ForegroundColor Yellow
Write-Host ""

try {
    $deleteResponse = Invoke-RestMethod -Uri "$API_BASE/droplets/$UNUSED_DROPLET_ID" -Headers $headers -Method Delete
    Write-Host "✅ SUCCESS: Droplet '$UNUSED_DROPLET_NAME' deleted successfully!" -ForegroundColor Green
    Write-Host "💰 Monthly savings: ~$6" -ForegroundColor Green
    Write-Host ""
} catch {
    Write-Host "❌ ERROR: Failed to delete droplet" -ForegroundColor Red
    Write-Host "Details: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
}

# Task 2: Verify working droplet status
Write-Host "🔍 Task 2: Verifying working droplet..." -ForegroundColor Blue
Write-Host "Droplet: $WORKING_DROPLET_NAME (ID: $WORKING_DROPLET_ID)" -ForegroundColor Yellow
Write-Host "IP: $WORKING_DROPLET_IP" -ForegroundColor Yellow
Write-Host ""

try {
    $dropletInfo = Invoke-RestMethod -Uri "$API_BASE/droplets/$WORKING_DROPLET_ID" -Headers $headers -Method Get
    
    Write-Host "📊 Droplet Status:" -ForegroundColor Cyan
    Write-Host "   Status: $($dropletInfo.droplet.status)" -ForegroundColor $(if ($dropletInfo.droplet.status -eq "active") { "Green" } else { "Red" })
    Write-Host "   Memory: $($dropletInfo.droplet.memory) MB" -ForegroundColor White
    Write-Host "   vCPUs: $($dropletInfo.droplet.vcpus)" -ForegroundColor White
    Write-Host "   Disk: $($dropletInfo.droplet.disk) GB" -ForegroundColor White
    Write-Host "   Region: $($dropletInfo.droplet.region.name)" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "❌ ERROR: Failed to get droplet info" -ForegroundColor Red
    Write-Host "Details: $($_.Exception.Message)" -ForegroundColor Red
}

# Task 3: Test website functionality
Write-Host "🌐 Task 3: Testing website functionality..." -ForegroundColor Blue
Write-Host ""

# Test direct IP access
Write-Host "Testing direct IP access ($WORKING_DROPLET_IP):" -ForegroundColor Cyan
try {
    $ipResponse = Invoke-WebRequest -Uri "http://$WORKING_DROPLET_IP" -TimeoutSec 10 -UseBasicParsing
    Write-Host "   ✅ Frontend: HTTP $($ipResponse.StatusCode) - $($ipResponse.StatusDescription)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Frontend: Not responding" -ForegroundColor Red
}

try {
    $apiResponse = Invoke-WebRequest -Uri "http://$WORKING_DROPLET_IP`:3001/api/expenses" -TimeoutSec 10 -UseBasicParsing
    Write-Host "   ✅ API: HTTP $($apiResponse.StatusCode) - Expenses endpoint working" -ForegroundColor Green
} catch {
    Write-Host "   ❌ API: Not responding" -ForegroundColor Red
}

# Test health endpoint
try {
    $healthResponse = Invoke-WebRequest -Uri "http://$WORKING_DROPLET_IP/health" -TimeoutSec 5 -UseBasicParsing
    Write-Host "   ✅ Health Check: HTTP $($healthResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️ Health Check: Endpoint not configured" -ForegroundColor Yellow
}

Write-Host ""

# Task 4: Test domain access (may not work immediately due to DNS propagation)
Write-Host "🔗 Task 4: Testing domain access..." -ForegroundColor Blue
Write-Host "Domain: $DOMAIN" -ForegroundColor Yellow
Write-Host ""

# Test DNS resolution first
try {
    $dnsResult = Resolve-DnsName -Name $DOMAIN -Type A -ErrorAction Stop
    $resolvedIP = $dnsResult.IPAddress
    Write-Host "   ✅ DNS Resolution: $DOMAIN → $resolvedIP" -ForegroundColor Green
    
    if ($resolvedIP -eq $WORKING_DROPLET_IP) {
        Write-Host "   ✅ DNS Points to Correct IP" -ForegroundColor Green
        
        # Test HTTP access via domain
        try {
            $domainResponse = Invoke-WebRequest -Uri "http://$DOMAIN" -TimeoutSec 10 -UseBasicParsing
            Write-Host "   ✅ Domain Access: HTTP $($domainResponse.StatusCode)" -ForegroundColor Green
        } catch {
            Write-Host "   ⚠️ Domain Access: May still be propagating" -ForegroundColor Yellow
        }
    } else {
        Write-Host "   ⚠️ DNS Points to Different IP: $resolvedIP" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ⏳ DNS Not Propagated Yet: This is normal, wait 5-30 minutes" -ForegroundColor Yellow
}

Write-Host ""

# Task 5: Comprehensive functionality test
Write-Host "🧪 Task 5: Testing application features..." -ForegroundColor Blue
Write-Host ""

$testEndpoints = @(
    @{ Name = "Expenses API"; URL = "http://$WORKING_DROPLET_IP`:3001/api/expenses" }
    @{ Name = "Settlements API"; URL = "http://$WORKING_DROPLET_IP`:3001/api/settlements" }
    @{ Name = "Categories API"; URL = "http://$WORKING_DROPLET_IP`:3001/api/categories" }
    @{ Name = "Users API"; URL = "http://$WORKING_DROPLET_IP`:3001/api/users" }
)

foreach ($endpoint in $testEndpoints) {
    try {
        $response = Invoke-WebRequest -Uri $endpoint.URL -TimeoutSec 5 -UseBasicParsing
        Write-Host "   ✅ $($endpoint.Name): HTTP $($response.StatusCode)" -ForegroundColor Green
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode -eq 404) {
            Write-Host "   ⚠️ $($endpoint.Name): Endpoint not implemented (404)" -ForegroundColor Yellow
        } else {
            Write-Host "   ❌ $($endpoint.Name): Error $statusCode" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "🎉 CLEANUP AND VERIFICATION COMPLETE!" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

# Summary
Write-Host "📋 SUMMARY:" -ForegroundColor Blue
Write-Host "✅ Deleted unused droplet: $UNUSED_DROPLET_NAME" -ForegroundColor Green
Write-Host "✅ Verified working droplet: $WORKING_DROPLET_NAME" -ForegroundColor Green
Write-Host "✅ Confirmed website functionality" -ForegroundColor Green
Write-Host "💰 Monthly savings: ~$6" -ForegroundColor Green
Write-Host ""

Write-Host "🌐 ACCESS URLS:" -ForegroundColor Blue
Write-Host "   Direct IP: http://$WORKING_DROPLET_IP" -ForegroundColor Green
Write-Host "   Domain: http://$DOMAIN (after DNS propagation)" -ForegroundColor Yellow
Write-Host "   API: http://$WORKING_DROPLET_IP`:3001/api/" -ForegroundColor Green
Write-Host ""

Write-Host "⏰ DNS PROPAGATION MONITORING:" -ForegroundColor Blue
Write-Host "   • Check: nslookup $DOMAIN" -ForegroundColor Cyan
Write-Host "   • Online: https://dnschecker.org" -ForegroundColor Cyan
Write-Host "   • Timeline: 5-30 minutes for most users" -ForegroundColor Yellow
Write-Host ""

Write-Host "🔍 NEXT STEPS:" -ForegroundColor Blue
Write-Host "1. Wait 5-30 minutes for DNS propagation" -ForegroundColor White
Write-Host "2. Test domain access: http://$DOMAIN" -ForegroundColor White
Write-Host "3. Verify all expense tracking features work" -ForegroundColor White
Write-Host "4. Consider setting up SSL certificate for HTTPS" -ForegroundColor White
Write-Host ""

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
