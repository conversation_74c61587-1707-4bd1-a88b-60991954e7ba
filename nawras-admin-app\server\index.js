const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Mock expense data (in-memory storage)
let mockExpenses = [
  {
    id: 1,
    amount: 25.50,
    description: "Lunch at downtown cafe",
    category: "Food",
    paidById: "taha",
    date: "2024-01-15",
    createdAt: "2024-01-15T12:30:00Z"
  },
  {
    id: 2,
    amount: 120.00,
    description: "Grocery shopping",
    category: "Groceries",
    paidById: "burak",
    date: "2024-01-14",
    createdAt: "2024-01-14T18:45:00Z"
  },
  {
    id: 3,
    amount: 45.75,
    description: "Gas station fill-up",
    category: "Transportation",
    paidById: "taha",
    date: "2024-01-13",
    createdAt: "2024-01-13T09:15:00Z"
  },
  {
    id: 4,
    amount: 89.99,
    description: "Monthly internet bill",
    category: "Utilities",
    paidById: "burak",
    date: "2024-01-12",
    createdAt: "2024-01-12T14:20:00Z"
  },
  {
    id: 5,
    amount: 15.25,
    description: "Coffee and pastry",
    category: "Food",
    paidById: "taha",
    date: "2024-01-11",
    createdAt: "2024-01-11T08:30:00Z"
  }
];

// Helper function to get next ID
let nextId = 6;

// Data sanitization function
function sanitizeExpenseData() {
  mockExpenses = mockExpenses.map(expense => ({
    ...expense,
    amount: parseFloat(expense.amount) || 0
  }));
}

// Data sanitization function for settlements
function sanitizeSettlementData() {
  mockSettlements = mockSettlements.map(settlement => ({
    ...settlement,
    amount: parseFloat(settlement.amount) || 0
  }));
}

// Mock settlement data (in-memory storage)
let mockSettlements = [
  {
    id: 1,
    amount: 50.00,
    paidBy: "taha",
    paidTo: "burak",
    description: "Settling grocery expenses",
    date: "2024-01-10",
    createdAt: "2024-01-10T15:30:00Z"
  },
  {
    id: 2,
    amount: 25.75,
    paidBy: "burak",
    paidTo: "taha",
    description: "Gas money settlement",
    date: "2024-01-05",
    createdAt: "2024-01-05T10:15:00Z"
  }
];

// Helper function for settlement IDs
let nextSettlementId = 3;

// Sanitize data on startup (after all data is defined)
sanitizeExpenseData();
sanitizeSettlementData();

// Routes
app.get('/api/expenses', (req, res) => {
  try {
    // Enhanced query parameter support for filtering
    const {
      category,
      paidBy,
      limit,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      search,
      sortBy = 'date',
      sortOrder = 'desc'
    } = req.query;

    let filteredExpenses = [...mockExpenses];

    // Category filter (partial match)
    if (category) {
      filteredExpenses = filteredExpenses.filter(expense =>
        expense.category.toLowerCase().includes(category.toLowerCase())
      );
    }

    // Paid by filter (exact match)
    if (paidBy) {
      filteredExpenses = filteredExpenses.filter(expense =>
        expense.paidById === paidBy
      );
    }

    // Date range filter
    if (startDate) {
      filteredExpenses = filteredExpenses.filter(expense =>
        new Date(expense.date) >= new Date(startDate)
      );
    }

    if (endDate) {
      filteredExpenses = filteredExpenses.filter(expense =>
        new Date(expense.date) <= new Date(endDate)
      );
    }

    // Amount range filter
    if (minAmount) {
      const min = parseFloat(minAmount);
      if (!isNaN(min)) {
        filteredExpenses = filteredExpenses.filter(expense =>
          expense.amount >= min
        );
      }
    }

    if (maxAmount) {
      const max = parseFloat(maxAmount);
      if (!isNaN(max)) {
        filteredExpenses = filteredExpenses.filter(expense =>
          expense.amount <= max
        );
      }
    }

    // Text search filter (searches description and category)
    if (search) {
      const searchTerm = search.toLowerCase();
      filteredExpenses = filteredExpenses.filter(expense =>
        expense.description.toLowerCase().includes(searchTerm) ||
        expense.category.toLowerCase().includes(searchTerm)
      );
    }

    // Sort expenses
    filteredExpenses.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'amount':
          comparison = a.amount - b.amount;
          break;
        case 'description':
          comparison = a.description.localeCompare(b.description);
          break;
        case 'category':
          comparison = a.category.localeCompare(b.category);
          break;
        case 'paidBy':
          comparison = a.paidById.localeCompare(b.paidById);
          break;
        case 'date':
        default:
          comparison = new Date(a.date) - new Date(b.date);
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    // Apply limit after filtering and sorting
    if (limit) {
      filteredExpenses = filteredExpenses.slice(0, parseInt(limit));
    }

    res.json({
      success: true,
      data: filteredExpenses,
      total: filteredExpenses.length,
      totalUnfiltered: mockExpenses.length,
      filters: {
        category,
        paidBy,
        startDate,
        endDate,
        minAmount,
        maxAmount,
        search,
        sortBy,
        sortOrder
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch expenses',
      message: error.message
    });
  }
});

// Get single expense
app.get('/api/expenses/:id', (req, res) => {
  try {
    const { id } = req.params;
    const expense = mockExpenses.find(exp => exp.id === parseInt(id));

    if (!expense) {
      return res.status(404).json({
        success: false,
        error: 'Expense not found'
      });
    }

    res.json({
      success: true,
      data: expense
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch expense',
      message: error.message
    });
  }
});

// Create new expense
app.post('/api/expenses', (req, res) => {
  try {
    const { amount, description, category, paidById, date } = req.body;

    // Validation
    if (!amount || !description || !category || !paidById || !date) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        message: 'amount, description, category, paidById, and date are required'
      });
    }

    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid amount',
        message: 'Amount must be a positive number'
      });
    }

    if (!['taha', 'burak'].includes(paidById)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid paidById',
        message: 'paidById must be either "taha" or "burak"'
      });
    }

    // Create new expense
    const newExpense = {
      id: nextId++,
      amount: numericAmount,
      description: description.trim(),
      category: category.trim(),
      paidById,
      date,
      createdAt: new Date().toISOString()
    };

    // Add to mock data
    mockExpenses.push(newExpense);

    res.status(201).json({
      success: true,
      data: newExpense,
      message: 'Expense created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to create expense',
      message: error.message
    });
  }
});

// Full update existing expense (PUT)
app.put('/api/expenses/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { amount, description, category, paidById, date } = req.body;

    // Validate required fields for full update
    if (!amount || !description || !category || !paidById || !date) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        message: 'amount, description, category, paidById, and date are required for full update'
      });
    }

    // Find the expense
    const expenseIndex = mockExpenses.findIndex(exp => exp.id === parseInt(id));

    if (expenseIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Expense not found'
      });
    }

    // Validate amount
    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid amount',
        message: 'Amount must be a positive number'
      });
    }

    // Validate paidById
    if (!['taha', 'burak'].includes(paidById)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid paidById',
        message: 'paidById must be either "taha" or "burak"'
      });
    }

    // Create fully updated expense
    const updatedExpense = {
      id: parseInt(id),
      amount: numericAmount,
      description: description.trim(),
      category: category.trim(),
      paidById,
      date,
      createdAt: mockExpenses[expenseIndex].createdAt // Preserve original creation time
    };

    // Replace the expense in the array
    mockExpenses[expenseIndex] = updatedExpense;

    res.json({
      success: true,
      data: updatedExpense,
      message: 'Expense updated successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to update expense',
      message: error.message
    });
  }
});

// Partial update existing expense (PATCH)
app.patch('/api/expenses/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { amount, description, category, paidById, date } = req.body;

    // Find the expense
    const expenseIndex = mockExpenses.findIndex(exp => exp.id === parseInt(id));

    if (expenseIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Expense not found'
      });
    }

    const existingExpense = mockExpenses[expenseIndex];

    // Validate updated fields
    if (amount !== undefined) {
      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount) || numericAmount <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid amount',
          message: 'Amount must be a positive number'
        });
      }
      existingExpense.amount = numericAmount;
    }

    if (description !== undefined) {
      if (!description.trim()) {
        return res.status(400).json({
          success: false,
          error: 'Invalid description',
          message: 'Description cannot be empty'
        });
      }
      existingExpense.description = description.trim();
    }

    if (category !== undefined) {
      if (!category.trim()) {
        return res.status(400).json({
          success: false,
          error: 'Invalid category',
          message: 'Category cannot be empty'
        });
      }
      existingExpense.category = category.trim();
    }

    if (paidById !== undefined) {
      if (!['taha', 'burak'].includes(paidById)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid paidById',
          message: 'paidById must be either "taha" or "burak"'
        });
      }
      existingExpense.paidById = paidById;
    }

    if (date !== undefined) {
      existingExpense.date = date;
    }

    // Update the expense in the array
    mockExpenses[expenseIndex] = existingExpense;

    res.json({
      success: true,
      data: existingExpense,
      message: 'Expense updated successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to update expense',
      message: error.message
    });
  }
});

// Delete expense
app.delete('/api/expenses/:id', (req, res) => {
  try {
    const { id } = req.params;
    const expenseIndex = mockExpenses.findIndex(exp => exp.id === parseInt(id));

    if (expenseIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Expense not found'
      });
    }

    // Remove the expense from the array
    const deletedExpense = mockExpenses.splice(expenseIndex, 1)[0];

    res.json({
      success: true,
      data: deletedExpense,
      message: 'Expense deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to delete expense',
      message: error.message
    });
  }
});

// Settlement endpoints

// Get all settlements
app.get('/api/settlements', (req, res) => {
  try {
    const { paidBy, paidTo, limit } = req.query;

    let filteredSettlements = [...mockSettlements];

    if (paidBy) {
      filteredSettlements = filteredSettlements.filter(settlement =>
        settlement.paidBy === paidBy
      );
    }

    if (paidTo) {
      filteredSettlements = filteredSettlements.filter(settlement =>
        settlement.paidTo === paidTo
      );
    }

    if (limit) {
      filteredSettlements = filteredSettlements.slice(0, parseInt(limit));
    }

    // Sort by date (newest first)
    filteredSettlements.sort((a, b) => new Date(b.date) - new Date(a.date));

    res.json({
      success: true,
      data: filteredSettlements,
      total: filteredSettlements.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch settlements',
      message: error.message
    });
  }
});

// Get single settlement
app.get('/api/settlements/:id', (req, res) => {
  try {
    const { id } = req.params;
    const settlement = mockSettlements.find(s => s.id === parseInt(id));

    if (!settlement) {
      return res.status(404).json({
        success: false,
        error: 'Settlement not found'
      });
    }

    res.json({
      success: true,
      data: settlement
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch settlement',
      message: error.message
    });
  }
});

// Create new settlement
app.post('/api/settlements', (req, res) => {
  try {
    const { amount, paidBy, paidTo, description, date } = req.body;

    // Validation
    if (!amount || !paidBy || !paidTo || !date) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        message: 'amount, paidBy, paidTo, and date are required'
      });
    }

    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid amount',
        message: 'Amount must be a positive number'
      });
    }

    if (!['taha', 'burak'].includes(paidBy) || !['taha', 'burak'].includes(paidTo)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid users',
        message: 'paidBy and paidTo must be either "taha" or "burak"'
      });
    }

    if (paidBy === paidTo) {
      return res.status(400).json({
        success: false,
        error: 'Invalid settlement',
        message: 'paidBy and paidTo cannot be the same person'
      });
    }

    // Create new settlement
    const newSettlement = {
      id: nextSettlementId++,
      amount: numericAmount,
      paidBy,
      paidTo,
      description: description ? description.trim() : '',
      date,
      createdAt: new Date().toISOString()
    };

    // Add to mock data
    mockSettlements.push(newSettlement);

    res.status(201).json({
      success: true,
      data: newSettlement,
      message: 'Settlement created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to create settlement',
      message: error.message
    });
  }
});

// Data sanitization endpoint
app.post('/api/sanitize', (req, res) => {
  try {
    sanitizeExpenseData();
    sanitizeSettlementData();

    res.json({
      success: true,
      message: 'Data sanitized successfully',
      expenses: mockExpenses.length,
      settlements: mockSettlements.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to sanitize data',
      message: error.message
    });
  }
});

// Analytics endpoints

// Monthly aggregation analytics
app.get('/api/analytics/monthly', (req, res) => {
  try {
    const { startDate, endDate, granularity = 'month' } = req.query;

    let filteredExpenses = [...mockExpenses];

    // Apply date filters
    if (startDate) {
      filteredExpenses = filteredExpenses.filter(expense =>
        new Date(expense.date) >= new Date(startDate)
      );
    }

    if (endDate) {
      filteredExpenses = filteredExpenses.filter(expense =>
        new Date(expense.date) <= new Date(endDate)
      );
    }

    // Group by month
    const monthlyData = {};

    filteredExpenses.forEach(expense => {
      const date = new Date(expense.date);
      const monthKey = granularity === 'month'
        ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        : `${date.getFullYear()}-W${Math.ceil(date.getDate() / 7)}`;

      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          month: monthKey,
          totalExpenses: 0,
          tahaExpenses: 0,
          burakExpenses: 0,
          expenseCount: 0,
          avgExpenseAmount: 0
        };
      }

      monthlyData[monthKey].totalExpenses += expense.amount;
      monthlyData[monthKey].expenseCount += 1;

      if (expense.paidById === 'taha') {
        monthlyData[monthKey].tahaExpenses += expense.amount;
      } else if (expense.paidById === 'burak') {
        monthlyData[monthKey].burakExpenses += expense.amount;
      }
    });

    // Calculate averages and sort
    const result = Object.values(monthlyData)
      .map(month => ({
        ...month,
        avgExpenseAmount: month.totalExpenses / month.expenseCount || 0
      }))
      .sort((a, b) => a.month.localeCompare(b.month));

    res.json({
      success: true,
      data: result,
      metadata: {
        totalExpenses: filteredExpenses.length,
        totalAmount: filteredExpenses.reduce((sum, exp) => sum + exp.amount, 0),
        dateRange: {
          start: startDate || 'all',
          end: endDate || 'all'
        },
        lastUpdated: new Date().toISOString(),
        currency: 'USD'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch monthly analytics',
      message: error.message
    });
  }
});

// Category analytics
app.get('/api/analytics/categories', (req, res) => {
  try {
    const { period = '6months', groupBy = 'category' } = req.query;

    let filteredExpenses = [...mockExpenses];

    // Apply period filter
    if (period !== 'all') {
      const periodMap = {
        '1week': 7,
        '1month': 30,
        '3months': 90,
        '6months': 180,
        '1year': 365
      };

      const days = periodMap[period] || 180;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      filteredExpenses = filteredExpenses.filter(expense =>
        new Date(expense.date) >= cutoffDate
      );
    }

    const totalAmount = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);

    // Group by category
    const categoryData = {};

    filteredExpenses.forEach(expense => {
      const category = expense.category;

      if (!categoryData[category]) {
        categoryData[category] = {
          category,
          amount: 0,
          count: 0,
          avgAmount: 0
        };
      }

      categoryData[category].amount += expense.amount;
      categoryData[category].count += 1;
    });

    // Calculate percentages and colors
    const categoryColors = {
      'Food': '#3B82F6',
      'Groceries': '#10B981',
      'Transportation': '#F59E0B',
      'Utilities': '#EF4444',
      'Entertainment': '#8B5CF6',
      'Healthcare': '#06B6D4',
      'Shopping': '#F97316',
      'Travel': '#84CC16',
      'Other': '#6B7280'
    };

    const result = Object.values(categoryData)
      .map(category => ({
        ...category,
        percentage: totalAmount > 0 ? (category.amount / totalAmount) * 100 : 0,
        color: categoryColors[category.category] || '#6B7280',
        avgAmount: category.amount / category.count || 0
      }))
      .sort((a, b) => b.amount - a.amount);

    res.json({
      success: true,
      data: result,
      metadata: {
        totalCategories: result.length,
        topCategory: result[0]?.category || 'None',
        totalExpenses: filteredExpenses.length,
        totalAmount,
        period,
        lastUpdated: new Date().toISOString(),
        currency: 'USD'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch category analytics',
      message: error.message
    });
  }
});

// User comparison analytics
app.get('/api/analytics/users', (req, res) => {
  try {
    const { period = '1year', granularity = 'month' } = req.query;

    let filteredExpenses = [...mockExpenses];

    // Apply period filter
    if (period !== 'all') {
      const periodMap = {
        '1week': 7,
        '1month': 30,
        '3months': 90,
        '6months': 180,
        '1year': 365
      };

      const days = periodMap[period] || 365;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      filteredExpenses = filteredExpenses.filter(expense =>
        new Date(expense.date) >= cutoffDate
      );
    }

    // Group by time period
    const userData = {};

    filteredExpenses.forEach(expense => {
      const date = new Date(expense.date);
      const periodKey = granularity === 'month'
        ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        : `${date.getFullYear()}-W${Math.ceil(date.getDate() / 7)}`;

      if (!userData[periodKey]) {
        userData[periodKey] = {
          period: periodKey,
          taha: 0,
          burak: 0,
          tahaCount: 0,
          burakCount: 0,
          difference: 0
        };
      }

      if (expense.paidById === 'taha') {
        userData[periodKey].taha += expense.amount;
        userData[periodKey].tahaCount += 1;
      } else if (expense.paidById === 'burak') {
        userData[periodKey].burak += expense.amount;
        userData[periodKey].burakCount += 1;
      }
    });

    // Calculate differences and sort
    const result = Object.values(userData)
      .map(period => ({
        ...period,
        difference: period.taha - period.burak
      }))
      .sort((a, b) => a.period.localeCompare(b.period));

    // Calculate balance history
    let runningBalance = 0;
    const balanceHistory = result.map(period => {
      runningBalance += period.difference;
      return {
        ...period,
        runningBalance
      };
    });

    res.json({
      success: true,
      data: result,
      metadata: {
        totalUsers: 2,
        balanceHistory,
        currentBalance: runningBalance,
        trend: runningBalance > 0 ? 'taha_owes' : runningBalance < 0 ? 'burak_owes' : 'balanced',
        period,
        granularity,
        lastUpdated: new Date().toISOString(),
        currency: 'USD'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user analytics',
      message: error.message
    });
  }
});

// Balance history analytics
app.get('/api/analytics/balance-history', (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    let filteredExpenses = [...mockExpenses];

    // Apply date filters
    if (startDate) {
      filteredExpenses = filteredExpenses.filter(expense =>
        new Date(expense.date) >= new Date(startDate)
      );
    }

    if (endDate) {
      filteredExpenses = filteredExpenses.filter(expense =>
        new Date(expense.date) <= new Date(endDate)
      );
    }

    // Sort by date
    filteredExpenses.sort((a, b) => new Date(a.date) - new Date(b.date));

    // Calculate running balance
    let runningBalance = 0;
    let cumulativeExpenses = 0;
    let totalSettlements = 0; // No settlements in mock data yet

    const balanceHistory = filteredExpenses.map(expense => {
      cumulativeExpenses += expense.amount;

      // Taha's expenses are positive (he owes), Burak's are negative (he's owed)
      if (expense.paidById === 'taha') {
        runningBalance += expense.amount;
      } else if (expense.paidById === 'burak') {
        runningBalance -= expense.amount;
      }

      return {
        date: expense.date,
        balance: runningBalance,
        cumulativeExpenses,
        settlements: totalSettlements,
        runningBalance
      };
    });

    res.json({
      success: true,
      data: balanceHistory,
      metadata: {
        currentBalance: runningBalance,
        trend: runningBalance > 0 ? 'taha_owes' : runningBalance < 0 ? 'burak_owes' : 'balanced',
        totalExpenses: filteredExpenses.length,
        totalAmount: cumulativeExpenses,
        dateRange: {
          start: startDate || 'all',
          end: endDate || 'all'
        },
        lastUpdated: new Date().toISOString(),
        currency: 'USD'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch balance history',
      message: error.message
    });
  }
});

// Time patterns analytics
app.get('/api/analytics/time-patterns', (req, res) => {
  try {
    const { period = '3months' } = req.query;

    let filteredExpenses = [...mockExpenses];

    // Apply period filter
    if (period !== 'all') {
      const periodMap = {
        '1week': 7,
        '1month': 30,
        '3months': 90,
        '6months': 180,
        '1year': 365
      };

      const days = periodMap[period] || 90;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      filteredExpenses = filteredExpenses.filter(expense =>
        new Date(expense.date) >= cutoffDate
      );
    }

    // Group by day of week and hour
    const timePatterns = {};

    filteredExpenses.forEach(expense => {
      const date = new Date(expense.date);
      const dayOfWeek = date.getDay();
      const hour = date.getHours();
      const key = `${dayOfWeek}-${hour}`;

      if (!timePatterns[key]) {
        timePatterns[key] = {
          dayOfWeek,
          hour,
          amount: 0,
          count: 0,
          dayName: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek],
          timeSlot: getTimeSlot(hour)
        };
      }

      timePatterns[key].amount += expense.amount;
      timePatterns[key].count += 1;
    });

    const result = Object.values(timePatterns);

    res.json({
      success: true,
      data: result,
      metadata: {
        totalPatterns: result.length,
        period,
        peakHour: result.reduce((max, curr) => curr.amount > max.amount ? curr : max, { hour: 0, amount: 0 }).hour,
        peakDay: result.reduce((max, curr) => curr.amount > max.amount ? curr : max, { dayName: 'None', amount: 0 }).dayName,
        lastUpdated: new Date().toISOString(),
        currency: 'USD'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch time patterns',
      message: error.message
    });
  }
});

// Helper function for time slots
function getTimeSlot(hour) {
  if (hour >= 6 && hour < 12) return 'Morning';
  if (hour >= 12 && hour < 18) return 'Afternoon';
  if (hour >= 18 && hour < 22) return 'Evening';
  return 'Night';
}

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Nawras Admin API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Nawras Admin API server running on port ${PORT}`);
  console.log(`📊 Expenses endpoint: http://localhost:${PORT}/api/expenses`);
  console.log(`❤️  Health check: http://localhost:${PORT}/api/health`);
});
