# GitHub Deployment Monitoring Script
# Monitors the deployment status after GitHub integration

$DOMAIN = "partner.nawrasinchina.com"
$GITHUB_REPO = "nawras-admin-partner"

Write-Host "🚀 GITHUB DEPLOYMENT MONITORING" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host "Domain: $DOMAIN" -ForegroundColor Yellow
Write-Host "Repository: $GITHUB_REPO" -ForegroundColor Yellow
Write-Host "Started: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

Write-Host "📋 DEPLOYMENT CHECKLIST:" -ForegroundColor Blue
Write-Host "========================" -ForegroundColor Blue
Write-Host "1. ✅ Local Git repository initialized and committed" -ForegroundColor Green
Write-Host "2. ⏳ Create GitHub repository: https://github.com/new" -ForegroundColor Yellow
Write-Host "3. ⏳ Push code to GitHub repository" -ForegroundColor Yellow
Write-Host "4. ⏳ Create DigitalOcean App: https://cloud.digitalocean.com/apps" -ForegroundColor Yellow
Write-Host "5. ⏳ Configure domain and SSL" -ForegroundColor Yellow
Write-Host "6. ⏳ Verify deployment and test endpoints" -ForegroundColor Yellow
Write-Host ""

Write-Host "📝 GITHUB REPOSITORY SETUP:" -ForegroundColor Blue
Write-Host "============================" -ForegroundColor Blue
Write-Host "Repository Name: nawras-admin-partner" -ForegroundColor Green
Write-Host "Description: Nawras Admin Partner - Expense Tracking System" -ForegroundColor Green
Write-Host "Visibility: Public (recommended)" -ForegroundColor Green
Write-Host "Initialize: Do NOT add README (we have files already)" -ForegroundColor Green
Write-Host ""

Write-Host "💻 COMMANDS TO PUSH TO GITHUB:" -ForegroundColor Blue
Write-Host "===============================" -ForegroundColor Blue
Write-Host "git remote add origin https://github.com/YOUR_USERNAME/nawras-admin-partner.git" -ForegroundColor Green
Write-Host "git branch -M main" -ForegroundColor Green
Write-Host "git push -u origin main" -ForegroundColor Green
Write-Host ""

Write-Host "🌐 DIGITALOCEAN APP CONFIGURATION:" -ForegroundColor Blue
Write-Host "===================================" -ForegroundColor Blue
Write-Host "App Name: nawras-admin-partner" -ForegroundColor Green
Write-Host "Source: GitHub repository" -ForegroundColor Green
Write-Host "Branch: main" -ForegroundColor Green
Write-Host "Build Command: npm install" -ForegroundColor Green
Write-Host "Run Command: npm start" -ForegroundColor Green
Write-Host "Port: 3001" -ForegroundColor Green
Write-Host "Environment: NODE_ENV=production" -ForegroundColor Green
Write-Host "Domain: partner.nawrasinchina.com" -ForegroundColor Green
Write-Host ""

# Function to test deployment status
function Test-GitHubDeployment {
    Write-Host "🔍 Testing deployment status..." -ForegroundColor Cyan
    
    $results = @{
        Domain = $false
        Health = $false
        GitHubDeployed = $false
        SSL = $false
    }
    
    # Test domain
    try {
        $domainTest = Invoke-WebRequest -Uri "https://$DOMAIN" -TimeoutSec 10 -UseBasicParsing
        if ($domainTest.StatusCode -eq 200) {
            $results.Domain = $true
            $results.SSL = $true
            Write-Host "✅ Domain: HTTPS working!" -ForegroundColor Green
            
            if ($domainTest.Content -like "*GitHub Deployed*") {
                Write-Host "🎉 GITHUB DEPLOYMENT DETECTED!" -ForegroundColor Green
            }
        }
    } catch {
        # Try HTTP if HTTPS fails
        try {
            $httpTest = Invoke-WebRequest -Uri "http://$DOMAIN" -TimeoutSec 5 -UseBasicParsing
            if ($httpTest.StatusCode -eq 200) {
                $results.Domain = $true
                Write-Host "✅ Domain: HTTP working (SSL pending)" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "❌ Domain: Not accessible yet" -ForegroundColor Red
        }
    }
    
    # Test API Health
    try {
        $healthUrl = if ($results.SSL) { "https://$DOMAIN/api/health" } else { "http://$DOMAIN/api/health" }
        $healthTest = Invoke-WebRequest -Uri $healthUrl -TimeoutSec 5 -UseBasicParsing
        if ($healthTest.StatusCode -eq 200) {
            $results.Health = $true
            Write-Host "✅ API Health: Working!" -ForegroundColor Green
            
            $healthData = $healthTest.Content | ConvertFrom-Json
            Write-Host "   Server: $($healthData.server)" -ForegroundColor White
            Write-Host "   Version: $($healthData.version)" -ForegroundColor White
            
            if ($healthData.server -eq "github-deployed") {
                $results.GitHubDeployed = $true
                Write-Host "🚀 GITHUB DEPLOYMENT CONFIRMED!" -ForegroundColor Green
            }
        }
    } catch {
        Write-Host "❌ API Health: Not accessible" -ForegroundColor Red
    }
    
    return $results
}

# Initial status check
Write-Host "🔍 INITIAL STATUS CHECK:" -ForegroundColor Blue
Write-Host "=========================" -ForegroundColor Blue
$initialStatus = Test-GitHubDeployment
Write-Host ""

if ($initialStatus.GitHubDeployed) {
    Write-Host "🎉 GITHUB DEPLOYMENT ALREADY SUCCESSFUL!" -ForegroundColor Green
    Write-Host "=========================================" -ForegroundColor Green
    Write-Host "✅ Domain: Working" -ForegroundColor Green
    Write-Host "✅ SSL: $(if ($initialStatus.SSL) { 'Enabled' } else { 'Pending' })" -ForegroundColor Green
    Write-Host "✅ API: Working" -ForegroundColor Green
    Write-Host "✅ GitHub: Deployed" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Your application is live at:" -ForegroundColor Green
    Write-Host "   Main Site: https://$DOMAIN" -ForegroundColor Green
    Write-Host "   API Health: https://$DOMAIN/api/health" -ForegroundColor Green
    Write-Host "   API Endpoints: All working" -ForegroundColor Green
} else {
    Write-Host "⏳ WAITING FOR GITHUB DEPLOYMENT..." -ForegroundColor Yellow
    Write-Host "====================================" -ForegroundColor Yellow
    Write-Host "Please complete the GitHub and DigitalOcean setup steps above." -ForegroundColor White
    Write-Host ""
    
    # Monitor for deployment
    Write-Host "🔍 Monitoring for deployment (checking every 30 seconds)..." -ForegroundColor Cyan
    
    for ($i = 1; $i -le 20; $i++) {
        Write-Host "📊 Check #$i - $(Get-Date -Format 'HH:mm:ss')" -ForegroundColor Blue
        
        $status = Test-GitHubDeployment
        
        if ($status.GitHubDeployed) {
            Write-Host ""
            Write-Host "🎉 GITHUB DEPLOYMENT SUCCESSFUL!" -ForegroundColor Green
            Write-Host "=================================" -ForegroundColor Green
            Write-Host "✅ Domain: Working" -ForegroundColor Green
            Write-Host "✅ SSL: $(if ($status.SSL) { 'Enabled' } else { 'Pending' })" -ForegroundColor Green
            Write-Host "✅ API: Working" -ForegroundColor Green
            Write-Host "✅ GitHub: Deployed" -ForegroundColor Green
            Write-Host ""
            Write-Host "🌐 Your application is live at:" -ForegroundColor Green
            Write-Host "   Main Site: https://$DOMAIN" -ForegroundColor Green
            Write-Host "   API Health: https://$DOMAIN/api/health" -ForegroundColor Green
            Write-Host "   API Endpoints: All working" -ForegroundColor Green
            break
        }
        
        Write-Host ""
        if ($i -lt 20) {
            Start-Sleep -Seconds 30
        }
    }
}

Write-Host ""
Write-Host "📋 NEXT STEPS:" -ForegroundColor Blue
Write-Host "===============" -ForegroundColor Blue
Write-Host "1. Test all functionality on the live site" -ForegroundColor White
Write-Host "2. Configure SSL certificate (automatic with custom domain)" -ForegroundColor White
Write-Host "3. Set up monitoring and alerts" -ForegroundColor White
Write-Host "4. Plan for authentication system integration" -ForegroundColor White
Write-Host "5. Consider adding database integration" -ForegroundColor White
Write-Host ""
Write-Host "🎯 DEPLOYMENT COMPLETE!" -ForegroundColor Green
Write-Host "Your Nawras Admin Partner application is now deployed with GitHub integration!" -ForegroundColor Green
